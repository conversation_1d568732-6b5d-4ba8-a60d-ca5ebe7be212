// StepProcessor.js
// Component that processes individual algorithm steps

import React, { useState, useEffect, useCallback } from 'react';
import { useStep } from '../context/StepContext';
import { useSimulation, SimulationState } from '../context/SimulationContext';

/**
 * StepProcessor - Processes individual algorithm steps
 * 
 * This component handles the processing of individual algorithm steps,
 * including step type detection, execution, and completion notification.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.stepHandlers - Object mapping step types to handler functions
 * @param {Function} props.onStepStart - Callback when a step starts
 * @param {Function} props.onStepProgress - Callback with step progress (0-1)
 * @param {Function} props.onStepComplete - Callback when a step completes
 * @param {Function} props.getStepDuration - Function to get step duration based on type
 */
const StepProcessor = ({
  stepHandlers = {},
  onStepStart,
  onStepProgress,
  onStepComplete,
  getStepDuration
}) => {
  // Get contexts
  const { 
    currentStep, 
    isAnimating,
    startStepAnimation,
    stopStepAnimation
  } = useStep();
  
  const { 
    state,
    speed,
    getDelay
  } = useSimulation();

  // Animation state
  const [progress, setProgress] = useState(0);
  const [animationFrame, setAnimationFrame] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [duration, setDuration] = useState(null);

  // Get step duration
  const calculateDuration = useCallback((stepType) => {
    // If a custom function is provided, use it
    if (getStepDuration) {
      return getStepDuration(stepType, speed);
    }

    // Default duration based on speed
    // Faster for simple steps, slower for complex steps
    const baseDelay = getDelay();
    
    // Adjust duration based on step type
    switch (stepType) {
      case 'comparison':
        return baseDelay * 0.7;
      case 'swap':
        return baseDelay * 1.2;
      case 'merge':
        return baseDelay * 1.5;
      case 'split':
        return baseDelay * 0.8;
      case 'place':
        return baseDelay * 1.0;
      default:
        return baseDelay;
    }
  }, [getDelay, getStepDuration, speed]);

  // Start step animation
  const startStepProcessing = useCallback(() => {
    // Skip if no current step or already animating
    if (!currentStep || animationFrame) {
      return;
    }

    // Determine if this step needs animation
    const needsAnimation = 
      currentStep.type !== 'initial' &&
      currentStep.type !== 'complete' &&
      currentStep.type !== 'description';

    // Call the onStepStart callback
    if (onStepStart) {
      onStepStart(currentStep);
    }

    if (needsAnimation) {
      // Calculate duration for this step
      const stepDuration = calculateDuration(currentStep.type);
      setDuration(stepDuration);

      // Start animation
      setProgress(0);
      setStartTime(Date.now());
      startStepAnimation();

      // Start animation frame loop
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const newProgress = Math.min(elapsed / stepDuration, 1);

        setProgress(newProgress);

        // Call progress callback
        if (onStepProgress) {
          onStepProgress(newProgress, currentStep);
        }

        if (newProgress < 1) {
          // Continue animation
          const frame = requestAnimationFrame(animate);
          setAnimationFrame(frame);
        } else {
          // Animation complete
          setProgress(1);
          setAnimationFrame(null);
          stopStepAnimation();

          // Call the step handler if available
          if (stepHandlers[currentStep.type]) {
            stepHandlers[currentStep.type](currentStep, 1);
          }

          // Call the onStepComplete callback
          if (onStepComplete) {
            onStepComplete(currentStep);
          }
        }
      };

      // Start the animation
      const frame = requestAnimationFrame(animate);
      setAnimationFrame(frame);
    } else {
      // No animation needed, complete immediately
      
      // Call the step handler if available
      if (stepHandlers[currentStep.type]) {
        stepHandlers[currentStep.type](currentStep, 1);
      }

      // Call the onStepComplete callback
      if (onStepComplete) {
        onStepComplete(currentStep);
      }
    }
  }, [
    currentStep, 
    animationFrame, 
    calculateDuration, 
    onStepStart, 
    onStepProgress, 
    onStepComplete, 
    startStepAnimation, 
    stopStepAnimation,
    stepHandlers
  ]);

  // Process step when it changes or animation state changes
  useEffect(() => {
    // Skip if no current step
    if (!currentStep) {
      return;
    }

    // Start processing if we're in the right state
    if (state === SimulationState.RUNNING && !isAnimating) {
      startStepProcessing();
    }
  }, [currentStep, state, isAnimating, startStepProcessing]);

  // Call step handler during animation
  useEffect(() => {
    // Skip if no current step or not animating
    if (!currentStep || !isAnimating || !stepHandlers[currentStep.type]) {
      return;
    }

    // Call the step handler with the current progress
    stepHandlers[currentStep.type](currentStep, progress);
  }, [currentStep, isAnimating, progress, stepHandlers]);

  // Clean up animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [animationFrame]);

  // This component doesn't render anything
  return null;
};

export default StepProcessor;
