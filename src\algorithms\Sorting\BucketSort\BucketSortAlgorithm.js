// BucketSortAlgorithm.js
// Implementation of the Bucket Sort algorithm with step generation

/**
 * Generates steps for the Bucket Sort algorithm
 * @param {Array} arr - The array to sort
 * @param {Number} bucketCount - Number of buckets to use (default: 5)
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateBucketSortSteps = (arr, bucketCount = 5) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    buckets: [],
    movement: 'Initialize Bucket Sort'
  });

  // Find the minimum and maximum values
  const min = Math.min(...inputArray);
  const max = Math.max(...inputArray);

  steps.push({
    type: 'findMinMax',
    array: [...inputArray],
    min,
    max,
    movement: `Found minimum value: ${min} and maximum value: ${max}`
  });

  // Create buckets
  const buckets = Array.from({ length: bucketCount }, () => []);

  steps.push({
    type: 'createBuckets',
    array: [...inputArray],
    buckets: JSON.parse(JSON.stringify(buckets)),
    bucketCount,
    movement: `Created ${bucketCount} empty buckets`
  });

  // Calculate the range for each bucket
  const range = (max - min) / bucketCount + 1;

  steps.push({
    type: 'calculateRange',
    array: [...inputArray],
    buckets: JSON.parse(JSON.stringify(buckets)),
    range,
    movement: `Calculated bucket range: (${max} - ${min}) / ${bucketCount} + 1 = ${range.toFixed(2)}`
  });

  // Track which indices have been distributed to buckets
  const distributedIndices = [];

  // Distribute elements into buckets
  for (let i = 0; i < inputArray.length; i++) {
    const value = inputArray[i];
    // Calculate bucket index
    const bucketIndex = Math.floor((value - min) / range);

    // Add step before placing in bucket
    steps.push({
      type: 'distribute',
      array: [...inputArray],
      buckets: JSON.parse(JSON.stringify(buckets)),
      currentIndex: i,
      currentValue: value,
      bucketIndex,
      distributedIndices: [...distributedIndices], // Include the current distributed indices
      movement: `Distributing element ${value} from index ${i} to bucket ${bucketIndex}`
    });

    // Place element in bucket
    buckets[bucketIndex].push(value);

    // Add this index to the distributed indices
    distributedIndices.push(i);

    // Add step after placing in bucket
    steps.push({
      type: 'placed',
      array: [...inputArray],
      buckets: JSON.parse(JSON.stringify(buckets)),
      currentIndex: i,
      currentValue: value,
      bucketIndex,
      distributedIndices: [...distributedIndices], // Include the updated distributed indices
      movement: `Placed element ${value} in bucket ${bucketIndex}`
    });
  }

  // Add step to show all elements distributed
  steps.push({
    type: 'distributed',
    array: [...inputArray],
    buckets: JSON.parse(JSON.stringify(buckets)),
    distributedIndices: [...distributedIndices], // Include all distributed indices
    movement: 'All elements distributed into buckets'
  });

  // Sort individual buckets
  for (let i = 0; i < bucketCount; i++) {
    if (buckets[i].length <= 1) {
      // Skip sorting if bucket has 0 or 1 element
      if (buckets[i].length === 1) {
        steps.push({
          type: 'skipSort',
          array: [...inputArray],
          buckets: JSON.parse(JSON.stringify(buckets)),
          currentBucket: i,
          distributedIndices: [...distributedIndices],
          movement: `Bucket ${i} has only ${buckets[i].length} element, no need to sort`
        });
      }
      continue;
    }

    // Add step before sorting bucket
    steps.push({
      type: 'sortBucket',
      array: [...inputArray],
      buckets: JSON.parse(JSON.stringify(buckets)),
      currentBucket: i,
      distributedIndices: [...distributedIndices],
      movement: `Sorting bucket ${i} with ${buckets[i].length} elements`
    });

    // Sort the bucket (using insertion sort)
    const bucket = buckets[i];
    for (let j = 1; j < bucket.length; j++) {
      const current = bucket[j];
      let k = j - 1;

      // Add step before insertion sort iteration
      steps.push({
        type: 'insertionStart',
        array: [...inputArray],
        buckets: JSON.parse(JSON.stringify(buckets)),
        currentBucket: i,
        currentIndex: j,
        currentValue: current,
        distributedIndices: [...distributedIndices],
        movement: `Insertion sort: comparing ${current} with previous elements in bucket ${i}`
      });

      while (k >= 0 && bucket[k] > current) {
        // Add step for comparison
        steps.push({
          type: 'compare',
          array: [...inputArray],
          buckets: JSON.parse(JSON.stringify(buckets)),
          currentBucket: i,
          compareIndices: [k, j],
          distributedIndices: [...distributedIndices],
          movement: `Comparing ${bucket[k]} > ${current} in bucket ${i}`
        });

        // Shift element
        bucket[k + 1] = bucket[k];

        // Add step after shifting
        steps.push({
          type: 'shift',
          array: [...inputArray],
          buckets: JSON.parse(JSON.stringify(buckets)),
          currentBucket: i,
          shiftIndex: k,
          distributedIndices: [...distributedIndices],
          movement: `Shifted ${bucket[k]} to position ${k + 1} in bucket ${i}`
        });

        k--;
      }

      // Place element in correct position
      bucket[k + 1] = current;

      // Add step after insertion
      steps.push({
        type: 'insert',
        array: [...inputArray],
        buckets: JSON.parse(JSON.stringify(buckets)),
        currentBucket: i,
        insertIndex: k + 1,
        insertValue: current,
        distributedIndices: [...distributedIndices],
        movement: `Inserted ${current} at position ${k + 1} in bucket ${i}`
      });
    }

    // Update bucket in buckets array
    buckets[i] = bucket;

    // Add step after sorting bucket
    steps.push({
      type: 'bucketSorted',
      array: [...inputArray],
      buckets: JSON.parse(JSON.stringify(buckets)),
      currentBucket: i,
      distributedIndices: [...distributedIndices],
      movement: `Bucket ${i} sorted`
    });
  }

  // Add step to show all buckets sorted
  steps.push({
    type: 'allBucketsSorted',
    array: [...inputArray],
    buckets: JSON.parse(JSON.stringify(buckets)),
    distributedIndices: [...distributedIndices],
    movement: 'All buckets sorted'
  });

  // Concatenate sorted buckets
  const sortedArray = [];
  let outputIndex = 0;

  for (let i = 0; i < bucketCount; i++) {
    // Add step before concatenating bucket
    steps.push({
      type: 'concatenateBucket',
      array: [...inputArray],
      buckets: JSON.parse(JSON.stringify(buckets)),
      sortedArray: [...sortedArray],
      currentBucket: i,
      distributedIndices: [...distributedIndices],
      movement: `Concatenating elements from bucket ${i}`
    });

    // Add elements from current bucket to sorted array
    for (let j = 0; j < buckets[i].length; j++) {
      const value = buckets[i][j];

      // Add step before adding element to result
      steps.push({
        type: 'concatenateElement',
        array: [...inputArray],
        buckets: JSON.parse(JSON.stringify(buckets)),
        sortedArray: [...sortedArray],
        currentBucket: i,
        currentIndex: j,
        currentValue: value,
        outputIndex,
        distributedIndices: [...distributedIndices],
        movement: `Adding ${value} from bucket ${i} to position ${outputIndex} in result array`
      });

      // Add element to sorted array
      sortedArray.push(value);
      outputIndex++;

      // Add step after adding element to result
      steps.push({
        type: 'elementAdded',
        array: [...inputArray],
        buckets: JSON.parse(JSON.stringify(buckets)),
        sortedArray: [...sortedArray],
        currentBucket: i,
        currentIndex: j,
        currentValue: value,
        distributedIndices: [...distributedIndices],
        movement: `Added ${value} to result array`
      });
    }

    // Add step after concatenating bucket
    if (buckets[i].length > 0) {
      steps.push({
        type: 'bucketConcatenated',
        array: [...inputArray],
        buckets: JSON.parse(JSON.stringify(buckets)),
        sortedArray: [...sortedArray],
        currentBucket: i,
        distributedIndices: [...distributedIndices],
        movement: `Concatenated all elements from bucket ${i}`
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    sortedArray: [...sortedArray],
    buckets: JSON.parse(JSON.stringify(buckets)),
    sorted: Array.from({ length: sortedArray.length }, (_, i) => i),
    distributedIndices: [...distributedIndices],
    movement: 'Bucket Sort complete'
  });

  return { steps, sortedArray };
};

// Default export
const BucketSortAlgorithm = {
  generateSteps: generateBucketSortSteps
};

export default BucketSortAlgorithm;
