// StepBoardWrapper.js
// A wrapper component that integrates the StepBoard components into algorithm visualizations

import React, { useState, useEffect } from 'react';
import { Box, FormControlLabel, Switch, useTheme, Paper, Typography } from '@mui/material';
import StepBoard2D from './StepBoard2D';
import StepBoard3D from './StepBoard3D';

/**
 * A wrapper component that integrates the StepBoard components into algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.steps - Array of step descriptions
 * @param {number} props.currentStep - Current step index
 * @param {function} props.onStepChange - Function to call when step changes
 * @param {boolean} props.default3D - Whether to use 3D by default
 * @param {boolean} props.showToggle - Whether to show the 2D/3D toggle
 * @param {Object} props.sx - Additional styles
 */
const StepBoardWrapper = ({
  steps = [],
  currentStep = 0,
  onStepChange = () => {},
  default3D = true,
  showToggle = true,
  sx = {}
}) => {
  const theme = useTheme();
  const [use3D, setUse3D] = useState(default3D);
  const [webGLSupported, setWebGLSupported] = useState(true);

  // Check if device likely supports WebGL
  useEffect(() => {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        // WebGL not supported, fallback to 2D
        setWebGLSupported(false);
        setUse3D(false);
      }
    } catch (e) {
      console.error('Error checking WebGL support:', e);
      setWebGLSupported(false);
      setUse3D(false);
    }
  }, []);

  // Format steps for display
  console.log('StepBoardWrapper received steps:', steps);
  const formattedSteps = Array.isArray(steps) && steps.length > 0 ?
    steps.map((step, index) => {
      if (typeof step === 'string') {
        return step;
      } else if (step && step.description) {
        return step.description;
      } else {
        return `Step ${index + 1}`;
      }
    }) : ['No steps available'];
  console.log('StepBoardWrapper formatted steps:', formattedSteps);

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        borderRadius: 1,
        width: '100%', // Make sure it takes full width of its container
        maxWidth: '100%',
        ...sx
      }}
    >
      <Typography
        variant="subtitle1"
        sx={{
          mb: 1,
          fontWeight: 500,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        Algorithm Steps

        {showToggle && webGLSupported && (
          <FormControlLabel
            control={
              <Switch
                checked={use3D}
                onChange={(e) => setUse3D(e.target.checked)}
                size="small"
                color="primary"
              />
            }
            label="3D View"
            sx={{ mr: 0 }}
          />
        )}
      </Typography>

      <Box sx={{
        height: use3D ? 300 : 'auto',
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 1,
        overflow: 'hidden',
        mb: 1
      }}>
        {use3D && webGLSupported ? (
          <StepBoard3D
            steps={formattedSteps}
            currentStep={currentStep}
            onStepChange={onStepChange}
            width={14}
            height={3.5}
            animationSpeed={1}
          />
        ) : (
          <StepBoard2D
            steps={formattedSteps}
            currentStep={currentStep}
            onStepChange={onStepChange}
          />
        )}
      </Box>
    </Paper>
  );
};

export default StepBoardWrapper;
