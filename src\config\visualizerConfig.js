/**
 * Default configuration for the AlgorithmVisualizer
 * These settings can be overridden at the algorithm or visualization level
 */

// Default scene configuration
export const defaultSceneConfig = {
  // Camera settings
  camera: {
    position: [0, 2, 15],
    fov: 60,
    near: 0.1,
    far: 1000,
    lookAt: [0, 0, 0]
  },

  // Lighting settings
  lighting: {
    ambient: {
      enabled: true,
      intensity: 0.4
    },
    directional: [
      {
        enabled: true,
        position: [10, 10, 5],
        intensity: 0.8,
        castShadow: true,
        shadowMapSize: [2048, 2048],
        shadowCameraBounds: [-10, 10, 10, -10],
        shadowCameraNearFar: [0.1, 50],
        shadowBias: -0.001
      },
      {
        enabled: true,
        position: [-5, 8, -5],
        intensity: 0.3,
        castShadow: false,
        useDynamicColor: true
      }
    ],
    hemisphere: {
      enabled: true,
      intensity: 0.5,
      useDynamicColors: true
    },
    spot: {
      enabled: true,
      position: [0, 10, 0],
      angle: 0.3,
      penumbra: 0.8,
      intensity: 0.5,
      castShadow: true,
      shadowMapSize: [1024, 1024],
      shadowBias: -0.001
    }
  },

  // Environment settings
  environment: {
    enabled: true,
    preset: "city" // Options: "sunset", "dawn", "night", "warehouse", "forest", "apartment", "studio", "city", "park", "lobby"
  },

  // Controls settings
  controls: {
    orbit: {
      enabled: true,
      enableDamping: true,
      dampingFactor: 0.05,
      enableZoom: true,
      enableRotate: true,
      enablePan: true,
      autoRotate: false
    }
  },

  // Rendering settings
  rendering: {
    shadows: true,
    antialias: true,
    shadowMapType: "PCFSoft", // Options: "Basic", "PCF", "PCFSoft", "VSM"
    pixelRatio: window.devicePixelRatio
  },

  // Physics settings (for future use)
  physics: {
    enabled: false,
    gravity: [0, -9.8, 0]
  }
};

// Algorithm-specific configurations
export const algorithmConfigs = {
  // Sorting algorithms
  BubbleSort: {
    camera: {
      position: [0, 3, 10],
      fov: 60,
      lookAt: [0, 0, 0]
    }
  },

  MergeSort: {
    camera: {
      position: [0, 3, 12],
      fov: 55,
      lookAt: [0, 0, 0]
    }
  },

  QuickSort: {
    camera: {
      position: [0, 3, 12],
      fov: 55,
      lookAt: [0, 0, 0]
    }
  },

  HeapSort: {
    camera: {
      position: [0, 5, 15],
      fov: 50,
      lookAt: [0, 0, 0]
    }
  },

  // Graph algorithms
  BFS: {
    camera: {
      position: [0, 10, 20],
      fov: 50,
      lookAt: [0, 0, 0]
    }
  },

  DFS: {
    camera: {
      position: [0, 10, 20],
      fov: 50,
      lookAt: [0, 0, 0]
    }
  },

  Dijkstra: {
    camera: {
      position: [0, 15, 25],
      fov: 45,
      lookAt: [0, 0, 0]
    }
  }
};

/**
 * Get the configuration for a specific algorithm
 * @param {string} algorithmName - The name of the algorithm
 * @returns {Object} - The merged configuration (default + algorithm-specific)
 */
export const getAlgorithmConfig = (algorithmName) => {
  // Start with the default configuration
  const config = JSON.parse(JSON.stringify(defaultSceneConfig));

  // If there's an algorithm-specific configuration, merge it with the default
  if (algorithmConfigs[algorithmName]) {
    return mergeConfigs(config, algorithmConfigs[algorithmName]);
  }

  return config;
};

/**
 * Deep merge two configuration objects
 * @param {Object} target - The target object to merge into
 * @param {Object} source - The source object to merge from
 * @returns {Object} - The merged object
 */
const mergeConfigs = (target, source) => {
  // Create a new object to avoid modifying the original
  const result = { ...target };

  // Iterate through all properties in the source object
  for (const key in source) {
    // If the property is an object and exists in both target and source, merge recursively
    if (
      typeof source[key] === 'object' &&
      source[key] !== null &&
      !Array.isArray(source[key]) &&
      target[key]
    ) {
      result[key] = mergeConfigs(target[key], source[key]);
    }
    // If the property is an array, replace the entire array
    else if (Array.isArray(source[key])) {
      result[key] = [...source[key]];
    }
    // Otherwise, just copy the property
    else {
      result[key] = source[key];
    }
  }

  return result;
};
