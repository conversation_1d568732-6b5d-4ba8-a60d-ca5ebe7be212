// StepVisualizer.js
// Component that visualizes algorithm steps

import React, { useState, useEffect, useCallback } from 'react';
import { useStep } from '../context/StepContext';
import { useSimulation } from '../context/SimulationContext';
import { useAlgorithmData } from '../context/AlgorithmDataContext';

/**
 * StepVisualizer - Visualizes algorithm steps
 * 
 * This component handles the visualization of algorithm steps,
 * including animations and transitions between steps.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.visualizers - Object mapping step types to visualizer components
 * @param {Function} props.defaultVisualizer - Default visualizer component
 * @param {Function} props.onRender - Callback when a step is rendered
 */
const StepVisualizer = ({
  visualizers = {},
  defaultVisualizer: DefaultVisualizer,
  onRender
}) => {
  // Get contexts
  const { 
    currentStep, 
    isAnimating,
    stepMetadata
  } = useStep();
  
  const { 
    state
  } = useSimulation();
  
  const { 
    inputData,
    intermediateState
  } = useAlgorithmData();

  // Track the current visualizer component
  const [VisualizerComponent, setVisualizerComponent] = useState(null);

  // Update the visualizer component when the step changes
  useEffect(() => {
    if (!currentStep) {
      setVisualizerComponent(null);
      return;
    }

    // Get the visualizer for this step type
    const stepVisualizer = visualizers[currentStep.type];

    // Use the step-specific visualizer, or fall back to the default
    setVisualizerComponent(() => stepVisualizer || DefaultVisualizer);

    // Call the onRender callback
    if (onRender) {
      onRender(currentStep);
    }
  }, [currentStep, visualizers, DefaultVisualizer, onRender]);

  // If no visualizer component, render nothing
  if (!VisualizerComponent || !currentStep) {
    return null;
  }

  // Render the visualizer component with all necessary props
  return (
    <VisualizerComponent
      step={currentStep}
      isAnimating={isAnimating}
      state={state}
      inputData={inputData}
      intermediateState={intermediateState}
      metadata={stepMetadata}
    />
  );
};

export default StepVisualizer;
