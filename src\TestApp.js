import React from 'react';
import { CssB<PERSON>line, ThemeProvider, createTheme } from '@mui/material';
import TestPage from './pages/TestPage';

// Create a simple theme
const theme = createTheme({
    palette: {
        mode: 'light',
    },
});

function TestApp() {
    return (
        <ThemeProvider theme={theme}>
            <CssBaseline />
            <TestPage />
        </ThemeProvider>
    );
}

export default TestApp;
