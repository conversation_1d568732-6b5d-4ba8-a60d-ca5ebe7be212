// PrimalityTestController.js
// This component provides the controls for the Primality Test algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, TextField } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FunctionsIcon from '@mui/icons-material/Functions';
import SettingsIcon from '@mui/icons-material/Settings';

// Import algorithm functions
import { generatePrimalityTestSteps } from './PrimalityTestAlgorithm';

const PrimalityTestController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [number, setNumber] = useState(params?.number || 17);
  const [method, setMethod] = useState(params?.method || 'trial_division');

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { number, method });

    // Update params first
    onParamChange({
      number,
      method
    });

    // Set steps and movements directly
    try {
      const result = generatePrimalityTestSteps({
        number,
        method
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [number, method, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle parameter changes
  const handleNumberChange = useCallback((event) => {
    const value = parseInt(event.target.value, 10);
    if (!isNaN(value) && value >= 0) {
      setNumber(value);
    }
  }, []);

  const handleMethodChange = useCallback((event) => {
    setMethod(event.target.value);
  }, []);

  // Handle apply button click
  const handleApplyClick = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Trial Division
  const trialDivisionPseudocode = [
    { code: "function isPrime_trialDivision(n):", lineNumber: 1, indent: 0 },
    { code: "    // Handle special cases", lineNumber: 2, indent: 0 },
    { code: "    if n <= 1:", lineNumber: 3, indent: 1 },
    { code: "        return false", lineNumber: 4, indent: 2 },
    { code: "    if n <= 3:", lineNumber: 5, indent: 1 },
    { code: "        return true", lineNumber: 6, indent: 2 },
    { code: "    // Check divisibility by small primes", lineNumber: 7, indent: 0 },
    { code: "    if n % 2 == 0 or n % 3 == 0:", lineNumber: 8, indent: 1 },
    { code: "        return false", lineNumber: 9, indent: 2 },
    { code: "    // Check all numbers of form 6k ± 1 up to sqrt(n)", lineNumber: 10, indent: 0 },
    { code: "    i = 5", lineNumber: 11, indent: 1 },
    { code: "    while i * i <= n:", lineNumber: 12, indent: 1 },
    { code: "        if n % i == 0 or n % (i + 2) == 0:", lineNumber: 13, indent: 2 },
    { code: "            return false", lineNumber: 14, indent: 3 },
    { code: "        i += 6", lineNumber: 15, indent: 2 },
    { code: "    return true", lineNumber: 16, indent: 1 },
  ];

  // Pseudocode for Fermat Primality Test
  const fermatPseudocode = [
    { code: "function isPrime_fermat(n, k):", lineNumber: 1, indent: 0 },
    { code: "    // Handle special cases", lineNumber: 2, indent: 0 },
    { code: "    if n <= 1:", lineNumber: 3, indent: 1 },
    { code: "        return false", lineNumber: 4, indent: 2 },
    { code: "    if n <= 3:", lineNumber: 5, indent: 1 },
    { code: "        return true", lineNumber: 6, indent: 2 },
    { code: "    // Fermat's test: if a^(n-1) ≡ 1 (mod n) for several random a, n is probably prime", lineNumber: 7, indent: 0 },
    { code: "    for i = 1 to k:", lineNumber: 8, indent: 1 },
    { code: "        a = random(2, n-2)", lineNumber: 9, indent: 2 },
    { code: "        if modPow(a, n-1, n) != 1:", lineNumber: 10, indent: 2 },
    { code: "            return false", lineNumber: 11, indent: 3 },
    { code: "    // If all tests pass, n is probably prime", lineNumber: 12, indent: 0 },
    { code: "    return true", lineNumber: 13, indent: 1 },
  ];

  // Pseudocode for Miller-Rabin Primality Test
  const millerRabinPseudocode = [
    { code: "function isPrime_millerRabin(n, k):", lineNumber: 1, indent: 0 },
    { code: "    // Handle special cases", lineNumber: 2, indent: 0 },
    { code: "    if n <= 1:", lineNumber: 3, indent: 1 },
    { code: "        return false", lineNumber: 4, indent: 2 },
    { code: "    if n <= 3:", lineNumber: 5, indent: 1 },
    { code: "        return true", lineNumber: 6, indent: 2 },
    { code: "    if n % 2 == 0:", lineNumber: 7, indent: 1 },
    { code: "        return false", lineNumber: 8, indent: 2 },
    { code: "    // Write n-1 as 2^r * d where d is odd", lineNumber: 9, indent: 0 },
    { code: "    r = 0, d = n - 1", lineNumber: 10, indent: 1 },
    { code: "    while d % 2 == 0:", lineNumber: 11, indent: 1 },
    { code: "        d /= 2", lineNumber: 12, indent: 2 },
    { code: "        r += 1", lineNumber: 13, indent: 2 },
    { code: "    // Witness loop", lineNumber: 14, indent: 0 },
    { code: "    for i = 1 to k:", lineNumber: 15, indent: 1 },
    { code: "        a = random(2, n-2)", lineNumber: 16, indent: 2 },
    { code: "        x = modPow(a, d, n)", lineNumber: 17, indent: 2 },
    { code: "        if x == 1 or x == n-1:", lineNumber: 18, indent: 2 },
    { code: "            continue", lineNumber: 19, indent: 3 },
    { code: "        for j = 0 to r-1:", lineNumber: 20, indent: 2 },
    { code: "            x = modPow(x, 2, n)", lineNumber: 21, indent: 3 },
    { code: "            if x == n-1:", lineNumber: 22, indent: 3 },
    { code: "                break", lineNumber: 23, indent: 4 },
    { code: "            if j == r-1:", lineNumber: 24, indent: 3 },
    { code: "                return false", lineNumber: 25, indent: 4 },
    { code: "    // If all tests pass, n is probably prime", lineNumber: 26, indent: 0 },
    { code: "    return true", lineNumber: 27, indent: 1 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Choose the appropriate pseudocode
  let pseudocode;
  switch (method) {
    case 'fermat':
      pseudocode = fermatPseudocode;
      break;
    case 'miller_rabin':
      pseudocode = millerRabinPseudocode;
      break;
    default:
      pseudocode = trialDivisionPseudocode;
  }

  // Custom component for method selection
  const MethodSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Method</Typography>
        <RadioGroup
          row
          value={value}
          onChange={onChange}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          <FormControlLabel
            value="trial_division"
            control={<Radio size="small" />}
            label="Trial Division"
            disabled={disabled}
          />
          <FormControlLabel
            value="fermat"
            control={<Radio size="small" />}
            label="Fermat Test"
            disabled={disabled}
          />
          <FormControlLabel
            value="miller_rabin"
            control={<Radio size="small" />}
            label="Miller-Rabin Test"
            disabled={disabled}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for number input
  const NumberInput = ({ label, value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>{label}</Typography>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          disabled={disabled}
          type="number"
          inputProps={{ min: 0 }}
        />
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Primality Test"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            A primality test is an algorithm for determining whether a given number is prime.
            This implementation includes three different primality testing methods:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Trial Division:</Typography>
              <Typography variant="body2" paragraph>
                The simplest primality test. It checks whether a number n is divisible by any integer from 2 to √n.
                If no divisor is found, the number is prime. This method is deterministic (always gives the correct answer)
                but becomes inefficient for large numbers.
              </Typography>
              <Typography variant="body2" paragraph>
                Time complexity: O(√n)
              </Typography>
            </li>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Fermat Primality Test:</Typography>
              <Typography variant="body2" paragraph>
                Based on Fermat's Little Theorem, which states that if p is prime and a is not divisible by p,
                then a^(p-1) = 1 (mod p). The test checks this property for several random values of a.
                If the property holds for all tested values, the number is probably prime.
                This is a probabilistic test that may incorrectly identify some composite numbers as prime (Carmichael numbers).
              </Typography>
              <Typography variant="body2" paragraph>
                Time complexity: O(k log n) where k is the number of tests
              </Typography>
            </li>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Miller-Rabin Primality Test:</Typography>
              <Typography variant="body2" paragraph>
                An improved version of the Fermat test that overcomes the issue with Carmichael numbers.
                It's based on the fact that if n is prime and n-1 = 2^r * d where d is odd,
                then for any a, either a^d = 1 (mod n) or a^(2^j * d) = -1 (mod n) for some j where 0 is less than or equal to j and j is less than r.
                This is also a probabilistic test but with a much lower error rate.
              </Typography>
              <Typography variant="body2" paragraph>
                Time complexity: O(k log^3 n) where k is the number of tests
              </Typography>
            </li>
          </ol>
          <Typography variant="body2" paragraph>
            For practical purposes, the Miller-Rabin test is often the preferred choice due to its balance of efficiency and accuracy.
            For small numbers or when absolute certainty is required, trial division is suitable.
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'method',
            type: 'component',
            label: 'Method',
            component: MethodSelector,
            componentProps: {
              value: method,
              onChange: handleMethodChange,
              disabled: state !== 'idle'
            },
            icon: SettingsIcon
          },
          {
            name: 'number',
            type: 'component',
            label: 'Number to Test',
            component: NumberInput,
            componentProps: {
              label: 'Number to Test',
              value: number,
              onChange: handleNumberChange,
              disabled: state !== 'idle'
            },
            icon: FunctionsIcon
          }
        ]}
        values={{
          method,
          number
        }}
        disabled={state === 'running'}
        onApply={handleApplyClick}
        showApplyButton={true}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* No Results Display Section - visualization will handle this */}

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Primality Test Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default PrimalityTestController;
