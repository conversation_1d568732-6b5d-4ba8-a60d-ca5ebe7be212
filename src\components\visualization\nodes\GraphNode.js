// GraphNode.js
// A reusable component for graph nodes in 3D visualizations with enhanced SDF-based rendering

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Html, MeshTransmissionMaterial } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

const GraphNode = ({
  position = [0, 0, 0],
  label = '',
  color = '#2196f3',
  isStart = false,
  isHighlighted = false,
  distance,
  radius = 1.0,
  segments = 32,
  onClick,
  showDistance = true,
  startIndicatorColor = '#4caf50'
}) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';
  
  // Enhanced color handling with strong blue tones for dark theme
  const nodeColors = useMemo(() => {
    // Create color variations
    let baseColor, glowColor, bgColor, highlightColor;

    // Adjust colors based on theme for better visibility
    if (isDark) {
      // In dark theme, use vibrant blue colors regardless of input color
      // This makes nodes stand out dramatically against the dark background

      // Use a vibrant blue for the base color in dark theme
      baseColor = new THREE.Color('#2979ff'); // Bright blue

      // Create a bright cyan-shifted glow for dark theme
      glowColor = new THREE.Color('#00b0ff'); // Light blue / cyan

      // Create a deeper blue background for dark theme
      bgColor = new THREE.Color('#1565c0'); // Deeper blue

      // Create a bright highlight for dark theme
      highlightColor = new THREE.Color('#40c4ff'); // Bright cyan

      // Apply additional brightness to make them pop in dark theme
      baseColor.multiplyScalar(1.4);
      glowColor.multiplyScalar(1.6);
      bgColor.multiplyScalar(1.2);
      highlightColor.multiplyScalar(1.8);
    } else {
      // In light theme, use the original color with slight adjustments
      baseColor = new THREE.Color(color);

      // Darken and saturate the base color for light theme
      baseColor.offsetHSL(0, 0.2, -0.2); // Increase saturation, decrease lightness
      baseColor.multiplyScalar(0.7); // Reduce brightness by 30%

      // Create a saturated glow for light theme
      glowColor = new THREE.Color(color).offsetHSL(0, 0.3, -0.1);
      glowColor.multiplyScalar(0.8);

      // Create a dark background for light theme
      bgColor = new THREE.Color(color).offsetHSL(0, 0.1, -0.3);
      bgColor.multiplyScalar(0.5);

      // Create a saturated highlight for light theme
      highlightColor = new THREE.Color(color).offsetHSL(0, 0.4, -0.05);
      highlightColor.multiplyScalar(0.9);
    }

    // If highlighted, make colors more intense
    if (isHighlighted) {
      baseColor.multiplyScalar(1.2);
      glowColor.multiplyScalar(1.3);
      bgColor.multiplyScalar(1.1);
      highlightColor.multiplyScalar(1.4);
    }

    return {
      base: baseColor,
      glow: glowColor,
      bg: bgColor,
      highlight: highlightColor
    };
  }, [color, isHighlighted, isDark]);

  // Create refs for animation
  const nodeRef = useRef();
  const glowRef = useRef();
  const highlightRef = useRef();
  
  // Animate node
  useFrame(({ clock }) => {
    if (nodeRef.current && isHighlighted) {
      // Pulse effect for highlighted nodes
      const pulse = 1 + Math.sin(clock.getElapsedTime() * 3) * 0.05;
      nodeRef.current.scale.set(pulse, pulse, pulse);
    }
    
    if (glowRef.current && isHighlighted) {
      // Glow effect for highlighted nodes
      const glowPulse = 1 + Math.sin(clock.getElapsedTime() * 2) * 0.1;
      glowRef.current.scale.set(glowPulse, glowPulse, glowPulse);
      
      if (glowRef.current.material) {
        glowRef.current.material.opacity = 0.6 + Math.sin(clock.getElapsedTime() * 4) * 0.2;
      }
    }
    
    if (highlightRef.current && isHighlighted) {
      // Highlight effect for highlighted nodes
      const highlightPulse = 1 + Math.sin(clock.getElapsedTime() * 5) * 0.15;
      highlightRef.current.scale.set(highlightPulse, highlightPulse, highlightPulse);
      
      if (highlightRef.current.material) {
        highlightRef.current.material.opacity = 0.4 + Math.sin(clock.getElapsedTime() * 3) * 0.2;
      }
    }
  });

  // Determine if we should use SDF-based rendering
  // SDF rendering provides smoother edges and better visual quality
  const useSDFRendering = true;

  return (
    <group position={position} onClick={onClick}>
      {/* Base node sphere with SDF-based rendering for high quality */}
      <mesh ref={nodeRef}>
        <sphereGeometry args={[radius, segments, segments]} />
        {useSDFRendering ? (
          <MeshTransmissionMaterial
            backside={false}
            samples={16}
            thickness={1.5}
            chromaticAberration={0.05}
            anisotropy={0.1}
            distortion={0.0}
            distortionScale={0.3}
            temporalDistortion={0.0}
            iridescence={0.1}
            iridescenceIOR={1.1}
            iridescenceThicknessRange={[0, 1400]}
            color={nodeColors.base}
            transmissionSampler={false}
            reflectivity={0.2}
            roughness={0}
            metalness={0.1}
            envMapIntensity={1}
            clearcoat={1}
            clearcoatRoughness={0.1}
            resolution={256}
          />
        ) : (
          <meshStandardMaterial
            color={nodeColors.base}
            emissive={nodeColors.glow}
            emissiveIntensity={0.5}
            roughness={0.2}
            metalness={0.8}
          />
        )}
      </mesh>

      {/* Inner glow sphere */}
      <mesh ref={glowRef} scale={[1.2, 1.2, 1.2]}>
        <sphereGeometry args={[radius, segments, segments]} />
        <meshBasicMaterial
          color={nodeColors.glow}
          transparent={true}
          opacity={0.3}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Highlight sphere for highlighted nodes */}
      {isHighlighted && (
        <mesh ref={highlightRef} scale={[1.4, 1.4, 1.4]}>
          <sphereGeometry args={[radius, segments, segments]} />
          <meshBasicMaterial
            color={nodeColors.highlight}
            transparent={true}
            opacity={0.2}
            side={THREE.BackSide}
          />
        </mesh>
      )}

      {/* Node label */}
      <Html position={[0, 0, 0]} center distanceFactor={10} occlude={false} zIndexRange={[100, 0]}>
        <div style={{
          color: 'white',
          fontSize: '24px',
          fontWeight: 'bold',
          textShadow: '0 0 5px #000000, 0 0 10px #000000',
          userSelect: 'none',
          pointerEvents: 'none',
          width: '40px',
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          zIndex: 100,
        }}>
          {label}
        </div>
      </Html>

      {/* Distance label */}
      {showDistance && distance !== undefined && (
        <Html position={[0, -radius * 1.5, 0]} center distanceFactor={10}>
          <div style={{
            color: isDark ? '#ffffff' : '#000000',
            backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: 'bold',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none',
          }}>
            {distance === Infinity ? '∞' : distance}
          </div>
        </Html>
      )}

      {/* Start indicator */}
      {isStart && (
        <group position={[0, radius * 1.5, 0]}>
          <mesh>
            <coneGeometry args={[radius * 0.5, radius * 1.0, 16]} />
            <meshStandardMaterial
              color={startIndicatorColor}
              emissive={startIndicatorColor}
              emissiveIntensity={0.5}
              roughness={0.2}
              metalness={0.8}
            />
          </mesh>
        </group>
      )}
    </group>
  );
};

export default GraphNode;
