// NumberInput.js
// A reusable number input component with consistent styling

import React, { useState, useEffect, useRef } from 'react';
import { Box, TextField } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable number input component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string} props.value - Current input value
 * @param {function} props.onChange - Function to handle input changes
 * @param {function} props.onApply - Function to handle apply button click
 * @param {string} props.error - Error message to display
 * @param {string} props.helperText - Helper text to display
 * @param {string} props.label - Label for the input
 * @param {boolean} props.disabled - Whether the input is disabled
 * @param {number} props.min - Minimum allowed value
 * @param {number} props.max - Maximum allowed value
 * @param {Object} props.sx - Additional styles
 */
const NumberInput = ({
  value = '',
  onChange = () => {},
  onApply = () => {},
  error = '',
  helperText = 'Enter a number',
  label = 'Enter a number',
  disabled = false,
  min = 0,
  max = 100,
  sx = {}
}) => {
  // Local state for the input value
  const [localValue, setLocalValue] = useState(value);
  // Reference to the input element
  const inputRef = useRef(null);
  // Track if we're handling arrow keys to prevent blur
  const isHandlingArrowKey = useRef(false);

  // Update local value when external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Handle text input change
  const handleChange = (e) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(newValue);
  };

  // Handle blur event to validate and apply the value
  const handleBlur = () => {
    // Don't apply if we're handling arrow keys
    if (!isHandlingArrowKey.current) {
      onApply();
    }
  };

  // Handle keydown to prevent default scroll behavior on arrow keys
  const handleKeyDown = (e) => {
    // Prevent default behavior for arrow up and arrow down keys
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      e.preventDefault();

      // Set flag to indicate we're handling arrow keys
      isHandlingArrowKey.current = true;

      // Get the current value and step
      const currentValue = parseInt(localValue, 10) || 0;
      const step = 1; // Default step for number inputs

      // Calculate new value based on key pressed
      let newValue;
      if (e.key === 'ArrowUp') {
        newValue = currentValue + step;
      } else {
        newValue = currentValue - step;
      }

      // Apply min/max constraints
      newValue = Math.max(min, Math.min(max, newValue));

      // Update the input value and trigger change event
      setLocalValue(String(newValue));
      onChange(String(newValue));

      // Keep focus on the input element
      if (inputRef.current) {
        // Use setTimeout to ensure the focus is maintained after the current event cycle
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
            // Reset the flag after focus is set
            isHandlingArrowKey.current = false;
          }
        }, 0);
      }
    }
  };

  // Handle key up to reset the flag
  const handleKeyUp = (e) => {
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      // Reset the flag when the key is released
      isHandlingArrowKey.current = false;
    }
  };

  return (
    <Box sx={{ ...sx }}>
      <TextField
        fullWidth
        size="small"
        label={label}
        value={localValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        disabled={disabled}
        error={!!error}
        helperText={error || helperText}
        type="number"
        // Use the sx prop to apply min, max, and step attributes directly to the input element
        sx={{
          '& input': {
            // Hide the spinner buttons and set constraints
            WebkitAppearance: 'none',
            MozAppearance: 'textfield',
            min: min,
            max: max,
            step: 1
          },
          ...sx
        }}
        inputRef={inputRef}
      />
    </Box>
  );
};

NumberInput.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  onApply: PropTypes.func.isRequired,
  error: PropTypes.string,
  helperText: PropTypes.string,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  min: PropTypes.number,
  max: PropTypes.number,
  sx: PropTypes.object
};

export default NumberInput;
