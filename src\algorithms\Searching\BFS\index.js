// BFS/index.js
// Export only what's needed from this algorithm

import BFSVisualization from './BFSVisualization';
import BFSController from './BFSController';
import BFSAlgorithm from './BFSAlgorithm';

export const metadata = {
  id: 'BFS',
  name: 'Breadth First Search',
  description: 'An algorithm for traversing or searching tree or graph data structures.',
  timeComplexity: 'O(V + E)',
  spaceComplexity: 'O(V)',
  defaultParams: {
    nodes: 6,
    startNode: 0,
    targetNode: 5,
    density: 0.5,
    customEdges: [],
  },
};

export const components = {
  visualization: BFSVisualization,
  controller: BFSController,
  algorithm: BFSAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
