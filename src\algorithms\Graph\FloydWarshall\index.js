// FloydWarshall/index.js
// Export only what's needed from this algorithm

import Floyd<PERSON>arshallVisualization from './Floyd<PERSON>arshallVisualization';
import Floyd<PERSON><PERSON>hallController from './Floyd<PERSON>arshallController';
import Floyd<PERSON><PERSON>hallAlgorithm from './<PERSON><PERSON><PERSON>hallAlgorithm';

export const metadata = {
  id: '<PERSON><PERSON><PERSON><PERSON>',
  name: 'Floyd<PERSON><PERSON>hall Algorithm',
  description: 'An algorithm for finding shortest paths in a weighted graph with positive or negative edge weights (but no negative cycles).',
  timeComplexity: 'O(V³)',
  spaceComplexity: 'O(V²)',
  defaultParams: {
    vertices: 5,
    density: 0.7,
    minWeight: 1,
    maxWeight: 10,
    allowNegative: false,
    animationSpeed: 5,
  },
};

export const components = {
  visualization: Floyd<PERSON>arshallVisualization,
  controller: FloydWarshallController,
  algorithm: FloydWarshallAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
