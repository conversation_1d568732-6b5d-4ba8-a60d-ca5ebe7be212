// NQueens/index.js
// Export only what's needed from this algorithm

import NQueensVisualization from './NQueensVisualization';
import NQueensController from './NQueensController';
import NQueensAlgorithm from './NQueensAlgorithm';

export const metadata = {
  id: 'NQueens',
  name: 'N-Queens Problem',
  description: 'A backtracking algorithm that places N chess queens on an N×N chessboard so that no two queens threaten each other.',
  timeComplexity: 'O(N!)',
  spaceComplexity: 'O(N²)',
  defaultParams: {
    boardSize: 4,
    findAllSolutions: false,
  },
};

export const components = {
  visualization: NQueensVisualization,
  controller: NQueensController,
  algorithm: NQueensAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
