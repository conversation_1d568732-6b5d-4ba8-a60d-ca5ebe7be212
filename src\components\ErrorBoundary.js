import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
    this.setState({ errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    // If a reset function is provided, call it
    if (this.props.onReset) {
      this.props.onReset();
    }
  }

  render() {
    const { style } = this.props;

    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <Paper
          elevation={3}
          sx={{
            p: 3,
            m: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            bgcolor: 'error.light',
            color: 'error.contrastText',
            ...(style || {})
          }}
        >
          <ErrorIcon sx={{ fontSize: 60, mb: 2, color: 'error.main' }} />
          <Typography variant="h5" gutterBottom>
            Something went wrong
          </Typography>
          <Typography variant="body1" sx={{ mb: 2, textAlign: 'center' }}>
            {this.state.error && this.state.error.toString()}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={this.handleReset}
          >
            Try Again
          </Button>
          {this.props.showDetails && this.state.errorInfo && (
            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, width: '100%', overflow: 'auto' }}>
              <Typography variant="subtitle2" gutterBottom>
                Component Stack:
              </Typography>
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '0.8rem' }}>
                {this.state.errorInfo.componentStack}
              </pre>
            </Box>
          )}
        </Paper>
      );
    }

    // When no error, render children in a container with the provided style
    return (
      <Box sx={{
        width: '100%',
        height: '100%',
        flex: 1,
        display: 'flex',
        ...style
      }}>
        {this.props.children}
      </Box>
    );
  }
}

export default ErrorBoundary;
