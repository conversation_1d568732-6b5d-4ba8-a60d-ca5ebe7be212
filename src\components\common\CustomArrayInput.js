// CustomArrayInput.js
// A reusable custom array input component with consistent styling

import React, { useState } from 'react';
import { Box, Button, TextField as MuiTextField } from '@mui/material';
import PropTypes from 'prop-types';
// No longer need to import parseCustomArray as we've integrated the logic directly

/**
 * A reusable custom array input component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string} props.value - Current input value
 * @param {function} props.onChange - Function to handle input changes
 * @param {function} props.onApply - Function to handle apply button click
 * @param {string} props.error - Error message to display
 * @param {string} props.helperText - Helper text to display
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.disabled - Whether the input is disabled
 * @param {Object} props.sx - Additional styles
 */
const CustomArrayInput = ({
  value = '',
  onChange = () => {},
  onApply = () => {},
  error: externalError = '',
  helperText = 'Enter comma-separated numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.',
  placeholder = 'e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)',
  disabled = false,
  minLength = 3,
  maxLength = 20,
  sx = {}
}) => {
  // Internal state for validation errors
  const [internalError, setInternalError] = useState('');

  // Use external error if provided, otherwise use internal error
  const error = externalError || internalError;

  const handleChange = (e) => {
    // Only allow numbers, commas, and spaces
    const value = e.target.value;
    // Allow the input to be empty or contain only valid characters
    if (value === '' || /^[0-9,\s]*$/.test(value)) {
      onChange(value);
      // Clear internal error when input changes
      if (internalError) setInternalError('');
    }
  };

  const handleApply = () => {
    try {
      // Initialize result
      let parsedArray = [];

      // Check if input is empty or only whitespace
      if (!value || value.trim() === '') {
        setInternalError('Please enter some numbers');
        onApply(false);
        return;
      }

      // Parse the input string into an array of numbers
      const parts = value.split(',');
      const trimmedParts = parts.map(item => item.trim()).filter(item => item !== '');

      // Check if there are any parts after trimming and filtering
      if (trimmedParts.length === 0) {
        setInternalError('Please enter valid numbers separated by commas');
        onApply(false);
        return;
      }

      // Convert to numbers and check for validity
      for (const item of trimmedParts) {
        const num = Number(item);
        if (isNaN(num)) {
          setInternalError(`Invalid number: "${item}". Please enter valid numbers.`);
          onApply(false);
          return;
        }
        parsedArray.push(num);
      }

      // Validate array length
      if (parsedArray.length < minLength) {
        setInternalError(`Please enter at least ${minLength} numbers`);
        onApply(false);
        return;
      }

      if (parsedArray.length > maxLength) {
        setInternalError(`Maximum ${maxLength} numbers allowed`);
        onApply(false);
        return;
      }

      // Success - clear any internal errors
      setInternalError('');
      // Call the onApply callback with the parsed array
      onApply(parsedArray);
    } catch (error) {
      console.error('CustomArrayInput - Error parsing input:', error);
      setInternalError('Please enter valid numbers separated by commas');
      onApply(false);
    }
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start', ...sx }}>
      <MuiTextField
        fullWidth
        size="small"
        label="Enter comma-separated numbers (1-9999)"
        value={value}
        onChange={handleChange}
        disabled={disabled}
        error={!!error}
        helperText={error || helperText}
        placeholder={placeholder}
        sx={{ mr: 1 }}
        // Filter out props that might cause conflicts
        // Don't spread all rest props to avoid defaultValue conflicts
      />
      <Button
        variant="contained"
        size="small"
        onClick={handleApply}
        disabled={disabled}
        sx={{ mt: 1 }}
      >
        Apply
      </Button>
    </Box>
  );
};

CustomArrayInput.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  onApply: PropTypes.func.isRequired,
  error: PropTypes.string,
  helperText: PropTypes.string,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  minLength: PropTypes.number,
  maxLength: PropTypes.number,
  sx: PropTypes.object
};

export default CustomArrayInput;
