// ArticulationPointsController.js
// This component provides the controls for Articulation Points algorithm visualization

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GraphIcon from '@mui/icons-material/AccountTree';
import TuneIcon from '@mui/icons-material/Tune';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'; // Used in customEdges parameter

// Import algorithm functions
import ArticulationPointsAlgorithm from './ArticulationPointsAlgorithm';

const ArticulationPointsController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Graph parameters
    const [numNodes, setNumNodes] = useState(params?.nodes || 6);
    const [density, setDensity] = useState(params?.density || 0.5);
    const [minWeight, setMinWeight] = useState(params?.minWeight || 1);
    const [maxWeight, setMaxWeight] = useState(params?.maxWeight || 10);
    const [useCustomGraph, setUseCustomGraph] = useState(params?.useCustomGraph === true);
    const [customEdges, setCustomEdges] = useState('');
    const [customEdgesError, setCustomEdgesError] = useState('');

    // Parse custom edges from string input
    const parseCustomEdges = useCallback((edgesText) => {
        const parsedEdges = [];
        const edgesRegex = /\\[\\s*(\\d+)\\s*,\\s*(\\d+)\\s*(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\]/g;
        let match;

        while ((match = edgesRegex.exec(edgesText)) !== null) {
            const from = parseInt(match[1], 10);
            const to = parseInt(match[2], 10);
            const weight = match[3] ? parseFloat(match[3]) : 1;
            parsedEdges.push([from, to, weight]);
        }

        return parsedEdges;
    }, []);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { numNodes, density, minWeight, maxWeight });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { numNodes, density, minWeight, maxWeight, useCustomGraph });

        // Update params first
        onParamChange({
            nodes: numNodes,
            density,
            minWeight,
            maxWeight,
            customEdges: useCustomGraph ? parseCustomEdges(customEdges) : [],
            useCustomGraph
        });

        // Set steps and movements directly
        try {
            const result = ArticulationPointsAlgorithm.generateArticulationPointsSteps({
                nodes: numNodes,
                density,
                minWeight,
                maxWeight,
                customEdges: useCustomGraph ? parseCustomEdges(customEdges) : []
            });
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [numNodes, density, minWeight, maxWeight, useCustomGraph, customEdges, parseCustomEdges, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleNumNodesChange = useCallback((value) => {
        if (value >= 3 && value <= 10) {
            console.log('handleNumNodesChange called with value:', value);
            setNumNodes(value);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleDensityChange = useCallback((value) => {
        if (value >= 0.1 && value <= 0.9) {
            console.log('handleDensityChange called with value:', value);
            setDensity(value);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleMinWeightChange = useCallback((value) => {
        if (value >= 1 && value <= maxWeight) {
            console.log('handleMinWeightChange called with value:', value);
            setMinWeight(value);
            resetAndGenerateSteps();
        }
    }, [maxWeight, resetAndGenerateSteps]);

    const handleMaxWeightChange = useCallback((value) => {
        if (value >= minWeight && value <= 100) {
            console.log('handleMaxWeightChange called with value:', value);
            setMaxWeight(value);
            resetAndGenerateSteps();
        }
    }, [minWeight, resetAndGenerateSteps]);

    const handleUseCustomGraphChange = useCallback((value) => {
        console.log('handleUseCustomGraphChange called with value:', value);
        setUseCustomGraph(value);
        if (!value) {
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleCustomEdgesChange = useCallback((value) => {
        console.log('handleCustomEdgesChange called with value:', value);
        setCustomEdges(value);
        setCustomEdgesError('');
    }, []);

    // Handle custom edges apply
    const handleApplyCustomEdges = useCallback(() => {
        try {
            const parsedEdges = parseCustomEdges(customEdges);

            // Validate edges
            if (parsedEdges.length === 0) {
                setCustomEdgesError('Please enter at least one edge');
                return;
            }

            // Check for valid node indices
            for (const [from, to] of parsedEdges) {
                if (from < 0 || to < 0 || from >= numNodes || to >= numNodes) {
                    setCustomEdgesError(`Node indices must be between 0 and ${numNodes - 1}`);
                    return;
                }
            }

            // Clear error and update
            setCustomEdgesError('');
            resetAndGenerateSteps();
        } catch (error) {
            setCustomEdgesError('Invalid edge format');
        }
    }, [customEdges, parseCustomEdges, resetAndGenerateSteps, numNodes]);

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Articulation Points algorithm
    const pseudocode = [
        { code: "function findArticulationPoints(graph):", lineNumber: 1, indent: 0 },
        { code: "// Initialize variables", lineNumber: 2, indent: 1 },
        { code: "for each vertex v in graph:", lineNumber: 3, indent: 1 },
        { code: "  if v is not visited:", lineNumber: 4, indent: 2 },
        { code: "    DFS(v, true) // v is a root", lineNumber: 5, indent: 3 },
        { code: "function DFS(u, isRoot):", lineNumber: 6, indent: 1 },
        { code: "  mark u as visited", lineNumber: 7, indent: 2 },
        { code: "  discoveryTime[u] = time++", lineNumber: 8, indent: 2 },
        { code: "  lowTime[u] = discoveryTime[u]", lineNumber: 9, indent: 2 },
        { code: "  childCount = 0", lineNumber: 10, indent: 2 },
        { code: "  for each neighbor v of u:", lineNumber: 11, indent: 2 },
        { code: "    if v is not visited:", lineNumber: 12, indent: 3 },
        { code: "      parent[v] = u", lineNumber: 13, indent: 4 },
        { code: "      childCount++", lineNumber: 14, indent: 4 },
        { code: "      DFS(v, false)", lineNumber: 15, indent: 4 },
        { code: "      if lowTime[v] >= discoveryTime[u] and not isRoot:", lineNumber: 16, indent: 4 },
        { code: "        u is an articulation point", lineNumber: 17, indent: 5 },
        { code: "      lowTime[u] = min(lowTime[u], lowTime[v])", lineNumber: 18, indent: 4 },
        { code: "    else if v != parent[u]:", lineNumber: 19, indent: 3 },
        { code: "      lowTime[u] = min(lowTime[u], discoveryTime[v])", lineNumber: 20, indent: 4 },
        { code: "  if isRoot and childCount > 1:", lineNumber: 21, indent: 2 },
        { code: "    u is an articulation point", lineNumber: 22, indent: 3 },
        { code: "return articulation points", lineNumber: 23, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Articulation Points Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        Articulation Points (or Cut Vertices) are nodes in an undirected graph whose removal increases the number of connected components.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(V + E) where V is the number of vertices and E is the number of edges
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(V) for storing the discovery time, low time, and parent arrays
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Uses a modified Depth-First Search (DFS) algorithm
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Identifies nodes that are critical for maintaining connectivity in the graph
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'numNodes',
                        type: 'slider',
                        label: 'Number of Nodes',
                        min: 3,
                        max: 10,
                        step: 1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: GraphIcon
                    },
                    {
                        name: 'density',
                        type: 'slider',
                        label: 'Edge Density',
                        min: 0.1,
                        max: 0.9,
                        step: 0.1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: TuneIcon
                    },
                    {
                        name: 'minWeight',
                        type: 'slider',
                        label: 'Min Weight',
                        min: 1,
                        max: 10,
                        step: 1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: TuneIcon
                    },
                    {
                        name: 'maxWeight',
                        type: 'slider',
                        label: 'Max Weight',
                        min: 1,
                        max: 100,
                        step: 1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: TuneIcon
                    },
                    {
                        name: 'useCustomGraph',
                        type: 'switch',
                        label: 'Use Custom Graph',
                        icon: ShuffleIcon
                    },
                    {
                        name: 'customEdges',
                        type: 'customArray',
                        label: 'Custom Edges',
                        placeholder: 'Format: [from, to, weight], [from, to, weight], ...',
                        helperText: 'Example: [0, 1, 5], [1, 2, 3], [2, 0, 7]',
                        error: customEdgesError,
                        showOnlyWhen: 'useCustomGraph',
                        onApply: handleApplyCustomEdges,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    numNodes,
                    density,
                    minWeight,
                    maxWeight,
                    useCustomGraph,
                    customEdges
                }}
                onChange={(newValues) => {
                    if (newValues.numNodes !== undefined && newValues.numNodes !== numNodes) {
                        handleNumNodesChange(newValues.numNodes);
                    }
                    if (newValues.density !== undefined && newValues.density !== density) {
                        handleDensityChange(newValues.density);
                    }
                    if (newValues.minWeight !== undefined && newValues.minWeight !== minWeight) {
                        handleMinWeightChange(newValues.minWeight);
                    }
                    if (newValues.maxWeight !== undefined && newValues.maxWeight !== maxWeight) {
                        handleMaxWeightChange(newValues.maxWeight);
                    }
                    if (newValues.useCustomGraph !== undefined && newValues.useCustomGraph !== useCustomGraph) {
                        handleUseCustomGraphChange(newValues.useCustomGraph);
                    }
                    if (newValues.customEdges !== undefined) {
                        setCustomEdges(newValues.customEdges);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default ArticulationPointsController;
