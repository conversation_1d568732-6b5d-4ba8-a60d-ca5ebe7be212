// GCDAlgorithm.js
// Implementation of the Euclidean GCD algorithm

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for the Euclidean GCD algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateGCDSteps = (params) => {
  console.log('generateGCDSteps called with params:', params);
  const { a = 48, b = 18, method = 'division' } = params;
  
  const steps = [];
  let result = 0;
  
  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize Euclidean GCD algorithm with a = ${a} and b = ${b} using ${method} method.`,
    a,
    b,
    remainder: null,
    quotient: null,
    method,
    progressStep: 'init',
    pseudocodeLine: 1
  });
  
  // Compute GCD using the selected method
  if (method === 'division') {
    result = gcdDivision(a, b, steps);
  } else {
    result = gcdSubtraction(a, b, steps);
  }
  
  // Add final step
  steps.push({
    type: 'complete',
    message: `The GCD of ${a} and ${b} is ${result}.`,
    a: result,
    b: 0,
    remainder: 0,
    quotient: null,
    method,
    progressStep: 'complete',
    pseudocodeLine: 0
  });
  
  return { steps, result };
};

/**
 * Compute GCD using the division method (Euclidean algorithm)
 * @param {Number} a - First number
 * @param {Number} b - Second number
 * @param {Array} steps - Array to store steps
 * @returns {Number} - GCD of a and b
 */
const gcdDivision = (a, b, steps) => {
  let x = Math.max(a, b);
  let y = Math.min(a, b);
  
  // If one of the numbers is 0, the GCD is the other number
  if (y === 0) {
    steps.push({
      type: 'base_case',
      message: `Since b = 0, the GCD is a = ${x}.`,
      a: x,
      b: y,
      remainder: 0,
      quotient: null,
      method: 'division',
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    return x;
  }
  
  // Main Euclidean algorithm loop
  while (y !== 0) {
    const quotient = Math.floor(x / y);
    const remainder = x % y;
    
    steps.push({
      type: 'division',
      message: `Divide ${x} by ${y}: ${x} = ${y} × ${quotient} + ${remainder}`,
      a: x,
      b: y,
      remainder,
      quotient,
      method: 'division',
      progressStep: 'process',
      pseudocodeLine: 6
    });
    
    x = y;
    y = remainder;
    
    steps.push({
      type: 'update',
      message: `Update a = ${x} and b = ${y}`,
      a: x,
      b: y,
      remainder,
      quotient,
      method: 'division',
      progressStep: 'process',
      pseudocodeLine: 7
    });
  }
  
  return x;
};

/**
 * Compute GCD using the subtraction method
 * @param {Number} a - First number
 * @param {Number} b - Second number
 * @param {Array} steps - Array to store steps
 * @returns {Number} - GCD of a and b
 */
const gcdSubtraction = (a, b, steps) => {
  let x = Math.max(a, b);
  let y = Math.min(a, b);
  
  // If one of the numbers is 0, the GCD is the other number
  if (y === 0) {
    steps.push({
      type: 'base_case',
      message: `Since b = 0, the GCD is a = ${x}.`,
      a: x,
      b: y,
      remainder: 0,
      quotient: null,
      method: 'subtraction',
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    return x;
  }
  
  // Main subtraction method loop
  while (x !== y) {
    if (x > y) {
      const oldX = x;
      x = x - y;
      
      steps.push({
        type: 'subtraction',
        message: `Subtract ${y} from ${oldX}: ${oldX} - ${y} = ${x}`,
        a: x,
        b: y,
        remainder: null,
        quotient: null,
        method: 'subtraction',
        progressStep: 'process',
        pseudocodeLine: 12
      });
    } else {
      const oldY = y;
      y = y - x;
      
      steps.push({
        type: 'subtraction',
        message: `Subtract ${x} from ${oldY}: ${oldY} - ${x} = ${y}`,
        a: x,
        b: y,
        remainder: null,
        quotient: null,
        method: 'subtraction',
        progressStep: 'process',
        pseudocodeLine: 14
      });
    }
  }
  
  return x;
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const GCDAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Determine which pseudocode to show based on the method
  const isDivisionMethod = !currentAlgorithmStep || currentAlgorithmStep.method === 'division';

  // Pseudocode for Euclidean GCD algorithm (Division method)
  const divisionPseudocode = [
    { line: 'function gcd_division(a, b):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Base case: if b is 0, return a', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if b == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return a', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    // Recursive case: gcd(a, b) = gcd(b, a mod b)', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    remainder = a % b', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    return gcd_division(b, remainder)', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
  ];

  // Pseudocode for Euclidean GCD algorithm (Subtraction method)
  const subtractionPseudocode = [
    { line: 'function gcd_subtraction(a, b):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Base case: if b is 0, return a', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if b == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return a', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    // Ensure a >= b', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    if a < b:', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '        swap(a, b)', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    // Main loop: subtract the smaller number from the larger', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    while a != b:', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '        if a > b:', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '            // a is larger, subtract b from a', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            a = a - b', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '        else:', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '            // b is larger, subtract a from b', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '            b = b - a', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '    // When a == b, that value is the GCD', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '    return a', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
  ];

  // Choose the appropriate pseudocode
  const pseudocode = isDivisionMethod ? divisionPseudocode : subtractionPseudocode;

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default GCDAlgorithm;
