// AlgorithmRegistry.js
// This file serves as a central registry for all algorithms in the application.
// Each algorithm is registered with its metadata, visualization component, and controller component.

// ===== SORTING ALGORITHMS =====
// Import sorting algorithm components
import BubbleSortVisualization from './Sorting/BubbleSort/BubbleSortVisualization';
import BubbleSortController from './Sorting/BubbleSort/BubbleSortController';
import BubbleSortAlgorithm from './Sorting/BubbleSort/BubbleSortAlgorithm';

import MergeSortVisualization from './Sorting/MergeSort/MergeSortVisualization';
import MergeSortController from './Sorting/MergeSort/MergeSortController';
import MergeSortAlgorithm from './Sorting/MergeSort/MergeSortAlgorithm';

import QuickSortVisualization from './Sorting/QuickSort/QuickSortVisualization';
import QuickSortController from './Sorting/QuickSort/QuickSortController';
import QuickSortAlgorithm from './Sorting/QuickSort/QuickSortAlgorithm';

import HeapSortVisualization from './Sorting/HeapSort/HeapSortVisualization';
import HeapSortController from './Sorting/HeapSort/HeapSortController';
import HeapSortAlgorithm from './Sorting/HeapSort/HeapSortAlgorithm';

import InsertionSortVisualization from './Sorting/InsertionSort/InsertionSortVisualization';
import InsertionSortController from './Sorting/InsertionSort/InsertionSortController';
import InsertionSortAlgorithm from './Sorting/InsertionSort/InsertionSortAlgorithm';

import SelectionSortVisualization from './Sorting/SelectionSort/SelectionSortVisualization';
import SelectionSortController from './Sorting/SelectionSort/SelectionSortController';
import SelectionSortAlgorithm from './Sorting/SelectionSort/SelectionSortAlgorithm';

import RadixSortVisualization from './Sorting/RadixSort/RadixSortVisualization';
import RadixSortController from './Sorting/RadixSort/RadixSortController';
import RadixSortAlgorithm from './Sorting/RadixSort/RadixSortAlgorithm';

import CountingSortVisualization from './Sorting/CountingSort/CountingSortVisualization';
import CountingSortController from './Sorting/CountingSort/CountingSortController';
import CountingSortAlgorithm from './Sorting/CountingSort/CountingSortAlgorithm';

import BucketSortVisualization from './Sorting/BucketSort/BucketSortVisualization';
import BucketSortController from './Sorting/BucketSort/BucketSortController';
import BucketSortAlgorithm from './Sorting/BucketSort/BucketSortAlgorithm';

import ShellSortVisualization from './Sorting/ShellSort/ShellSortVisualization';
import ShellSortController from './Sorting/ShellSort/ShellSortController';
import ShellSortAlgorithm from './Sorting/ShellSort/ShellSortAlgorithm';

import TimSortVisualization from './Sorting/TimSort/TimSortVisualization';
import TimSortController from './Sorting/TimSort/TimSortController';
import TimSortAlgorithm from './Sorting/TimSort/TimSortAlgorithm';

// Import Searching algorithms
import BinarySearchVisualization from './Searching/BinarySearch/BinarySearchVisualization';
import BinarySearchController from './Searching/BinarySearch/BinarySearchController';
import BinarySearchAlgorithm from './Searching/BinarySearch/BinarySearchAlgorithm';

import LinearSearchVisualization from './Searching/LinearSearch/LinearSearchVisualization';
import LinearSearchController from './Searching/LinearSearch/LinearSearchController';
import LinearSearchAlgorithm from './Searching/LinearSearch/LinearSearchAlgorithm';

import JumpSearchVisualization from './Searching/JumpSearch/JumpSearchVisualization';
import JumpSearchController from './Searching/JumpSearch/JumpSearchController';
import JumpSearchAlgorithm from './Searching/JumpSearch/JumpSearchAlgorithm';

import InterpolationSearchVisualization from './Searching/InterpolationSearch/InterpolationSearchVisualization';
import InterpolationSearchController from './Searching/InterpolationSearch/InterpolationSearchController';
import InterpolationSearchAlgorithm from './Searching/InterpolationSearch/InterpolationSearchAlgorithm';

import ExponentialSearchVisualization from './Searching/ExponentialSearch/ExponentialSearchVisualization';
import ExponentialSearchController from './Searching/ExponentialSearch/ExponentialSearchController';
import ExponentialSearchAlgorithm from './Searching/ExponentialSearch/ExponentialSearchAlgorithm';

// ===== SEARCHING ALGORITHMS =====
// Import searching algorithm components
import BFSVisualization from './Searching/BFS/BFSVisualization';
import BFSController from './Searching/BFS/BFSController';
import BFSAlgorithm from './Searching/BFS/BFSAlgorithm';

import DFSVisualization from './Searching/DFS/DFSVisualization';
import DFSController from './Searching/DFS/DFSController';
import DFSAlgorithm from './Searching/DFS/DFSAlgorithm';

// ===== GRAPH ALGORITHMS =====
// Import graph algorithm components
import DijkstraVisualization from './Graph/Dijkstra/DijkstraVisualization';
import DijkstraController from './Graph/Dijkstra/DijkstraController';
import DijkstraAlgorithm from './Graph/Dijkstra/DijkstraAlgorithm';

import FloydWarshallVisualization from './Graph/FloydWarshall/FloydWarshallVisualization';
import FloydWarshallController from './Graph/FloydWarshall/FloydWarshallController';
import FloydWarshallAlgorithm from './Graph/FloydWarshall/FloydWarshallAlgorithm';

import KruskalsVisualization from './Graph/Kruskals/KruskalsVisualization';
import KruskalsController from './Graph/Kruskals/KruskalsController';
import KruskalsAlgorithm from './Graph/Kruskals/KruskalsAlgorithm';

import BellmanFordVisualization from './Graph/BellmanFord/BellmanFordVisualization';
import BellmanFordController from './Graph/BellmanFord/BellmanFordController';
import BellmanFordAlgorithm from './Graph/BellmanFord/BellmanFordAlgorithm';

import PrimsVisualization from './Graph/Prims/PrimsVisualization';
import PrimsController from './Graph/Prims/PrimsController';
import PrimsAlgorithm from './Graph/Prims/PrimsAlgorithm';

import TopologicalSortVisualization from './Graph/TopologicalSort/TopologicalSortVisualization';
import TopologicalSortController from './Graph/TopologicalSort/TopologicalSortController';
import TopologicalSortAlgorithm from './Graph/TopologicalSort/TopologicalSortAlgorithm';

import AStarVisualization from './Graph/AStar/AStarVisualization';
import AStarController from './Graph/AStar/AStarController';
import AStarAlgorithm from './Graph/AStar/AStarAlgorithm';

import StronglyConnectedVisualization from './Graph/StronglyConnected/StronglyConnectedVisualization';
import StronglyConnectedController from './Graph/StronglyConnected/StronglyConnectedController';
import StronglyConnectedAlgorithm from './Graph/StronglyConnected/StronglyConnectedAlgorithm';

import ArticulationPointsVisualization from './Graph/ArticulationPoints/ArticulationPointsVisualization';
import ArticulationPointsController from './Graph/ArticulationPoints/ArticulationPointsController';
import ArticulationPointsAlgorithm from './Graph/ArticulationPoints/ArticulationPointsAlgorithm';

import BridgesVisualization from './Graph/Bridges/BridgesVisualization';
import BridgesController from './Graph/Bridges/BridgesController';
import BridgesAlgorithm from './Graph/Bridges/BridgesAlgorithm';

// ===== DYNAMIC PROGRAMMING ALGORITHMS =====
// Import dynamic programming algorithm components
import LCSVisualization from './DP/LCS/LCSVisualization';
import LCSController from './DP/LCS/LCSController';
import LCSAlgorithm from './DP/LCS/LCSAlgorithm';

import FibonacciVisualization from './DP/Fibonacci/FibonacciVisualization';
import FibonacciController from './DP/Fibonacci/FibonacciController';
import FibonacciAlgorithm from './DP/Fibonacci/FibonacciAlgorithm';

import KnapsackVisualization from './DP/Knapsack/KnapsackVisualization';
import KnapsackController from './DP/Knapsack/KnapsackController';
import KnapsackAlgorithm from './DP/Knapsack/KnapsackAlgorithm';

import EditDistanceVisualization from './DP/EditDistance/EditDistanceVisualization';
import EditDistanceController from './DP/EditDistance/EditDistanceController';
import EditDistanceAlgorithm from './DP/EditDistance/EditDistanceAlgorithm';

import MatrixChainMultiplicationVisualization from './DP/MatrixChainMultiplication/MatrixChainMultiplicationVisualization';
import MatrixChainMultiplicationController from './DP/MatrixChainMultiplication/MatrixChainMultiplicationController';
import MatrixChainMultiplicationAlgorithm from './DP/MatrixChainMultiplication/MatrixChainMultiplicationAlgorithm';

import CoinChangeVisualization from './DP/CoinChange/CoinChangeVisualization';
import CoinChangeController from './DP/CoinChange/CoinChangeController';
import CoinChangeAlgorithm from './DP/CoinChange/CoinChangeAlgorithm';

import LISVisualization from './DP/LIS/LISVisualization';
import LISController from './DP/LIS/LISController';
import LISAlgorithm from './DP/LIS/LISAlgorithm';

import RodCuttingVisualization from './DP/RodCutting/RodCuttingVisualization';
import RodCuttingController from './DP/RodCutting/RodCuttingController';
import RodCuttingAlgorithm from './DP/RodCutting/RodCuttingAlgorithm';

// ===== RECURSION & BACKTRACKING ALGORITHMS =====
// Import recursion algorithm components
import TowersOfHanoiVisualization from './Recursion/TowersOfHanoi/TowersOfHanoiVisualization';
import TowersOfHanoiController from './Recursion/TowersOfHanoi/TowersOfHanoiController';
import TowersOfHanoiAlgorithm from './Recursion/TowersOfHanoi/TowersOfHanoiAlgorithm';

import NQueensVisualization from './Recursion/NQueens/NQueensVisualization';
import NQueensController from './Recursion/NQueens/NQueensController';
import NQueensAlgorithm from './Recursion/NQueens/NQueensAlgorithm';

import SudokuVisualization from './Recursion/Sudoku/SudokuVisualization';
import SudokuController from './Recursion/Sudoku/SudokuController';
import SudokuAlgorithm from './Recursion/Sudoku/SudokuAlgorithm';

import RatInMazeVisualization from './Recursion/RatInMaze/RatInMazeVisualization';
import RatInMazeController from './Recursion/RatInMaze/RatInMazeController';
import RatInMazeAlgorithm from './Recursion/RatInMaze/RatInMazeAlgorithm';

import PermutationsVisualization from './Recursion/Permutations/PermutationsVisualization';
import PermutationsController from './Recursion/Permutations/PermutationsController';
import PermutationsAlgorithm from './Recursion/Permutations/PermutationsAlgorithm';

import CombinationsVisualization from './Recursion/Combinations/CombinationsVisualization';
import CombinationsController from './Recursion/Combinations/CombinationsController';
import CombinationsAlgorithm from './Recursion/Combinations/CombinationsAlgorithm';

import SubsetSumVisualization from './Recursion/SubsetSum/SubsetSumVisualization';
import SubsetSumController from './Recursion/SubsetSum/SubsetSumController';
import SubsetSumAlgorithm from './Recursion/SubsetSum/SubsetSumAlgorithm';

import SieveVisualization from './Math/Sieve/SieveVisualization';
import SieveController from './Math/Sieve/SieveController';
import SieveAlgorithm from './Math/Sieve/SieveAlgorithm';

import GCDVisualization from './Math/GCD/GCDVisualization';
import GCDController from './Math/GCD/GCDController';
import GCDAlgorithm from './Math/GCD/GCDAlgorithm';

import PrimalityTestVisualization from './Math/PrimalityTest/PrimalityTestVisualization';
import PrimalityTestController from './Math/PrimalityTest/PrimalityTestController';
import PrimalityTestAlgorithm from './Math/PrimalityTest/PrimalityTestAlgorithm';

// Algorithm registry
const algorithms = {
  TowersOfHanoi: {
    id: 'TowersOfHanoi',
    name: 'Towers of Hanoi',
    description: 'A classic recursive algorithm for solving the Tower of Hanoi puzzle.',
    timeComplexity: 'O(2^n)',
    spaceComplexity: 'O(n)',
    visualization: TowersOfHanoiVisualization,
    controller: TowersOfHanoiController,
    algorithm: TowersOfHanoiAlgorithm,
    defaultParams: {
      numDiscs: 3,
    },
  },
  NQueens: {
    id: 'NQueens',
    name: 'N-Queens Problem',
    description: 'A backtracking algorithm that places N chess queens on an N×N chessboard so that no two queens threaten each other.',
    timeComplexity: 'O(N!)',
    spaceComplexity: 'O(N²)',
    visualization: NQueensVisualization,
    controller: NQueensController,
    algorithm: NQueensAlgorithm,
    defaultParams: {
      boardSize: 4,
      findAllSolutions: false,
    },
  },
  Sudoku: {
    id: 'Sudoku',
    name: 'Sudoku Solver',
    description: 'A backtracking algorithm that solves Sudoku puzzles by trying different values for each empty cell and backtracking when a value doesn\'t lead to a solution.',
    timeComplexity: 'O(9^m)', // m is the number of empty cells
    spaceComplexity: 'O(n²)', // n is the board size (9x9)
    visualization: SudokuVisualization,
    controller: SudokuController,
    algorithm: SudokuAlgorithm,
    defaultParams: {
      difficulty: 'medium',
      board: null,
    },
  },
  RatInMaze: {
    id: 'RatInMaze',
    name: 'Rat in a Maze',
    description: 'A backtracking algorithm that finds a path from the start to the end of a maze, exploring all possible paths and backtracking when necessary.',
    timeComplexity: 'O(4^(n²))', // Worst case when exploring all paths in an n×n maze
    spaceComplexity: 'O(n²)', // For the solution matrix
    visualization: RatInMazeVisualization,
    controller: RatInMazeController,
    algorithm: RatInMazeAlgorithm,
    defaultParams: {
      mazeSize: 4,
      customMaze: null,
      useCustomMaze: false,
      allowDiagonal: false,
    },
  },
  Permutations: {
    id: 'Permutations',
    name: 'Generate Permutations',
    description: 'A backtracking algorithm that generates all possible arrangements (permutations) of a given set of elements.',
    timeComplexity: 'O(n!)', // n! permutations to generate
    spaceComplexity: 'O(n)', // For the recursion stack and to store each permutation
    visualization: PermutationsVisualization,
    controller: PermutationsController,
    algorithm: PermutationsAlgorithm,
    defaultParams: {
      elements: ['A', 'B', 'C'],
      useNumbers: false,
    },
  },
  Combinations: {
    id: 'Combinations',
    name: 'Generate Combinations',
    description: 'A backtracking algorithm that generates all possible ways to select k elements from a set of n elements, where the order doesn\'t matter.',
    timeComplexity: 'O(n choose k)', // Combinations to generate
    spaceComplexity: 'O(k)', // For the recursion stack and to store each combination
    visualization: CombinationsVisualization,
    controller: CombinationsController,
    algorithm: CombinationsAlgorithm,
    defaultParams: {
      elements: ['A', 'B', 'C', 'D'],
      k: 2,
      useNumbers: false,
    },
  },
  SubsetSum: {
    id: 'SubsetSum',
    name: 'Subset Sum Problem',
    description: 'An algorithm that determines if there exists a subset of the given numbers that adds up exactly to a given target sum.',
    timeComplexity: 'O(n*target) for DP, O(2^n) for recursive', // Depends on the approach
    spaceComplexity: 'O(n*target) for DP, O(n) for recursive', // Depends on the approach
    visualization: SubsetSumVisualization,
    controller: SubsetSumController,
    algorithm: SubsetSumAlgorithm,
    defaultParams: {
      numbers: [3, 34, 4, 12, 5, 2],
      target: 9,
      approach: 'dp',
    },
  },
  Sieve: {
    id: 'Sieve',
    name: 'Sieve of Eratosthenes',
    description: 'An efficient algorithm for finding all prime numbers up to a specified limit by iteratively marking the multiples of each prime number as composite.',
    timeComplexity: 'O(n log log n)', // Very efficient for finding primes
    spaceComplexity: 'O(n)', // For the boolean array
    visualization: SieveVisualization,
    controller: SieveController,
    algorithm: SieveAlgorithm,
    defaultParams: {
      limit: 30,
    },
  },
  GCD: {
    id: 'GCD',
    name: 'Euclidean GCD',
    description: 'An efficient algorithm for computing the greatest common divisor (GCD) of two integers using either division or subtraction.',
    timeComplexity: 'O(log(min(a, b))) for division, O(max(a, b)) for subtraction', // Depends on the method
    spaceComplexity: 'O(1)', // Constant space
    visualization: GCDVisualization,
    controller: GCDController,
    algorithm: GCDAlgorithm,
    defaultParams: {
      a: 48,
      b: 18,
      method: 'division',
    },
  },
  PrimalityTest: {
    id: 'PrimalityTest',
    name: 'Primality Test',
    description: 'Algorithms for determining whether a given number is prime, including Trial Division, Fermat Primality Test, and Miller-Rabin Primality Test.',
    timeComplexity: 'O(sqrt(n)) for Trial Division, O(k log n) for Fermat, O(k log^3 n) for Miller-Rabin', // Depends on the method
    spaceComplexity: 'O(1)', // Constant space
    visualization: PrimalityTestVisualization,
    controller: PrimalityTestController,
    algorithm: PrimalityTestAlgorithm,
    defaultParams: {
      number: 17,
      method: 'trial_division',
    },
  },
  BubbleSort: {
    id: 'BubbleSort',
    name: 'Bubble Sort',
    description: 'A simple sorting algorithm that repeatedly steps through the list, compares adjacent elements and swaps them if they are in the wrong order.',
    timeComplexity: 'O(n²)',
    spaceComplexity: 'O(1)',
    visualization: BubbleSortVisualization,
    controller: BubbleSortController,
    algorithm: BubbleSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  // Add more algorithms here as they are implemented
  QuickSort: {
    id: 'QuickSort',
    name: 'Quick Sort',
    description: 'A divide-and-conquer sorting algorithm that uses a pivot element to partition the array.',
    timeComplexity: 'O(n log n) average, O(n²) worst case',
    spaceComplexity: 'O(log n)',
    visualization: QuickSortVisualization,
    controller: QuickSortController,
    algorithm: QuickSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  MergeSort: {
    id: 'MergeSort',
    name: 'Merge Sort',
    description: 'An efficient, stable, comparison-based, divide and conquer sorting algorithm.',
    timeComplexity: 'O(n log n)',
    spaceComplexity: 'O(n)',
    visualization: MergeSortVisualization,
    controller: MergeSortController,
    algorithm: MergeSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  Dijkstra: {
    id: 'Dijkstra',
    name: 'Dijkstra\'s Algorithm',
    description: 'An algorithm for finding the shortest paths between nodes in a graph.',
    timeComplexity: 'O((V + E) log V)',
    spaceComplexity: 'O(V)',
    visualization: DijkstraVisualization,
    controller: DijkstraController,
    algorithm: DijkstraAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      endNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  BFS: {
    id: 'BFS',
    name: 'Breadth First Search',
    description: 'An algorithm for traversing or searching tree or graph data structures.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: BFSVisualization,
    controller: BFSController,
    algorithm: BFSAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      targetNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  DFS: {
    id: 'DFS',
    name: 'Depth-First Search',
    description: 'An algorithm for traversing or searching tree or graph data structures using a stack.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: DFSVisualization,
    controller: DFSController,
    algorithm: DFSAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      targetNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  HeapSort: {
    id: 'HeapSort',
    name: 'Heap Sort',
    description: 'A comparison-based sorting algorithm that uses a binary heap data structure.',
    timeComplexity: 'O(n log n)',
    spaceComplexity: 'O(1)',
    visualization: HeapSortVisualization,
    controller: HeapSortController,
    algorithm: HeapSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  LCS: {
    id: 'LCS',
    name: 'Longest Common Subsequence',
    description: 'A dynamic programming algorithm that finds the longest subsequence common to two sequences.',
    timeComplexity: 'O(m*n)',
    spaceComplexity: 'O(m*n)',
    visualization: LCSVisualization,
    controller: LCSController,
    algorithm: LCSAlgorithm,
    defaultParams: {
      string1: 'ABCBDAB',
      string2: 'BDCABA',
      animationSpeed: 5,
    },
  },
  FloydWarshall: {
    id: 'FloydWarshall',
    name: 'Floyd-Warshall Algorithm',
    description: 'An algorithm for finding shortest paths in a weighted graph with positive or negative edge weights (but no negative cycles).',
    timeComplexity: 'O(V³)',
    spaceComplexity: 'O(V²)',
    visualization: FloydWarshallVisualization,
    controller: FloydWarshallController,
    algorithm: FloydWarshallAlgorithm,
    defaultParams: {
      vertices: 5,
      density: 0.7,
      minWeight: 1,
      maxWeight: 10,
      allowNegative: false,
      animationSpeed: 5,
    },
  },
  BellmanFord: {
    id: 'BellmanFord',
    name: 'Bellman-Ford Algorithm',
    description: 'An algorithm for finding the shortest paths from a source vertex to all other vertices in a weighted graph, which can handle negative edge weights and detect negative cycles.',
    timeComplexity: 'O(V × E)',
    spaceComplexity: 'O(V)',
    visualization: BellmanFordVisualization,
    controller: BellmanFordController,
    algorithm: BellmanFordAlgorithm,
    defaultParams: {
      graphPreset: 'simple',
      startNode: 'A',
      useCustomGraph: false,
      customGraph: null,
    },
  },
  Kruskals: {
    id: 'Kruskals',
    name: 'Kruskal\'s Algorithm',
    description: 'A minimum spanning tree algorithm that finds an edge of the least possible weight that connects any two trees in the forest.',
    timeComplexity: 'O(E log E) or O(E log V)',
    spaceComplexity: 'O(V + E)',
    visualization: KruskalsVisualization,
    controller: KruskalsController,
    algorithm: KruskalsAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.7,
      minWeight: 1,
      maxWeight: 10,
      customEdges: [],
    },
  },
  Prims: {
    id: 'Prims',
    name: 'Prim\'s Algorithm',
    description: 'A greedy algorithm that finds a minimum spanning tree for a weighted undirected graph.',
    timeComplexity: 'O(E log V)',
    spaceComplexity: 'O(V + E)',
    visualization: PrimsVisualization,
    controller: PrimsController,
    algorithm: PrimsAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.7,
      minWeight: 1,
      maxWeight: 10,
      startNode: 0,
      customEdges: [],
    },
  },
  TopologicalSort: {
    id: 'TopologicalSort',
    name: 'Topological Sort',
    description: 'An algorithm for ordering the vertices of a directed acyclic graph (DAG) such that for every directed edge (u, v), vertex u comes before vertex v in the ordering.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: TopologicalSortVisualization,
    controller: TopologicalSortController,
    algorithm: TopologicalSortAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.5,
      customEdges: [],
    },
  },
  AStar: {
    id: 'AStar',
    name: 'A* Algorithm',
    description: 'A* is a pathfinding algorithm that combines the advantages of Dijkstra\'s algorithm and greedy best-first search. It uses a heuristic function to estimate the cost from the current node to the goal, which helps guide the search more efficiently.',
    timeComplexity: 'O((V + E) log V)',
    spaceComplexity: 'O(V)',
    visualization: AStarVisualization,
    controller: AStarController,
    algorithm: AStarAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      endNode: 5,
      density: 0.5,
      customEdges: [],
      useCustomGraph: false,
    },
  },
  StronglyConnected: {
    id: 'StronglyConnected',
    name: 'Strongly Connected Components',
    description: 'An algorithm for finding strongly connected components (SCCs) in a directed graph. A strongly connected component is a subgraph where every vertex is reachable from every other vertex.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: StronglyConnectedVisualization,
    controller: StronglyConnectedController,
    algorithm: StronglyConnectedAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.5,
      minWeight: 1,
      maxWeight: 10,
      customEdges: [],
    },
  },
  ArticulationPoints: {
    id: 'ArticulationPoints',
    name: 'Articulation Points',
    description: 'An algorithm for finding articulation points (cut vertices) in an undirected graph. Articulation points are nodes whose removal increases the number of connected components in the graph.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: ArticulationPointsVisualization,
    controller: ArticulationPointsController,
    algorithm: ArticulationPointsAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.5,
      minWeight: 1,
      maxWeight: 10,
      customEdges: [],
    },
  },
  Bridges: {
    id: 'Bridges',
    name: 'Bridges',
    description: 'An algorithm for finding bridges (cut edges) in an undirected graph. Bridges are edges whose removal increases the number of connected components in the graph.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: BridgesVisualization,
    controller: BridgesController,
    algorithm: BridgesAlgorithm,
    defaultParams: {
      nodes: 6,
      density: 0.5,
      minWeight: 1,
      maxWeight: 10,
      customEdges: [],
    },
  },
  InsertionSort: {
    id: 'InsertionSort',
    name: 'Insertion Sort',
    description: 'A simple sorting algorithm that builds the final sorted array one item at a time by comparing each element with the already-sorted portion of the array.',
    timeComplexity: 'O(n²) worst and average case, O(n) best case',
    spaceComplexity: 'O(1)',
    visualization: InsertionSortVisualization,
    controller: InsertionSortController,
    algorithm: InsertionSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  SelectionSort: {
    id: 'SelectionSort',
    name: 'Selection Sort',
    description: 'A simple sorting algorithm that repeatedly finds the minimum element from the unsorted part of the array and puts it at the beginning.',
    timeComplexity: 'O(n²) in all cases',
    spaceComplexity: 'O(1)',
    visualization: SelectionSortVisualization,
    controller: SelectionSortController,
    algorithm: SelectionSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  RadixSort: {
    id: 'RadixSort',
    name: 'Radix Sort',
    description: 'A non-comparative sorting algorithm that sorts integers by processing individual digits, starting from the least significant digit to the most significant digit.',
    timeComplexity: 'O(n·k) where n is the number of elements and k is the number of digits',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    visualization: RadixSortVisualization,
    controller: RadixSortController,
    algorithm: RadixSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  CountingSort: {
    id: 'CountingSort',
    name: 'Counting Sort',
    description: 'A non-comparative sorting algorithm that works well when the range of input values is not significantly larger than the number of elements to be sorted.',
    timeComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    visualization: CountingSortVisualization,
    controller: CountingSortController,
    algorithm: CountingSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  BucketSort: {
    id: 'BucketSort',
    name: 'Bucket Sort',
    description: 'A distribution sort that works by distributing the elements into a number of buckets, then sorting each bucket individually, and finally concatenating the sorted buckets.',
    timeComplexity: 'O(n+k) average case, O(n²) worst case',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the number of buckets',
    visualization: BucketSortVisualization,
    controller: BucketSortController,
    algorithm: BucketSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      bucketCount: 5,
      randomize: true,
      customArray: [],
    },
  },
  ShellSort: {
    id: 'ShellSort',
    name: 'Shell Sort',
    description: 'An in-place comparison sort that generalizes insertion sort by allowing the exchange of items that are far apart. The method starts by sorting pairs of elements far apart from each other, then progressively reducing the gap between elements to be compared.',
    timeComplexity: 'O(n log² n) average case, O(n²) worst case',
    spaceComplexity: 'O(1)',
    visualization: ShellSortVisualization,
    controller: ShellSortController,
    algorithm: ShellSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  TimSort: {
    id: 'TimSort',
    name: 'Tim Sort',
    description: 'A hybrid sorting algorithm derived from merge sort and insertion sort. It was designed to perform well on many kinds of real-world data. Tim Sort is the standard sorting algorithm used in Python, Java, and many other programming languages.',
    timeComplexity: 'O(n log n)',
    spaceComplexity: 'O(n)',
    visualization: TimSortVisualization,
    controller: TimSortController,
    algorithm: TimSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  // Searching Algorithms
  BinarySearch: {
    id: 'BinarySearch',
    name: 'Binary Search',
    description: 'An efficient algorithm for finding a target value within a sorted array. It works by repeatedly dividing the search interval in half.',
    timeComplexity: 'O(log n)',
    spaceComplexity: 'O(1)',
    visualization: BinarySearchVisualization,
    controller: BinarySearchController,
    algorithm: BinarySearchAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
      target: 42,
    },
  },
  LinearSearch: {
    id: 'LinearSearch',
    name: 'Linear Search',
    description: 'The simplest search algorithm that checks each element in the array sequentially until the target element is found or the end of the array is reached.',
    timeComplexity: 'O(n)',
    spaceComplexity: 'O(1)',
    visualization: LinearSearchVisualization,
    controller: LinearSearchController,
    algorithm: LinearSearchAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
      target: 42,
    },
  },
  JumpSearch: {
    id: 'JumpSearch',
    name: 'Jump Search',
    description: 'A searching algorithm for sorted arrays that works by jumping ahead by fixed steps and then performing a linear search within a smaller range.',
    timeComplexity: 'O(√n)',
    spaceComplexity: 'O(1)',
    visualization: JumpSearchVisualization,
    controller: JumpSearchController,
    algorithm: JumpSearchAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
      target: 42,
    },
  },
  InterpolationSearch: {
    id: 'InterpolationSearch',
    name: 'Interpolation Search',
    description: 'An improved variant of binary search that uses the value of the target element to estimate its position in the array. It works best on uniformly distributed data.',
    timeComplexity: 'O(log log n)',
    spaceComplexity: 'O(1)',
    visualization: InterpolationSearchVisualization,
    controller: InterpolationSearchController,
    algorithm: InterpolationSearchAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
      target: 42,
    },
  },
  ExponentialSearch: {
    id: 'ExponentialSearch',
    name: 'Exponential Search',
    description: 'A searching algorithm that works on sorted arrays by finding a range where the target might be by repeatedly doubling an index, then performing a binary search within that range.',
    timeComplexity: 'O(log n)',
    spaceComplexity: 'O(1)',
    visualization: ExponentialSearchVisualization,
    controller: ExponentialSearchController,
    algorithm: ExponentialSearchAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
      target: 42,
    },
  },
  Fibonacci: {
    id: 'Fibonacci',
    name: 'Fibonacci Sequence',
    description: 'A dynamic programming algorithm that calculates the Fibonacci sequence, where each number is the sum of the two preceding ones. Demonstrates both bottom-up (tabulation) and top-down (memoization) approaches.',
    timeComplexity: 'O(n)',
    spaceComplexity: 'O(n)',
    visualization: FibonacciVisualization,
    controller: FibonacciController,
    algorithm: FibonacciAlgorithm,
    defaultParams: {
      n: 10,
      approach: 'tabulation',
    },
  },
  Knapsack: {
    id: 'Knapsack',
    name: '0/1 Knapsack',
    description: 'A dynamic programming algorithm that solves the 0/1 Knapsack problem, maximizing value while staying within a weight constraint. Each item can either be selected (1) or not (0).',
    timeComplexity: 'O(n*W)',
    spaceComplexity: 'O(n*W)',
    visualization: KnapsackVisualization,
    controller: KnapsackController,
    algorithm: KnapsackAlgorithm,
    defaultParams: {
      weights: [2, 3, 4, 5],
      values: [3, 4, 5, 6],
      capacity: 8,
    },
  },
  EditDistance: {
    id: 'EditDistance',
    name: 'Edit Distance',
    description: 'A dynamic programming algorithm that calculates the minimum number of operations (insertions, deletions, or substitutions) required to transform one string into another.',
    timeComplexity: 'O(m*n)',
    spaceComplexity: 'O(m*n)',
    visualization: EditDistanceVisualization,
    controller: EditDistanceController,
    algorithm: EditDistanceAlgorithm,
    defaultParams: {
      string1: 'kitten',
      string2: 'sitting',
    },
  },
  MatrixChainMultiplication: {
    id: 'MatrixChainMultiplication',
    name: 'Matrix Chain Multiplication',
    description: 'A dynamic programming algorithm that determines the most efficient way to multiply a sequence of matrices to minimize the total number of scalar multiplications.',
    timeComplexity: 'O(n³)',
    spaceComplexity: 'O(n²)',
    visualization: MatrixChainMultiplicationVisualization,
    controller: MatrixChainMultiplicationController,
    algorithm: MatrixChainMultiplicationAlgorithm,
    defaultParams: {
      dimensions: [30, 35, 15, 5, 10, 20, 25],
    },
  },
  CoinChange: {
    id: 'CoinChange',
    name: 'Coin Change',
    description: 'A dynamic programming algorithm that finds the minimum number of coins needed to make a specific amount of change, given a set of coin denominations.',
    timeComplexity: 'O(amount * n)',
    spaceComplexity: 'O(amount)',
    visualization: CoinChangeVisualization,
    controller: CoinChangeController,
    algorithm: CoinChangeAlgorithm,
    defaultParams: {
      coins: [1, 2, 5],
      amount: 11,
    },
  },
  LIS: {
    id: 'LIS',
    name: 'Longest Increasing Subsequence',
    description: 'A dynamic programming algorithm that finds the length of the longest subsequence of a given sequence such that all elements of the subsequence are sorted in increasing order.',
    timeComplexity: 'O(n²)',
    spaceComplexity: 'O(n)',
    visualization: LISVisualization,
    controller: LISController,
    algorithm: LISAlgorithm,
    defaultParams: {
      sequence: [10, 22, 9, 33, 21, 50, 41, 60, 80],
    },
  },
  RodCutting: {
    id: 'RodCutting',
    name: 'Rod Cutting',
    description: 'A dynamic programming algorithm that determines the maximum value obtainable by cutting a rod of a given length into smaller pieces, where each piece has a specific price.',
    timeComplexity: 'O(n²)',
    spaceComplexity: 'O(n)',
    visualization: RodCuttingVisualization,
    controller: RodCuttingController,
    algorithm: RodCuttingAlgorithm,
    defaultParams: {
      prices: [0, 1, 5, 8, 9, 10, 17, 17, 20, 24, 30],
      rodLength: 8,
    },
  },
};

// Helper functions
export const getAlgorithmList = () => {
  return Object.values(algorithms).map(algo => ({
    id: algo.id,
    name: algo.name,
    description: algo.description,
    timeComplexity: algo.timeComplexity,
    spaceComplexity: algo.spaceComplexity,
  }));
};

export const getAlgorithm = (id) => {
  return algorithms[id] || null;
};

export const getDefaultParams = (id) => {
  return algorithms[id]?.defaultParams || {};
};

export default algorithms;
