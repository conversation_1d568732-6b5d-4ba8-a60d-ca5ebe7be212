// DijkstraAlgorithm.js
// This file contains the implementation of <PERSON><PERSON><PERSON>'s algorithm and the visualization component.

import React from 'react';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    background: isDark ? '#1e1e1e' : '#f5f5f5',
    text: isDark ? '#d4d4d4' : '#333333',
    comment: isDark ? '#6a9955' : '#008000',
    keyword: isDark ? '#569cd6' : '#0000ff',
    string: isDark ? '#ce9178' : '#a31515',
    number: isDark ? '#b5cea8' : '#098658',
    function: isDark ? '#dcdcaa' : '#795e26',
    variable: isDark ? '#9cdcfe' : '#001080',
    property: isDark ? '#4fc1ff' : '#0070c1',
    operator: isDark ? '#d4d4d4' : '#000000',
    punctuation: isDark ? '#d4d4d4' : '#000000',
    border: isDark ? '#444444' : '#cccccc',
    highlight: isDark ? 'rgba(14, 99, 156, 0.3)' : 'rgba(173, 214, 255, 0.3)',
    activeHighlight: isDark ? 'rgba(14, 99, 156, 0.8)' : 'rgba(0, 120, 215, 0.4)',
    stepBackground: isDark ? '#252525' : '#f0f0f0',
  };
};

/**
 * Generate a graph with random weights
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @returns {Object} - Graph representation
 */
export const generateRandomGraph = (numNodes, density = 0.5) => {
  // Create an empty adjacency list
  const graph = {};

  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = {};
  }

  // Add random edges with weights
  for (let i = 0; i < numNodes; i++) {
    for (let j = 0; j < numNodes; j++) {
      if (i !== j && Math.random() < density) {
        // Random weight between 1 and 10
        const weight = Math.floor(Math.random() * 10) + 1;
        graph[i][j] = weight;
      }
    }
  }

  // Ensure the graph is connected
  for (let i = 0; i < numNodes - 1; i++) {
    // If there's no edge from i to i+1, add one
    if (!graph[i][i+1]) {
      const weight = Math.floor(Math.random() * 10) + 1;
      graph[i][i+1] = weight;
    }
    // If there's no edge from i+1 to i, add one
    if (!graph[i+1][i]) {
      const weight = Math.floor(Math.random() * 10) + 1;
      graph[i+1][i] = weight;
    }
  }

  return graph;
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edges - Array of edges [from, to, weight]
 * @returns {Object} - Graph representation
 */
export const generateCustomGraph = (numNodes, edges) => {
  // Create an empty adjacency list
  const graph = {};

  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = {};
  }

  // Add edges
  edges.forEach(([from, to, weight]) => {
    if (from >= 0 && from < numNodes && to >= 0 && to < numNodes) {
      graph[from][to] = weight;
    }
  });

  return graph;
};

/**
 * Generate steps for Dijkstra's algorithm visualization
 * @param {Object} graph - Graph representation (adjacency list)
 * @param {number} startNode - Starting node
 * @param {number} endNode - Target node (optional)
 * @returns {Object} - Object containing steps and shortest paths
 */
export const generateDijkstraSteps = (graph, startNode, endNode = null) => {
  const steps = [];
  const nodes = Object.keys(graph).map(Number);

  // Initialize distances and previous nodes
  const distances = {};
  const previous = {};
  const unvisited = new Set(nodes);

  // Initialize all distances as Infinity and previous as null
  nodes.forEach(node => {
    distances[node] = Infinity;
    previous[node] = null;
  });

  // Distance from start node to itself is 0
  distances[startNode] = 0;

  // Add initialization step
  steps.push({
    type: 'initialize',
    distances: { ...distances },
    previous: { ...previous },
    current: startNode,
    unvisited: [...unvisited],
    movement: `Initialize distances: set distance to start node ${startNode} as 0, all others as Infinity`
  });

  // Main algorithm loop
  while (unvisited.size > 0) {
    // Find the unvisited node with the smallest distance
    let current = null;
    let smallestDistance = Infinity;

    unvisited.forEach(node => {
      if (distances[node] < smallestDistance) {
        smallestDistance = distances[node];
        current = node;
      }
    });

    // If the smallest distance is Infinity, there's no path to remaining nodes
    if (smallestDistance === Infinity) {
      steps.push({
        type: 'unreachable',
        distances: { ...distances },
        previous: { ...previous },
        unvisited: [...unvisited],
        movement: `Remaining nodes are unreachable from start node ${startNode}`
      });
      break;
    }

    // If we've reached the end node, we can stop
    if (endNode !== null && current === endNode) {
      steps.push({
        type: 'found',
        distances: { ...distances },
        previous: { ...previous },
        current,
        unvisited: [...unvisited],
        movement: `Found shortest path to target node ${endNode} with distance ${distances[endNode]}`
      });
      break;
    }

    // Add step for selecting current node
    steps.push({
      type: 'select',
      distances: { ...distances },
      previous: { ...previous },
      current,
      unvisited: [...unvisited],
      movement: `Select node ${current} with smallest distance ${distances[current]}`
    });

    // Remove current node from unvisited set
    unvisited.delete(current);

    // Add step for marking node as visited
    steps.push({
      type: 'visit',
      distances: { ...distances },
      previous: { ...previous },
      current,
      unvisited: [...unvisited],
      movement: `Mark node ${current} as visited`
    });

    // Check all neighbors of the current node
    for (const neighbor in graph[current]) {
      const neighborNode = parseInt(neighbor);

      // Skip if neighbor is already visited
      if (!unvisited.has(neighborNode)) continue;

      // Calculate new distance to neighbor
      const edgeWeight = graph[current][neighborNode];
      const newDistance = distances[current] + edgeWeight;

      // Add step for checking neighbor
      steps.push({
        type: 'check',
        distances: { ...distances },
        previous: { ...previous },
        current,
        neighbor: neighborNode,
        edgeWeight,
        newDistance,
        unvisited: [...unvisited],
        movement: `Check neighbor ${neighborNode}: current distance ${distances[neighborNode]}, new potential distance ${newDistance}`
      });

      // If new distance is shorter, update distance and previous node
      if (newDistance < distances[neighborNode]) {
        distances[neighborNode] = newDistance;
        previous[neighborNode] = current;

        // Add step for updating distance
        steps.push({
          type: 'update',
          distances: { ...distances },
          previous: { ...previous },
          current,
          neighbor: neighborNode,
          edgeWeight,
          newDistance,
          unvisited: [...unvisited],
          movement: `Update distance to node ${neighborNode} to ${newDistance} via node ${current}`
        });
      }
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    distances: { ...distances },
    previous: { ...previous },
    movement: `Dijkstra's algorithm complete. Found shortest paths from node ${startNode} to all reachable nodes`
  });

  // Reconstruct the shortest path if end node is specified
  let shortestPath = [];
  if (endNode !== null && distances[endNode] !== Infinity) {
    let current = endNode;
    while (current !== null) {
      shortestPath.unshift(current);
      current = previous[current];
    }
  }

  return { steps, distances, previous, shortestPath };
};

/**
 * Dijkstra's Algorithm visualization component
 */
const DijkstraAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Define the steps of the algorithm
  const steps = [
    { line: 0, description: "Initialize distances: set start node to 0, all others to Infinity" },
    { line: 1, description: "Initialize previous nodes to null and create unvisited set" },
    { line: 2, description: "While unvisited nodes remain" },
    { line: 3, description: "Find unvisited node with smallest distance" },
    { line: 4, description: "If smallest distance is Infinity, remaining nodes are unreachable" },
    { line: 5, description: "If current node is target, we've found the shortest path" },
    { line: 6, description: "Mark current node as visited" },
    { line: 7, description: "For each unvisited neighbor of current node" },
    { line: 8, description: "Calculate new distance to neighbor" },
    { line: 9, description: "If new distance is shorter, update distance and previous node" },
    { line: 10, description: "Algorithm complete: shortest paths found" },
  ];

  // Get the current step
  const currentStep = steps[Math.min(step % steps.length, steps.length - 1)];

  // Dijkstra's algorithm pseudocode
  const pseudocode = [
    { code: "function dijkstra(graph, startNode, endNode):", highlight: currentStep.line === 0 },
    { code: "  // Initialize distances, previous nodes, and unvisited set", highlight: false },
    { code: "  distances = {}, previous = {}, unvisited = new Set()", highlight: currentStep.line === 1 },
    { code: "  for each node in graph:", highlight: false },
    { code: "    distances[node] = Infinity", highlight: currentStep.line === 0 },
    { code: "    previous[node] = null", highlight: currentStep.line === 1 },
    { code: "    unvisited.add(node)", highlight: currentStep.line === 1 },
    { code: "  distances[startNode] = 0", highlight: currentStep.line === 0 },
    { code: "", highlight: false },
    { code: "  // Main algorithm loop", highlight: false },
    { code: "  while unvisited is not empty:", highlight: currentStep.line === 2 },
    { code: "    // Find unvisited node with smallest distance", highlight: false },
    { code: "    current = node in unvisited with smallest distances[node]", highlight: currentStep.line === 3 },
    { code: "", highlight: false },
    { code: "    // If smallest distance is Infinity, remaining nodes are unreachable", highlight: false },
    { code: "    if distances[current] is Infinity:", highlight: currentStep.line === 4 },
    { code: "      break  // No path to remaining nodes", highlight: currentStep.line === 4 },
    { code: "", highlight: false },
    { code: "    // If we've reached the end node, we can stop", highlight: false },
    { code: "    if endNode is not null and current equals endNode:", highlight: currentStep.line === 5 },
    { code: "      break  // Found shortest path to target", highlight: currentStep.line === 5 },
    { code: "", highlight: false },
    { code: "    // Remove current node from unvisited set", highlight: false },
    { code: "    unvisited.remove(current)", highlight: currentStep.line === 6 },
    { code: "", highlight: false },
    { code: "    // Check all neighbors of the current node", highlight: false },
    { code: "    for each neighbor of current:", highlight: currentStep.line === 7 },
    { code: "      if neighbor is not in unvisited:", highlight: currentStep.line === 7 },
    { code: "        continue  // Skip visited neighbors", highlight: currentStep.line === 7 },
    { code: "", highlight: false },
    { code: "      // Calculate new distance to neighbor", highlight: false },
    { code: "      edgeWeight = graph[current][neighbor]", highlight: currentStep.line === 8 },
    { code: "      newDistance = distances[current] + edgeWeight", highlight: currentStep.line === 8 },
    { code: "", highlight: false },
    { code: "      // If new distance is shorter, update distance and previous node", highlight: false },
    { code: "      if newDistance < distances[neighbor]:", highlight: currentStep.line === 9 },
    { code: "        distances[neighbor] = newDistance", highlight: currentStep.line === 9 },
    { code: "        previous[neighbor] = current", highlight: currentStep.line === 9 },
    { code: "", highlight: false },
    { code: "  // Return distances and previous nodes", highlight: false },
    { code: "  return { distances, previous }", highlight: currentStep.line === 10 },
  ];

  return (
    <Box
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
        '&::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
          },
        },
      }}
    >
      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {currentStep?.description || "Algorithm complete"}
        </Typography>
      </Paper>

      {/* Pseudocode */}
      <Box
        sx={{
          fontFamily: "monospace",
          fontSize: "0.9rem",
          lineHeight: 1.5,
          whiteSpace: "pre",
          p: 1,
          overflowX: "auto",
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        {pseudocode.map((line, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              bgcolor: line.highlight ? colors.highlight : "transparent",
              borderRadius: 1,
              px: 1,
              py: 0.25,
            }}
          >
            {line.highlight && (
              <ArrowRightIcon
                sx={{
                  color: colors.function,
                  mr: 1,
                  fontSize: "1.2rem",
                }}
              />
            )}
            <Box sx={{ ml: line.highlight ? 0 : 3 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  color: colors.text,
                  fontWeight: line.highlight ? 600 : 400,
                }}
              >
                {line.code}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default DijkstraAlgorithm;
