// CoinChange/index.js
// Export only what's needed from this algorithm

import CoinChangeVisualization from './CoinChangeVisualization';
import CoinChangeController from './CoinChangeController';
import CoinChangeAlgorithm from './CoinChangeAlgorithm';

export const metadata = {
  id: 'CoinChange',
  name: 'Coin Change',
  description: 'A dynamic programming algorithm that finds the minimum number of coins needed to make a specific amount of change, given a set of coin denominations.',
  timeComplexity: 'O(amount * n)',
  spaceComplexity: 'O(amount)',
  defaultParams: {
    coins: [1, 2, 5],
    amount: 11,
  },
};

export const components = {
  visualization: CoinChangeVisualization,
  controller: CoinChangeController,
  algorithm: CoinChangeAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
