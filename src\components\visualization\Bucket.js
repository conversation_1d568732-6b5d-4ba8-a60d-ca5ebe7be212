import React, { useState, useEffect, useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable 3D bucket component for algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the bucket
 * @param {number} props.width - Width of the bucket
 * @param {number} props.height - Height of the bucket
 * @param {number} props.depth - Depth of the bucket
 * @param {string} props.color - Color of the bucket
 * @param {number} props.bucketIndex - Index of the bucket
 * @param {Array} props.elements - Elements contained in the bucket
 * @param {boolean} props.isHighlighted - Whether the bucket is highlighted
 * @param {boolean} props.showLabel - Whether to show the bucket index label
 * @param {boolean} props.showElements - Whether to show the elements in the bucket
 * @param {boolean} props.animateLid - Whether to animate the bucket lid
 * @returns {JSX.Element} - The rendered bucket component
 */
const Bucket = ({
    position,
    width = 2,
    height = 2,
    depth = 2,
    color,
    bucketIndex,
    elements = [],
    isHighlighted = false,
    showLabel = true,
    showElements = true,
    animateLid = true
}) => {
    const theme = useTheme();
    const bucketColor = isHighlighted ? theme.palette.secondary.main : color;

    // Animation for lid opening
    const [lidOpen, setLidOpen] = useState(false);
    const lidRotation = useRef(0);

    // Toggle lid open/close when bucket is highlighted
    useEffect(() => {
        if (animateLid) {
            if (isHighlighted) {
                setLidOpen(true);
            } else {
                // Add a small delay before closing the lid
                const timer = setTimeout(() => setLidOpen(false), 500);
                return () => clearTimeout(timer);
            }
        }
    }, [isHighlighted, animateLid]);

    // Animate lid opening/closing
    useFrame(() => {
        if (animateLid) {
            // Open to 100 degrees for a more natural look
            const targetRotation = lidOpen ? -Math.PI * 0.55 : 0;
            // Faster opening, slower closing for better effect
            const speed = lidOpen ? 0.15 : 0.08;
            lidRotation.current += (targetRotation - lidRotation.current) * speed;
        }
    });

    // Calculate a slightly darker color for the bucket edges
    const getEdgeColor = (baseColor) => {
        // Convert hex to RGB
        const r = parseInt(baseColor.slice(1, 3), 16);
        const g = parseInt(baseColor.slice(3, 5), 16);
        const b = parseInt(baseColor.slice(5, 7), 16);

        // Darken the color
        const darkenFactor = 0.7;
        const newR = Math.floor(r * darkenFactor);
        const newG = Math.floor(g * darkenFactor);
        const newB = Math.floor(b * darkenFactor);

        // Convert back to hex
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    };

    const edgeColor = getEdgeColor(bucketColor);

    return (
        <group position={position}>
            {/* Bucket container with solid 3D appearance - completely opaque */}
            <mesh position={[0, height / 2, 0]} castShadow receiveShadow>
                <boxGeometry args={[width, height, depth]} />
                <meshStandardMaterial
                    color={bucketColor}
                    transparent={false}
                    opacity={1}
                    metalness={0.4}
                    roughness={0.6}
                    depthWrite={true}
                    depthTest={true}
                />
            </mesh>

            {/* Enhanced internal surfaces with more realistic details */}
            {/* Bottom internal surface with subtle texture */}
            <mesh position={[0, 0.05, 0]} receiveShadow>
                <boxGeometry args={[width - 0.1, 0.05, depth - 0.1]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#0a0a0a' : '#e0e0e0'}
                    metalness={0.05}
                    roughness={0.95}
                />
            </mesh>

            {/* Inner corner reinforcements for more realism */}
            {/* Bottom-left corner */}
            <mesh position={[-width/2 + 0.1, 0.2, 0]} receiveShadow>
                <cylinderGeometry args={[0.05, 0.05, height - 0.2, 8]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#101010' : '#c8c8c8'}
                    metalness={0.1}
                    roughness={0.9}
                />
            </mesh>

            {/* Bottom-right corner */}
            <mesh position={[width/2 - 0.1, 0.2, 0]} rotation={[0, 0, 0]} receiveShadow>
                <cylinderGeometry args={[0.05, 0.05, height - 0.2, 8]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#101010' : '#c8c8c8'}
                    metalness={0.1}
                    roughness={0.9}
                />
            </mesh>

            {/* Left internal surface with subtle gradient */}
            <mesh position={[-width/2 + 0.05, height/2, 0]} receiveShadow>
                <boxGeometry args={[0.05, height - 0.1, depth - 0.1]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#080808' : '#d8d8d8'}
                    metalness={0.05}
                    roughness={0.95}
                />
            </mesh>

            {/* Right internal surface with subtle gradient */}
            <mesh position={[width/2 - 0.05, height/2, 0]} receiveShadow>
                <boxGeometry args={[0.05, height - 0.1, depth - 0.1]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#080808' : '#d8d8d8'}
                    metalness={0.05}
                    roughness={0.95}
                />
            </mesh>

            {/* Back internal surface with subtle texture */}
            <mesh position={[0, height/2, -depth/2 + 0.05]} receiveShadow>
                <boxGeometry args={[width - 0.1, height - 0.1, 0.05]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#050505' : '#d0d0d0'}
                    metalness={0.05}
                    roughness={0.95}
                />
            </mesh>

            {/* Front internal surface with subtle texture */}
            <mesh position={[0, height/2, depth/2 - 0.05]} receiveShadow>
                <boxGeometry args={[width - 0.1, height - 0.1, 0.05]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#050505' : '#d0d0d0'}
                    metalness={0.05}
                    roughness={0.95}
                />
            </mesh>

            {/* Subtle rim around the top edge */}
            <mesh position={[0, height - 0.05, 0]} receiveShadow>
                <boxGeometry args={[width + 0.02, 0.05, depth + 0.02]} />
                <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#2a2a2a' : '#e0e0e0'}
                    metalness={0.2}
                    roughness={0.7}
                />
            </mesh>

            {/* Bucket edges for better 3D definition - completely opaque */}
            <mesh position={[0, height / 2, 0]} receiveShadow>
                <boxGeometry args={[width + 0.05, height + 0.05, depth + 0.05]} />
                <meshStandardMaterial
                    color={edgeColor}
                    transparent={false}
                    opacity={1}
                    metalness={0.5}
                    roughness={0.5}
                    depthWrite={true}
                    depthTest={true}
                />
            </mesh>

            {/* Side panels for better 3D effect */}
            <mesh position={[width/2 + 0.01, height/2, 0]} castShadow receiveShadow>
                <boxGeometry args={[0.05, height - 0.1, depth - 0.1]} />
                <meshStandardMaterial color={edgeColor} />
            </mesh>

            <mesh position={[-width/2 - 0.01, height/2, 0]} castShadow receiveShadow>
                <boxGeometry args={[0.05, height - 0.1, depth - 0.1]} />
                <meshStandardMaterial color={edgeColor} />
            </mesh>

            {/* Animated Bucket Lid - fixed rotation */}
            {animateLid && (
                <group position={[0, height, -depth/2]} rotation={[lidRotation.current, 0, 0]}>
                    {/* Pivot point at the back edge of the lid */}
                    <group position={[0, 0, depth]}>
                        {/* Lid with rounded edges */}
                        <mesh position={[0, 0.025, -depth/2]} castShadow receiveShadow>
                            {/* Box for the lid */}
                            <boxGeometry args={[width + 0.1, 0.05, depth]} />
                            <meshStandardMaterial
                                color={edgeColor}
                                metalness={0.6}
                                roughness={0.4}
                            />
                        </mesh>

                        {/* Lid inner surface */}
                        <mesh position={[0, -0.01, -depth/2]} receiveShadow>
                            <boxGeometry args={[width - 0.05, 0.02, depth - 0.05]} />
                            <meshStandardMaterial
                                color={theme.palette.mode === 'dark' ? '#111111' : '#f8f8f8'}
                                metalness={0.1}
                                roughness={0.9}
                            />
                        </mesh>

                        {/* Lid handle with rounded edges */}
                        <mesh position={[0, 0.1, -depth*0.75]} rotation={[0, 0, Math.PI/2]} castShadow>
                            <cylinderGeometry args={[0.05, 0.05, width/3, 8]} />
                            <meshStandardMaterial
                                color={edgeColor}
                                metalness={0.7}
                                roughness={0.3}
                            />
                        </mesh>
                    </group>
                </group>
            )}

            {/* Bucket label */}
            {showLabel && (
                <Html position={[0, -0.8, 0]} center>
                    <div style={{
                        color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)',
                        background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(255,255,255,0.4)',
                        padding: '2px 8px',
                        borderRadius: '12px',
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        opacity: 0.9,
                        pointerEvents: 'none',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)'}`
                    }}>
                        {bucketIndex}
                    </div>
                </Html>
            )}

            {/* Bucket elements */}
            {showElements && elements.map((value, index) => {
                // Arrange elements in a neat grid pattern inside the bucket
                // Use a fixed number of columns based on bucket width
                const maxColumns = Math.max(2, Math.floor(width * 1.5));
                const rowIndex = Math.floor(index / maxColumns);
                const colIndex = index % maxColumns;

                // Calculate spacing between elements
                const elementSpacing = Math.min(0.4, (width - 0.2) / maxColumns);

                // Calculate starting positions to center the grid
                const startX = -(elementSpacing * (Math.min(elements.length, maxColumns) - 1)) / 2;
                const maxRows = Math.ceil(elements.length / maxColumns);
                const startZ = -(elementSpacing * (maxRows - 1)) / 2;

                // Calculate final position
                const x = startX + colIndex * elementSpacing;
                // Fixed height based on row for stability and better visibility
                const y = 0.3 + rowIndex * 0.15;
                const z = startZ + rowIndex * elementSpacing;

                return (
                    <Html key={`bucket-${bucketIndex}-element-${index}`} position={[x, y, z]} center distanceFactor={10}>
                        <div style={{
                            color: theme.palette.text.primary,
                            background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.95)',
                            padding: '3px 6px',
                            borderRadius: '6px',
                            fontSize: '0.7rem',
                            fontWeight: 'bold',
                            textAlign: 'center',
                            boxShadow: `0 2px 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.3)'}`,
                            border: `1px solid ${isHighlighted ? theme.palette.secondary.main : theme.palette.divider}`,
                            transform: 'scale(1)',
                            transition: 'transform 0.2s ease-in-out',
                            userSelect: 'none'
                        }}>
                            {value}
                        </div>
                    </Html>
                );
            })}
        </group>
    );
};

export default Bucket;
