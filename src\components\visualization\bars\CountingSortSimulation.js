// CountingSortSimulation.js
// Modern simulation component for CountingSort visualization

import React, { useMemo } from 'react';
import { useTheme } from '@mui/material/styles';
import { Text } from '@react-three/drei';
import getAlgorithmColors from '../../../utils/algorithmColors';
import CONFIG from '../../../algorithms/Sorting/CountingSort/CountingSortConfig';

const CountingSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight,
  barWidth,
  barSpacing,
  showValues,
  showIndices,
  config,
  state
}) => {
  console.log("counting sort details: ", {
    currentStep,
    colors,
    maxBarHeight,
    barWidth,
    barSpacing,
    showValues,
    showIndices,
    config,
    state
  })

  const theme = useTheme();


  // Extract visualization data from current step
  const visualizationData = currentStep?.visualizationData || {};
  const mainArray = visualizationData.mainArray || {};
  const countArray = visualizationData.countArray || [];
  const outputArray = visualizationData.outputArray || [];
  const maxValue = visualizationData.maxValue;

  const values = mainArray.values || [];
  const currentIndex = mainArray.currentIndex;
  const currentElement = mainArray.currentElement;
  const sortedIndices = mainArray.sortedIndices || [];
  const targetIndex = visualizationData.targetIndex;

  // Calculate positions and dimensions
  const totalWidth = values.length > 0 ? values.length * (barWidth + barSpacing) - barSpacing : 1;
  const startX = -totalWidth / 2;

  // Get colors for different states
  const algorithmColors = getAlgorithmColors(theme);
  const getBarColor = (index, value) => {
    if (sortedIndices.includes(index)) return colors.sorted || algorithmColors.sorted;
    if (index === currentIndex) return colors.current || algorithmColors.comparing;
    if (index === targetIndex) return colors.placing || algorithmColors.swapping;
    return colors.default || algorithmColors.default;
  };

  // Main array bars
  const mainArrayBars = useMemo(() => {
    if (!values || values.length === 0) return [];

    // Ensure maxValue is valid and positive
    const validValues = values.filter(v => v !== null && v !== undefined && !isNaN(v) && typeof v === 'number');
    if (validValues.length === 0) return [];

    const maxVal = Math.max(...validValues);
    if (maxVal <= 0) return [];

    // Calculate scaling factor based on maxBarHeight
    const scalingFactor = maxBarHeight / maxVal;

    return values.map((value, index) => {
      if (value === null || value === undefined || isNaN(value)) return null;

      const x = startX + index * (barWidth + barSpacing);
      const height = value * scalingFactor;
      const color = getBarColor(index, value);

      // Validate dimensions before creating geometry
      if (isNaN(x) || isNaN(height) || height <= 0 || isNaN(barWidth) || barWidth <= 0) {
        console.warn(`Invalid geometry values for main array bar: x=${x}, height=${height}, barWidth=${barWidth}`);
        return null;
      }

      return (
        <group key={`main-${index}`} position={[x, height / 2, 0]}>
          {/* Bar */}
          <mesh>
            {/* <boxGeometry args={[barWidth * 0.8, height, barWidth * 0.8]} /> */}
            <meshStandardMaterial
              color={color}
              roughness={CONFIG.mainArray.bars.material.roughness}
              metalness={CONFIG.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {showValues && (
            <Text
              position={[0, height / 2 + 0.3, 0]}
              fontSize={0.4}
              color={theme.palette.text.primary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {value}
            </Text>
          )}

          {/* Index label */}
          {showIndices && (
            <Text
              position={[0, -height / 2 - 0.5, 0]}
              fontSize={0.3}
              color={theme.palette.text.secondary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {index}
            </Text>
          )}
        </group>
      );
    }).filter(Boolean);
  }, [values, currentIndex, sortedIndices, targetIndex, barWidth, barSpacing, maxBarHeight, showValues, showIndices, colors, theme, startX, getBarColor]);

  // Count array visualization
  const countArrayBars = useMemo(() => {
    if (!countArray || countArray.length === 0) return null;

    const countBarWidth = CONFIG.countArray.bars.width;
    const countBarSpacing = CONFIG.countArray.bars.spacing;
    const countMaxHeight = CONFIG.countArray.bars.maxHeight;

    // Validate bar dimensions
    if (countBarWidth <= 0 || countBarSpacing < 0 || countMaxHeight <= 0) {
      console.warn(`Invalid count array dimensions: width=${countBarWidth}, spacing=${countBarSpacing}, maxHeight=${countMaxHeight}`);
      return null;
    }

    const countTotalWidth = countArray.length * (countBarWidth + countBarSpacing) - countBarSpacing;
    const countStartX = -countTotalWidth / 2;

    // Filter and validate count values
    const validCounts = countArray.filter(c => typeof c === 'number' && !isNaN(c) && c >= 0);
    if (validCounts.length === 0) return null;

    const maxCount = Math.max(...validCounts, 1);
    const platformWidth = Math.max(countTotalWidth + 1, 2);

    console.log('Rendering count array with maxCount:', maxCount);

    return (
      <group position={CONFIG.countArray.position}>
        {/* Count array platform */}
        <mesh position={[0, -0.1, 0]}>
          {/* <boxGeometry args={[platformWidth, 0.1, 1.5]} /> */}
          <meshStandardMaterial
            color={colors.countPlatform || CONFIG.colors.palette.countPlatform}
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Count bars */}
        {countArray.map((count, index) => {
          if (typeof count !== 'number' || isNaN(count) || count < 0) return null;

          const x = countStartX + index * (countBarWidth + countBarSpacing);
          const height = Math.max((count / maxCount) * countMaxHeight, CONFIG.countArray.bars.minHeight);
          const isHighlighted = currentElement === index;
          const color = isHighlighted
            ? (colors.countHighlight || CONFIG.colors.palette.countHighlight)
            : (colors.countBar || CONFIG.colors.palette.countBar);

          // Validate dimensions before creating geometry
          if (isNaN(x) || isNaN(height) || height <= 0) {
            console.warn(`Invalid count bar geometry: x=${x}, height=${height}, index=${index}`);
            return null;
          }

          return (
            <group key={`count-${index}`} position={[x, height / 2, 0]}>
              {/* Count bar */}
              <mesh>
                {/* <boxGeometry args={[countBarWidth * 0.9, height, countBarWidth * 0.9]} /> */}
                <meshStandardMaterial
                  color={color}
                  roughness={CONFIG.countArray.bars.material.roughness}
                  metalness={CONFIG.countArray.bars.material.metalness}
                />
              </mesh>

              {/* Count value label */}
              <Text
                position={[0, height / 2 + 0.2, 0]}
                fontSize={0.3}
                color={theme.palette.text.primary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {count}
              </Text>

              {/* Index label */}
              <Text
                position={[0, -height / 2 - 0.3, 0]}
                fontSize={0.25}
                color={theme.palette.text.secondary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {index}
              </Text>
            </group>
          );
        }).filter(Boolean)}

        {/* Count array title */}
        <Text
          position={[0, countMaxHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Count Array
        </Text>
      </group>
    );
  }, [countArray, currentElement, colors, theme]);

  // Output array visualization
  const outputArrayBars = useMemo(() => {
    if (!outputArray || outputArray.length === 0) return null;

    const outputBarWidth = CONFIG.outputArray.bars.width;
    const outputBarSpacing = CONFIG.outputArray.bars.spacing;
    const outputMaxHeight = CONFIG.outputArray.bars.maxHeight;

    // Validate bar dimensions
    if (outputBarWidth <= 0 || outputBarSpacing < 0 || outputMaxHeight <= 0) {
      console.warn(`Invalid output array dimensions: width=${outputBarWidth}, spacing=${outputBarSpacing}, maxHeight=${outputMaxHeight}`);
      return null;
    }

    // Calculate array dimensions
    const outputTotalWidth = outputArray.length * (outputBarWidth + outputBarSpacing) - outputBarSpacing;
    const outputStartX = -outputTotalWidth / 2;

    // Filter and validate values
    const validValues = outputArray.filter(v => v !== null && v !== undefined && !isNaN(v) && typeof v === 'number');
    if (validValues.length === 0) return null;

    const maxVal = Math.max(...validValues);
    if (maxVal <= 0) return null;

    // Calculate scaling factor
    const scalingFactor = outputMaxHeight / maxVal;
    const platformWidth = Math.max(outputTotalWidth + 1, 2);

    return (
      <group position={CONFIG.outputArray.position}>
        {/* Output array platform */}
        <mesh position={[0, -0.1, 0]}>
          {/* <boxGeometry args={[platformWidth, 0.1, 2.0]} /> */}
          <meshStandardMaterial
            color={colors.outputPlatform || CONFIG.colors.palette.outputPlatform}
            roughness={CONFIG.outputArray.platform.material.roughness}
            metalness={CONFIG.outputArray.platform.material.metalness}
          />
        </mesh>

        {/* Output array bars */}
        {outputArray.map((value, index) => {
          if (value === null || value === undefined || isNaN(value) || typeof value !== 'number') return null;

          const x = outputStartX + index * (outputBarWidth + outputBarSpacing);
          const height = value * scalingFactor;

          // Validate dimensions before creating geometry
          if (isNaN(x) || isNaN(height) || height <= 0) {
            console.warn(`Invalid output bar geometry: x=${x}, height=${height}, index=${index}`);
            return null;
          }

          return (
            <group key={`output-${index}`} position={[x, height / 2, 0]}>
              {/* Bar */}
              <mesh>
                {/* <boxGeometry args={[outputBarWidth * 0.8, height, outputBarWidth * 0.8]} /> */}
                <meshStandardMaterial
                  color={colors.default}
                  roughness={CONFIG.outputArray.bars.material.roughness}
                  metalness={CONFIG.outputArray.bars.material.metalness}
                />
              </mesh>

              {/* Value label */}
              {showValues && (
                <Text
                  position={[0, height / 2 + 0.3, 0]}
                  fontSize={0.4}
                  color={theme.palette.text.primary}
                  anchorX="center"
                  anchorY="middle"
                  depthOffset={-1}
                >
                  {value}
                </Text>
              )}

              {/* Index label */}
              {showIndices && (
                <Text
                  position={[0, -height / 2 - 0.5, 0]}
                  fontSize={0.3}
                  color={theme.palette.text.secondary}
                  anchorX="center"
                  anchorY="middle"
                  depthOffset={-1}
                >
                  {index}
                </Text>
              )}
            </group>
          );
        }).filter(Boolean)}

        {/* Output array title */}
        <Text
          position={[0, outputMaxHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Output Array
        </Text>
      </group>
    );
  }, [outputArray, showValues, showIndices, colors, theme]);

  return (
    <group>
      {/* Main array */}
      <group position={CONFIG.mainArray.bars.baseOffset}>
        {/* Main array platform */}
        <mesh position={[0, -0.1, 0]}>
          {/* <boxGeometry args={[Math.max(totalWidth + 0.5, 1), 0.1, 2.0]} /> */}
          <meshStandardMaterial
            color={colors.base || CONFIG.colors.palette.base}
            roughness={CONFIG.mainArray.platform.material.roughness}
            metalness={CONFIG.mainArray.platform.material.metalness}
          />
        </mesh>

        {/* Main array bars */}
        {/* {mainArrayBars} */}

        {/* Main array title */}
        <Text
          position={[0, maxBarHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Input Array
        </Text>
      </group>

      {/* Count array */}
      {countArrayBars}

      {/* Output array */}
      {outputArrayBars}
    </group>
  );
};

export default CountingSortSimulation;
