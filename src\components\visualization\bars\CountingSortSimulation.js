// CountingSortSimulation.js - Clean counting sort visualization component
// Consumes visualizationData from controller with minimal complexity

import React, { useMemo } from 'react';
import { Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ThemeHtml from '../ThemeHtml';
import CountingSortConfig from '../../../algorithms/Sorting/CountingSort/CountingSortConfig';

const CountingSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true
}) => {
  const theme = useTheme();

  // Get configuration for positioning
  const config = CountingSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // Calculate bar positions and layout
  const layoutData = useMemo(() => {
    if (!vizData?.mainArray?.values) return { barPositions: [], totalWidth: 0 };

    const values = vizData.mainArray.values;
    const baseSpacing = barWidth + barSpacing;
    const visualOffset = config.mainArray.bars.visualOffset;

    // Calculate static bar positions
    const staticPositions = [];
    const barsOnlyWidth = (values.length * baseSpacing) - barSpacing;
    const startX = -barsOnlyWidth / 2 + visualOffset;

    for (let i = 0; i < values.length; i++) {
      staticPositions.push({
        index: i,
        xPos: startX + i * baseSpacing
      });
    }

    return {
      barPositions: staticPositions,
      totalWidth: barsOnlyWidth
    };
  }, [vizData?.mainArray?.values, barWidth, barSpacing, config.mainArray.bars.visualOffset]);

  // Render the main array bars
  const renderMainArray = () => {
    if (!vizData?.mainArray?.values) return null;

    const {
      values,
      highlightedIndices = [],
      comparingIndices = [],
      sortedIndices = []
    } = vizData.mainArray;

    const maxValue = Math.max(...values);

    return values.map((value, index) => {
      const barHeight = (value / maxValue) * maxBarHeight;
      const position = layoutData.barPositions[index];
      if (!position) return null;

      const { xPos } = position;

      // Determine bar color based on state
      let barColor = colors.bar;

      if (sortedIndices.includes(index)) {
        barColor = colors.sorted;
      } else if (highlightedIndices.includes(index)) {
        barColor = colors.current;
      } else if (comparingIndices.includes(index)) {
        barColor = colors.comparing;
      }

      // Check if this is the current element being processed
      const isCurrentElement = vizData.currentElement !== undefined &&
                              vizData.currentIndex === index;
      if (isCurrentElement) {
        barColor = colors.current;
      }

      // Check if this element is being placed
      const isPlacing = vizData.targetIndex === index;
      if (isPlacing) {
        barColor = colors.placing;
      }

      return (
        <group key={`bar-${index}`} position={[xPos, 0, 0]}>
          {/* Bar base */}
          {config.mainArray.bars.base.enabled && (
            <mesh position={[0, -config.mainArray.bars.base.height / 2, 0]}>
              <boxGeometry args={[
                barWidth * config.mainArray.bars.base.widthScale,
                config.mainArray.bars.base.height,
                barWidth * config.mainArray.bars.base.depthScale
              ]} />
              <meshStandardMaterial
                color={colors.base}
                roughness={config.mainArray.bars.base.material.roughness}
                metalness={config.mainArray.bars.base.material.metalness}
                opacity={config.mainArray.bars.base.material.opacity}
                transparent={config.mainArray.bars.base.material.transparent}
              />
            </mesh>
          )}

          {/* Main bar */}
          <mesh position={[0, barHeight / 2, 0]} castShadow receiveShadow>
            <boxGeometry args={[
              barWidth * config.mainArray.bars.geometry.widthScale,
              barHeight,
              barWidth * config.mainArray.bars.geometry.depthScale
            ]} />
            <meshStandardMaterial
              color={barColor}
              roughness={config.mainArray.bars.material.roughness}
              metalness={config.mainArray.bars.material.metalness}
              opacity={config.mainArray.bars.material.opacity}
              transparent={config.mainArray.bars.material.transparent}
            />
          </mesh>

          {/* Value label */}
          {showValues && config.mainArray.valueLabels.enabled && (
            <ThemeHtml
              position={[
                config.mainArray.valueLabels.offset[0],
                barHeight + config.mainArray.valueLabels.offset[1],
                config.mainArray.valueLabels.offset[2]
              ]}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={config.mainArray.valueLabels.elevation}
                sx={{
                  px: config.mainArray.valueLabels.padding.horizontal,
                  py: config.mainArray.valueLabels.padding.vertical,
                  minWidth: config.mainArray.valueLabels.minWidth,
                  borderRadius: config.mainArray.valueLabels.borderRadius,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.valueLabels.fontSize,
                    fontWeight: config.mainArray.valueLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Index label */}
          {showIndices && config.mainArray.indexLabels.enabled && (
            <ThemeHtml
              position={[
                config.mainArray.indexLabels.offset[0],
                config.mainArray.indexLabels.offset[1],
                config.mainArray.indexLabels.offset[2]
              ]}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={config.mainArray.indexLabels.elevation}
                sx={{
                  width: config.mainArray.indexLabels.size.width,
                  height: config.mainArray.indexLabels.size.height,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.indexLabels.fontSize,
                    fontWeight: config.mainArray.indexLabels.fontWeight
                  }}
                  color="text.secondary"
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };

  // Render count array (specific to CountingSort)
  const renderCountArray = () => {
    if (!vizData?.countArray?.values) return null;

    const {
      values: countValues,
      highlightedIndices = []
    } = vizData.countArray;

    const position = config.countArray.arrangement.position;
    const cellWidth = config.countArray.cell.width;
    const cellSpacing = config.countArray.cell.spacing;
    const totalWidth = Math.min(config.countArray.arrangement.maxWidth, countValues.length * (cellWidth + cellSpacing));
    const startX = -totalWidth / 2;

    return (
      <group position={position}>
        {/* Count array title */}
        <ThemeHtml position={[0, 1, 0]} center sprite occlude theme={theme}>
          <Typography variant="h6" color="text.primary">
            Count Array
          </Typography>
        </ThemeHtml>

        {countValues.map((count, index) => {
          const isHighlighted = highlightedIndices.includes(index);
          const x = startX + index * (cellWidth + cellSpacing);

          return (
            <group key={`count-${index}`} position={[x, 0, 0]}>
              {/* Count cell */}
              <mesh>
                <boxGeometry args={[
                  config.countArray.cell.width,
                  config.countArray.cell.height,
                  config.countArray.cell.depth
                ]} />
                <meshStandardMaterial
                  color={isHighlighted ? colors.activeCell : colors.countArray}
                  transparent
                  opacity={0.8}
                />
              </mesh>

              {/* Index label */}
              {config.countArray.labels.enabled && (
                <ThemeHtml
                  position={config.countArray.labels.indexOffset}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.countArray.labels.fontSize,
                      fontWeight: config.countArray.labels.fontWeight
                    }}
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </ThemeHtml>
              )}

              {/* Value label */}
              {config.countArray.labels.enabled && (
                <ThemeHtml
                  position={config.countArray.labels.valueOffset}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.countArray.labels.fontSize,
                      fontWeight: config.countArray.labels.fontWeight
                    }}
                    color={isHighlighted ? "text.primary" : "text.secondary"}
                  >
                    {count}
                  </Typography>
                </ThemeHtml>
              )}
            </group>
          );
        })}
      </group>
    );
  };

  // Render output array (specific to CountingSort)
  const renderOutputArray = () => {
    if (!vizData?.outputArray?.values) return null;

    const {
      values: outputValues,
      highlightedIndices = []
    } = vizData.outputArray;

    const position = config.outputArray.arrangement.position;
    const cellWidth = config.outputArray.cell.width;
    const cellSpacing = config.outputArray.cell.spacing;
    const totalWidth = Math.min(config.outputArray.arrangement.maxWidth, outputValues.length * (cellWidth + cellSpacing));
    const startX = -totalWidth / 2;

    return (
      <group position={position}>
        {/* Output array title */}
        <ThemeHtml position={[0, 1, 0]} center sprite occlude theme={theme}>
          <Typography variant="h6" color="text.primary">
            Output Array
          </Typography>
        </ThemeHtml>

        {outputValues.map((value, index) => {
          const isHighlighted = highlightedIndices.includes(index);
          const x = startX + index * (cellWidth + cellSpacing);

          return (
            <group key={`output-${index}`} position={[x, 0, 0]}>
              {/* Output cell */}
              <mesh>
                <boxGeometry args={[
                  config.outputArray.cell.width,
                  config.outputArray.cell.height,
                  config.outputArray.cell.depth
                ]} />
                <meshStandardMaterial
                  color={isHighlighted ? colors.activeCell : colors.outputArray}
                  transparent
                  opacity={0.8}
                />
              </mesh>

              {/* Index label */}
              {config.outputArray.labels.enabled && (
                <ThemeHtml
                  position={config.outputArray.labels.indexOffset}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.outputArray.labels.fontSize,
                      fontWeight: config.outputArray.labels.fontWeight
                    }}
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </ThemeHtml>
              )}

              {/* Value label */}
              {config.outputArray.labels.enabled && value !== null && value !== undefined && (
                <ThemeHtml
                  position={config.outputArray.labels.valueOffset}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.outputArray.labels.fontSize,
                      fontWeight: config.outputArray.labels.fontWeight
                    }}
                    color={isHighlighted ? "text.primary" : "text.secondary"}
                  >
                    {value}
                  </Typography>
                </ThemeHtml>
              )}
            </group>
          );
        })}
      </group>
    );
  };

  return (
    <group>
      {/* Main array bars */}
      {renderMainArray()}

      {/* Count array (CountingSort-specific) */}
      {renderCountArray()}

      {/* Output array (CountingSort-specific) */}
      {renderOutputArray()}
    </group>
  );
};

export default CountingSortSimulation;