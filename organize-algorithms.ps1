# PowerShell script to organize algorithms into category folders

# Define the mapping of algorithms to categories
$mapping = @{
  "sorting" = @("BubbleSort", "MergeSort", "QuickSort", "HeapSort", "InsertionSort", "SelectionSort", "RadixSort", "CountingSort", "BucketSort")
  "searching" = @("BFS", "DFS")
  "graph" = @("<PERSON><PERSON><PERSON>", "FloydWarshall")
  "dp" = @("LCS")
  "recursion" = @("TowersOfHanoi")
}

# Create category folders if they don't exist
foreach ($category in $mapping.Keys) {
  $categoryPath = "src\algorithms\$category"
  if (-not (Test-Path $categoryPath)) {
    Write-Host "Creating folder: $categoryPath"
    New-Item -ItemType Directory -Path $categoryPath -Force | Out-Null
  }
}

# Move algorithms to their respective category folders
foreach ($category in $mapping.Keys) {
  foreach ($algorithm in $mapping[$category]) {
    $sourcePath = "src\algorithms\$algorithm"
    $destinationPath = "src\algorithms\$category\$algorithm"
    
    if (Test-Path $sourcePath) {
      Write-Host "Moving $algorithm to $category category"
      # Use Copy-Item instead of Move-Item to keep the original files as backup
      Copy-Item -Path $sourcePath -Destination "src\algorithms\$category\" -Recurse -Force
    } else {
      Write-Host "Algorithm folder not found: $sourcePath"
    }
  }
}

Write-Host "Algorithm reorganization completed!"
Write-Host "Please update the import paths in AlgorithmRegistry.js to point to the new locations."
