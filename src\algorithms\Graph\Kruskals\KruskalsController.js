// KruskalsController.js
// This component provides the controls for Kruskal's algorithm visualization

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';
// Visualization components will be used in the scene, not in the controller

// Import icons
import GraphIcon from '@mui/icons-material/AccountTree';
import TuneIcon from '@mui/icons-material/Tune';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'; // Used in customEdges parameter

// Import algorithm functions
import { generateKruskalsSteps } from './KruskalsAlgorithm';

// Import the WeightRangeInput component from common components
import { WeightRangeInput } from '../../../components/common';

// Weight Range Fields component (inline for consistency with other controllers)
const WeightRangeFields = ({
    minWeight,
    maxWeight,
    onMinWeightChange,
    onMaxWeightChange,
    disabled = false
}) => {
    // Simply use the reusable WeightRangeInput component
    return (
        <WeightRangeInput
            minWeight={minWeight}
            maxWeight={maxWeight}
            onMinWeightChange={onMinWeightChange}
            onMaxWeightChange={onMaxWeightChange}
            disabled={disabled}
            absoluteMin={1}
            absoluteMax={100}
        />
    );
};



const KruskalsController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();



    // Graph parameters
    const [numNodes, setNumNodes] = useState(params?.nodes || 6);
    const [density, setDensity] = useState(params?.density || 0.7);
    const [minWeight, setMinWeight] = useState(params?.minWeight || 1);
    const [maxWeight, setMaxWeight] = useState(params?.maxWeight || 10);
    const [useCustomGraph, setUseCustomGraph] = useState(params?.useCustomGraph || false);
    const [customEdges, setCustomEdges] = useState('');
    const [customEdgesError, setCustomEdgesError] = useState('');

    // Progress is tracked by the step and totalSteps props

    // No need to update progress step as it's handled by the ProgressSection component

    // Parse custom edges from string input
    const parseCustomEdges = useCallback((edgesText) => {
        const parsedEdges = [];
        const edgesRegex = /\[\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\]/g;
        let match;

        while ((match = edgesRegex.exec(edgesText)) !== null) {
            const from = parseInt(match[1], 10);
            const to = parseInt(match[2], 10);
            const weight = parseInt(match[3], 10);
            parsedEdges.push([from, to, weight]);
        }

        return parsedEdges;
    }, []);

    // Import the generateKruskalsSteps function directly
    // This is now used in the resetAndGenerateSteps function

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { numNodes, density, minWeight, maxWeight });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { numNodes, density, minWeight, maxWeight, useCustomGraph });

        // Set steps and movements directly
        try {
            const result = generateKruskalsSteps({
                nodes: numNodes,
                density,
                minWeight,
                maxWeight,
                customEdges: useCustomGraph ? parseCustomEdges(customEdges) : []
            });
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }

        // Force a re-render by setting a timeout
        setTimeout(() => {
            setStep(0);
        }, 0);
    }, [numNodes, density, minWeight, maxWeight, useCustomGraph, customEdges, parseCustomEdges, setState, setStep, setSteps, setTotalSteps, setMovements]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleNumNodesChange = useCallback((value) => {
        if (value >= 3 && value <= 10) {
            console.log('handleNumNodesChange called with value:', value);
            setNumNodes(value);
            onParamChange({ ...params, nodes: value });
            // Use the resetAndGenerateSteps function to properly reset and update
            console.log('Calling resetAndGenerateSteps from handleNumNodesChange');
            resetAndGenerateSteps();
            console.log('After resetAndGenerateSteps from handleNumNodesChange');
        }
    }, [params, onParamChange, resetAndGenerateSteps]);

    const handleDensityChange = useCallback((value) => {
        console.log('handleDensityChange called with value:', value);
        setDensity(value);
        onParamChange({ ...params, density: value });
        // Use the resetAndGenerateSteps function to properly reset and update
        resetAndGenerateSteps();
    }, [params, onParamChange, resetAndGenerateSteps]);

    // Create refs for debounce timers
    const minWeightTimerRef = useRef(null);
    const maxWeightTimerRef = useRef(null);

    // Clean up timers when component unmounts
    useEffect(() => {
        return () => {
            if (minWeightTimerRef.current) clearTimeout(minWeightTimerRef.current);
            if (maxWeightTimerRef.current) clearTimeout(maxWeightTimerRef.current);
        };
    }, []);

    const handleMinWeightChange = useCallback((value) => {
        if (value >= 1 && value < maxWeight) {
            console.log('handleMinWeightChange called with value:', value);
            setMinWeight(value);

            // Clear any existing timer
            if (minWeightTimerRef.current) {
                clearTimeout(minWeightTimerRef.current);
            }

            // Check if we're handling arrow keys
            const isUsingArrowKeys = document.activeElement && document.activeElement.type === 'number';

            // If using arrow keys, debounce the update
            if (isUsingArrowKeys) {
                minWeightTimerRef.current = setTimeout(() => {
                    onParamChange({ ...params, minWeight: value });
                    resetAndGenerateSteps();
                    minWeightTimerRef.current = null;
                }, 500); // 500ms debounce time
            } else {
                // If not using arrow keys, update immediately
                onParamChange({ ...params, minWeight: value });
                resetAndGenerateSteps();
            }
        }
    }, [params, onParamChange, maxWeight, resetAndGenerateSteps]);

    const handleMaxWeightChange = useCallback((value) => {
        if (value > minWeight && value <= 100) {
            console.log('handleMaxWeightChange called with value:', value);
            setMaxWeight(value);

            // Clear any existing timer
            if (maxWeightTimerRef.current) {
                clearTimeout(maxWeightTimerRef.current);
            }

            // Check if we're handling arrow keys
            const isUsingArrowKeys = document.activeElement && document.activeElement.type === 'number';

            // If using arrow keys, debounce the update
            if (isUsingArrowKeys) {
                maxWeightTimerRef.current = setTimeout(() => {
                    onParamChange({ ...params, maxWeight: value });
                    resetAndGenerateSteps();
                    maxWeightTimerRef.current = null;
                }, 500); // 500ms debounce time
            } else {
                // If not using arrow keys, update immediately
                onParamChange({ ...params, maxWeight: value });
                resetAndGenerateSteps();
            }
        }
    }, [params, onParamChange, minWeight, resetAndGenerateSteps]);

    const handleUseCustomGraphChange = useCallback((value) => {
        console.log('handleUseCustomGraphChange called with value:', value);
        setUseCustomGraph(value);
        onParamChange({ ...params, useCustomGraph: value });
        // Use the resetAndGenerateSteps function to properly reset and update
        resetAndGenerateSteps();
    }, [params, onParamChange, resetAndGenerateSteps]);

    // Handle custom edges apply
    const handleApplyCustomEdges = useCallback(() => {
        try {
            const parsedEdges = parseCustomEdges(customEdges);

            // Validate edges
            if (parsedEdges.length === 0) {
                setCustomEdgesError('Please enter at least one edge');
                return;
            }

            // Check for valid node indices
            for (const [from, to] of parsedEdges) {
                if (from < 0 || to < 0) {
                    setCustomEdgesError('Node indices must be non-negative');
                    return;
                }
            }

            // Clear error and update
            setCustomEdgesError('');
            // Use the resetAndGenerateSteps function to properly reset and update
            resetAndGenerateSteps();
        } catch (error) {
            setCustomEdgesError('Invalid edge format');
        }
    }, [customEdges, parseCustomEdges, resetAndGenerateSteps]);



    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        console.log('handleStepForward called, step:', step, 'totalSteps:', totalSteps);
        if (step < totalSteps - 1) {
            console.log('Advancing to next step:', step + 1);
            setState('paused');
            setStep(step + 1);
        } else if (step === totalSteps - 1) {
            console.log('At last step, setting state to completed');
            // If we're at the last step, set state to completed
            setState('completed');
            // Also advance to the next step to ensure we've processed the last step
            setStep(step + 1);
        } else {
            console.log('Cannot advance further, step:', step, 'totalSteps:', totalSteps);
        }
    }, [step, totalSteps, setState, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setState('paused');
            setStep(step - 1);
        }
    }, [step, setState, setStep]);

    // Current line in pseudocode
    const currentLine = steps && steps[step] ? steps[step].pseudocodeLine : 0;
    console.log('Current step:', step, 'Current line:', currentLine, 'Steps:', steps ? steps.length : 0, 'Total steps:', totalSteps);

    // Pseudocode for Kruskal's algorithm
    const pseudocode = [
        { code: "function KruskalMST(graph):", lineNumber: 1, indent: 0 },
        { code: "// Create a forest where each vertex is a separate tree", lineNumber: 2, indent: 1 },
        { code: "forest = DisjointSet(graph.vertices)", lineNumber: 3, indent: 1 },
        { code: "", lineNumber: 4, indent: 1 },
        { code: "// Create a list of all edges in the graph", lineNumber: 5, indent: 1 },
        { code: "edges = []", lineNumber: 6, indent: 1 },
        { code: "for each edge (u, v, weight) in graph:", lineNumber: 7, indent: 1 },
        { code: "edges.add(edge)", lineNumber: 8, indent: 2 },
        { code: "", lineNumber: 9, indent: 1 },
        { code: "// Sort edges by weight in ascending order", lineNumber: 10, indent: 1 },
        { code: "edges.sort(by weight)", lineNumber: 11, indent: 1 },
        { code: "", lineNumber: 12, indent: 1 },
        { code: "// Initialize MST", lineNumber: 13, indent: 1 },
        { code: "mst = []", lineNumber: 14, indent: 1 },
        { code: "", lineNumber: 15, indent: 1 },
        { code: "// Process edges in sorted order", lineNumber: 16, indent: 1 },
        { code: "for each edge (u, v, weight) in edges:", lineNumber: 17, indent: 1 },
        { code: "// Check if adding this edge creates a cycle", lineNumber: 18, indent: 2 },
        { code: "if forest.find(u) != forest.find(v):", lineNumber: 19, indent: 2 },
        { code: "// Add edge to MST", lineNumber: 20, indent: 3 },
        { code: "mst.add(edge)", lineNumber: 21, indent: 3 },
        { code: "// Merge trees", lineNumber: 22, indent: 3 },
        { code: "forest.union(u, v)", lineNumber: 23, indent: 3 },
        { code: "", lineNumber: 24, indent: 1 },
        { code: "return mst", lineNumber: 25, indent: 1 }
    ];

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Kruskal's Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        Kruskal's algorithm finds a minimum spanning tree for a connected weighted graph.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(E log E) where E is the number of edges
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Uses a greedy approach, selecting the edge with the smallest weight
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Uses a disjoint-set data structure to detect cycles
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'numNodes',
                        type: 'slider',
                        label: 'Number of Nodes',
                        min: 3,
                        max: 10,
                        step: 1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: GraphIcon
                    },
                    {
                        name: 'density',
                        type: 'slider',
                        label: 'Edge Density',
                        min: 0.1,
                        max: 1,
                        step: 0.1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: TuneIcon
                    },
                    {
                        name: 'weightRange',
                        type: 'component',
                        label: 'Weight Range',
                        component: WeightRangeFields,
                        componentProps: {
                            minWeight: minWeight,
                            maxWeight: maxWeight,
                            onMinWeightChange: handleMinWeightChange,
                            onMaxWeightChange: handleMaxWeightChange,
                            // Add a key to force re-render when values change
                            key: `${minWeight}-${maxWeight}`
                        },
                        disableWhen: 'useCustomGraph'
                    },
                    {
                        name: 'useCustomGraph',
                        type: 'switch',
                        label: 'Use Custom Graph',
                        icon: ShuffleIcon
                    },
                    {
                        name: 'customEdges',
                        type: 'customArray',
                        label: 'Custom Edges',
                        placeholder: 'Format: [from, to, weight], [from, to, weight], ...',
                        helperText: 'Example: [0, 1, 5], [1, 2, 3], [0, 2, 7]',
                        error: customEdgesError,
                        showOnlyWhen: 'useCustomGraph',
                        onApply: handleApplyCustomEdges,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    numNodes,
                    density,
                    useCustomGraph,
                    customEdges
                }}
                onChange={(newValues) => {
                    if (newValues.numNodes !== undefined && newValues.numNodes !== numNodes) {
                        // For slider, the value is already a number
                        handleNumNodesChange(newValues.numNodes);
                    }
                    if (newValues.density !== undefined && newValues.density !== density) {
                        handleDensityChange(newValues.density);
                    }
                    if (newValues.useCustomGraph !== undefined && newValues.useCustomGraph !== useCustomGraph) {
                        handleUseCustomGraphChange(newValues.useCustomGraph);
                    }
                    if (newValues.customEdges !== undefined) {
                        setCustomEdges(newValues.customEdges);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default KruskalsController;
