// CountingSortDetailedSteps.js
// Generates detailed steps for CountingSort algorithm with proper visualizationData structure

/**
 * Generates detailed steps for the CountingSort algorithm
 * @param {Array} inputArray - The array to sort
 * @returns {Array} - Array of detailed steps with visualizationData
 */
export const generateCountingSortDetailedSteps = (inputArray) => {
  const steps = [];
  const arr = [...inputArray];
  const n = arr.length;

  // Helper function to create a step with proper structure
  const createStep = (type, statement, visualizationData) => ({
    type,
    statement,
    visualizationData
  });

  // Step 0: Initial state
  steps.push(createStep(
    'init',
    `Counting Sort: Initial Array [${arr.join(', ')}]`,
    {
      mainArray: {
        values: [...arr],
        highlightedIndices: [],
        comparingIndices: [],
        sortedIndices: [],
      }
    }
  ));

  // Step 1: Find maximum element
  const max = Math.max(...arr);
  steps.push(createStep(
    'findMax',
    `Find maximum element: ${max}`,
    {
      mainArray: {
        values: [...arr],
        highlightedIndices: [],
        comparingIndices: [],
        sortedIndices: [],
      },
      maxValue: max
    }
  ));

  // Step 2: Initialize count array
  const count = new Array(max + 1).fill(0);
  steps.push(createStep(
    'initCount',
    `Initialize count array of size ${max + 1} with zeros`,
    {
      mainArray: {
        values: [...arr],
        highlightedIndices: [],
        comparingIndices: [],
        sortedIndices: [],
      },
      countArray: {
        values: [...count],
        highlightedIndices: [],
      },
      maxValue: max
    }
  ));

  // Step 3-4: Count occurrences of each element
  for (let i = 0; i < n; i++) {
    const element = arr[i];

    // Before incrementing count
    steps.push(createStep(
      'counting',
      `Count element ${element} at index ${i}`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [element],
        },
        currentElement: element,
        currentIndex: i
      }
    ));

    // Increment count
    count[element]++;

    // After incrementing count
    steps.push(createStep(
      'incrementCount',
      `Incremented count[${element}] to ${count[element]}`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [element],
        },
        currentElement: element,
        currentIndex: i
      }
    ));
  }

  // Step 5-6: Calculate cumulative positions
  for (let i = 1; i <= max; i++) {
    // Before updating position
    steps.push(createStep(
      'calculatePosition',
      `Calculate cumulative position for ${i}: count[${i}] + count[${i-1}] = ${count[i]} + ${count[i-1]}`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [i, i-1],
        },
        currentIndex: i
      }
    ));

    // Update position
    count[i] += count[i - 1];

    // After updating position
    steps.push(createStep(
      'updatePosition',
      `Updated count[${i}] to ${count[i]} (cumulative position)`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [i],
        },
        currentIndex: i
      }
    ));
  }

  // Step 7: Initialize output array
  const output = new Array(n).fill(null);
  steps.push(createStep(
    'initOutput',
    `Initialize output array of size ${n}`,
    {
      mainArray: {
        values: [...arr],
        highlightedIndices: [],
        comparingIndices: [],
        sortedIndices: [],
      },
      countArray: {
        values: [...count],
        highlightedIndices: [],
      },
      outputArray: {
        values: [...output],
        highlightedIndices: [],
      }
    }
  ));

  // Step 8-9: Place elements in output array (from right to left for stability)
  for (let i = n - 1; i >= 0; i--) {
    const element = arr[i];
    const targetIndex = count[element] - 1;

    // Before placing element
    steps.push(createStep(
      'placing',
      `Place element ${element} from index ${i} to position ${targetIndex} in output array`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [element],
        },
        outputArray: {
          values: [...output],
          highlightedIndices: [targetIndex],
        },
        currentElement: element,
        currentIndex: i,
        targetIndex: targetIndex
      }
    ));

    // Place element
    output[targetIndex] = element;
    count[element]--;

    // After placing element
    steps.push(createStep(
      'placed',
      `Placed element ${element} at position ${targetIndex}, decremented count[${element}] to ${count[element]}`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: [],
        },
        countArray: {
          values: [...count],
          highlightedIndices: [element],
        },
        outputArray: {
          values: [...output],
          highlightedIndices: [targetIndex],
        },
        currentElement: element,
        currentIndex: i,
        targetIndex: targetIndex
      }
    ));
  }

  // Step 10-11: Copy back to original array
  for (let i = 0; i < n; i++) {
    // Before copying
    steps.push(createStep(
      'copying',
      `Copy element ${output[i]} from output[${i}] to arr[${i}]`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: [],
        },
        outputArray: {
          values: [...output],
          highlightedIndices: [i],
        },
        currentIndex: i
      }
    ));

    // Copy element
    arr[i] = output[i];

    // After copying
    steps.push(createStep(
      'copied',
      `Copied element ${output[i]} to position ${i}`,
      {
        mainArray: {
          values: [...arr],
          highlightedIndices: [i],
          comparingIndices: [],
          sortedIndices: Array.from({ length: i + 1 }, (_, idx) => idx),
        },
        outputArray: {
          values: [...output],
          highlightedIndices: [i],
        },
        currentIndex: i
      }
    ));
  }

  // Final step: Complete
  steps.push(createStep(
    'complete',
    'Counting Sort completed successfully',
    {
      mainArray: {
        values: [...arr],
        highlightedIndices: [],
        comparingIndices: [],
        sortedIndices: Array.from({ length: n }, (_, i) => i),
      },
      outputArray: {
        values: [...output],
        highlightedIndices: [],
      }
    }
  ));

  return steps;
};
