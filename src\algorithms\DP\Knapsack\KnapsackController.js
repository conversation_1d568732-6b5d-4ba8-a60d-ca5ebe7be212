// KnapsackController.js
// This component provides the controls for 0/1 Knapsack algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, TextField, Grid, Paper, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import WorkIcon from '@mui/icons-material/Work';
import SettingsIcon from '@mui/icons-material/Settings';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// Import algorithm functions
import KnapsackAlgorithm from './KnapsackAlgorithm';

const KnapsackController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [capacity, setCapacity] = useState(params?.capacity || 8);
    const [items, setItems] = useState(params?.items || [
        { weight: 2, value: 3 },
        { weight: 3, value: 4 },
        { weight: 4, value: 5 },
        { weight: 5, value: 6 }
    ]);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { capacity, items });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { capacity, items });

        // Update params first
        onParamChange({
            capacity,
            weights: items.map(item => item.weight),
            values: items.map(item => item.value)
        });

        // Set steps and movements directly
        try {
            const weights = items.map(item => item.weight);
            const values = items.map(item => item.value);

            const result = KnapsackAlgorithm.generateKnapsackSteps({
                weights,
                values,
                capacity
            });

            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [capacity, items, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleCapacityChange = useCallback((value) => {
        if (value >= 1 && value <= 20) {
            console.log('handleCapacityChange called with value:', value);
            setCapacity(value);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    // Handle item weight change
    const handleWeightChange = useCallback((index, value) => {
        const newItems = [...items];
        newItems[index].weight = parseInt(value, 10) || 0;
        setItems(newItems);
        resetAndGenerateSteps();
    }, [items, resetAndGenerateSteps]);

    // Handle item value change
    const handleValueChange = useCallback((index, value) => {
        const newItems = [...items];
        newItems[index].value = parseInt(value, 10) || 0;
        setItems(newItems);
        resetAndGenerateSteps();
    }, [items, resetAndGenerateSteps]);

    // Add a new item
    const addItem = useCallback(() => {
        if (items.length < 10) { // Limit to 10 items for visualization clarity
            const newItems = [...items, { weight: 1, value: 1 }];
            setItems(newItems);
            resetAndGenerateSteps();
        }
    }, [items, resetAndGenerateSteps]);

    // Remove an item
    const removeItem = useCallback((index) => {
        if (items.length > 1) { // Keep at least one item
            const newItems = [...items];
            newItems.splice(index, 1);
            setItems(newItems);
            resetAndGenerateSteps();
        }
    }, [items, resetAndGenerateSteps]);

    // Custom component for items management
    const ItemsManager = ({ disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>Items (Weight/Value)</Typography>
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                    <Grid container spacing={2}>
                        {items.map((item, index) => (
                            <Grid key={index} style={{ width: '100%' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                    <Typography variant="body2" sx={{ minWidth: 60 }}>
                                        Item {index + 1}:
                                    </Typography>
                                    <TextField
                                        label="Weight"
                                        type="number"
                                        size="small"
                                        value={item.weight}
                                        onChange={(e) => handleWeightChange(index, e.target.value)}
                                        slotProps={{ input: { min: 1, max: 20 } }}
                                        sx={{ width: 100 }}
                                        disabled={disabled}
                                    />
                                    <TextField
                                        label="Value"
                                        type="number"
                                        size="small"
                                        value={item.value}
                                        onChange={(e) => handleValueChange(index, e.target.value)}
                                        slotProps={{ input: { min: 1, max: 100 } }}
                                        sx={{ width: 100 }}
                                        disabled={disabled}
                                    />
                                    <IconButton
                                        size="small"
                                        onClick={() => removeItem(index)}
                                        disabled={items.length <= 1 || disabled}
                                    >
                                        <RemoveIcon />
                                    </IconButton>
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                        <Button
                            startIcon={<AddIcon />}
                            onClick={addItem}
                            disabled={items.length >= 10 || disabled}
                            variant="outlined"
                            size="small"
                        >
                            Add Item
                        </Button>
                    </Box>
                </Paper>
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for 0/1 Knapsack algorithm
    const pseudocode = [
        { code: "function KnapsackDP(weights, values, capacity):", lineNumber: 1, indent: 0 },
        { code: "  n = length(weights)", lineNumber: 2, indent: 1 },
        { code: "  // Create a table for DP", lineNumber: 3, indent: 1 },
        { code: "  dp[0...n][0...capacity] = 0", lineNumber: 4, indent: 1 },
        { code: "", lineNumber: 5, indent: 0 },
        { code: "  for i = 1 to n:", lineNumber: 6, indent: 1 },
        { code: "    for w = 0 to capacity:", lineNumber: 7, indent: 2 },
        { code: "      if weights[i-1] <= w:", lineNumber: 8, indent: 3 },
        { code: "        dp[i][w] = max(values[i-1] + dp[i-1][w-weights[i-1]], dp[i-1][w])", lineNumber: 9, indent: 4 },
        { code: "      else:", lineNumber: 10, indent: 3 },
        { code: "        dp[i][w] = dp[i-1][w]", lineNumber: 11, indent: 4 },
        { code: "", lineNumber: 12, indent: 0 },
        { code: "  // Traceback to find selected items", lineNumber: 13, indent: 1 },
        { code: "  selected = []", lineNumber: 14, indent: 1 },
        { code: "  w = capacity", lineNumber: 15, indent: 1 },
        { code: "  for i = n downto 1:", lineNumber: 16, indent: 1 },
        { code: "    if dp[i][w] != dp[i-1][w]:", lineNumber: 17, indent: 2 },
        { code: "      selected.add(i-1)", lineNumber: 18, indent: 3 },
        { code: "      w = w - weights[i-1]", lineNumber: 19, indent: 3 },
        { code: "    // else: item i was not included", lineNumber: 20, indent: 2 },
        { code: "", lineNumber: 21, indent: 0 },
        { code: "  return dp[n][capacity], selected", lineNumber: 22, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="0/1 Knapsack Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The 0/1 Knapsack problem is a classic optimization problem where we need to select items to maximize value while staying within a weight capacity constraint. Each item can either be selected (1) or not (0).
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(n*W) where n is the number of items and W is the capacity
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(n*W) for the dynamic programming table
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to build a table of optimal solutions for subproblems
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • After filling the table, we trace back to find which items were selected
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'capacity',
                        type: 'slider',
                        label: 'Knapsack Capacity',
                        min: 1,
                        max: 20,
                        step: 1,
                        marks: true,
                        icon: WorkIcon
                    },
                    {
                        name: 'items',
                        type: 'component',
                        label: 'Items',
                        component: ItemsManager,
                        icon: SettingsIcon
                    }
                ]}
                values={{
                    capacity,
                    items
                }}
                onChange={(newValues) => {
                    if (newValues.capacity !== undefined && newValues.capacity !== capacity) {
                        handleCapacityChange(newValues.capacity);
                    }
                    // Items are handled by the ItemsManager component
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default KnapsackController;
