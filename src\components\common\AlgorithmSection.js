// AlgorithmSection.js
// A reusable algorithm section component with consistent styling

import React, { useState, useEffect } from 'react';
import { Box, Typography, Stack, Paper, Collapse, useTheme } from '@mui/material';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import PropTypes from 'prop-types';

/**
 * A reusable algorithm section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The content to display in the algorithm section
 * @param {string} props.title - Optional custom title for the section (defaults to "Algorithm")
 * @param {boolean} props.defaultExpanded - Whether the section is expanded by default
 * @param {Array} props.algorithm - Array of algorithm steps to display
 * @param {number} props.currentStep - Current step index
 * @param {function} props.renderStep - Optional custom render function for each step
 */
const AlgorithmSection = ({
  title = 'Algorithm',
  defaultExpanded = true,
  algorithm = [],
  currentStep = 0,
  renderStep,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(defaultExpanded);

  // Keep track of the highest step reached
  const [highestStep, setHighestStep] = useState(currentStep || 0);

  // Update highest step when currentStep changes
  useEffect(() => {
    // Reset highlighting when currentStep is 0 (algorithm reset)
    if (currentStep === 0) {
      setHighestStep(0);
    }
    // Update highest step when currentStep increases
    else if (currentStep !== undefined && currentStep > highestStep) {
      setHighestStep(currentStep);
    }
  }, [currentStep, highestStep]);

  // Default step renderer
  const defaultRenderStep = (step, index) => {
    // Check if step has a valid line number
    if (!step || step.lineNumber === undefined) {
      return null;
    }

    // Check if the step is active or current
    // Use highestStep to maintain highlighting even after algorithm completes
    const isActive = step.lineNumber <= highestStep;
    const isCurrentLine = currentStep !== undefined && step.lineNumber === currentStep;

    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {isCurrentLine && (
          <ArrowRightIcon
            sx={{
              color: theme.palette.success.main,
              mr: -1,
              fontSize: '1.2rem',
              animation: 'pulse 1.5s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 0.5 },
                '50%': { opacity: 1 },
                '100%': { opacity: 0.5 },
              },
            }}
          />
        )}
        <Typography
          variant="body2"
          sx={{
            fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
            fontSize: '0.85rem',
            color: isActive ? (isCurrentLine ? theme.palette.success.main : theme.palette.primary.main) : theme.palette.text.primary,
            fontWeight: isActive ? 'bold' : 'normal',
            pl: step.indent ? step.indent * 2 : 0,
            whiteSpace: 'nowrap',
          }}
        >
          {step.code}
        </Typography>
      </Box>
    );
  };

  // Use custom renderer if provided, otherwise use default
  const stepRenderer = renderStep || defaultRenderStep;

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <AccountTreeIcon fontSize="small" sx={{ mr: 0.5 }} /> {title}
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{
          py: 1.5,
          pl: 1.5,
          pr: 0, // Remove right padding
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          borderRadius: 1,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
        }}>
          <Box
            sx={{
              // No maxHeight to allow content to determine height
              overflowY: 'visible', // Don't need vertical scrolling
              pr: 0, // Remove right padding
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              },
            }}
          >
            <Box
              sx={{
                overflowX: 'auto',
                whiteSpace: 'nowrap',
                '&::-webkit-scrollbar': {
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                  borderRadius: '4px',
                },
              }}
            >
              <Stack spacing={0.5}>
                {algorithm.map((step, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      py: 0.25,
                    }}
                  >
                    {stepRenderer(step, index)}
                  </Box>
                ))}
              </Stack>
            </Box>
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

AlgorithmSection.propTypes = {
  title: PropTypes.string,
  defaultExpanded: PropTypes.bool,
  algorithm: PropTypes.arrayOf(
    PropTypes.shape({
      code: PropTypes.string.isRequired,
      lineNumber: PropTypes.number,
      indent: PropTypes.number,
    })
  ),
  currentStep: PropTypes.number,
  renderStep: PropTypes.func,
};

export default AlgorithmSection;
