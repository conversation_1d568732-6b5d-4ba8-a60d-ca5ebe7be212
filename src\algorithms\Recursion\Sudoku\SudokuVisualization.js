// SudokuVisualization.js
// 3D visualization component for the Sudoku solver algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const CELL_SIZE = 1.0;
const BOARD_ELEVATION = 0.2;
const BOARD_GAP = 0.05;
const CELL_PADDING = 0.1;

const SudokuVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Log theme information for debugging
  useEffect(() => {
    console.log('Theme:', theme);
    console.log('Is Dark Mode:', isDark);
  }, [theme, isDark]);

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 15, 15);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();

  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);

      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty board to show when no steps are available
      setCurrentStepData({
        board: Array(9).fill().map(() => Array(9).fill(0)),
        currentRow: -1,
        currentCol: -1,
        currentValue: null,
        isValid: true
      });
    }

    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Track the last time we updated the step
  const lastUpdateTimeRef = useRef(0);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;

      // Get current time
      const currentTime = clock.getElapsedTime();

      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);

        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Board base and frame colors
    boardBase: isDark ? '#1e272e' : '#f5f6fa',  // Darker in dark mode, lighter in light mode
    boardFrame: isDark ? '#2d3436' : '#dfe6e9',  // Slightly darker than base

    // Cell colors
    cellLight: isDark ? '#2d3436' : '#ffffff',  // Main cell color
    cellDark: isDark ? '#222f3e' : '#f1f2f6',   // Alternate cell color

    // Highlight colors
    cellHighlight: isDark ? '#0984e3' : '#74b9ff',  // Blue highlight
    cellActive: isDark ? '#d63031' : '#ff7675',     // Red for active cells
    cellInvalid: isDark ? '#e84118' : '#ff6b6b',    // Red for invalid placements
    cellValid: isDark ? '#00b894' : '#55efc4',      // Green for valid placements

    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',       // Dark text
    textLight: isDark ? '#dfe6e9' : '#636e72',      // Light text
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.cellLight, label: 'Empty Cell' },
    { color: colors.cellHighlight, label: 'Current Cell' },
    { color: colors.cellActive, label: 'Active Cell' },
    { color: colors.cellInvalid, label: 'Invalid Placement' },
    { color: colors.cellValid, label: 'Valid Placement' },
  ], [colors]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, 0, 0];

  // Render the Sudoku visualization
  const renderSudokuVisualization = () => {
    if (!currentStepData) return null;

    const { board, currentRow, currentCol, currentValue, isValid } = currentStepData;

    // Calculate board size
    const boardSize = board.length;

    // Calculate offset to center the board
    const offset = -(boardSize * CELL_SIZE) / 2 + CELL_SIZE / 2;

    // Calculate board dimensions for the frame
    const boardWidth = boardSize * CELL_SIZE;
    const frameThickness = 0.3;
    const frameHeight = 0.4;

    return (
      <group position={position} rotation={rotation}>
        {/* Board base */}
        <mesh
          position={[0, -BOARD_ELEVATION/2, 0]}
          receiveShadow
        >
          <boxGeometry args={[boardWidth + frameThickness*2, BOARD_ELEVATION, boardWidth + frameThickness*2]} />
          <meshStandardMaterial
            color={colors.boardBase}
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Board frame */}
        <group>
          {/* Bottom frame */}
          <mesh
            castShadow
            receiveShadow
            position={[0, -BOARD_ELEVATION/2 - frameHeight/2, 0]}
          >
            <boxGeometry args={[boardWidth + frameThickness*2, frameHeight, boardWidth + frameThickness*2]} />
            <meshStandardMaterial
              color={colors.boardFrame}
              roughness={0.8}
              metalness={0.2}
            />
          </mesh>

          {/* Frame sides */}
          {[[-1, 0], [1, 0], [0, -1], [0, 1]].map((dir, i) => {
            const isXDir = dir[0] !== 0;
            const size = isXDir ?
              [frameThickness, BOARD_ELEVATION + 0.05, boardWidth + frameThickness*2] :
              [boardWidth + frameThickness*2, BOARD_ELEVATION + 0.05, frameThickness];

            return (
              <mesh
                key={`frame-${i}`}
                castShadow
                receiveShadow
                position={[
                  dir[0] * (boardWidth/2 + frameThickness/2),
                  0,
                  dir[1] * (boardWidth/2 + frameThickness/2)
                ]}
              >
                <boxGeometry args={size} />
                <meshStandardMaterial
                  color={colors.boardFrame}
                  roughness={0.8}
                  metalness={0.2}
                />
              </mesh>
            );
          })}
        </group>

        {/* Sudoku grid */}
        <group position={[0, 0.01, 0]}>
          {/* Grid cells */}
          {board.map((row, rowIndex) => (
            row.map((cell, colIndex) => {
              const x = offset + colIndex * CELL_SIZE;
              const z = offset + rowIndex * CELL_SIZE;

              // Determine cell color based on state
              const isCurrentCell = rowIndex === currentRow && colIndex === currentCol;
              const isActive = isCurrentCell && currentValue !== null;

              // Calculate box row and column for 3x3 boxes
              const boxRow = Math.floor(rowIndex / 3);
              const boxCol = Math.floor(colIndex / 3);

              let cellColor;
              if (isActive) {
                cellColor = isValid ? colors.cellValid : colors.cellInvalid;
              } else if (isCurrentCell) {
                cellColor = colors.cellHighlight;
              } else {
                // Alternate colors for 3x3 boxes
                cellColor = (boxRow + boxCol) % 2 === 0 ? colors.cellLight : colors.cellDark;
              }

              return (
                <group key={`cell-${rowIndex}-${colIndex}`} position={[x, 0, z]}>
                  {/* Cell background */}
                  <mesh
                    receiveShadow
                    position={[0, 0, 0]}
                  >
                    <boxGeometry args={[CELL_SIZE - BOARD_GAP, 0.05, CELL_SIZE - BOARD_GAP]} />
                    <meshStandardMaterial color={cellColor} />
                  </mesh>

                  {/* Cell value */}
                  {cell !== 0 && (
                    <Html
                      position={[0, 0.1, 0]}
                      center
                      occlude
                    >
                      <div style={{
                        color: isActive ? 'white' : (boxRow + boxCol) % 2 === 0 ? colors.textLight : colors.textDark,
                        fontSize: '24px',
                        fontWeight: 'bold',
                        width: '40px',
                        height: '40px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}>
                        {cell}
                      </div>
                    </Html>
                  )}
                </group>
              );
            })
          ))}

          {/* Grid lines */}
          {/* Horizontal lines */}
          {Array.from({ length: boardSize + 1 }).map((_, i) => {
            const lineWidth = i % 3 === 0 ? 0.08 : 0.03;
            const linePos = offset - CELL_SIZE / 2 + i * CELL_SIZE;

            return (
              <mesh
                key={`h-line-${i}`}
                position={[0, 0.06, linePos]}
              >
                <boxGeometry args={[boardWidth + 0.1, 0.02, lineWidth]} />
                <meshStandardMaterial color={colors.boardFrame} />
              </mesh>
            );
          })}

          {/* Vertical lines */}
          {Array.from({ length: boardSize + 1 }).map((_, i) => {
            const lineWidth = i % 3 === 0 ? 0.08 : 0.03;
            const linePos = offset - CELL_SIZE / 2 + i * CELL_SIZE;

            return (
              <mesh
                key={`v-line-${i}`}
                position={[linePos, 0.06, 0]}
              >
                <boxGeometry args={[lineWidth, 0.02, boardWidth + 0.1]} />
                <meshStandardMaterial color={colors.boardFrame} />
              </mesh>
            );
          })}
        </group>
      </group>
    );
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Sudoku Solver Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position} rotation={rotation}>
          {renderSudokuVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default SudokuVisualization;
