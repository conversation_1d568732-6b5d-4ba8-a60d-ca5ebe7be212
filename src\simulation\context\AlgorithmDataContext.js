// AlgorithmDataContext.js
// This context provides state management for algorithm-specific data

import React, { createContext, useState, useContext, useCallback, useMemo } from 'react';

// Create the context
const AlgorithmDataContext = createContext();

/**
 * AlgorithmDataProvider - Provides state management for algorithm data
 * 
 * This context manages:
 * - Algorithm input data
 * - Algorithm output data
 * - Algorithm intermediate state
 * - Algorithm configuration
 */
export const AlgorithmDataProvider = ({ children }) => {
  // Algorithm data
  const [inputData, setInputData] = useState(null);
  const [outputData, setOutputData] = useState(null);
  const [intermediateState, setIntermediateState] = useState({});
  const [algorithmConfig, setAlgorithmConfig] = useState({});

  // Update input data
  const updateInputData = useCallback((data) => {
    setInputData(data);
  }, []);

  // Update output data
  const updateOutputData = useCallback((data) => {
    setOutputData(data);
  }, []);

  // Update intermediate state
  const updateIntermediateState = useCallback((state) => {
    setIntermediateState(prevState => ({
      ...prevState,
      ...state
    }));
  }, []);

  // Get intermediate state value
  const getIntermediateState = useCallback((key, defaultValue = null) => {
    return intermediateState[key] !== undefined ? intermediateState[key] : defaultValue;
  }, [intermediateState]);

  // Reset intermediate state
  const resetIntermediateState = useCallback(() => {
    setIntermediateState({});
  }, []);

  // Update algorithm configuration
  const updateAlgorithmConfig = useCallback((config) => {
    setAlgorithmConfig(prevConfig => ({
      ...prevConfig,
      ...config
    }));
  }, []);

  // Reset algorithm data
  const resetAlgorithmData = useCallback(() => {
    setInputData(null);
    setOutputData(null);
    setIntermediateState({});
  }, []);

  // Create the value object
  const value = useMemo(() => ({
    // Algorithm data
    inputData,
    outputData,
    intermediateState,
    algorithmConfig,

    // Data management functions
    updateInputData,
    updateOutputData,
    updateIntermediateState,
    getIntermediateState,
    resetIntermediateState,
    updateAlgorithmConfig,
    resetAlgorithmData
  }), [
    inputData, outputData, intermediateState, algorithmConfig,
    updateInputData, updateOutputData, 
    updateIntermediateState, getIntermediateState, resetIntermediateState,
    updateAlgorithmConfig, resetAlgorithmData
  ]);

  return (
    <AlgorithmDataContext.Provider value={value}>
      {children}
    </AlgorithmDataContext.Provider>
  );
};

/**
 * useAlgorithmData - Custom hook to use the algorithm data context
 * 
 * @returns {Object} The algorithm data context value
 */
export const useAlgorithmData = () => {
  const context = useContext(AlgorithmDataContext);
  if (context === undefined) {
    throw new Error('useAlgorithmData must be used within an AlgorithmDataProvider');
  }
  return context;
};
