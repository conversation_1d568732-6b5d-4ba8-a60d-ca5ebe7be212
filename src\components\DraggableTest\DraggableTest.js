// DraggableTest.js
// A simple test component for draggable nodes in 3D space with direct dragging

import React, { useState, useRef, useEffect } from 'react';
import { Canvas, useThree, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

// Simple draggable sphere component with direct dragging
const DraggableSphere = ({ position, color, index, onDrag }) => {
  const meshRef = useRef();
  const { camera, gl, raycaster, mouse, scene } = useThree();
  const [isDragging, setIsDragging] = useState(false);
  const [dragPlane, setDragPlane] = useState(null);
  const [dragOffset, setDragOffset] = useState(new THREE.Vector3());

  // Create a drag plane
  useEffect(() => {
    // Create an invisible plane for dragging
    const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
    setDragPlane(plane);
  }, []);

  // Handle pointer down
  const handlePointerDown = (e) => {
    e.stopPropagation();

    // Disable orbit controls during drag
    const controls = gl.domElement.__r3f?.controls;
    if (controls) {
      controls.enabled = false;
    }

    // Set dragging state
    setIsDragging(true);

    // Update drag plane to face the camera
    if (dragPlane) {
      dragPlane.normal.copy(camera.position).normalize();
      dragPlane.constant = -meshRef.current.position.clone().dot(dragPlane.normal);
    }

    // Calculate drag offset
    const intersection = new THREE.Vector3();
    raycaster.ray.intersectPlane(dragPlane, intersection);
    setDragOffset(intersection.sub(meshRef.current.position));

    // Capture pointer
    gl.domElement.setPointerCapture(e.pointerId);
  };

  // Handle pointer up
  const handlePointerUp = (e) => {
    // Release pointer
    gl.domElement.releasePointerCapture(e.pointerId);

    // Reset dragging state
    setIsDragging(false);

    // Re-enable orbit controls
    const controls = gl.domElement.__r3f?.controls;
    if (controls) {
      controls.enabled = true;
    }
  };

  // Handle dragging
  useFrame(() => {
    if (isDragging && dragPlane && meshRef.current) {
      try {
        // Update raycaster with current mouse position
        raycaster.setFromCamera(mouse, camera);

        // Calculate intersection with drag plane
        const intersection = new THREE.Vector3();
        const didIntersect = raycaster.ray.intersectPlane(dragPlane, intersection);

        if (didIntersect) {
          // Update position
          const newPosition = intersection.sub(dragOffset);
          meshRef.current.position.copy(newPosition);

          // Call drag callback
          if (onDrag) {
            onDrag(index, [newPosition.x, newPosition.y, newPosition.z]);
          }
        }
      } catch (error) {
        console.error('Error during drag:', error);
      }
    }
  });

  return (
    <mesh
      ref={meshRef}
      position={position}
      onPointerDown={handlePointerDown}
      onPointerUp={handlePointerUp}
      onPointerLeave={handlePointerUp}
    >
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
};

// Line component to connect spheres
const Line = ({ start, end }) => {
  const points = [
    new THREE.Vector3(...start),
    new THREE.Vector3(...end)
  ];

  const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);

  return (
    <line geometry={lineGeometry}>
      <lineBasicMaterial color="white" linewidth={2} />
    </line>
  );
};

// Scene setup component
const Scene = () => {
  // Define initial positions for spheres
  const initialPositions = [
    [-4, 0, 0],
    [0, 4, 0],
    [4, 0, 0],
    [0, -4, 0],
    [0, 0, 4]
  ];

  // State to track sphere positions
  const [positions, setPositions] = useState(initialPositions);

  // Colors for spheres
  const colors = ['#f44336', '#2196f3', '#4caf50', '#ff9800', '#9c27b0'];

  // Handle sphere drag
  const handleSphereDrag = (index, newPosition) => {
    setPositions(prevPositions => {
      const newPositions = [...prevPositions];
      newPositions[index] = newPosition;
      return newPositions;
    });
  };

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 10]} intensity={0.8} />

      {/* Orbit controls */}
      <OrbitControls
        makeDefault
        minDistance={5}
        maxDistance={50}
      />

      {/* Draggable spheres */}
      {positions.map((position, index) => (
        <DraggableSphere
          key={index}
          position={position}
          color={colors[index % colors.length]}
          index={index}
          onDrag={handleSphereDrag}
        />
      ))}

      {/* Lines connecting spheres */}
      {positions.map((position, index) => (
        <Line
          key={index}
          start={position}
          end={positions[(index + 1) % positions.length]}
        />
      ))}

      {/* Grid helper */}
      <gridHelper args={[20, 20, '#444444', '#222222']} />
    </>
  );
};

// Main component
const DraggableTest = () => {
  console.log('Rendering DraggableTest component');

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Canvas camera={{ position: [0, 0, 15], fov: 60 }} style={{ width: '100%', height: '100%' }}>
        <Scene />
      </Canvas>
    </div>
  );
};

export default DraggableTest;
