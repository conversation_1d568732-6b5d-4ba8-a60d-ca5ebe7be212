// OverlayLabels.js
// A component to render labels as an overlay on top of the 3D scene

import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import './OverlayLabels.css';

// Component to render labels as an overlay
const OverlayLabels = ({ canvasRef, labels }) => {
  const [containerElement, setContainerElement] = useState(null);
  
  // Create container element on mount
  useEffect(() => {
    // Find the canvas parent element
    if (!canvasRef.current) return;
    
    const parent = canvasRef.current.parentElement;
    if (!parent) return;
    
    // Create container for labels if it doesn't exist
    let container = parent.querySelector('.overlay-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'overlay-container';
      parent.appendChild(container);
    }
    
    setContainerElement(container);
    
    // Clean up on unmount
    return () => {
      if (container && container.parentElement) {
        container.parentElement.removeChild(container);
      }
    };
  }, [canvasRef]);
  
  // If no container or no labels, don't render anything
  if (!containerElement || !labels || labels.length === 0) return null;
  
  // Render labels into the container
  return ReactDOM.createPortal(
    <>
      {labels.map(label => (
        <div
          key={label.id}
          className={`overlay-label ${label.type}-label`}
          style={{
            left: `${label.x}px`,
            top: `${label.y}px`,
          }}
        >
          {label.content}
        </div>
      ))}
    </>,
    containerElement
  );
};

export default OverlayLabels;
