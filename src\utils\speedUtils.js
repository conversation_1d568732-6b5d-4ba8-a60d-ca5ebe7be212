// speedUtils.js
// Shared utility functions for speed and delay calculations across algorithm visualizations

/**
 * Enhanced speed and delay calculation with exponential curve
 * Provides a more intuitive feel where:
 * - Speed 1 (slowest): 3000ms delay - very slow for educational purposes
 * - Speed 5 (medium): ~750ms delay - good for understanding
 * - Speed 10 (fastest): 100ms delay - quick demonstration
 * 
 * @param {number} speed - Speed value from 1 (slowest) to 10 (fastest)
 * @returns {number} Calculated delay in milliseconds
 */
export const getEnhancedDelay = (speed) => {
  // Enhanced delay calculation with more granular control
  const minDelay = 100;   // Minimum delay
  const maxDelay = 3000;  // Maximum delay
  
  // Ensure speed is within valid range
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  
  // Calculate delay with exponential curve for better feel
  const normalizedSpeed = (validSpeed - 1) / 9; // Normalize to 0-1
  const exponentialFactor = Math.pow(1 - normalizedSpeed, 2); // Exponential curve
  const calculatedDelay = minDelay + (maxDelay - minDelay) * exponentialFactor;
  
  return Math.round(calculatedDelay);
};

/**
 * Enhanced animation duration calculation with exponential curve
 * Similar to delay but typically shorter for smooth animations
 * 
 * @param {number} speed - Speed value from 1 (slowest) to 10 (fastest)
 * @returns {number} Calculated animation duration in milliseconds
 */
export const getEnhancedAnimationDuration = (speed) => {
  // Enhanced animation duration calculation
  const minDuration = 80;   // Minimum animation duration
  const maxDuration = 2000; // Maximum animation duration
  
  // Ensure speed is within valid range
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  
  // Calculate duration with exponential curve for better feel
  const normalizedSpeed = (validSpeed - 1) / 9; // Normalize to 0-1
  const exponentialFactor = Math.pow(1 - normalizedSpeed, 2); // Exponential curve
  const calculatedDuration = minDuration + (maxDuration - minDuration) * exponentialFactor;
  
  return Math.round(calculatedDuration);
};

/**
 * Legacy delay calculation for backward compatibility
 * @deprecated Use getEnhancedDelay instead
 */
export const getDelay = (speed) => {
  return getEnhancedDelay(speed);
};

/**
 * Legacy animation duration calculation for backward compatibility
 * @deprecated Use getEnhancedAnimationDuration instead
 */
export const getAnimationDuration = (speed) => {
  return getEnhancedAnimationDuration(speed);
};

export default {
  getEnhancedDelay,
  getEnhancedAnimationDuration,
  getDelay,
  getAnimationDuration
};
