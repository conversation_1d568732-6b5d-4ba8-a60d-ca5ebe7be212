# [CATEGORY_NAME] Algorithms

This directory contains implementations of various [CATEGORY_NAME_LOWERCASE] algorithms.

## Implemented Algorithms

- [Algorithm 1] - Brief description
- [Algorithm 2] - Brief description
- ...

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `NewAlgorithm`)
2. Implement the required files:
   - `NewAlgorithmAlgorithm.js` - Core algorithm logic
   - `NewAlgorithmVisualization.js` - Visualization component
   - `NewAlgorithmController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file

## Implementation Guidelines

Follow the project's standard implementation patterns for consistency.
