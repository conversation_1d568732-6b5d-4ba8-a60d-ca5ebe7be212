/* GraphLabelsOverlay.css */
/* Styles for the graph labels overlay */

.graph-label {
  position: absolute;
  pointer-events: none;
  user-select: none;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: transform, opacity;
  transform-style: preserve-3d;
  transition: none !important;
  animation: none !important;
}

.node-label {
  font-size: 20px;
  font-weight: bold;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Removed background circle */
}

.edge-label {
  font-size: 16px;
  font-weight: bold;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}
