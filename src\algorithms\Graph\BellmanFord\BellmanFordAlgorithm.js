// BellmanFordAlgorithm.js
// Implementation of the Bellman-Ford algorithm with step generation

/**
 * Generates steps for the Bellman-Ford algorithm
 * @param {Object} graph - The graph representation with nodes and edges
 * @param {string} startNode - The starting node for the algorithm
 * @returns {Object} - Object containing steps and the result
 */
export const generateBellmanFordSteps = (graph, startNode) => {
  const steps = [];
  const nodes = graph.nodes || [];
  const edges = graph.edges || [];
  
  // Initialize distances and predecessors
  const distances = {};
  const predecessors = {};
  const nodeIds = nodes.map(node => node.id);
  
  // Add initial step
  steps.push({
    type: 'init',
    message: 'Initialize Bellman-Ford algorithm',
    distances: { ...distances },
    predecessors: { ...predecessors },
    currentEdge: null,
    relaxedEdge: null,
    currentIteration: 0,
    negCycleDetection: false,
    negCycleFound: false,
    negCycleEdge: null,
    negCyclePath: null,
  });

  // Initialize distances to Infinity and predecessors to null
  nodeIds.forEach(nodeId => {
    distances[nodeId] = nodeId === startNode ? 0 : Infinity;
    predecessors[nodeId] = null;
  });

  // Add step after initialization
  steps.push({
    type: 'initialize',
    message: `Set distance of start node ${startNode} to 0, all others to Infinity`,
    distances: { ...distances },
    predecessors: { ...predecessors },
    currentEdge: null,
    relaxedEdge: null,
    currentIteration: 0,
    negCycleDetection: false,
    negCycleFound: false,
    negCycleEdge: null,
    negCyclePath: null,
  });

  // Main Bellman-Ford algorithm
  // Relax edges |V| - 1 times
  const V = nodeIds.length;
  
  for (let i = 1; i <= V - 1; i++) {
    // Add step for iteration start
    steps.push({
      type: 'iterationStart',
      message: `Starting iteration ${i} of ${V - 1}`,
      distances: { ...distances },
      predecessors: { ...predecessors },
      currentEdge: null,
      relaxedEdge: null,
      currentIteration: i,
      negCycleDetection: false,
      negCycleFound: false,
      negCycleEdge: null,
      negCyclePath: null,
    });
    
    let relaxedAnyEdge = false;
    
    // For each edge, try to relax it
    for (const edge of edges) {
      const { source, target, weight } = edge;
      
      // Add step for considering an edge
      steps.push({
        type: 'considerEdge',
        message: `Considering edge ${source} → ${target} with weight ${weight}`,
        distances: { ...distances },
        predecessors: { ...predecessors },
        currentEdge: edge,
        relaxedEdge: null,
        currentIteration: i,
        negCycleDetection: false,
        negCycleFound: false,
        negCycleEdge: null,
        negCyclePath: null,
      });
      
      // Check if we can relax the edge
      if (distances[source] !== Infinity && distances[source] + weight < distances[target]) {
        // Relax the edge
        const oldDistance = distances[target];
        distances[target] = distances[source] + weight;
        predecessors[target] = source;
        relaxedAnyEdge = true;
        
        // Add step for relaxing an edge
        steps.push({
          type: 'relaxEdge',
          message: `Relaxed edge ${source} → ${target}: Updated distance of ${target} from ${oldDistance === Infinity ? '∞' : oldDistance} to ${distances[target]}`,
          distances: { ...distances },
          predecessors: { ...predecessors },
          currentEdge: edge,
          relaxedEdge: edge,
          currentIteration: i,
          negCycleDetection: false,
          negCycleFound: false,
          negCycleEdge: null,
          negCyclePath: null,
        });
      } else {
        // Add step for not relaxing an edge
        steps.push({
          type: 'skipEdge',
          message: `No need to relax edge ${source} → ${target}: Current distance to ${target} (${distances[target] === Infinity ? '∞' : distances[target]}) is already optimal`,
          distances: { ...distances },
          predecessors: { ...predecessors },
          currentEdge: edge,
          relaxedEdge: null,
          currentIteration: i,
          negCycleDetection: false,
          negCycleFound: false,
          negCycleEdge: null,
          negCyclePath: null,
        });
      }
    }
    
    // Add step for iteration end
    steps.push({
      type: 'iterationEnd',
      message: `Completed iteration ${i} of ${V - 1}${relaxedAnyEdge ? '' : '. No edges were relaxed, which means we have reached the optimal solution'}`,
      distances: { ...distances },
      predecessors: { ...predecessors },
      currentEdge: null,
      relaxedEdge: null,
      currentIteration: i,
      negCycleDetection: false,
      negCycleFound: false,
      negCycleEdge: null,
      negCyclePath: null,
    });
    
    // If no edges were relaxed in this iteration, we can stop early
    if (!relaxedAnyEdge) {
      steps.push({
        type: 'earlyTermination',
        message: 'No edges were relaxed in this iteration. The algorithm can terminate early as we have found the optimal solution.',
        distances: { ...distances },
        predecessors: { ...predecessors },
        currentEdge: null,
        relaxedEdge: null,
        currentIteration: i,
        negCycleDetection: false,
        negCycleFound: false,
        negCycleEdge: null,
        negCyclePath: null,
      });
      break;
    }
  }
  
  // Check for negative cycles
  steps.push({
    type: 'startNegativeCycleCheck',
    message: 'Checking for negative cycles...',
    distances: { ...distances },
    predecessors: { ...predecessors },
    currentEdge: null,
    relaxedEdge: null,
    currentIteration: V,
    negCycleDetection: true,
    negCycleFound: false,
    negCycleEdge: null,
    negCyclePath: null,
  });
  
  let negCycleFound = false;
  let negCycleEdge = null;
  let negCyclePath = null;
  
  // One more pass to check for negative cycles
  for (const edge of edges) {
    const { source, target, weight } = edge;
    
    // Add step for checking an edge for negative cycle
    steps.push({
      type: 'checkNegativeCycle',
      message: `Checking edge ${source} → ${target} for negative cycle`,
      distances: { ...distances },
      predecessors: { ...predecessors },
      currentEdge: edge,
      relaxedEdge: null,
      currentIteration: V,
      negCycleDetection: true,
      negCycleFound: false,
      negCycleEdge: null,
      negCyclePath: null,
    });
    
    // If we can still relax an edge, there's a negative cycle
    if (distances[source] !== Infinity && distances[source] + weight < distances[target]) {
      negCycleFound = true;
      negCycleEdge = edge;
      
      // Find the negative cycle
      const visited = new Set();
      let current = target;
      negCyclePath = [current];
      visited.add(current);
      
      // Trace back until we find a cycle
      while (true) {
        current = predecessors[current];
        
        // If we've reached the start of the path or a node not in the path
        if (!current) break;
        
        negCyclePath.unshift(current);
        
        // If we've seen this node before, we've found a cycle
        if (visited.has(current)) {
          // Adjust the path to only include the cycle
          const cycleStart = negCyclePath.indexOf(current);
          negCyclePath = negCyclePath.slice(cycleStart);
          break;
        }
        
        visited.add(current);
      }
      
      // Add step for finding a negative cycle
      steps.push({
        type: 'negCycleFound',
        message: `Negative cycle detected! Edge ${source} → ${target} can still be relaxed after ${V-1} iterations.`,
        distances: { ...distances },
        predecessors: { ...predecessors },
        currentEdge: edge,
        relaxedEdge: edge,
        currentIteration: V,
        negCycleDetection: true,
        negCycleFound: true,
        negCycleEdge: edge,
        negCyclePath: negCyclePath,
      });
      
      break;
    }
  }
  
  // Final step
  if (!negCycleFound) {
    steps.push({
      type: 'complete',
      message: 'Bellman-Ford algorithm completed successfully. No negative cycles found.',
      distances: { ...distances },
      predecessors: { ...predecessors },
      currentEdge: null,
      relaxedEdge: null,
      currentIteration: V,
      negCycleDetection: true,
      negCycleFound: false,
      negCycleEdge: null,
      negCyclePath: null,
    });
  }
  
  // Prepare the result
  const result = {
    distances,
    predecessors,
    hasNegativeCycle: negCycleFound,
    negativeCycle: negCycleFound ? negCyclePath : null,
    negativeCycleEdge: negCycleFound ? negCycleEdge : null,
  };
  
  return { steps, result };
};

// Helper function to reconstruct the shortest path from source to target
export const getShortestPath = (source, target, predecessors) => {
  if (!predecessors[target]) return null;
  
  const path = [target];
  let current = target;
  
  while (current !== source) {
    current = predecessors[current];
    if (!current) return null; // No path exists
    path.unshift(current);
  }
  
  return path;
};

// Default export
const BellmanFordAlgorithm = {
  generateSteps: generateBellmanFordSteps,
  getShortestPath
};

export default BellmanFordAlgorithm;
