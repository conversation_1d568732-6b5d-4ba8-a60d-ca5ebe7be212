// SieveController.js
// This component provides the controls for the Sieve of Eratosthenes algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Slider, Grid } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FunctionsIcon from '@mui/icons-material/Functions';

// Import algorithm functions
import { generateSieveSteps } from './SieveAlgorithm';

const SieveController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [limit, setLimit] = useState(params?.limit || 30);

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { limit });

    // Update params first
    onParamChange({
      limit
    });

    // Set steps and movements directly
    try {
      const result = generateSieveSteps({
        limit
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [limit, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle limit change
  const handleLimitChange = useCallback((event, newValue) => {
    setLimit(newValue);
  }, []);

  // Handle apply button click
  const handleApplyClick = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Sieve of Eratosthenes
  const pseudocode = [
    { code: "function sieveOfEratosthenes(limit):", lineNumber: 1, indent: 0 },
    { code: "    // Create a boolean array \"isPrime[0..limit]\" and initialize all entries as true", lineNumber: 2, indent: 0 },
    { code: "    isPrime = new boolean[limit+1]", lineNumber: 3, indent: 1 },
    { code: "    for i = 0 to limit:", lineNumber: 4, indent: 1 },
    { code: "        isPrime[i] = true", lineNumber: 5, indent: 2 },
    { code: "", lineNumber: 6, indent: 0 },
    { code: "    // 0 and 1 are not prime", lineNumber: 7, indent: 0 },
    { code: "    isPrime[0] = isPrime[1] = false", lineNumber: 8, indent: 1 },
    { code: "", lineNumber: 9, indent: 0 },
    { code: "    // Main Sieve algorithm", lineNumber: 10, indent: 0 },
    { code: "    for i = 2 to sqrt(limit):", lineNumber: 11, indent: 1 },
    { code: "        // If i is prime (not marked as composite yet)", lineNumber: 12, indent: 0 },
    { code: "        if isPrime[i] is true:", lineNumber: 13, indent: 2 },
    { code: "            // Mark all multiples of i as non-prime", lineNumber: 14, indent: 0 },
    { code: "            for j = i*i to limit step i:", lineNumber: 15, indent: 3 },
    { code: "                isPrime[j] = false", lineNumber: 16, indent: 4 },
    { code: "", lineNumber: 17, indent: 0 },
    { code: "    // Collect all prime numbers", lineNumber: 18, indent: 0 },
    { code: "    primes = []", lineNumber: 19, indent: 1 },
    { code: "    for i = 2 to limit:", lineNumber: 20, indent: 1 },
    { code: "        if isPrime[i] is true:", lineNumber: 21, indent: 2 },
    { code: "            primes.add(i)", lineNumber: 22, indent: 3 },
    { code: "", lineNumber: 23, indent: 0 },
    { code: "    return primes", lineNumber: 24, indent: 1 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Custom component for limit slider
  const LimitSlider = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Limit (Maximum Number to Check)
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{ flexGrow: 1 }}>
            <Slider
              value={value}
              onChange={onChange}
              min={10}
              max={100}
              step={1}
              marks={[
                { value: 10, label: '10' },
                { value: 30, label: '30' },
                { value: 50, label: '50' },
                { value: 100, label: '100' }
              ]}
              valueLabelDisplay="auto"
              disabled={disabled}
            />
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">
              {value}
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  // No custom results components - visualization will handle this

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Sieve of Eratosthenes"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Sieve of Eratosthenes is an ancient algorithm for finding all prime numbers up to a specified limit.
            It works by iteratively marking the multiples of each prime, starting from 2.
            The multiples of each prime are generated as a sequence of numbers starting from the prime's square,
            stepping by the prime number itself.
          </Typography>
          <Typography variant="body2" paragraph>
            This algorithm is particularly efficient for finding all primes below a certain limit, with a time complexity of O(n log log n).
          </Typography>
          <Typography variant="body2" gutterBottom>
            The algorithm follows these steps:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2">Create a list of consecutive integers from 2 to n: (2, 3, 4, ..., n).</Typography>
            </li>
            <li>
              <Typography variant="body2">Start with the first prime number, p = 2.</Typography>
            </li>
            <li>
              <Typography variant="body2">Mark all multiples of p that are greater than p as non-prime (composite numbers).</Typography>
            </li>
            <li>
              <Typography variant="body2">Find the smallest number greater than p that is not marked. If no such number exists, stop. Otherwise, let this number be the next prime p, and repeat from step 3.</Typography>
            </li>
          </ol>
          <Typography variant="body2" paragraph>
            When the algorithm terminates, all numbers that are not marked are prime.
          </Typography>
          <Typography variant="body2" paragraph>
            The Sieve of Eratosthenes is named after Eratosthenes of Cyrene, a Greek mathematician who lived in the 3rd century BCE.
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'limit',
            type: 'component',
            label: 'Limit',
            component: LimitSlider,
            componentProps: {
              value: limit,
              onChange: handleLimitChange,
              disabled: state !== 'idle'
            },
            icon: FunctionsIcon
          }
        ]}
        values={{
          limit
        }}
        disabled={state === 'running'}
        onApply={handleApplyClick}
        showApplyButton={true}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* No Results Display Section - visualization will handle this */}

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Sieve of Eratosthenes Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default SieveController;
