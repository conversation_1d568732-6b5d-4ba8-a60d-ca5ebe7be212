// CountingSortVisualization.js
// Visualization component for CountingSort algorithm following the new architecture

import React, { useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';

// Import configuration and components
import CONFIG from './CountingSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

// Import standard visualization components
import { FixedStepBoard, FixedColorLegend, SortingBase } from '../../../components/visualization';
import CountingSortSimulation from '../../../components/visualization/bars/CountingSortSimulation';

/**
 * Main CountingSort visualization component using new architecture
 */
const CountingSortVisualization = () => {
  const theme = useTheme();
  const { camera } = useThree();
  const { state, setState, step, setStep, steps, algorithmArray } = useAlgorithm();
  const { speed } = useSpeed();

  const speedRef = useRef(speed);
  const animatingRef = useRef(false);
  const timeoutIdRef = useRef(null);
  const groupRef = useRef();

  const colors = useMemo(() => {
    const isDark = theme?.palette?.mode === 'dark';
    return isDark ? CONFIG.colors?.themes?.dark : CONFIG.colors?.themes?.light;
  }, [theme]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  useFrame(({ clock }) => {
    if (state === 'running' && !animatingRef.current && steps && steps.length > 0) {
      if (step < steps.length - 1) {
        animatingRef.current = true;
        const delay = getEnhancedDelay(speedRef.current);
        timeoutIdRef.current = setTimeout(() => {
          setStep(prev => prev + 1);
          animatingRef.current = false;
        }, delay);
      } else {
        setState('completed');
      }
    }

    // Levitation animation
    if (CONFIG.mainArray?.levitation?.enabled &&
      (state === 'idle' || !CONFIG.mainArray.levitation.disableDuringSimulation) &&
      groupRef.current) {
      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.mainArray.levitation;
      const basePosition = CONFIG.basePlatform.position;

      // Y-axis levitation
      groupRef.current.position.y = basePosition[1] + Math.sin(time * (levitationConfig.frequency || 0.5) + (levitationConfig.offset || 0)) * (levitationConfig.amplitude || 0.1);

      // Keep X and Z static to basePosition
      groupRef.current.position.x = basePosition[0];
      groupRef.current.position.z = basePosition[2];
    } else if (groupRef.current && CONFIG.basePlatform?.position) {
      // Reset to base position if levitation is off
      groupRef.current.position.set(...CONFIG.basePlatform.position);
    }
  });

  useEffect(() => {
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (camera && CONFIG.camera) {
      const cameraConfig = CONFIG.camera;
      camera.position.set(...cameraConfig.position);
      camera.lookAt(...cameraConfig.lookAt);
      camera.fov = cameraConfig.fov;
      camera.updateProjectionMatrix();
    }
  }, [camera]);

  let currentStepData = null;
  if (steps && steps.length > 0) {
    if (step >= 0 && step < steps.length) {
      currentStepData = steps[step];
    } else if (step >= steps.length) {
      currentStepData = steps[steps.length - 1];
    } else if (step === 0 && steps.length > 0) {
      currentStepData = steps[0];
    }
  }

  const legendItems = useMemo(() => {
    return CONFIG.colorLegend?.legendItems?.map(item => ({
      ...item,
      color: colors[item.colorKey] || colors.bar
    })) || [];
  }, [colors]);

  // Calculate dynamic platform width
  const dynamicPlatformWidth = useMemo(() => {
    const arrayValues = currentStepData?.visualizationData?.mainArray?.values || algorithmArray;
    const arrayLength = arrayValues?.length ?? (CONFIG.mainArray.bars.defaultCountForSizing ?? 10);

    const barW = CONFIG.mainArray.bars.width;
    const barS = CONFIG.mainArray.bars.spacing;

    let totalWidthOfBarsOnly = 0;
    if (arrayLength > 0) {
      totalWidthOfBarsOnly = (arrayLength * barW) + ((arrayLength - 1) * barS);
      if (arrayLength === 1) totalWidthOfBarsOnly = barW;
    }
    if (arrayLength === 0) totalWidthOfBarsOnly = 0;

    const paddingL = CONFIG.basePlatform.dimensions.lengthPadding.left;
    const paddingR = CONFIG.basePlatform.dimensions.lengthPadding.right;
    const totalPadding = paddingL + paddingR;

    let calculatedWidth = totalWidthOfBarsOnly + totalPadding;

    const minPlatformWidth = CONFIG.basePlatform.dimensions.minWidth ?? (totalPadding + (arrayLength > 0 ? barW : 0));

    calculatedWidth = Math.max(calculatedWidth, minPlatformWidth);

    if (isNaN(calculatedWidth) || calculatedWidth <= 0) {
      return totalPadding + (CONFIG.mainArray.bars.defaultCountForSizing ?? 10) * (barW + barS) - barS;
    }

    return calculatedWidth;
  }, [
    currentStepData?.visualizationData?.mainArray?.values,
    algorithmArray,
    CONFIG.mainArray.bars.defaultCountForSizing,
    CONFIG.mainArray.bars.width,
    CONFIG.mainArray.bars.spacing,
    CONFIG.basePlatform.dimensions.lengthPadding.left,
    CONFIG.basePlatform.dimensions.lengthPadding.right,
    CONFIG.basePlatform.dimensions.minWidth,
  ]);

  return (
    <>
      {CONFIG.stepBoard?.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''}
          totalSteps={steps && steps.length > 0 ? steps.length - 1 : 0}
          description={step === 0 ?
            `Counting Sort: Initial Array [${(currentStepData?.visualizationData?.mainArray?.values || algorithmArray)?.join(', ') || ''}]` :
            (currentStepData?.statement || 'Counting Sort Algorithm')}
          stepData={currentStepData}
          showStepNumber={step > 0}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {CONFIG.colorLegend?.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      <group ref={groupRef} position={CONFIG.basePlatform.position}>
        {/* Base platform */}
        <SortingBase
          width={dynamicPlatformWidth}
          height={CONFIG.basePlatform.dimensions.height}
          depth={CONFIG.basePlatform.dimensions.depth}
          color={colors?.platform}
          position={[0, -CONFIG.basePlatform.dimensions.height / 2, 0]}
        />

        {/* CountingSort simulation (bars) */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <CountingSortSimulation
            currentStep={currentStepData}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width}
            barSpacing={CONFIG.mainArray.bars.spacing}
            showValues={CONFIG.mainArray.valueLabels.enabled}
            showIndices={CONFIG.mainArray.indexLabels.enabled}
          />
        </group>
      </group>

      <ambientLight intensity={0.6} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={0.8}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
      />
      <directionalLight
        position={[-10, 5, -5]}
        intensity={0.3}
      />
    </>
  );
};

export default CountingSortVisualization;

