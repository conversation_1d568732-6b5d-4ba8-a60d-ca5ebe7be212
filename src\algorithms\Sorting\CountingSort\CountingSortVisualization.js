// CountingSortVisualization.js
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateCountingSortSteps } from './CountingSortAlgorithm';

// Constants for visualization
const BAR_WIDTH = 0.6;
const BAR_SPACING = 0.3;
const BASE_HEIGHT = 0.2;
const MAX_BAR_HEIGHT = 3.8;
const CAMERA_POSITION = [0, 4, 12];
const CAMERA_LOOKAT = [0, 0, -2];

// Enhanced Bar component with better visual distinction
const Bar = ({ position, height, width, color, value, index, showValue, showArrow = false }) => {
  const barHeight = Math.max(0.1, height);
  const theme = useTheme();

  // Calculate a slightly darker color for the bar edges
  const getEdgeColor = (baseColor) => {
    // Convert hex to RGB
    const r = parseInt(baseColor.slice(1, 3), 16);
    const g = parseInt(baseColor.slice(3, 5), 16);
    const b = parseInt(baseColor.slice(5, 7), 16);

    // Darken the color
    const darkenFactor = 0.7;
    const newR = Math.floor(r * darkenFactor);
    const newG = Math.floor(g * darkenFactor);
    const newB = Math.floor(b * darkenFactor);

    // Convert back to hex
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  };

  const edgeColor = getEdgeColor(color);

  return (
    <group position={position}>
      {/* Bar base */}
      <mesh position={[0, 0.05, 0]} castShadow receiveShadow>
        <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
        <meshStandardMaterial color={edgeColor} />
      </mesh>

      {/* Main bar */}
      <mesh position={[0, barHeight / 2 + 0.05, 0]} castShadow receiveShadow>
        <boxGeometry args={[width, barHeight, width]} />
        <meshStandardMaterial
          color={color}
          metalness={0.3}
          roughness={0.7}
          emissive={color}
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Bar top */}
      <mesh position={[0, barHeight + 0.05, 0]} castShadow receiveShadow>
        <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
        <meshStandardMaterial color={edgeColor} />
      </mesh>

      {/* Value label */}
      {showValue && (
        <Html position={[0, barHeight + 0.4, 0]} center>
          <div style={{
            color: theme.palette.text.primary,
            background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.6)' : 'rgba(255,255,255,0.8)',
            padding: '3px 8px',
            borderRadius: '8px',
            fontSize: '0.8rem',
            fontWeight: 'bold',
            textAlign: 'center',
            boxShadow: `0 2px 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)'}`,
            border: `1px solid ${color}80`
          }}>
            {value}
          </div>
        </Html>
      )}

      {/* Index label */}
      <Html position={[0, -0.3, 0]} center>
        <div style={{
          color: theme.palette.text.secondary,
          background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.7)',
          padding: '2px 6px',
          borderRadius: '10px',
          fontSize: '0.7rem',
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: `0 0 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.3)'}`,
          boxShadow: `0 1px 3px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.2)'}`
        }}>
          {index}
        </div>
      </Html>

      {/* Current element arrow */}
      {showArrow && (
        <Html position={[0, barHeight + 1.2, 0]} center>
          <div style={{
            color: theme.palette.secondary.main,
            background: 'transparent',
            fontSize: '1.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            textShadow: `0 0 8px ${theme.palette.secondary.main}`
          }}>
            ↓
          </div>
        </Html>
      )}
    </group>
  );
};

// Base component
const Base = ({ width, color }) => {
  return (
    <group>
      {/* Main base platform */}
      <mesh position={[0, -BASE_HEIGHT / 2 - 0.1, 0]} receiveShadow>
        <boxGeometry args={[width, BASE_HEIGHT, width / 1.8, 8, 8, 8]} />
        <meshStandardMaterial
          color={color}
          metalness={0.2}
          roughness={0.5}
        />
      </mesh>
    </group>
  );
};

// Count Array component
const CountArray = ({ counts, position, width, height, currentIndex = -1, theme }) => {
  return (
    <group position={position}>
      {/* Count array title */}
      <Html position={[0, height + 0.5, 0]} center>
        <div style={{
          color: theme.palette.text.primary,
          fontSize: '0.9rem',
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: `0 0 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)'}`
        }}>
          Count Array
        </div>
      </Html>

      {/* Count array cells */}
      {counts.map((count, index) => {
        // Highlight the cell if it matches the current element value
        const isHighlighted = index === currentIndex;
        const cellWidth = Math.min(0.8, width / counts.length);
        const spacing = Math.min(0.2, width / (counts.length * 4));
        const startX = -(width / 2) + (cellWidth / 2);
        const x = startX + index * (cellWidth + spacing);

        return (
          <group key={`count-${index}`} position={[x, 0, 0]}>
            {/* Cell background */}
            <mesh position={[0, 0, 0]} receiveShadow>
              <boxGeometry args={[cellWidth, height, cellWidth]} />
              <meshStandardMaterial
                color={isHighlighted ? theme.palette.secondary.main : theme.palette.mode === 'dark' ? '#2c2c2c' : '#f0f0f0'}
                transparent
                opacity={0.8}
              />
            </mesh>

            {/* Index label */}
            <Html position={[0, -height/2 - 0.3, 0]} center>
              <div style={{
                color: theme.palette.text.secondary,
                background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.7)',
                padding: '2px 4px',
                borderRadius: '4px',
                fontSize: '0.6rem',
                fontWeight: 'bold',
                textAlign: 'center'
              }}>
                {index}
              </div>
            </Html>

            {/* Count value */}
            <Html position={[0, 0, cellWidth/2 + 0.05]} center>
              <div style={{
                color: isHighlighted ? theme.palette.secondary.contrastText : theme.palette.text.primary,
                fontSize: '0.8rem',
                fontWeight: 'bold',
                textAlign: 'center'
              }}>
                {count}
              </div>
            </Html>
          </group>
        );
      })}
    </group>
  );
};

// Output Array component
const OutputArray = ({ array, position, width, height, currentIndex = -1, theme }) => {
  return (
    <group position={position}>
      {/* Output array title */}
      <Html position={[0, height + 0.5, 0]} center>
        <div style={{
          color: theme.palette.text.primary,
          fontSize: '0.9rem',
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: `0 0 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)'}`
        }}>
          Output Array
        </div>
      </Html>

      {/* Output array cells */}
      {array.map((value, index) => {
        const isCurrentIndex = index === currentIndex;
        const cellWidth = Math.min(0.8, width / array.length);
        const spacing = Math.min(0.2, width / (array.length * 4));
        const startX = -(width / 2) + (cellWidth / 2);
        const x = startX + index * (cellWidth + spacing);

        return (
          <group key={`output-${index}`} position={[x, 0, 0]}>
            {/* Cell background */}
            <mesh position={[0, 0, 0]} receiveShadow>
              <boxGeometry args={[cellWidth, height, cellWidth]} />
              <meshStandardMaterial
                color={isCurrentIndex ? theme.palette.secondary.main : theme.palette.mode === 'dark' ? '#2c2c2c' : '#f0f0f0'}
                transparent
                opacity={0.8}
              />
            </mesh>

            {/* Index label */}
            <Html position={[0, -height/2 - 0.3, 0]} center>
              <div style={{
                color: theme.palette.text.secondary,
                background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.7)',
                padding: '2px 4px',
                borderRadius: '4px',
                fontSize: '0.6rem',
                fontWeight: 'bold',
                textAlign: 'center'
              }}>
                {index}
              </div>
            </Html>

            {/* Value */}
            <Html position={[0, 0, cellWidth/2 + 0.05]} center>
              <div style={{
                color: isCurrentIndex ? theme.palette.secondary.contrastText : theme.palette.text.primary,
                fontSize: '0.8rem',
                fontWeight: 'bold',
                textAlign: 'center'
              }}>
                {value || ''}
              </div>
            </Html>
          </group>
        );
      })}
    </group>
  );
};

// Step Board component - enhanced explanatory design
const StepBoard = ({ currentStep, position = [0, 0, 0] }) => {
  const theme = useTheme();

  // Format step description based on step type
  let stepDescription = '';
  let stepType = '';

  if (currentStep) {
    stepDescription = currentStep.movement || '';
    stepType = currentStep.type || '';
  }

  // Get color based on step type for better visual cues
  const getStepColor = (type) => {
    switch (type) {
      case 'init': return '#2196f3'; // blue
      case 'findMax': return '#9c27b0'; // purple
      case 'initCount': return '#673ab7'; // deep purple
      case 'counting': return '#ff9800'; // orange
      case 'incrementCount': return '#ff5722'; // deep orange
      case 'calculatePosition': return '#795548'; // brown
      case 'updatePosition': return '#607d8b'; // blue grey
      case 'initOutput': return '#009688'; // teal
      case 'placing': return '#4caf50'; // green
      case 'placed': return '#8bc34a'; // light green
      case 'copying': return '#f44336'; // red
      case 'copied': return '#e57373'; // light red
      case 'complete': return '#388e3c'; // dark green
      default: return theme.palette.mode === 'dark' ? '#ffffff' : '#000000';
    }
  };

  // Enhanced description with step type highlighted
  const enhancedDescription = stepType ?
    <span>
      <span style={{
        color: getStepColor(stepType),
        fontWeight: 'bold',
        fontSize: '10px',
      }}>
        [{stepType.toUpperCase()}]
      </span>
      {' '}{stepDescription}
    </span> :
    stepDescription;

  return (
    <group position={position}>
      <Html
        position={[0, 0, 0]}
        center
        transform
      >
        <div style={{
          fontSize: '9px',
          fontFamily: 'monospace',
          color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
          textShadow: `0 0 3px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)'}`,
          fontWeight: 'bold',
          textAlign: 'center',
          whiteSpace: 'nowrap',
          maxWidth: '500px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}>
          {enhancedDescription}
        </div>
      </Html>
    </group>
  );
};

// Color Legend component - simple high-quality design with better arrangement
const ColorLegend = ({ colors, position = [0, 0, 0] }) => {
  const theme = useTheme();

  // Create a single HTML element for all legend items
  return (
    <group position={position}>
      <Html
        position={[0, 0, 0]}
        center
        transform
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '16px',
          padding: '4px 8px',
        }}>
          {/* Default */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '14px',
              height: '14px',
              backgroundColor: colors.bar,
              marginRight: '6px',
              borderRadius: '3px',
            }} />
            <span style={{
              fontSize: '12px',
              color: theme.palette.text.primary,
              fontWeight: '500',
            }}>Default</span>
          </div>

          {/* Current */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '14px',
              height: '14px',
              backgroundColor: colors.current,
              marginRight: '6px',
              borderRadius: '3px',
            }} />
            <span style={{
              fontSize: '12px',
              color: theme.palette.text.primary,
              fontWeight: '500',
            }}>Current</span>
          </div>

          {/* Counting */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '14px',
              height: '14px',
              backgroundColor: colors.counting,
              marginRight: '6px',
              borderRadius: '3px',
            }} />
            <span style={{
              fontSize: '12px',
              color: theme.palette.text.primary,
              fontWeight: '500',
            }}>Counting</span>
          </div>

          {/* Placing */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '14px',
              height: '14px',
              backgroundColor: colors.placing,
              marginRight: '6px',
              borderRadius: '3px',
            }} />
            <span style={{
              fontSize: '12px',
              color: theme.palette.text.primary,
              fontWeight: '500',
            }}>Placing</span>
          </div>

          {/* Sorted */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '14px',
              height: '14px',
              backgroundColor: colors.sorted,
              marginRight: '6px',
              borderRadius: '3px',
            }} />
            <span style={{
              fontSize: '12px',
              color: theme.palette.text.primary,
              fontWeight: '500',
            }}>Sorted</span>
          </div>
        </div>
      </Html>
    </group>
  );
};

// Helper function to get animation duration based on speed
const getAnimationDuration = (speed) => {
  return 1100 - (speed * 100);
};

// Helper function to get delay between steps based on speed
const getDelay = (speed) => {
  // Add a minimum delay of 300ms to ensure smoother transitions
  return Math.max(300, 1100 - (speed * 100));
};

// Main visualization component
const CountingSortVisualization = ({ params = {} }) => {
    const theme = useTheme();
  const { camera } = useThree();
  const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
  const { speed } = useSpeed();

  // Refs for animation control
  const speedRef = useRef(speed);
  const stepsRef = useRef([]);
  const initialArrayRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const animatingRef = useRef(false);
  const timeoutIdRef = useRef(null);
  const stateRef = useRef(state);
  const currentStepRef = useRef(step);

  // State for array data
  const [arrayData, setArrayData] = useState([]);
  const [countArray, setCountArray] = useState([]);
  const [outputArray, setOutputArray] = useState([]);

  // State for visualization
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [targetIndex, setTargetIndex] = useState(-1);
  const [currentElement, setCurrentElement] = useState(null);
  const [sorted, setSorted] = useState([]);

  // Animation state
  const [moveAnimation, setMoveAnimation] = useState({
    active: false,
    fromIndex: -1,
    toIndex: -1,
    progress: 0,
    startTime: 0
  });

  // Colors based on theme
  const colors = useMemo(() => {
    const isDark = theme.palette.mode === 'dark';
    return {
      bar: isDark ? '#42a5f5' : '#1976d2',
      current: isDark ? '#ab47bc' : '#7b1fa2',
      counting: isDark ? '#ff9800' : '#f57c00',
      placing: isDark ? '#4caf50' : '#388e3c',
      sorted: isDark ? '#66bb6a' : '#388e3c',
      base: isDark ? '#1a1a1a' : '#f5f5f5',
      ground: isDark ? '#121212' : '#eeeeee'
    };
  }, [theme.palette.mode]);

  // Update refs when state changes
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  useEffect(() => {
    currentStepRef.current = step;
  }, [step]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Initialize array when params change
  useEffect(() => {
    // Clear any existing animation timeouts
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    // Reset animation state
    animatingRef.current = false;
    setMoveAnimation({
      active: false,
      fromIndex: -1,
      toIndex: -1,
      progress: 0,
      startTime: 0
    });

    // Extract parameters with safety checks
    const { arraySize = 10, randomize = true, customArray = [] } = params;

    // Generate array data
    let newArray = [];

    // Use custom array if provided
    if (customArray && customArray.length > 0) {
      newArray = [...customArray];
    } else if (randomize) {
      // Generate random array - for counting sort, ensure all numbers are positive integers
      newArray = Array.from({ length: arraySize }, () =>
        Math.floor(Math.random() * 9) + 1
      );
    } else {
      // Generate reverse sorted array
      newArray = Array.from({ length: arraySize }, (_, i) => arraySize - i);
    }

    // Store the initial array
    initialArrayRef.current = [...newArray];
    setArrayData(newArray);

    // Generate steps
    const { steps } = generateCountingSortSteps(newArray);
    stepsRef.current = steps;
    setSteps(steps);
    setTotalSteps(steps.length);
    setAlgorithmSteps(steps.map(step => step.movement));

    // Reset visualization state
    setCurrentIndex(-1);
    setTargetIndex(-1);
    setCurrentElement(null);
    setCountArray([]);
    setOutputArray([]);
    setSorted([]);

    // Reset step if needed
    setStep(0);
    lastAppliedStepRef.current = -1;

    // Position camera
    if (camera) {
      camera.position.set(...CAMERA_POSITION);
      camera.lookAt(...CAMERA_LOOKAT);
    }

    // Force a small delay to ensure everything is reset
    setTimeout(() => {
      // Apply the initial step
      if (steps.length > 0) {
        applyStep(0);
      }
    }, 50);
  }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

  // Apply a single step of the counting sort algorithm
  const applyStep = (stepIndex) => {
    if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
      console.log('Invalid step index:', stepIndex, 'total steps:', stepsRef.current.length);
      return;
    }

    const currentStep = stepsRef.current[stepIndex];
    console.log('Applying step', stepIndex, 'type:', currentStep.type);
    lastAppliedStepRef.current = stepIndex;

    // Update array data if needed
    if (currentStep.array) {
      setArrayData(currentStep.array);
    }

    // Update count array if needed
    if (currentStep.countArray) {
      setCountArray(currentStep.countArray);
    }

    // Update output array if needed
    if (currentStep.outputArray) {
      setOutputArray(currentStep.outputArray);
    }

    // Update visualization state based on step type
    switch (currentStep.type) {
      case 'init':
        setCurrentIndex(-1);
        setTargetIndex(-1);
        setCurrentElement(null);
        setCountArray([]);
        setOutputArray([]);
        setSorted([]);
        break;

      case 'findMax':
        setCurrentIndex(-1);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'initCount':
        setCurrentIndex(-1);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'counting':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(currentStep.currentElement);
        setSorted([]);
        break;

      case 'incrementCount':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(currentStep.currentElement);
        setSorted([]);
        break;

      case 'calculatePosition':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'updatePosition':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'initOutput':
        setCurrentIndex(-1);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'placing':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(currentStep.targetIndex);
        setCurrentElement(currentStep.currentElement);
        setSorted([]);

        // Start animation to move item from array to output
        if (!animatingRef.current) {
          animatingRef.current = true;
          setMoveAnimation({
            active: true,
            fromIndex: currentStep.currentIndex,
            toIndex: currentStep.targetIndex,
            progress: 0,
            startTime: performance.now()
          });

          // End animation after duration
          const animationDuration = getAnimationDuration(speedRef.current);
          timeoutIdRef.current = setTimeout(() => {
            console.log('Animation completed for step', currentStepRef.current);
            setMoveAnimation(prev => ({
              ...prev,
              active: false
            }));
            animatingRef.current = false;

            // If we're in running state, schedule the next step
            if (stateRef.current === 'running') {
              setTimeout(() => {
                if (stateRef.current === 'running') {
                  console.log('Advancing to next step after animation');
                  setStep(prevStep => prevStep + 1);
                }
              }, 100); // Small delay before next step
            }
          }, animationDuration);
        }
        break;

      case 'placed':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(currentStep.currentElement);
        setSorted([]);
        break;

      case 'copying':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted([]);
        break;

      case 'copied':
        setCurrentIndex(currentStep.currentIndex);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted(currentStep.sorted || []);
        break;

      case 'complete':
        setCurrentIndex(-1);
        setTargetIndex(-1);
        setCurrentElement(null);
        setSorted(currentStep.sorted || []);
        break;

      default:
        break;
    }
  };

  // Handle step changes
  useEffect(() => {
    console.log('Step changed to', step, 'total steps:', stepsRef.current?.length);

    // Clear any existing timeout
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    // Reset animation state if step changes manually
    if (animatingRef.current && lastAppliedStepRef.current !== step - 1) {
      console.log('Resetting animation state due to manual step change');
      animatingRef.current = false;
      setMoveAnimation(prev => ({
        ...prev,
        active: false
      }));
    }

    // Don't apply steps if we're still animating and the step is sequential
    if (animatingRef.current && lastAppliedStepRef.current === step - 1) {
      console.log('Skipping step application due to ongoing animation');
      return;
    }

    // Apply the current step
    console.log('Applying step', step, 'type:', stepsRef.current[step]?.type);
    applyStep(step);
  }, [step]);

  // Handle automatic stepping when state is 'running'
  useEffect(() => {
    // Reset animation state when state changes
    if (state === 'idle') {
      // Clear any existing timeouts
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      // Reset animation state
      animatingRef.current = false;
      setMoveAnimation({
        active: false,
        fromIndex: -1,
        toIndex: -1,
        progress: 0,
        startTime: 0
      });

      // Reset visualization state
      setCurrentIndex(-1);
      setTargetIndex(-1);
      setCurrentElement(null);
      setCountArray([]);
      setOutputArray([]);
      setSorted([]);

      // Apply the initial step
      if (stepsRef.current && stepsRef.current.length > 0) {
        applyStep(0);
      }

      return;
    }

    // Only proceed if state is 'running'
    if (state !== 'running') {
      return;
    }

    const steps = stepsRef.current || [];

    // Stop if we've reached the end
    if (step >= steps.length) {
      setState('completed');
      return;
    }

    // Get the current step
    const currentStep = steps[step];

    // Don't schedule next step if we're animating
    if (animatingRef.current) {
      return;
    }

    // If the current step involves animation, set the animation flag and handle it
    if (currentStep && currentStep.type === 'placing') {
      animatingRef.current = true;

      // Apply the step which will start the animation
      applyStep(step);
      return;
    }

    // Schedule the next step with a delay
    const timeoutId = setTimeout(() => {
      // Only increment if still in running state
      if (stateRef.current === 'running' && !animatingRef.current) {
        console.log('Scheduling next step from', step, 'current step type:', currentStep?.type);
        setStep(prevStep => prevStep + 1);
      } else {
        console.log('Not advancing step. Running:', stateRef.current === 'running', 'Animating:', animatingRef.current);
      }
    }, getDelay(speedRef.current));

    // Store the timeout ID for cleanup
    timeoutIdRef.current = timeoutId;

    // Cleanup function
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }
    };
  }, [state, step, setStep, setState]);

  // Animation frame for move animation
  useFrame(() => {
    if (moveAnimation.active) {
      // Use performance.now() for more accurate timing
      const now = performance.now();
      const elapsed = now - moveAnimation.startTime;
      const duration = getAnimationDuration(speedRef.current);
      const progress = Math.min(elapsed / duration, 1);

      // Use requestAnimationFrame to schedule the state update off the main thread
      // Only update if there's a meaningful change to reduce unnecessary renders
      if (Math.abs(progress - moveAnimation.progress) > 0.01) {
        requestAnimationFrame(() => {
          setMoveAnimation(prev => ({
            ...prev,
            progress
          }));
        });
      }

      // If animation is complete, mark it as inactive
      if (progress >= 1) {
        // Small delay to ensure the animation completes visually
        setTimeout(() => {
          setMoveAnimation(prev => ({
            ...prev,
            active: false
          }));
          animatingRef.current = false;

          // If we're in running state, schedule the next step
          if (stateRef.current === 'running') {
            setTimeout(() => {
              if (stateRef.current === 'running') {
                // Ensure we're advancing to the next step
                setStep(prevStep => {
                  console.log('Advancing from step', prevStep, 'to', prevStep + 1);
                  return prevStep + 1;
                });
              }
            }, 50); // Small delay before next step
          }
        }, 50);
      }
    }
  });

  // Calculate maximum value for scaling
  const maxValue = useMemo(() => {
    if (!arrayData || arrayData.length === 0) return 1;
    return Math.max(...arrayData);
  }, [arrayData]);

  // Calculate adaptive width and spacing based on array size
  const adaptiveWidth = Math.max(0.2, BAR_WIDTH - (arrayData.length * 0.01));
  const adaptiveSpacing = Math.max(0.1, BAR_SPACING - (arrayData.length * 0.01));

  // Calculate total width of all bars
  const totalWidth = (adaptiveWidth + adaptiveSpacing) * arrayData.length;

  // Calculate starting X position to center the array
  const startX = -(totalWidth / 2) + (adaptiveWidth / 2);

  // Get the current step data for the step board
  const currentStepData = useMemo(() => {
    if (step >= 0 && step < stepsRef.current?.length) {
      return stepsRef.current[step];
    }
    return null;
  }, [step]);

  // Render the visualization
  return (
    <group position={[0, -2, 0]}> {/* Lower the entire scene */}
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight
        position={[10, 10, 10]}
        intensity={0.8}
        castShadow
      />
      <directionalLight position={[-10, 10, -10]} intensity={0.4} />
      <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

      {/* Ground plane */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -BASE_HEIGHT - 0.3, 0]} receiveShadow>
        <planeGeometry args={[100, 100]} />
        <meshStandardMaterial color={colors.ground} roughness={0.8} metalness={0.2} />
      </mesh>

      {/* Base */}
      <Base width={Math.max(totalWidth, 12) + 2} color={colors.base} />

      {/* Step Board - positioned at the top center */}
      <StepBoard currentStep={currentStepData} position={[0, 6, 0.5]} />

      {/* Color Legend - positioned below the bars */}
      <ColorLegend colors={colors} position={[0, -3, 0.5]} />

      {/* Count Array - positioned above the bars */}
      {countArray.length > 0 && (
        <CountArray
          counts={countArray}
          position={[0, 3, 0]}
          width={Math.min(12, countArray.length * 0.8)}
          height={0.8}
          currentIndex={currentElement !== null ? currentElement : -1}
          theme={theme}
        />
      )}

      {/* Output Array - positioned below the bars */}
      {outputArray.length > 0 && (
        <OutputArray
          array={outputArray}
          position={[0, -1.5, 0]}
          width={Math.min(12, outputArray.length * 0.8)}
          height={0.8}
          currentIndex={targetIndex}
          theme={theme}
        />
      )}

      {/* Bars */}
      {arrayData.map((value, index) => {
        // Calculate bar properties
        const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

        // Calculate position with animation
        let x = startX + index * (adaptiveWidth + adaptiveSpacing);
        let y = 0;
        let z = 0;

        // Apply animation for items being moved
        if (moveAnimation.active && index === moveAnimation.fromIndex) {
          // Moving from array to output array
          const { progress } = moveAnimation;
          const easedProgress = Math.sin(progress * Math.PI / 2); // Eased movement

          // Calculate target position in output array
          const outputStartX = -(Math.min(12, outputArray.length * 0.8) / 2) + 0.4;
          const targetX = outputStartX + moveAnimation.toIndex * 0.8;
          const targetY = -1.5;

          // Calculate position along the path
          x = x + (targetX - x) * easedProgress;
          y = y + (targetY - y) * easedProgress;

          // Add arc motion
          const midPoint = progress < 0.5 ? progress * 2 : (1 - progress) * 2;
          y += 2 * midPoint; // Arc height

          // Move slightly forward in z
          z = 0.5 * midPoint;
        }

        // Determine bar color based on current state
        let barColor = colors.bar; // Default color
        let showArrow = false; // Flag to show arrow indicator

        // Use a priority system for coloring and arrow indicators
        if (sorted.includes(index)) {
          barColor = colors.sorted;
        } else if (moveAnimation.active && index === moveAnimation.fromIndex) {
          barColor = colors.placing;
          showArrow = true;
        } else if (index === currentIndex) {
          barColor = colors.current;
          showArrow = true;
        } else if (currentElement !== null && value === currentElement) {
          barColor = colors.counting;
          showArrow = true;
        }

        // Return the bar component
        return (
          <Bar
            key={index}
            position={[x, y, z]}
            height={normalizedHeight}
            width={adaptiveWidth}
            color={barColor}
            value={value}
            index={index}
            showValue={arrayData.length <= 15}
            showArrow={showArrow}
          />
        );
      })}
    </group>
  );
};

export default CountingSortVisualization;

