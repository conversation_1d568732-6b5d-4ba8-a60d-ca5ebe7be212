// BubbleSort/index.js
// Export only what's needed from this algorithm

import BubbleSortVisualization from './BubbleSortVisualization';
import BubbleSortController from './BubbleSortController';
import BubbleSortAlgorithm from './BubbleSortAlgorithm';

export const metadata = {
  id: 'BubbleSort',
  name: 'Bubble Sort',
  description: 'A simple sorting algorithm that repeatedly steps through the list, compares adjacent elements and swaps them if they are in the wrong order.',
  timeComplexity: 'O(n²)',
  spaceComplexity: 'O(1)',
  defaultParams: {
    arraySize: 10,
    randomize: true,
    customArray: [],
  },
};

export const components = {
  visualization: BubbleSortVisualization,
  controller: BubbleSortController,
  algorithm: BubbleSortAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
