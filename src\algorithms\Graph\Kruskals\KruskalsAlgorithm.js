// KruskalsAlgorithm.js
// Implementation of <PERSON><PERSON><PERSON>'s algorithm with step generation

// No imports needed

/**
 * Generate a random weighted graph
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @param {number} minWeight - Minimum edge weight
 * @param {number} maxWeight - Maximum edge weight
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateRandomGraph = (numNodes, density = 0.7, minWeight = 1, maxWeight = 10) => {
  console.log(`Generating random graph with ${numNodes} nodes, density ${density}, weights ${minWeight}-${maxWeight}`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // First, create a minimum spanning tree to ensure the graph is connected
  const edges = [];
  let edgeId = 0;

  // Create a set of nodes that are already in the MST
  const inMST = new Set([0]);

  // Add n-1 edges to connect all nodes
  while (inMST.size < numNodes) {
    // Pick a random node from the MST
    const fromNode = Array.from(inMST)[Math.floor(Math.random() * inMST.size)];

    // Pick a random node not in the MST
    const notInMST = Array.from(Array(numNodes).keys()).filter(n => !inMST.has(n));
    const toNode = notInMST[Math.floor(Math.random() * notInMST.length)];

    // Add an edge between them
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source: fromNode,
      target: toNode,
      weight
    });

    // Add the new node to the MST
    inMST.add(toNode);
  }

  console.log(`Created minimum spanning tree with ${edges.length} edges`);

  // Now add additional edges based on density
  // Calculate how many additional edges to add (0 to maxAdditional)
  const maxPossibleEdges = (numNodes * (numNodes - 1)) / 2;
  const maxAdditional = Math.min(10 - edges.length, maxPossibleEdges - edges.length);
  const additionalEdges = Math.floor(maxAdditional * density);

  console.log(`Adding ${additionalEdges} additional edges based on density ${density}`);

  // Create a set of existing edges
  const existingEdges = new Set();
  edges.forEach(edge => {
    const minNode = Math.min(edge.source, edge.target);
    const maxNode = Math.max(edge.source, edge.target);
    existingEdges.add(`${minNode}-${maxNode}`);
  });

  // Create a list of possible additional edges
  const possibleAdditionalEdges = [];
  for (let i = 0; i < numNodes; i++) {
    for (let j = i + 1; j < numNodes; j++) {
      const edgeKey = `${i}-${j}`;
      if (!existingEdges.has(edgeKey)) {
        possibleAdditionalEdges.push([i, j]);
      }
    }
  }

  // Shuffle the possible additional edges
  for (let i = possibleAdditionalEdges.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [possibleAdditionalEdges[i], possibleAdditionalEdges[j]] = [possibleAdditionalEdges[j], possibleAdditionalEdges[i]];
  }

  // Add the additional edges
  for (let i = 0; i < additionalEdges && i < possibleAdditionalEdges.length; i++) {
    const [source, target] = possibleAdditionalEdges[i];
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source,
      target,
      weight
    });
  }

  console.log(`Final graph has ${edges.length} edges (${numNodes - 1} for MST + ${edges.length - (numNodes - 1)} additional)`);

  return { nodes, edges };
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edgeList - Array of edges [from, to, weight]
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateCustomGraph = (numNodes, edgeList) => {
  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges from the edge list
  const edges = [];

  edgeList.forEach(([source, target, weight], index) => {
    if (source >= 0 && source < numNodes && target >= 0 && target < numNodes) {
      edges.push({
        id: index,
        source,
        target,
        weight
      });
    }
  });

  return { nodes, edges };
};

/**
 * Disjoint Set (Union-Find) data structure
 */
class DisjointSet {
  constructor(size) {
    this.parent = Array(size).fill().map((_, i) => i);
    this.rank = Array(size).fill(0);
  }

  // Find the representative of the set that x is an element of
  find(x) {
    if (this.parent[x] !== x) {
      this.parent[x] = this.find(this.parent[x]); // Path compression
    }
    return this.parent[x];
  }

  // Union the sets that x and y belong to
  union(x, y) {
    const rootX = this.find(x);
    const rootY = this.find(y);

    if (rootX === rootY) return false; // Already in the same set

    // Union by rank
    if (this.rank[rootX] < this.rank[rootY]) {
      this.parent[rootX] = rootY;
    } else if (this.rank[rootX] > this.rank[rootY]) {
      this.parent[rootY] = rootX;
    } else {
      this.parent[rootY] = rootX;
      this.rank[rootX]++;
    }

    return true;
  }

  // Get all sets as arrays of elements
  getSets() {
    const sets = {};

    for (let i = 0; i < this.parent.length; i++) {
      const root = this.find(i);
      if (!sets[root]) {
        sets[root] = [];
      }
      sets[root].push(i);
    }

    return Object.values(sets);
  }
}

/**
 * Generate steps for Kruskal's algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateKruskalsSteps = (params) => {
  console.log('generateKruskalsSteps called with params:', params);
  const { nodes: numNodes, density, minWeight, maxWeight, customEdges } = params;
  const steps = [];

  // Generate graph
  let graph;
  if (customEdges && customEdges.length > 0) {
    console.log('Generating custom graph with edges:', customEdges);
    graph = generateCustomGraph(numNodes, customEdges);
  } else {
    console.log('Generating random graph with params:', { numNodes, density, minWeight, maxWeight });
    graph = generateRandomGraph(numNodes, density, minWeight, maxWeight);
  }
  console.log('Generated graph:', graph);

  const { nodes, edges } = graph;

  // Add initial step
  steps.push({
    type: 'initialize',
    message: 'Initialize Kruskal\'s algorithm. Sort all edges by weight.',
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    currentEdge: null,
    sortedEdges: null,
    disjointSets: null,
    rejected: false,
    progressStep: 'initialize',
    pseudocodeLine: 1 // Line number in pseudocode
  });

  // Sort edges by weight
  const sortedEdges = [...edges].sort((a, b) => a.weight - b.weight);

  // Add step after sorting
  steps.push({
    type: 'sort',
    message: 'Edges sorted by weight in ascending order.',
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    currentEdge: null,
    sortedEdges: [...sortedEdges],
    disjointSets: null,
    rejected: false,
    progressStep: 'sort',
    pseudocodeLine: 11 // Sort edges by weight
  });

  // Initialize disjoint set
  const disjointSet = new DisjointSet(nodes.length);

  // Add step for disjoint set initialization
  steps.push({
    type: 'disjoint_init',
    message: 'Initialize disjoint sets. Each node is in its own set.',
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    currentEdge: null,
    sortedEdges: [...sortedEdges],
    disjointSets: disjointSet.getSets(),
    rejected: false,
    progressStep: 'process',
    pseudocodeLine: 3 // Initialize disjoint set
  });

  // MST edges
  const mst = [];

  // Process each edge in sorted order
  for (const edge of sortedEdges) {
    const { source, target, weight } = edge;

    // Add step for considering an edge
    steps.push({
      type: 'consider',
      message: `Consider edge ${source} → ${target} with weight ${weight}.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      mst: [...mst],
      currentEdge: edge,
      sortedEdges: [...sortedEdges],
      disjointSets: disjointSet.getSets(),
      rejected: false,
      progressStep: 'process',
      pseudocodeLine: 17 // Process edges in sorted order
    });

    // Check if adding the edge creates a cycle
    const sourceRoot = disjointSet.find(source);
    const targetRoot = disjointSet.find(target);

    if (sourceRoot !== targetRoot) {
      // No cycle, add to MST
      mst.push(edge);
      disjointSet.union(source, target);

      // Add step for adding edge to MST
      steps.push({
        type: 'add_to_mst',
        message: `Add edge ${source} → ${target} to MST. No cycle is formed.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        currentEdge: edge,
        sortedEdges: [...sortedEdges],
        disjointSets: disjointSet.getSets(),
        rejected: false,
        progressStep: 'process',
        pseudocodeLine: 21 // Add edge to MST
      });
    } else {
      // Adding this edge would create a cycle
      steps.push({
        type: 'reject',
        message: `Reject edge ${source} → ${target}. It would create a cycle.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        currentEdge: edge,
        sortedEdges: [...sortedEdges],
        disjointSets: disjointSet.getSets(),
        rejected: true,
        progressStep: 'process',
        pseudocodeLine: 19 // Check if adding edge creates a cycle
      });
    }

    // Check if MST is complete (n-1 edges)
    if (mst.length === nodes.length - 1) {
      steps.push({
        type: 'early_complete',
        message: `MST is complete with ${mst.length} edges. Algorithm can terminate early.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        currentEdge: null,
        sortedEdges: [...sortedEdges],
        disjointSets: disjointSet.getSets(),
        rejected: false,
        progressStep: 'complete',
        pseudocodeLine: 25 // Return MST
      });
      break;
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Kruskal's algorithm complete. MST has ${mst.length} edges with total weight ${mst.reduce((sum, edge) => sum + edge.weight, 0)}.`,
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [...mst],
    currentEdge: null,
    sortedEdges: [...sortedEdges],
    disjointSets: disjointSet.getSets(),
    rejected: false,
    progressStep: 'complete',
    pseudocodeLine: 25 // Return MST
  });

  console.log('Generated steps:', steps.length, 'steps');
  const result = {
    steps,
    graph,
    mst
  };
  console.log('Returning result:', result);
  return result;
};

// End of file

// Export the algorithm functions
export default {
  generateKruskalsSteps,
  generateRandomGraph,
  generateCustomGraph
};
