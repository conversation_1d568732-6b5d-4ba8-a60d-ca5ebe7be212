// TopologicalSortAlgorithm.js
// Implementation of Topological Sort algorithm with step generation

/**
 * Generate a random directed acyclic graph (DAG)
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateRandomDAG = (numNodes, density = 0.5) => {
  console.log(`Generating random DAG with ${numNodes} nodes, density ${density}`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges (ensuring acyclicity by only adding edges from lower to higher node IDs)
  const edges = [];
  let edgeId = 0;

  for (let i = 0; i < numNodes; i++) {
    for (let j = i + 1; j < numNodes; j++) { // Only add edges from lower to higher IDs to ensure DAG
      if (Math.random() < density) {
        edges.push({
          id: edgeId++,
          source: i,
          target: j
        });
      }
    }
  }

  // Create adjacency list
  const adjList = {};
  nodes.forEach(node => {
    adjList[node.id] = [];
  });

  edges.forEach(edge => {
    adjList[edge.source].push({
      node: edge.target,
      edgeId: edge.id
    });
  });

  return { nodes, edges, adjList };
};

/**
 * Generate a custom graph from edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} customEdges - Array of custom edges [source, target]
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateCustomGraph = (numNodes, customEdges) => {
  console.log(`Generating custom graph with ${numNodes} nodes and ${customEdges.length} edges`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges
  const edges = [];
  customEdges.forEach(([source, target], index) => {
    if (source >= 0 && source < numNodes && target >= 0 && target < numNodes) {
      edges.push({
        id: index,
        source,
        target
      });
    }
  });

  // Create adjacency list
  const adjList = {};
  nodes.forEach(node => {
    adjList[node.id] = [];
  });

  edges.forEach(edge => {
    adjList[edge.source].push({
      node: edge.target,
      edgeId: edge.id
    });
  });

  return { nodes, edges, adjList };
};

/**
 * Check if the graph has a cycle
 * @param {Object} graph - Graph representation with adjacency list
 * @returns {boolean} - True if the graph has a cycle, false otherwise
 */
export const hasCycle = (graph) => {
  const { nodes, adjList } = graph;
  const visited = new Set();
  const recStack = new Set();

  const dfs = (nodeId) => {
    // Mark the current node as visited and add to recursion stack
    visited.add(nodeId);
    recStack.add(nodeId);

    // Check all neighbors
    for (const neighbor of adjList[nodeId]) {
      // If not visited, recursively check
      if (!visited.has(neighbor.node)) {
        if (dfs(neighbor.node)) {
          return true;
        }
      }
      // If the neighbor is in recursion stack, we found a cycle
      else if (recStack.has(neighbor.node)) {
        return true;
      }
    }

    // Remove from recursion stack
    recStack.delete(nodeId);
    return false;
  };

  // Check all nodes
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Generate steps for Topological Sort algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateTopologicalSortSteps = (params) => {
  console.log('generateTopologicalSortSteps called with params:', params);
  const { nodes: numNodes = 6, density = 0.5, customEdges = [] } = params;
  const steps = [];

  // Generate graph
  let graph;
  if (customEdges && customEdges.length > 0) {
    console.log('Generating custom graph with edges:', customEdges);
    graph = generateCustomGraph(numNodes, customEdges);
  } else {
    console.log('Generating random graph with params:', { numNodes, density });
    graph = generateRandomDAG(numNodes, density);
  }

  const { nodes, edges, adjList } = graph;

  // Check if the graph has a cycle (for custom graphs)
  if (customEdges && customEdges.length > 0) {
    const hasCycleResult = hasCycle(graph);
    if (hasCycleResult) {
      steps.push({
        type: 'error',
        message: 'The graph contains a cycle. Topological sort requires a directed acyclic graph (DAG).',
        graph: { nodes: [...nodes], edges: [...edges] },
        visited: [],
        stack: [],
        sorted: [],
        current: null,
        progressStep: 'error',
        pseudocodeLine: 0
      });

      return {
        steps,
        graph,
        sorted: []
      };
    }
  }

  // Add initial step
  steps.push({
    type: 'init',
    message: 'Initialize topological sort algorithm.',
    graph: { nodes: [...nodes], edges: [...edges] },
    visited: [],
    stack: [],
    sorted: [],
    current: null,
    progressStep: 'init',
    pseudocodeLine: 1 // Initialize data structures
  });

  // Perform topological sort using DFS
  const visited = new Set();
  const stack = []; // For topological sort
  const sorted = []; // Final topological order

  // DFS function for topological sort
  const dfs = (nodeId) => {
    // Add step for visiting node
    steps.push({
      type: 'visit',
      message: `Visit node ${nodeId}.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      visited: Array.from(visited),
      stack: [...stack],
      sorted: [...sorted],
      current: nodeId,
      progressStep: 'process',
      pseudocodeLine: 8 // Visit node
    });

    // Mark the current node as visited
    visited.add(nodeId);

    // Add step for marking node as visited
    steps.push({
      type: 'mark_visited',
      message: `Mark node ${nodeId} as visited.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      visited: Array.from(visited),
      stack: [...stack],
      sorted: [...sorted],
      current: nodeId,
      progressStep: 'process',
      pseudocodeLine: 9 // Mark as visited
    });

    // Visit all neighbors
    for (const neighbor of adjList[nodeId]) {
      // Add step for checking neighbor
      steps.push({
        type: 'check_neighbor',
        message: `Check neighbor ${neighbor.node} of node ${nodeId}.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        visited: Array.from(visited),
        stack: [...stack],
        sorted: [...sorted],
        current: nodeId,
        neighbor: neighbor.node,
        progressStep: 'process',
        pseudocodeLine: 11 // For each neighbor
      });

      if (!visited.has(neighbor.node)) {
        // Add step for recursively visiting neighbor
        steps.push({
          type: 'recurse',
          message: `Recursively visit neighbor ${neighbor.node}.`,
          graph: { nodes: [...nodes], edges: [...edges] },
          visited: Array.from(visited),
          stack: [...stack],
          sorted: [...sorted],
          current: nodeId,
          neighbor: neighbor.node,
          progressStep: 'process',
          pseudocodeLine: 13 // Recursive call
        });

        // Recursively visit neighbor
        dfs(neighbor.node);
      } else {
        // Add step for skipping already visited neighbor
        steps.push({
          type: 'skip',
          message: `Skip already visited neighbor ${neighbor.node}.`,
          graph: { nodes: [...nodes], edges: [...edges] },
          visited: Array.from(visited),
          stack: [...stack],
          sorted: [...sorted],
          current: nodeId,
          neighbor: neighbor.node,
          progressStep: 'process',
          pseudocodeLine: 12 // If not visited
        });
      }
    }

    // Add node to stack (will be in reverse topological order)
    stack.push(nodeId);

    // Add step for adding node to stack
    steps.push({
      type: 'add_to_stack',
      message: `Add node ${nodeId} to stack.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      visited: Array.from(visited),
      stack: [...stack],
      sorted: [...sorted],
      current: nodeId,
      progressStep: 'process',
      pseudocodeLine: 16 // Add to stack
    });
  };

  // Visit all nodes
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      // Add step for starting DFS from unvisited node
      steps.push({
        type: 'start_dfs',
        message: `Start DFS from unvisited node ${node.id}.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        visited: Array.from(visited),
        stack: [...stack],
        sorted: [...sorted],
        current: null,
        progressStep: 'process',
        pseudocodeLine: 5 // For each node
      });

      // Perform DFS from this node
      dfs(node.id);
    }
  }

  // Reverse the stack to get topological order
  while (stack.length > 0) {
    const node = stack.pop();
    sorted.push(node);

    // Add step for popping from stack
    steps.push({
      type: 'pop_stack',
      message: `Pop node ${node} from stack and add to sorted order.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      visited: Array.from(visited),
      stack: [...stack],
      sorted: [...sorted],
      current: node,
      progressStep: 'process',
      pseudocodeLine: 20 // Reverse stack
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Topological sort complete. Order: [${sorted.join(', ')}]`,
    graph: { nodes: [...nodes], edges: [...edges] },
    visited: Array.from(visited),
    stack: [],
    sorted: [...sorted],
    current: null,
    progressStep: 'complete',
    pseudocodeLine: 22 // Return sorted
  });

  console.log('Generated steps:', steps.length, 'steps');
  const result = {
    steps,
    graph,
    sorted
  };
  console.log('Returning result:', result);
  return result;
};

// Export the algorithm functions
export default {
  generateTopologicalSortSteps,
  generateRandomDAG,
  generateCustomGraph,
  hasCycle
};
