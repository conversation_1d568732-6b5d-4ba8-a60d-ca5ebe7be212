// TextField.js
// A reusable text field component with consistent styling

import React from 'react';
import { TextField as MuiTextField } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable text field component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string|node} props.label - Field label (can be a string or a React node)
 * @param {string} props.value - Field value
 * @param {function} props.onChange - Change handler
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.fullWidth - Whether the field should take full width
 * @param {string} props.size - Field size (small, medium)
 * @param {string} props.variant - Field variant (outlined, filled, standard)
 * @param {boolean} props.required - Whether the field is required
 * @param {string} props.helperText - Helper text to display
 * @param {boolean} props.error - Whether the field has an error
 * @param {Object} props.sx - Additional styles
 */
const TextField = ({
  label,
  value,
  onChange,
  placeholder = '',
  fullWidth = true,
  size = 'medium',
  variant = 'outlined',
  required = false,
  helperText = '',
  error = false,
  sx = {},
  ...rest
}) => {
  const handleChange = (event) => {
    onChange(event.target.value);
  };

  return (
    <MuiTextField
      label={label}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      fullWidth={fullWidth}
      size={size}
      variant={variant}
      required={required}
      helperText={helperText}
      error={error}
      sx={{
        ...sx
      }}
      {...rest}
    />
  );
};

TextField.propTypes = {
  label: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node
  ]).isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  variant: PropTypes.oneOf(['outlined', 'filled', 'standard']),
  required: PropTypes.bool,
  helperText: PropTypes.string,
  error: PropTypes.bool,
  sx: PropTypes.object
};

export default TextField;
