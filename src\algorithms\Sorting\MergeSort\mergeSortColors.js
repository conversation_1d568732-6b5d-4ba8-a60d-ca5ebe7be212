// mergeSortColors.js - Centralized color configuration for Merge Sort visualization

/**
 * Get the appropriate color for a bar based on its state in the merge sort algorithm
 * @param {number} index - The index of the bar
 * @param {Object} currentStep - The current step of the algorithm
 * @param {boolean} shouldShowColoredBars - Whether colored bars should be shown
 * @param {Array} coloredIndices - Array of indices that should be colored
 * @param {Object} colors - The colors object from getAlgorithmColors
 * @returns {string} The color to use for the bar
 */
export const getBarColor = (index, currentStep, shouldShowColoredBars, coloredIndices, colors) => {
  // Default color for bars not being operated on
  if (!shouldShowColoredBars || !coloredIndices.includes(index)) {
    return colors.bar;
  }

  // Apply colors based on the current step type
  switch (currentStep.type) {
    case 'create_temp_arrays':
      // Get the mid point to determine left vs right subarray
      const mid = currentStep.tempArrays?.mid ||
        (currentStep.splitInfo?.mid) ||
        (currentStep.indices && Math.floor((currentStep.indices[0] + currentStep.indices[1]) / 2));

      // If this bar is in the left subarray, use the left color
      if (index <= mid) {
        return colors.tempStructure.left; // Left subarray (distinct blue)
      }
      // If this bar is in the right subarray, use the right color
      else {
        return colors.tempStructure.right; // Right subarray (distinct orange)
      }

    case 'merge':
    case 'split':
    case 'copy_back':
      return colors.merging;

    case 'compare':
      return colors.comparing;

    case 'place_from_left_subarray':
      return colors.tempStructure.left; // Left subarray (distinct blue)

    case 'place_from_right_subarray':
      return colors.tempStructure.right; // Right subarray (distinct orange)

    case 'create_result_array':
    case 'result_array_complete':
      return colors.tempStructure.result; // Result array (green)

    default:
      return colors.merging;
  }
};

/**
 * Get the color for a temporary array bar
 * @param {string} arrayType - The type of array ('left', 'right', or 'result')
 * @param {Object} colors - The colors object from getAlgorithmColors
 * @returns {string} The color to use for the temporary array bar
 */
export const getTempArrayColor = (arrayType, colors) => {
  switch (arrayType) {
    case 'left':
      return colors.tempStructure.left; // Left subarray (distinct blue)
    case 'right':
      return colors.tempStructure.right; // Right subarray (distinct orange)
    case 'result':
      return colors.tempStructure.result; // Result array (green)
    default:
      return colors.bar; // Default color
  }
};

export default {
  getBarColor,
  getTempArrayColor
};
