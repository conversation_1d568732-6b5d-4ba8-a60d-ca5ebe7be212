// PermutationsController.js
// This component provides the controls for the Permutations algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, TextField, Switch } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import TextFormatIcon from '@mui/icons-material/TextFormat';
import SettingsIcon from '@mui/icons-material/Settings';

// Import algorithm functions
import { generatePermutationsSteps } from './PermutationsAlgorithm';

const PermutationsController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [elements, setElements] = useState(params?.elements || ['A', 'B', 'C']);
  const [useNumbers, setUseNumbers] = useState(params?.useNumbers || false);
  const [inputType, setInputType] = useState('text');
  const [inputValue, setInputValue] = useState(elements.join(', '));
  const [numElements, setNumElements] = useState(params?.numElements || 3);

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Parse input elements
    let parsedElements;
    if (inputType === 'text') {
      parsedElements = inputValue.split(',').map(item => item.trim()).filter(item => item !== '');
    } else {
      parsedElements = Array.from({ length: numElements }, (_, i) => String.fromCharCode(65 + i));
    }

    // Limit to 8 elements to prevent performance issues
    if (parsedElements.length > 8) {
      parsedElements = parsedElements.slice(0, 8);
    }

    // Update elements state
    setElements(parsedElements);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { elements: parsedElements, useNumbers });

    // Update params first
    onParamChange({
      elements: parsedElements,
      useNumbers
    });

    // Set steps and movements directly
    try {
      const result = generatePermutationsSteps({
        elements: parsedElements,
        useNumbers
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [inputType, inputValue, numElements, useNumbers, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle input type change
  const handleInputTypeChange = useCallback((value) => {
    setInputType(value);

    // Update input value based on the new input type
    if (value === 'text') {
      setInputValue(elements.join(', '));
    } else {
      setNumElements(elements.length);
    }
  }, [elements]);

  // Handle input value change
  const handleInputValueChange = useCallback((event) => {
    setInputValue(event.target.value);
  }, []);

  // Handle number of elements change
  const handleNumElementsChange = useCallback((value) => {
    const num = parseInt(value, 10);
    if (num >= 1 && num <= 8) {
      setNumElements(num);
    }
  }, []);

  // Handle use numbers toggle
  const handleUseNumbersToggle = useCallback((event) => {
    setUseNumbers(event.target.checked);
  }, []);

  // Handle apply button click
  const handleApplyClick = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Permutations algorithm
  const pseudocode = [
    { code: "function generatePermutations(elements, currentPermutation, used):", lineNumber: 1, indent: 0 },
    { code: "    // If the current permutation is complete", lineNumber: 2, indent: 0 },
    { code: "    if currentPermutation.length == elements.length:", lineNumber: 3, indent: 1 },
    { code: "        // Add the permutation to the result list", lineNumber: 4, indent: 0 },
    { code: "        permutations.add(currentPermutation.copy())", lineNumber: 5, indent: 2 },
    { code: "        return", lineNumber: 6, indent: 2 },
    { code: "    // Try each element that hasn't been used yet", lineNumber: 7, indent: 0 },
    { code: "    for i = 0 to elements.length - 1:", lineNumber: 8, indent: 1 },
    { code: "        if i not in used:", lineNumber: 9, indent: 2 },
    { code: "            // Add this element to the current permutation", lineNumber: 10, indent: 0 },
    { code: "            currentPermutation.push(elements[i])", lineNumber: 11, indent: 3 },
    { code: "            used.add(i)", lineNumber: 12, indent: 3 },
    { code: "            // Recursively generate permutations with this element added", lineNumber: 13, indent: 0 },
    { code: "            generatePermutations(elements, currentPermutation, used)", lineNumber: 14, indent: 3 },
    { code: "            // Backtrack: remove the element and mark it as unused", lineNumber: 15, indent: 0 },
    { code: "            used.remove(i)", lineNumber: 16, indent: 3 },
    { code: "            currentPermutation.pop()", lineNumber: 17, indent: 3 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Custom component for input type selection
  const InputTypeSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Input Type</Typography>
        <RadioGroup
          row
          value={value}
          onChange={(e) => onChange(e.target.value)}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          <FormControlLabel
            value="text"
            control={<Radio size="small" />}
            label="Custom Elements"
            disabled={disabled}
          />
          <FormControlLabel
            value="letters"
            control={<Radio size="small" />}
            label="Letters (A, B, C, ...)"
            disabled={disabled}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for elements input
  const ElementsInput = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Elements (comma-separated)</Typography>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          disabled={disabled}
          placeholder="A, B, C"
          helperText="Maximum 8 elements to prevent performance issues"
        />
      </Box>
    );
  };

  // Custom component for number of elements selection
  const NumElementsSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Number of Elements</Typography>
        <RadioGroup
          row
          value={value.toString()}
          onChange={(e) => onChange(e.target.value)}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          {[2, 3, 4, 5, 6, 7, 8].map(num => (
            <FormControlLabel
              key={num}
              value={num.toString()}
              control={<Radio size="small" />}
              label={num}
              disabled={disabled}
            />
          ))}
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for use numbers toggle
  const UseNumbersToggle = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={value}
              onChange={onChange}
              disabled={disabled}
              color="primary"
            />
          }
          label="Use Numbers Instead of Letters/Elements"
        />
      </Box>
    );
  };

  // No custom results components - visualization will handle this

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Generate Permutations"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Permutations algorithm generates all possible arrangements of a given set of elements.
            For a set of n elements, there are n! (n factorial) different permutations.
            This algorithm uses backtracking to systematically generate all permutations.
          </Typography>
          <Typography variant="body2" gutterBottom>
            The algorithm follows these steps:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2">Start with an empty permutation and a set of unused elements.</Typography>
            </li>
            <li>
              <Typography variant="body2">If the current permutation is complete (contains all elements), add it to the result.</Typography>
            </li>
            <li>
              <Typography variant="body2">Otherwise, try adding each unused element to the current permutation.</Typography>
            </li>
            <li>
              <Typography variant="body2">For each element, mark it as used and recursively generate permutations with this element added.</Typography>
            </li>
            <li>
              <Typography variant="body2">After the recursive call, backtrack by removing the element and marking it as unused.</Typography>
            </li>
            <li>
              <Typography variant="body2">Continue until all permutations are generated.</Typography>
            </li>
          </ol>
          <Typography variant="body2" paragraph>
            For example, the permutations of [A, B, C] are:
            [A, B, C], [A, C, B], [B, A, C], [B, C, A], [C, A, B], [C, B, A]
          </Typography>
          <Typography variant="body2" paragraph>
            The time complexity is O(n!) where n is the number of elements, as we generate all n! permutations.
            The space complexity is O(n) for the recursion stack and to store each permutation.
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'inputType',
            type: 'component',
            label: 'Input Type',
            component: InputTypeSelector,
            componentProps: {
              value: inputType,
              onChange: handleInputTypeChange,
              disabled: state !== 'idle'
            },
            icon: SettingsIcon
          },
          ...(inputType === 'text' ? [
            {
              name: 'elements',
              type: 'component',
              label: 'Elements',
              component: ElementsInput,
              componentProps: {
                value: inputValue,
                onChange: handleInputValueChange,
                disabled: state !== 'idle'
              },
              icon: TextFormatIcon
            }
          ] : [
            {
              name: 'numElements',
              type: 'component',
              label: 'Number of Elements',
              component: NumElementsSelector,
              componentProps: {
                value: numElements,
                onChange: handleNumElementsChange,
                disabled: state !== 'idle'
              },
              icon: FormatListNumberedIcon
            }
          ]),
          {
            name: 'useNumbers',
            type: 'component',
            label: 'Use Numbers',
            component: UseNumbersToggle,
            componentProps: {
              value: useNumbers,
              onChange: handleUseNumbersToggle,
              disabled: state !== 'idle'
            }
          }
        ]}
        values={{
          inputType,
          elements: inputType === 'text' ? inputValue : numElements,
          useNumbers
        }}
        disabled={state === 'running'}
        onApply={handleApplyClick}
        showApplyButton={true}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* No Results Display Section - visualization will handle this */}

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Permutations Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default PermutationsController;
