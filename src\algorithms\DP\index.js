// DP/index.js
// Export all dynamic programming algorithms

import LCS from './LCS';
import <PERSON><PERSON><PERSON><PERSON> from './<PERSON><PERSON><PERSON><PERSON>';
import Knapsack from './Knapsack';
import EditDistance from './EditDistance';
import MatrixChainMultiplication from './MatrixChainMultiplication';
import CoinChange from './CoinChange';
import LIS from './LIS';
import RodCutting from './RodCutting';

export {
    LCS,
    <PERSON>bon<PERSON>ci,
    Knapsack,
    EditDistance,
    MatrixChainMultiplication,
    CoinChange,
    LIS,
    RodCutting
};

export default {
    LCS,
    Fibonacci,
    Knapsack,
    EditDistance,
    MatrixChainMultiplication,
    CoinChange,
    LIS,
    RodCutting
};
