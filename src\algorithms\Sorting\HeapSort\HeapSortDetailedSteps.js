// HeapSortDetailedSteps.js
// Detailed step generation for HeapSort algorithm

/**
 * Generate detailed steps for Heap Sort algorithm visualization
 * @param {Array} array - The array to sort
 * @returns {Array} - Array of detailed steps for visualization
 */
export const generateDetailedSteps = (array) => {
  // Validate input
  if (!Array.isArray(array) || array.length === 0) {
    return [];
  }

  // Create a copy of the array to avoid modifying the original
  const arr = [...array];
  const steps = [];
  const sortedIndices = new Set();
  const heapifiedIndices = new Set();

  // Helper functions for heap operations
  const getParentIndex = (i) => Math.floor((i - 1) / 2);
  const getLeftChildIndex = (i) => 2 * i + 1;
  const getRightChildIndex = (i) => 2 * i + 2;

  // Helper function to create step (simplified)
  const createStep = (type, statement, mainArrayData) => {
    return {
      type,
      statement,
      visualizationData: { mainArray: mainArrayData }
    };
  };

  // Add initial step
  steps.push(createStep(
    'initial',
    `Heap Sort: Initial array [${arr.join(', ')}] - Ready to build max heap`,
    {
      values: [...arr],
      sortedIndices: [],
      heapifiedIndices: [],
      comparingIndices: [],
      swappingIndices: [],
    }
  ));

  // Heapify function (sift down)
  const heapify = (n, i) => {
    let largest = i; // Initialize largest as root
    const left = getLeftChildIndex(i);
    const right = getRightChildIndex(i);

    // Compare with left child
    if (left < n) {
      steps.push({
        type: 'compare',
        statement: `Compare parent at index ${i} (value ${arr[i]}) with left child at index ${left} (value ${arr[left]})`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: [...sortedIndices],
            heapifiedIndices: [...heapifiedIndices],
            comparingIndices: [i, left],
            swappingIndices: [],
          }
        }
      });

      if (arr[left] > arr[largest]) {
        largest = left;
      }
    }

    // Compare with right child
    if (right < n) {
      steps.push({
        type: 'compare',
        statement: `Compare ${largest === i ? 'parent' : 'left child'} at index ${largest} (value ${arr[largest]}) with right child at index ${right} (value ${arr[right]})`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: [...sortedIndices],
            heapifiedIndices: [...heapifiedIndices],
            comparingIndices: [largest, right],
            swappingIndices: [],
          }
        }
      });

      if (arr[right] > arr[largest]) {
        largest = right;
      }
    }

    // If largest is not root
    if (largest !== i) {
      // Create swap step (shows what will be swapped) - BEFORE swapping
      const originalArray = [...arr];
      steps.push(createStep(
        'swap',
        `Swap element at index ${i} (value ${arr[i]}) with element at index ${largest} (value ${arr[largest]})`,
        {
          values: originalArray,
          sortedIndices: [...sortedIndices],
          heapifiedIndices: [...heapifiedIndices],
          comparingIndices: [],
          swappingIndices: [i, largest],
        }
      ));

      // Swap elements in the actual array
      [arr[i], arr[largest]] = [arr[largest], arr[i]];

      // Create swapped step (shows result after swap) - AFTER swapping
      const swappedArray = [...arr];
      steps.push(createStep(
        'swapped',
        `Swapped element at index ${i} with element at index ${largest}`,
        {
          values: swappedArray,
          sortedIndices: [...sortedIndices],
          heapifiedIndices: [...heapifiedIndices],
          comparingIndices: [],
          swappingIndices: [],
        }
      ));

      // Recursively heapify the affected sub-tree
      heapify(n, largest);
    }
  };

  // Build heap (rearrange array)
  steps.push({
    type: 'info',
    statement: `Phase 1: Build max heap from bottom up (starting from index ${Math.floor(arr.length / 2) - 1})`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [...heapifiedIndices],
        comparingIndices: [],
        swappingIndices: [],
      }
    }
  });

  // Start from the last non-leaf node and heapify each node in reverse order
  for (let i = Math.floor(arr.length / 2) - 1; i >= 0; i--) {
    steps.push({
      type: 'info',
      statement: `Heapifying subtree rooted at index ${i}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          heapifiedIndices: [...heapifiedIndices],
          comparingIndices: [],
          swappingIndices: [],
        }
      }
    });

    heapify(arr.length, i);

    // Mark this node as heapified
    heapifiedIndices.add(i);

    steps.push({
      type: 'heapified',
      statement: `Subtree rooted at index ${i} is now heapified`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          heapifiedIndices: [...heapifiedIndices],
          comparingIndices: [],
          swappingIndices: [],
        }
      }
    });
  }

  // Heap is built
  steps.push({
    type: 'info',
    statement: `Max heap built successfully! Root element ${arr[0]} is the maximum value`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [...heapifiedIndices],
        comparingIndices: [],
        swappingIndices: [],
      }
    }
  });

  // Add phase 2 start
  steps.push({
    type: 'info',
    statement: `Phase 2: Extract elements from heap one by one (${arr.length - 1} extractions needed)`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [...heapifiedIndices],
        comparingIndices: [],
        swappingIndices: [],
      }
    }
  });

  // Extract elements from heap one by one
  for (let i = arr.length - 1; i > 0; i--) {
    // Create swap step (shows what will be swapped) - BEFORE swapping
    const originalArray = [...arr];
    steps.push(createStep(
      'swap',
      `Swap root (max element) at index 0 (value ${arr[0]}) with element at index ${i} (value ${arr[i]})`,
      {
        values: originalArray,
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [...heapifiedIndices],
        comparingIndices: [],
        swappingIndices: [0, i],
      }
    ));

    // Move current root to end
    [arr[0], arr[i]] = [arr[i], arr[0]];

    // Mark the element as sorted
    sortedIndices.add(i);

    // Create swapped step (shows result after swap) - AFTER swapping
    const swappedArray = [...arr];
    steps.push(createStep(
      'swapped',
      `Element ${arr[i]} is now in its correct sorted position at index ${i}`,
      {
        values: swappedArray,
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [...heapifiedIndices],
        comparingIndices: [],
        swappingIndices: [],
      }
    ));

    // Heapify the reduced heap
    steps.push({
      type: 'info',
      statement: `Heapifying reduced heap of size ${i}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          heapifiedIndices: [...heapifiedIndices],
          comparingIndices: [],
          swappingIndices: [],
        }
      }
    });

    heapify(i, 0);
  }

  // Mark the first element as sorted
  sortedIndices.add(0);

  steps.push({
    type: 'complete',
    statement: `Heap Sort completed! Array is now sorted: [${arr.join(', ')}]`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        heapifiedIndices: [],
        comparingIndices: [],
        swappingIndices: [],
      }
    }
  });

  return steps;
};
