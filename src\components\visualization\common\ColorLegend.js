import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable color legend component for algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the legend
 * @param {Object} props.colors - Object containing color values for different states
 * @param {Array} props.items - Array of legend items, each with color and label
 * @param {Object} props.theme - MUI theme object
 */
const ColorLegend = ({ position = [0, 0, 0], items = [], theme }) => {
  // Always call useTheme hook first to follow React rules
  const themeFromContext = useTheme();
  // Then use provided theme or the one from context
  const muiTheme = theme || themeFromContext;
  const isDark = muiTheme?.palette?.mode === 'dark';

  // Default styles based on theme
  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'row', // Display items in a row
      alignItems: 'center',
      justifyContent: 'center',
      padding: '10px 15px',
      borderRadius: '8px',
      backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
      boxShadow: isDark
        ? '0 4px 20px rgba(0, 0, 0, 0.5), 0 0 10px rgba(255, 255, 255, 0.1) inset'
        : '0 4px 20px rgba(0, 0, 0, 0.2), 0 0 10px rgba(255, 255, 255, 0.5) inset',
      border: isDark
        ? '1px solid rgba(255, 255, 255, 0.1)'
        : '1px solid rgba(0, 0, 0, 0.1)',
      minWidth: '300px',
      maxWidth: '90%',
      margin: '0 auto',
      userSelect: 'none',
      pointerEvents: 'none',
    },
    title: {
      margin: '0 0 5px 0',
      fontSize: '16px',
      fontWeight: 'bold',
      color: isDark ? '#ffffff' : '#000000',
      textAlign: 'center',
      textShadow: isDark
        ? '0 1px 3px rgba(0, 0, 0, 0.8)'
        : '0 1px 3px rgba(255, 255, 255, 0.8)',
    },
    itemsContainer: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      gap: '15px', // Space between items
    },
    item: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: '0',
    },
    colorBox: {
      width: '20px',
      height: '20px',
      borderRadius: '4px',
      marginRight: '8px',
      boxShadow: isDark
        ? '0 2px 4px rgba(0, 0, 0, 0.5), 0 0 2px rgba(255, 255, 255, 0.3) inset'
        : '0 2px 4px rgba(0, 0, 0, 0.2), 0 0 2px rgba(255, 255, 255, 0.8) inset',
    },
    label: {
      fontSize: '14px',
      color: isDark ? '#ffffff' : '#000000',
      textShadow: isDark
        ? '0 1px 2px rgba(0, 0, 0, 0.8)'
        : '0 1px 2px rgba(255, 255, 255, 0.8)',
    },
  };

  return (
    <group position={position}>
      <Html center>
        <div style={styles.container}>
          <div style={styles.itemsContainer}>
            {items.map((item, index) => (
              <div key={index} style={styles.item}>
                <div
                  style={{
                    ...styles.colorBox,
                    backgroundColor: item.color,
                  }}
                />
                <span style={styles.label}>{item.label}</span>
              </div>
            ))}
          </div>
        </div>
      </Html>
    </group>
  );
};

export default ColorLegend;
