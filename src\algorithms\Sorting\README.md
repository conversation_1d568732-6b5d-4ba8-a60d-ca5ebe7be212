# Sorting Algorithms

This directory contains implementations of various sorting algorithms.

## Implemented Algorithms

- BubbleSort - A simple sorting algorithm that repeatedly steps through the list, compares adjacent elements and swaps them if they are in the wrong order.
- MergeSort - An efficient, stable, comparison-based, divide and conquer sorting algorithm.
- QuickSort - A divide-and-conquer sorting algorithm that uses a pivot element to partition the array.
- HeapSort - A comparison-based sorting algorithm that uses a binary heap data structure.
- InsertionSort - A simple sorting algorithm that builds the final sorted array one item at a time.
- SelectionSort - A simple sorting algorithm that repeatedly finds the minimum element from the unsorted part of the array.
- RadixSort - A non-comparative sorting algorithm that sorts integers by processing individual digits.
- CountingSort - A non-comparative sorting algorithm that works well when the range of input values is not significantly larger than the number of elements.
- BucketSort - A distribution sort that works by distributing the elements into a number of buckets.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `NewSortingAlgorithm`)
2. Implement the required files:
   - `NewSortingAlgorithmAlgorithm.js` - Core algorithm logic
   - `NewSortingAlgorithmVisualization.js` - Visualization component
   - `NewSortingAlgorithmController.js` - U<PERSON> controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
