// Knapsack/index.js
// Export only what's needed from this algorithm

import KnapsackVisualization from './KnapsackVisualization';
import KnapsackController from './KnapsackController';
import KnapsackAlgorithm from './KnapsackAlgorithm';

export const metadata = {
  id: 'Knapsack',
  name: '0/1 Knapsack',
  description: 'A dynamic programming algorithm that solves the 0/1 Knapsack problem, maximizing value while staying within a weight constraint.',
  timeComplexity: 'O(n*W)',
  spaceComplexity: 'O(n*W)',
  defaultParams: {
    weights: [2, 3, 4, 5],
    values: [3, 4, 5, 6],
    capacity: 8,
  },
};

export const components = {
  visualization: KnapsackVisualization,
  controller: KnapsackController,
  algorithm: KnapsackAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
