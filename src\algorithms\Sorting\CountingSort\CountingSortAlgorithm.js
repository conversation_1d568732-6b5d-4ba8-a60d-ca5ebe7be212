// CountingSortAlgorithm.js
// Implementation of the Counting Sort algorithm with step generation

/**
 * Generates steps for the Counting Sort algorithm
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateCountingSortSteps = (arr) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    movement: 'Initialize Counting Sort'
  });

  // Find the maximum element
  const max = Math.max(...inputArray);
  steps.push({
    type: 'findMax',
    array: [...inputArray],
    max: max,
    movement: `Found maximum value: ${max}`
  });

  // Create count array
  const count = new Array(max + 1).fill(0);
  steps.push({
    type: 'initCount',
    array: [...inputArray],
    countArray: [...count],
    movement: `Created count array of size ${max + 1} filled with zeros`
  });

  // Count occurrences
  for (let i = 0; i < inputArray.length; i++) {
    const element = inputArray[i];

    // Before incrementing count
    steps.push({
      type: 'counting',
      array: [...inputArray],
      countArray: [...count],
      currentIndex: i,
      currentElement: element,
      movement: `Counting element ${element} at index ${i}`
    });

    // Increment count
    count[element]++;

    // After incrementing count
    steps.push({
      type: 'incrementCount',
      array: [...inputArray],
      countArray: [...count],
      currentIndex: i,
      currentElement: element,
      movement: `Incremented count for ${element} to ${count[element]}`
    });
  }

  // Calculate positions
  for (let i = 1; i <= max; i++) {
    // Before updating position
    steps.push({
      type: 'calculatePosition',
      array: [...inputArray],
      countArray: [...count],
      currentIndex: i,
      movement: `Calculating position for ${i}: ${count[i]} + ${count[i-1]}`
    });

    // Update position
    count[i] += count[i - 1];

    // After updating position
    steps.push({
      type: 'updatePosition',
      array: [...inputArray],
      countArray: [...count],
      currentIndex: i,
      movement: `Updated position for ${i} to ${count[i]}`
    });
  }

  // Create output array
  const output = new Array(inputArray.length).fill(0);
  steps.push({
    type: 'initOutput',
    array: [...inputArray],
    countArray: [...count],
    outputArray: [...output],
    movement: `Created output array of size ${inputArray.length}`
  });

  // Place elements in output array
  for (let i = inputArray.length - 1; i >= 0; i--) {
    const element = inputArray[i];
    const targetIndex = count[element] - 1;

    // Before placing element
    steps.push({
      type: 'placing',
      array: [...inputArray],
      countArray: [...count],
      outputArray: [...output],
      currentIndex: i,
      currentElement: element,
      targetIndex: targetIndex,
      movement: `Placing element ${element} from index ${i} to position ${targetIndex} in output array`
    });

    // Place element
    output[targetIndex] = element;
    count[element]--;

    // After placing element
    steps.push({
      type: 'placed',
      array: [...inputArray],
      countArray: [...count],
      outputArray: [...output],
      currentIndex: i,
      currentElement: element,
      movement: `Placed element ${element} at position ${targetIndex} in output array and decremented count to ${count[element]}`
    });
  }

  // Copy back to original array (if needed for visualization)
  for (let i = 0; i < inputArray.length; i++) {
    // Before copying
    steps.push({
      type: 'copying',
      array: [...inputArray],
      outputArray: [...output],
      currentIndex: i,
      movement: `Copying element ${output[i]} from output array to index ${i} in original array`
    });

    // Copy element
    inputArray[i] = output[i];

    // After copying
    steps.push({
      type: 'copied',
      array: [...inputArray],
      outputArray: [...output],
      currentIndex: i,
      sorted: Array.from({ length: i + 1 }, (_, idx) => idx),
      movement: `Copied element ${output[i]} to index ${i} in original array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...output],
    sorted: Array.from({ length: output.length }, (_, i) => i),
    movement: 'Counting Sort complete'
  });

  return { steps, sortedArray: output };
};

// Default export
const CountingSortAlgorithm = {
  generateSteps: generateCountingSortSteps
};

export default CountingSortAlgorithm;
