// Export all contexts from a single file for easier imports
import React from 'react';
import { StepProvider, useStep } from './StepContext';
import { SimulationProvider, useSimulation, SimulationState } from './SimulationContext';
import { AlgorithmDataProvider, useAlgorithmData } from './AlgorithmDataContext';

export { StepProvider, useStep };
export { SimulationProvider, useSimulation, SimulationState };
export { AlgorithmDataProvider, useAlgorithmData };

// Combined provider for all simulation contexts

/**
 * SimulationContextProvider - Combined provider for all simulation contexts
 *
 * This component wraps all the simulation contexts in a single provider
 * for easier usage in components.
 */
export const SimulationContextProvider = ({ children }) => {
  return (
    <StepProvider>
      <SimulationProvider>
        <AlgorithmDataProvider>
          {children}
        </AlgorithmDataProvider>
      </SimulationProvider>
    </StepProvider>
  );
};
