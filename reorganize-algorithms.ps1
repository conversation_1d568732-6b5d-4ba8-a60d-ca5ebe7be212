# PowerShell script to reorganize algorithms into category folders

# Define the mapping of algorithms to categories
$mapping = @{
  "sorting" = @{
    "name" = "Sorting"
    "name_lowercase" = "sorting"
    "algorithms" = @("BubbleSort", "MergeSort", "QuickSort", "HeapSort", "InsertionSort", "SelectionSort", "RadixSort", "CountingSort", "BucketSort")
  }
  "searching" = @{
    "name" = "Searching"
    "name_lowercase" = "searching"
    "algorithms" = @("BFS", "DFS")
  }
  "graph" = @{
    "name" = "Graph"
    "name_lowercase" = "graph"
    "algorithms" = @("Dijkstra", "FloydWarshall")
  }
  "dp" = @{
    "name" = "Dynamic Programming"
    "name_lowercase" = "dynamic programming"
    "algorithms" = @("LCS")
  }
  "recursion" = @{
    "name" = "Recursion & Backtracking"
    "name_lowercase" = "recursion and backtracking"
    "algorithms" = @("TowersOfHanoi")
  }
  "math" = @{
    "name" = "Mathematical"
    "name_lowercase" = "mathematical"
    "algorithms" = @()
  }
  "other" = @{
    "name" = "Other"
    "name_lowercase" = "other"
    "algorithms" = @()
  }
}

# Create category folders if they don't exist
foreach ($category in $mapping.Keys) {
  $categoryPath = "src\algorithms\$category"
  if (-not (Test-Path $categoryPath)) {
    Write-Host "Creating folder: $categoryPath"
    New-Item -ItemType Directory -Path $categoryPath -Force | Out-Null
    
    # Create README for the category
    $readmeContent = Get-Content -Path "category-readme-template.md" -Raw
    $readmeContent = $readmeContent.Replace("[CATEGORY_NAME]", $mapping[$category].name)
    $readmeContent = $readmeContent.Replace("[CATEGORY_NAME_LOWERCASE]", $mapping[$category].name_lowercase)
    
    # Add implemented algorithms to the README
    $algorithmList = ""
    foreach ($algo in $mapping[$category].algorithms) {
      $algorithmList += "- $algo`n"
    }
    $readmeContent = $readmeContent.Replace("- [Algorithm 1] - Brief description`n- [Algorithm 2] - Brief description`n- ...", $algorithmList)
    
    Set-Content -Path "$categoryPath\README.md" -Value $readmeContent
  }
}

# Move algorithms to their respective category folders
foreach ($category in $mapping.Keys) {
  foreach ($algorithm in $mapping[$category].algorithms) {
    $sourcePath = "src\algorithms\$algorithm"
    $destinationPath = "src\algorithms\$category\$algorithm"
    
    if (Test-Path $sourcePath) {
      Write-Host "Moving $algorithm to $category category"
      # Use Copy-Item instead of Move-Item to keep the original files as backup
      Copy-Item -Path $sourcePath -Destination "src\algorithms\$category\" -Recurse -Force
    } else {
      Write-Host "Algorithm folder not found: $sourcePath"
    }
  }
}

# Copy the AlgorithmRegistry template
Copy-Item -Path "AlgorithmRegistry-template.js" -Destination "src\algorithms\AlgorithmRegistry-new.js" -Force

Write-Host "Algorithm reorganization completed!"
Write-Host "Please review the changes and then:"
Write-Host "1. Test the new structure by replacing src\algorithms\AlgorithmRegistry.js with src\algorithms\AlgorithmRegistry-new.js"
Write-Host "2. Once everything works, you can remove the original algorithm folders from the root of src\algorithms\"
