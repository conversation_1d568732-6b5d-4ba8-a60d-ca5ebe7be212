// ExponentialSearchAlgorithm.js
// Implementation of the Exponential Search algorithm with step generation

/**
 * Generates steps for the Exponential Search algorithm
 * @param {Array} arr - The array to search in (must be sorted)
 * @param {number} target - The value to search for
 * @returns {Object} - Object containing steps and the result
 */
export const generateExponentialSearchSteps = (arr, target) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Initialize Exponential Search'
  });

  // Add step to explain that array must be sorted
  steps.push({
    type: 'checkSorted',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Exponential Search requires a sorted array'
  });

  // Check if the first element is the target
  if (inputArray[0] === target) {
    steps.push({
      type: 'checkFirst',
      array: [...inputArray],
      target,
      currentIndex: 0,
      found: true,
      result: 0,
      movement: `First element ${inputArray[0]} at index 0 is equal to target ${target}`
    });

    steps.push({
      type: 'complete',
      array: [...inputArray],
      target,
      found: true,
      result: 0,
      movement: `Exponential Search complete: ${target} found at index 0`
    });

    return { 
      steps, 
      result: {
        found: true,
        index: 0
      }
    };
  }

  // Add step to check first element
  steps.push({
    type: 'checkFirst',
    array: [...inputArray],
    target,
    currentIndex: 0,
    found: false,
    movement: `First element ${inputArray[0]} at index 0 is not equal to target ${target}`
  });

  // Perform exponential search
  let bound = 1;
  const n = inputArray.length;
  let found = false;
  let result = -1;

  // Add step to start the exponential phase
  steps.push({
    type: 'startExponential',
    array: [...inputArray],
    target,
    bound,
    found: false,
    movement: `Start exponential phase with bound = ${bound}`
  });

  // Exponential phase: Find the range where target might be
  while (bound < n && inputArray[bound] < target) {
    // Add step to show the current bound
    steps.push({
      type: 'checkBound',
      array: [...inputArray],
      target,
      bound,
      boundValue: inputArray[bound],
      found: false,
      movement: `Check bound ${bound}: ${inputArray[bound]} < ${target}, double the bound`
    });

    // Double the bound
    bound *= 2;

    // Add step to show the new bound
    steps.push({
      type: 'updateBound',
      array: [...inputArray],
      target,
      bound: Math.min(bound, n - 1),
      found: false,
      movement: `Update bound to ${Math.min(bound, n - 1)}`
    });
  }

  // Adjust bound if it exceeds array length
  bound = Math.min(bound, n - 1);

  // Add step to show the final bound
  steps.push({
    type: 'finalBound',
    array: [...inputArray],
    target,
    bound,
    found: false,
    movement: `Final bound: ${bound}`
  });

  // Binary search phase
  let left = bound / 2;
  let right = bound;

  // Add step to start the binary search phase
  steps.push({
    type: 'startBinary',
    array: [...inputArray],
    target,
    left: Math.floor(left),
    right,
    found: false,
    movement: `Start binary search phase with left = ${Math.floor(left)} and right = ${right}`
  });

  // Binary search within the range [left, right]
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    // Add step to show the current mid
    steps.push({
      type: 'calculateMid',
      array: [...inputArray],
      target,
      left: Math.floor(left),
      right,
      mid,
      midValue: inputArray[mid],
      found: false,
      movement: `Calculate mid = floor((${Math.floor(left)} + ${right}) / 2) = ${mid}`
    });

    // Add step to compare the mid element with the target
    steps.push({
      type: 'compareMid',
      array: [...inputArray],
      target,
      left: Math.floor(left),
      right,
      mid,
      midValue: inputArray[mid],
      found: false,
      movement: `Compare ${inputArray[mid]} at index ${mid} with target ${target}`
    });

    // Check if the mid element is the target
    if (inputArray[mid] === target) {
      found = true;
      result = mid;

      // Add step to show the target found
      steps.push({
        type: 'found',
        array: [...inputArray],
        target,
        left: Math.floor(left),
        right,
        mid,
        midValue: inputArray[mid],
        found: true,
        result: mid,
        movement: `Target ${target} found at index ${mid}`
      });

      break;
    }

    // If the mid element is greater than the target, search in the left half
    if (inputArray[mid] > target) {
      // Add step to update right
      steps.push({
        type: 'updateRight',
        array: [...inputArray],
        target,
        left: Math.floor(left),
        right: mid - 1,
        mid,
        found: false,
        movement: `${inputArray[mid]} > ${target}, so update right = ${mid - 1}`
      });

      right = mid - 1;
    }
    // If the mid element is less than the target, search in the right half
    else {
      // Add step to update left
      steps.push({
        type: 'updateLeft',
        array: [...inputArray],
        target,
        left: mid + 1,
        right,
        mid,
        found: false,
        movement: `${inputArray[mid]} < ${target}, so update left = ${mid + 1}`
      });

      left = mid + 1;
    }
  }

  // If target not found, add a step to show that
  if (!found) {
    steps.push({
      type: 'notFound',
      array: [...inputArray],
      target,
      left: Math.floor(left),
      right,
      found: false,
      movement: `Target ${target} not found in the array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    target,
    found,
    result,
    movement: found ? `Exponential Search complete: ${target} found at index ${result}` : `Exponential Search complete: ${target} not found`
  });

  return { 
    steps, 
    result: {
      found,
      index: found ? result : -1
    }
  };
};

// Helper function for binary search (used internally)
const binarySearch = (arr, target, left, right) => {
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    
    if (arr[mid] === target) {
      return mid;
    }
    
    if (arr[mid] > target) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }
  
  return -1;
};

// Default export
const ExponentialSearchAlgorithm = {
  generateSteps: generateExponentialSearchSteps
};

export default ExponentialSearchAlgorithm;
