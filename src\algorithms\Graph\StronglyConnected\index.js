// StronglyConnected/index.js
// Export only what's needed from this algorithm

// Import the visualization
import StronglyConnectedVisualization from './StronglyConnectedVisualization';
import StronglyConnectedController from './StronglyConnectedController';
import StronglyConnectedAlgorithm from './StronglyConnectedAlgorithm';

export const metadata = {
  id: 'StronglyConnected',
  name: 'Strongly Connected Components',
  description: 'An algorithm for finding strongly connected components (SCCs) in a directed graph. A strongly connected component is a subgraph where every vertex is reachable from every other vertex.',
  timeComplexity: 'O(V + E)',
  spaceComplexity: 'O(V)',
  defaultParams: {
    nodes: 6,
    density: 0.5,
    customEdges: [],
  },
};

export const components = {
  visualization: StronglyConnectedVisualization,
  controller: StronglyConnectedController,
  algorithm: StronglyConnectedAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
