// BinarySearchVisualization.js
// This component provides the visualization for the Binary Search algorithm.

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateBinarySearchSteps } from './BinarySearchAlgorithm';
import * as THREE from 'three';

// Import reusable visualization components
import { Bar, ColorLegend, StepBoard, GroundPlane } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.8;
const BAR_SPACING = 0.3;
const MAX_BAR_HEIGHT = 3.5;
const CAMERA_POSITION = [0, 6, 12];
const CAMERA_LOOKAT = [0, 0, -2];

// Searchlight component for highlighting the search area
const Searchlight = ({ position = [0, 0, 0], radius = 5, intensity = 1, color = '#ffffff', visible = true }) => {
  return (
    <group position={position} visible={visible}>
      <pointLight
        position={[0, 3, 0]}
        intensity={intensity}
        color={color}
        distance={radius * 2}
        decay={2}
        castShadow
      />
      <mesh position={[0, 3, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <coneGeometry args={[radius * 0.5, radius, 32, 1, true]} />
        <meshBasicMaterial color={color} transparent opacity={0.1} side={THREE.BackSide} />
      </mesh>
    </group>
  );
};

// Pointer component for showing the left, right, and mid pointers
const Pointer = ({ position = [0, 0, 0], label = '', color = '#ffffff', visible = true, size = 0.3 }) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  return (
    <group position={position} visible={visible}>
      {/* Arrow */}
      <mesh position={[0, 0.5, 0]}>
        <coneGeometry args={[size, size * 2, 16]} />
        <meshStandardMaterial color={color} />
      </mesh>

      {/* Label */}
      <Html
        position={[0, 1.2, 0]}
        center
        style={{
          color: color,
          fontSize: '16px',
          fontWeight: 'bold',
          padding: '2px 6px',
          backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
          borderRadius: '4px',
          whiteSpace: 'nowrap',
        }}
      >
        {label}
      </Html>
    </group>
  );
};

// Range indicator component for showing the current search range
const RangeIndicator = ({ startX, endX, y = -0.5, color = '#ffffff', visible = true }) => {
  const width = endX - startX;

  return (
    <group position={[(startX + endX) / 2, y, 0]} visible={visible}>
      <mesh>
        <boxGeometry args={[width, 0.1, 0.1]} />
        <meshStandardMaterial color={color} transparent opacity={0.7} />
      </mesh>

      {/* Left cap */}
      <mesh position={[-width / 2, 0, 0]}>
        <boxGeometry args={[0.1, 0.3, 0.1]} />
        <meshStandardMaterial color={color} />
      </mesh>

      {/* Right cap */}
      <mesh position={[width / 2, 0, 0]}>
        <boxGeometry args={[0.1, 0.3, 0.1]} />
        <meshStandardMaterial color={color} />
      </mesh>
    </group>
  );
};

// Comparison indicator component for showing the comparison between mid value and target
const ComparisonIndicator = ({ position = [0, 0, 0], type = 'equal', visible = true, scale = 1 }) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  // Colors for different comparison types
  const colors = {
    equal: isDark ? '#4caf50' : '#388e3c',    // Green
    less: isDark ? '#ff9800' : '#f57c00',     // Orange
    greater: isDark ? '#f44336' : '#d32f2f'   // Red
  };

  // Symbols for different comparison types
  const symbols = {
    equal: '=',
    less: '<',
    greater: '>'
  };

  return (
    <group position={position} visible={visible} scale={[scale, scale, scale]}>
      <Html
        position={[0, 0, 0]}
        center
        style={{
          color: colors[type],
          fontSize: '32px',
          fontWeight: 'bold',
          padding: '2px 8px',
          backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
          borderRadius: '4px',
          whiteSpace: 'nowrap',
          transform: `scale(${scale})`,
        }}
      >
        {symbols[type]}
      </Html>
    </group>
  );
};

// Target indicator component for showing the target value
const TargetIndicator = ({ position = [0, 0, 0], value, visible = true }) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  return (
    <group position={position} visible={visible}>
      {/* Static ring */}
      <mesh>
        <torusGeometry args={[0.6, 0.05, 16, 32]} />
        <meshStandardMaterial color={isDark ? '#f44336' : '#d32f2f'} />
      </mesh>

      {/* Target value without background */}
      <Html
        position={[0, 0, 0]}
        center
        style={{
          color: '#ffffff',
          fontSize: '18px',
          fontWeight: 'bold',
          textShadow: '0 0 3px rgba(0,0,0,0.8), 0 0 5px rgba(0,0,0,0.6)',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
        }}
      >
        {value}
      </Html>
    </group>
  );
};

// Main visualization component
const BinarySearchVisualization = ({ params = {} }) => {
    const theme = useTheme();
    const { camera } = useThree();
    const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
    const { speed } = useSpeed();

    // Refs for animation control
    const speedRef = useRef(speed);
    const stepsRef = useRef([]);
    const initialArrayRef = useRef([]);
    const lastAppliedStepRef = useRef(-1);
    const animatingRef = useRef(false);
    const timeoutIdRef = useRef(null);
    const stateRef = useRef(state);
    const currentStepRef = useRef(step);

    // State for array data
    const [arrayData, setArrayData] = useState([]);
    const [target, setTarget] = useState(0);

    // State for visualization
    const [left, setLeft] = useState(0);
    const [right, setRight] = useState(0);
    const [mid, setMid] = useState(-1);
    const [found, setFound] = useState(false);
    const [result, setResult] = useState(-1);
    // We need to keep this state even though it's not directly used in rendering
    // It's used in the step logic to determine comparison type
    const [, setCompareResult] = useState('');

    // Animation states
    const [searchlightPosition, setSearchlightPosition] = useState([0, 0, 0]);
    const [searchlightRadius, setSearchlightRadius] = useState(10);
    const [searchlightIntensity, setSearchlightIntensity] = useState(1);
    const [searchlightVisible, setSearchlightVisible] = useState(false);

    const [leftPointerVisible, setLeftPointerVisible] = useState(false);
    const [rightPointerVisible, setRightPointerVisible] = useState(false);
    const [midPointerVisible, setMidPointerVisible] = useState(false);

    const [rangeIndicatorVisible, setRangeIndicatorVisible] = useState(false);
    const [comparisonVisible, setComparisonVisible] = useState(false);
    const [comparisonType, setComparisonType] = useState('equal');
    const [comparisonScale, setComparisonScale] = useState(1);

    const [targetIndicatorVisible, setTargetIndicatorVisible] = useState(false);
    const [targetIndicatorPosition, setTargetIndicatorPosition] = useState([0, 0, 0]);

    const [highlightedIndices, setHighlightedIndices] = useState([]);

    // Animation for the comparison indicator
    const [comparisonPulse, setComparisonPulse] = useState(0);

    // Colors based on theme
    const colors = useMemo(() => {
        const isDark = theme.palette.mode === 'dark';
        return {
            bar: isDark ? '#64b5f6' : '#2196f3',      // Default bar color
            current: isDark ? '#ce93d8' : '#9c27b0',  // Current element being considered
            compare: isDark ? '#ffb74d' : '#ff9800',  // Element being compared
            found: isDark ? '#81c784' : '#4caf50',    // Found element
            notFound: isDark ? '#e57373' : '#f44336', // Not found
            left: isDark ? '#4fc3f7' : '#03a9f4',     // Left pointer
            right: isDark ? '#4db6ac' : '#009688',    // Right pointer
            mid: isDark ? '#ce93d8' : '#9c27b0',      // Mid pointer
            range: isDark ? '#b39ddb' : '#673ab7',    // Search range
            target: isDark ? '#f44336' : '#d32f2f',   // Target value
            base: isDark ? '#1a1a1a' : '#f5f5f5',     // Base color
            ground: isDark ? '#121212' : '#eeeeee',   // Ground color
        };
    }, [theme.palette.mode]);

    // Update refs when state changes
    useEffect(() => {
        stateRef.current = state;
    }, [state]);

    useEffect(() => {
        currentStepRef.current = step;
    }, [step]);

    useEffect(() => {
        speedRef.current = speed;
    }, [speed]);

    // Initialize array when params change
    useEffect(() => {
        // Clear any existing animation timeouts
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state
        animatingRef.current = false;

        // Extract parameters with safety checks
        const { arraySize = 10, randomize = true, customArray = [], target = 42 } = params;

        // Generate array data
        let newArray = [];

        // Use custom array if provided
        if (customArray && customArray.length > 0) {
            newArray = [...customArray];
        } else if (randomize) {
            // Generate random sorted array
            newArray = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 99) + 1
            ).sort((a, b) => a - b);
        } else {
            // Generate sequential array
            newArray = Array.from({ length: arraySize }, (_, i) => (i + 1) * Math.floor(99 / arraySize));
        }

        // Store the initial array
        initialArrayRef.current = [...newArray];
        setArrayData(newArray);
        setTarget(target);

        // Generate steps
        const { steps } = generateBinarySearchSteps(newArray, target);
        stepsRef.current = steps;
        setSteps(steps);
        setTotalSteps(steps.length);
        setAlgorithmSteps(steps.map(step => step.movement));

        // Reset visualization state
        setLeft(0);
        setRight(newArray.length - 1);
        setMid(-1);
        setFound(false);
        setResult(-1);
        setCompareResult('');

        setSearchlightVisible(false);
        setLeftPointerVisible(false);
        setRightPointerVisible(false);
        setMidPointerVisible(false);
        setRangeIndicatorVisible(false);
        setComparisonVisible(false);
        setTargetIndicatorVisible(false);
        setHighlightedIndices([]);

        // Reset step if needed
        setStep(0);
        lastAppliedStepRef.current = -1;

        // Position camera
        if (camera) {
            camera.position.set(...CAMERA_POSITION);
            camera.lookAt(...CAMERA_LOOKAT);
        }

        // Force a small delay to ensure everything is reset
        setTimeout(() => {
            // Apply the initial step
            if (steps.length > 0) {
                applyStep(0);
            }
        }, 50);
    }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

    // Apply a single step of the Binary Search algorithm
    const applyStep = (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
            return;
        }

        const currentStep = stepsRef.current[stepIndex];
        lastAppliedStepRef.current = stepIndex;

        // Update array data if needed
        if (currentStep.array) {
            setArrayData(currentStep.array);
        }

        // Update target value if needed
        if (currentStep.target !== undefined) {
            setTarget(currentStep.target);
        }

        // Update visualization state based on step type
        switch (currentStep.type) {
            case 'init':
                setLeft(currentStep.left);
                setRight(currentStep.right);
                setMid(-1);
                setFound(false);
                setResult(-1);
                setCompareResult('');

                setSearchlightVisible(false);
                setLeftPointerVisible(false);
                setRightPointerVisible(false);
                setMidPointerVisible(false);
                setRangeIndicatorVisible(false);
                setComparisonVisible(false);
                setTargetIndicatorVisible(true);
                setHighlightedIndices([]);

                // Position the target indicator
                setTargetIndicatorPosition([0, 5, 0]);
                break;

            case 'searchRange':
                setLeft(currentStep.left);
                setRight(currentStep.right);
                setMid(-1);
                setCompareResult('');

                setSearchlightVisible(true);
                setLeftPointerVisible(true);
                setRightPointerVisible(true);
                setMidPointerVisible(false);
                setRangeIndicatorVisible(true);
                setComparisonVisible(false);
                setHighlightedIndices([]);

                // Animate the searchlight to cover the current range
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 500);
                break;

            case 'selectMid':
                setMid(currentStep.mid);
                setCompareResult('');

                setMidPointerVisible(true);
                setComparisonVisible(false);
                setHighlightedIndices([currentStep.mid]);
                break;

            case 'compare':
                setCompareResult(currentStep.compareResult);

                setComparisonVisible(true);
                setComparisonType(currentStep.compareResult);
                setComparisonScale(1);
                setComparisonPulse(0);

                // Start the comparison pulse animation
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 800);
                break;

            case 'updateLeft':
                setLeft(currentStep.left);

                // Animate the left pointer
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 500);
                break;

            case 'updateRight':
                setRight(currentStep.right);

                // Animate the right pointer
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 500);
                break;

            case 'found':
                setFound(true);
                setResult(currentStep.result);

                setSearchlightVisible(false);
                setLeftPointerVisible(false);
                setRightPointerVisible(false);
                setMidPointerVisible(false);
                setRangeIndicatorVisible(false);
                setComparisonVisible(false);
                setHighlightedIndices([currentStep.mid]);

                // Move the target indicator to the found element
                setTargetIndicatorPosition([getXPosition(currentStep.mid), 2, 0]);

                // Animate the found element
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 1000);
                break;

            case 'notFound':
                setFound(false);

                setSearchlightVisible(false);
                setLeftPointerVisible(false);
                setRightPointerVisible(false);
                setMidPointerVisible(false);
                setRangeIndicatorVisible(false);
                setComparisonVisible(false);
                setHighlightedIndices([]);

                // Animate the target indicator to show not found
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 1000);
                break;

            case 'complete':
                setFound(currentStep.found);
                setResult(currentStep.result);

                if (currentStep.found) {
                    setHighlightedIndices([currentStep.mid]);
                    setTargetIndicatorPosition([getXPosition(currentStep.mid), 2, 0]);
                } else {
                    setHighlightedIndices([]);
                    setTargetIndicatorPosition([0, 5, 0]);
                }
                break;

            default:
                break;
        }
    };

    // Helper function to get X position for an index
    const getXPosition = (index) => {
        const totalWidth = (BAR_WIDTH + BAR_SPACING) * arrayData.length;
        const startX = -(totalWidth / 2) + (BAR_WIDTH / 2);
        return startX + index * (BAR_WIDTH + BAR_SPACING);
    };

    // Handle step changes
    useEffect(() => {
        // Clear any existing timeout
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Don't apply steps if we're still animating
        if (animatingRef.current) {
            return;
        }

        // Apply the current step
        applyStep(step);
    }, [step]);

    // Handle automatic stepping when state is 'running'
    useEffect(() => {
        // Reset animation state when state changes to idle
        if (state === 'idle') {
            // Clear any existing timeouts
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }

            // Reset animation state
            animatingRef.current = false;

            // Apply the initial step
            if (stepsRef.current && stepsRef.current.length > 0) {
                applyStep(0);
            }

            return;
        }

        // Only proceed if state is 'running'
        if (state !== 'running') {
            return;
        }

        const steps = stepsRef.current || [];

        // Stop if we've reached the end
        if (step >= steps.length) {
            setState('completed');
            return;
        }

        // Don't schedule next step if we're animating
        if (animatingRef.current) {
            return;
        }

        // Schedule the next step with a delay
        const delay = Math.max(200, 800 - (speedRef.current * 80));
        const timeoutId = setTimeout(() => {
            // Only increment if still in running state
            if (stateRef.current === 'running' && !animatingRef.current) {
                setStep(prevStep => prevStep + 1);
            }
        }, delay);

        // Store the timeout ID for cleanup
        timeoutIdRef.current = timeoutId;

        // Cleanup function
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }
        };
    }, [state, step, setStep, setState]);

    // Animation frame for continuous animations
    useFrame(() => {
        // Animate the comparison indicator
        if (comparisonVisible) {
            setComparisonPulse(prev => {
                const newValue = prev + 0.05;
                return newValue > Math.PI * 2 ? 0 : newValue;
            });

            setComparisonScale(1 + 0.2 * Math.sin(comparisonPulse));
        }

        // Update searchlight position and radius based on current range
        if (searchlightVisible && left >= 0 && right >= 0 && left <= right) {
            const leftX = getXPosition(left);
            const rightX = getXPosition(right);
            const midX = (leftX + rightX) / 2;

            setSearchlightPosition([midX, 0, 0]);
            setSearchlightRadius(Math.max(5, (rightX - leftX) * 1.2));
            setSearchlightIntensity(0.8 + 0.2 * Math.sin(Date.now() * 0.002));
        }
    });

    // Calculate maximum value for scaling
    const maxValue = useMemo(() => {
        if (!arrayData || arrayData.length === 0) return 1;
        return Math.max(...arrayData);
    }, [arrayData]);

    // Get the current step data for the step board
    const currentStepData = useMemo(() => {
        if (step >= 0 && step < stepsRef.current?.length) {
            return stepsRef.current[step];
        }
        return null;
    }, [step]);

    // Render the visualization
    return (
        <group position={[0, -2, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <directionalLight position={[-10, 10, -10]} intensity={0.4} />
            <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

            {/* Ground plane */}
            <GroundPlane
                position={[0, -0.3, 0]}
                width={100}
                depth={100}
                color={colors.ground}
                receiveShadow={true}
            />

            {/* Step Board */}
            <StepBoard
                position={[0, 6, 0.5]}
                description={currentStepData?.movement || ''}
                stepData={currentStepData || {}}
                currentStep={step + 1}
                totalSteps={stepsRef.current.length}
                showBackground={false}
            />

            {/* Color Legend */}
            <ColorLegend
                position={[0, -3, 0.5]}
                colors={colors}
                items={[
                    { label: 'Default', color: colors.bar },
                    { label: 'Current', color: colors.current },
                    { label: 'Left', color: colors.left },
                    { label: 'Right', color: colors.right },
                    { label: 'Mid', color: colors.mid },
                    { label: 'Found', color: colors.found },
                    { label: 'Target', color: colors.target }
                ]}
            />

            {/* Searchlight for highlighting the search area */}
            <Searchlight
                position={searchlightPosition}
                radius={searchlightRadius}
                intensity={searchlightIntensity}
                color="#ffffff"
                visible={searchlightVisible}
            />

            {/* Range indicator */}
            {rangeIndicatorVisible && left >= 0 && right >= 0 && left <= right && (
                <RangeIndicator
                    startX={getXPosition(left)}
                    endX={getXPosition(right)}
                    y={-0.5}
                    color={colors.range}
                    visible={rangeIndicatorVisible}
                />
            )}

            {/* Array Bars */}
            {arrayData.map((value, index) => {
                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;
                const x = getXPosition(index);

                // Determine bar color based on current state
                let barColor = colors.bar; // Default color

                if (found && index === result) {
                    barColor = colors.found;
                } else if (highlightedIndices.includes(index)) {
                    barColor = colors.current;
                } else if (index === mid) {
                    barColor = colors.mid;
                }

                // Determine if this bar should be highlighted
                const isHighlighted = highlightedIndices.includes(index);

                // Calculate bar scale for animation
                let scaleY = 1;
                if (found && index === result) {
                    // Pulse animation for found element
                    scaleY = 1 + 0.2 * Math.sin(Date.now() * 0.005);
                }

                return (
                    <Bar
                        key={`bar-${index}`}
                        position={[x, 0, 0]}
                        height={normalizedHeight}
                        width={BAR_WIDTH}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={true}
                        showIndex={true}
                        showArrow={isHighlighted}
                        depth={BAR_WIDTH}
                        scaleY={scaleY}
                    />
                );
            })}

            {/* Left Pointer */}
            {leftPointerVisible && left >= 0 && left < arrayData.length && (
                <Pointer
                    position={[getXPosition(left), -1, 0]}
                    label="L"
                    color={colors.left}
                    visible={leftPointerVisible}
                />
            )}

            {/* Right Pointer */}
            {rightPointerVisible && right >= 0 && right < arrayData.length && (
                <Pointer
                    position={[getXPosition(right), -1, 0]}
                    label="R"
                    color={colors.right}
                    visible={rightPointerVisible}
                />
            )}

            {/* Mid Pointer */}
            {midPointerVisible && mid >= 0 && mid < arrayData.length && (
                <Pointer
                    position={[getXPosition(mid), -1, 0]}
                    label="M"
                    color={colors.mid}
                    visible={midPointerVisible}
                />
            )}

            {/* Comparison Indicator */}
            {comparisonVisible && mid >= 0 && mid < arrayData.length && (
                <ComparisonIndicator
                    position={[getXPosition(mid), 3, 0]}
                    type={comparisonType}
                    visible={comparisonVisible}
                    scale={comparisonScale}
                />
            )}

            {/* Target Indicator */}
            <TargetIndicator
                position={targetIndicatorPosition}
                value={target}
                visible={targetIndicatorVisible}
            />
        </group>
    );
};

export default BinarySearchVisualization;
