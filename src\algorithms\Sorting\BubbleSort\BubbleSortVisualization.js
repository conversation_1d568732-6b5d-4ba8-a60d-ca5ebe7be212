import React, { useState, useEffect, useRef, useMemo } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { SortingBars3D, SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import getAlgorithmColors from '../../../utils/algorithmColors';

// Constants for visualization
const BAR_WIDTH = 0.6; // Narrower bars for better spacing with larger arrays
const BAR_SPACING = 0.3; // More spacing between bars for clarity
const BASE_HEIGHT = 0.2; // Taller base
const MAX_BAR_HEIGHT = 3.8; // Adjusted max height for better visibility
const CAMERA_POSITION = [0, 3, 10]; // Closer camera position with lower height
const CAMERA_LOOKAT = [0, -0.5, 0]; // Camera looks slightly downward

// Flag to enable/disable levitation effect
const ENABLE_LEVITATION = true; // Set to false to disable levitation

// Use centralized algorithm colors utility

// Log colors for debugging
// console.log('Colors:', getColors({ palette: { mode: 'light' } }));

// Calculate delay based on speed
const getDelay = (speed) => {
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  // Faster speed = shorter delay
  // Speed 1 = 1000ms, Speed 10 = 100ms
  const delay = 1100 - (validSpeed * 100);
  return delay;
};

// Calculate animation duration based on speed
const getAnimationDuration = (speed) => {
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  // Faster speed = shorter duration
  // Speed 1 = 800ms, Speed 10 = 80ms
  const duration = 880 - (validSpeed * 80);
  return duration;
};

// Using the reusable RadixBar component instead of a local Bar component

// Using the reusable SortingBase component instead of a local Base component

// Log a message to confirm the component is being rendered
// Removed log

// Main BubbleSort Visualization Component
const BubbleSortVisualization = (props) => {
  // console.log('BubbleSortVisualization - props:', props);
  const { state, theme } = props;

  // Get array, steps, step and setStep from context
  const { algorithmArray, steps, step, setStep } = useAlgorithm();
  // console.log('BubbleSortVisualization - algorithmArray from context:', algorithmArray);
  // console.log('BubbleSortVisualization - steps from context:', steps);
  // Get theme-aware colors from centralized utility
  const colors = useMemo(() => getAlgorithmColors(theme), [theme?.palette?.mode]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Default' },
    { color: colors.comparing, label: 'Comparing' },
    { color: colors.swapping, label: 'Swapping' },
    { color: colors.sorted, label: 'Sorted' }
  ], [colors]);

  // Get camera from Three.js context
  const { camera } = useThree();

  // Get speed from context
  const { speed } = useSpeed();

  // State for array data
  const [arrayData, setArrayData] = useState([]);

  // State for animation
  const [comparing, setComparing] = useState([-1, -1]);
  const [swapping, setSwapping] = useState([-1, -1]);
  const [sorted, setSorted] = useState([]);
  const [arrows, setArrows] = useState([]);

  // Store the initial array for resetting
  const initialArrayRef = useRef([]);

  // Animation state for swapping
  const [swapAnimation, setSwapAnimation] = useState({
    active: false,
    indices: [-1, -1],
    progress: 0,
    startTime: 0
  });

  // Refs for animation control
  const timeoutIdRef = useRef(null);
  const speedRef = useRef(speed);
  const lastAppliedStepRef = useRef(0);
  const stepsRef = useRef([]);

  // Get the current step data for the step board
  const currentStepData = useMemo(() => {
    if (step >= 0 && step < stepsRef.current.length) {
      // Get the data directly from the steps array using the current step index
      return stepsRef.current[step];
    } else if (step >= stepsRef.current.length && stepsRef.current.length > 0) {
      // For the completed state, create a custom message showing the sorted array
      const sortedArray = [...initialArrayRef.current].sort((a, b) => a - b);
      return {
        type: 'completed',
        message: `Sorting completed! Final array: [${sortedArray.join(', ')}]`
      };
    }
    return null;
  }, [step]);

  // Update speedRef when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Set camera position dynamically based on array size and total width
  useEffect(() => {
    if (!camera || !arrayData.length) return;

    // Calculate the total width of the visualization based on current array size
    // This calculation needs to match the one used for rendering
    const arraySize = arrayData.length;

    // Calculate the adaptive width and spacing based on array size
    // Using a continuous function instead of discrete thresholds
    const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.025));
    const currentBarWidth = BAR_WIDTH * scaleFactor;
    const currentBarSpacing = BAR_SPACING * scaleFactor;

    // Calculate the total width of all bars
    const totalWidth = (arraySize * (currentBarWidth + currentBarSpacing)) - currentBarSpacing;

    // Calculate the camera distance based on the total width
    // We want to ensure the entire width is visible in the camera's field of view
    const fov = camera.fov * (Math.PI / 180); // Convert FOV to radians

    // Calculate the minimum distance needed to see the entire width
    // Using the formula: distance = (width/2) / tan(fov/2)
    // Adding extra padding to ensure everything is visible
    const padding = 2; // Extra padding factor
    const minDistance = (totalWidth / 2) / Math.tan(fov / 2) * padding;

    // Ensure a minimum distance for very small arrays
    const cameraDistance = Math.max(10, minDistance);

    // Position camera to see all bars
    camera.position.set(
      CAMERA_POSITION[0], // Keep X centered
      CAMERA_POSITION[1], // Keep Y height the same
      cameraDistance      // Dynamically calculated distance
    );

    camera.lookAt(CAMERA_LOOKAT[0], CAMERA_LOOKAT[1], CAMERA_LOOKAT[2]);
    camera.updateProjectionMatrix();

    // Log camera position and calculations for debugging
    console.log('Dynamic camera positioning:', {
      arraySize,
      scaleFactor,
      totalWidth,
      cameraDistance,
      fov: camera.fov
    });
  }, [camera, arrayData.length]);



  // Initialize array data from context
  useEffect(() => {
    console.log('BubbleSortVisualization - algorithmArray changed:', algorithmArray);
    // Only update if we have a valid array from context
    if (algorithmArray && algorithmArray.length > 0) {
      // Create a deep copy of the array to avoid reference issues
      const arrayCopy = [...algorithmArray];

      // Reset animation state
      setArrayData(arrayCopy);
      setComparing([-1, -1]);
      setSwapping([-1, -1]);
      setSorted([]);
      setSwapAnimation({
        active: false,
        indices: [-1, -1],
        progress: 0,
        startTime: 0
      });

      // Store the initial array for resetting
      initialArrayRef.current = arrayCopy;

      // Reset step counter
      lastAppliedStepRef.current = 0;

      console.log('BubbleSortVisualization - Initialized with array:', arrayCopy);
    }
  }, [algorithmArray]);

  // Update steps when they change in context
  useEffect(() => {
    console.log('BubbleSortVisualization - steps changed:', steps);
    // Use steps from context if available
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]); // Only depend on steps changes


  // Handle step changes
  useEffect(() => {
    console.log('BubbleSortVisualization - step changed to:', step);

    // Cancel any ongoing animations or timeouts
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    // Disable any active animations
    setSwapAnimation({
      active: false,
      indices: [-1, -1],
      progress: 0,
      startTime: 0
    });

    // Always use the consistent approach for all step changes
    // Start with the initial array
    const initialArray = [...initialArrayRef.current];

    // For step 0, show the initial state from the steps
    if (step === 0) {
      // Get the initial state step
      const initialStep = stepsRef.current[0];
      if (initialStep && initialStep.type === 'initial' && initialStep.array) {
        console.log('BubbleSortVisualization - Setting initial array from step:', initialStep.array);
        setArrayData([...initialStep.array]);
        initialArrayRef.current = [...initialStep.array]; // Update the initial array reference
      } else if (algorithmArray && algorithmArray.length > 0) {
        console.log('BubbleSortVisualization - Setting initial array from context:', algorithmArray);
        setArrayData([...algorithmArray]);
        initialArrayRef.current = [...algorithmArray]; // Update the initial array reference
      }

      // Reset all other state
      setComparing([-1, -1]);
      setSwapping([-1, -1]);
      setSorted([]);
      setArrows([]);
      setSwapAnimation({
        active: false,
        indices: [-1, -1],
        progress: 0,
        startTime: 0
      });
      lastAppliedStepRef.current = 0;
      return;
    }

    // Apply all steps up to the current step
    let currentArray = [...initialArray];
    let currentSorted = [];
    let currentComparing = [-1, -1];
    let currentSwapping = [-1, -1];

    // For step 0, we already handled it above
    // For steps > 0, apply the appropriate steps
    if (step > 0 && step < stepsRef.current.length) {
      // Apply all steps up to the current step to get the correct array state
      for (let i = 1; i < step; i++) {
        const stepData = stepsRef.current[i];
        if (stepData.type === 'swap') {
          const [a, b] = stepData.indices;
          const temp = currentArray[a];
          currentArray[a] = currentArray[b];
          currentArray[b] = temp;
        }

        // Add to sorted indices if this is a sorted step
        if (stepData.type === 'sorted' && !currentSorted.includes(stepData.index)) {
          currentSorted.push(stepData.index);
        }
      }

      // Now apply the current step's visual state
      const currentStep = stepsRef.current[step];
      console.log('BubbleSortVisualization - Applying current step:', currentStep);

      // Update state based on step type
      switch (currentStep.type) {
        case 'compare':
          currentComparing = currentStep.indices;
          currentSwapping = [-1, -1];
          // Set arrows on the comparing indices
          setArrows(currentStep.indices);
          break;

        case 'swap':
          currentComparing = [-1, -1];
          // Don't set swapping indices yet - we'll set them when animation starts
          currentSwapping = [-1, -1];
          // Keep arrows on the swapping indices during animation
          setArrows(currentStep.indices);

          // Create a temporary array for animation that doesn't reflect the swap yet
          const animationArray = [...currentArray];

          // Perform the actual swap in the current array (for future steps)
          const [a, b] = currentStep.indices;
          const temp = currentArray[a];
          currentArray[a] = currentArray[b];
          currentArray[b] = temp;

          // But don't update the displayed array yet - use the pre-swap array for animation
          setArrayData(animationArray);

          // Start swap animation - but don't set swapping indices yet
          setSwapAnimation({
            active: true,
            indices: currentStep.indices,
            progress: 0,
            startTime: Date.now()
          });

          // Now set the swapping indices to show the bars in swap state
          // This happens after the animation has started
          setTimeout(() => {
            setSwapping(currentStep.indices);
          }, 50); // Small delay to ensure animation starts first

          // Schedule the end of the animation
          const animationDuration = getAnimationDuration(speedRef.current);
          timeoutIdRef.current = setTimeout(() => {
            // Now update the displayed array to reflect the swap
            setArrayData([...currentArray]);

            // End the animation and clear swapping state and arrows
            setSwapAnimation({
              active: false,
              indices: [-1, -1],
              progress: 0,
              startTime: 0
            });
            setSwapping([-1, -1]);
            setArrows([]);
          }, animationDuration);
          break;

        case 'sorted':
          currentComparing = [-1, -1];
          currentSwapping = [-1, -1];
          // Clear arrows when marking as sorted
          setArrows([]);

          // Add to sorted indices
          if (!currentSorted.includes(currentStep.index)) {
            currentSorted.push(currentStep.index);
          }
          break;
      }
    }

    // Special case: if we've reached the last step, mark all bars as sorted
    if (step >= stepsRef.current.length) {
      // Mark all indices as sorted
      const allSorted = Array.from({ length: currentArray.length }, (_, i) => i);
      console.log('BubbleSortVisualization - All steps completed, marking all bars as sorted:', allSorted);

      // Make sure we're showing the correctly sorted array
      // Sort the array in ascending order
      const sortedArray = [...initialArrayRef.current].sort((a, b) => a - b);

      // Update the state with the sorted array and all bars marked as sorted
      setArrayData(sortedArray);
      setComparing([-1, -1]);
      setSwapping([-1, -1]);
      setSorted(allSorted);
    } else {
      // Update the state with the calculated values
      setArrayData(currentArray);
      setComparing(currentComparing);
      setSwapping(currentSwapping);
      setSorted(currentSorted);
    }

    // Update the last applied step
    lastAppliedStepRef.current = step;
  }, [step]); // Only depend on step changes

  // Handle automatic stepping when state is 'running'
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      return;
    }

    const steps = stepsRef.current || [];

    // Stop if we've reached the end
    if (step >= steps.length) {
      return;
    }

    // Schedule the next step with a delay
    const timeoutId = setTimeout(() => {
      // Only increment if still in running state
      if (state === 'running') {
        setStep(prevStep => prevStep + 1);
      }
    }, getDelay(speedRef.current)); // Use speed-based delay between steps

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, setStep]);

  // Handle state changes
  useEffect(() => {
    console.log('BubbleSortVisualization - State changed to:', state);

    // If state is completed, mark all bars as sorted and show the sorted array
    if (state === 'completed') {
      // Mark all indices as sorted
      const allSorted = Array.from({ length: arrayData.length }, (_, i) => i);
      console.log('BubbleSortVisualization - Algorithm completed, marking all bars as sorted:', allSorted);

      // Make sure we're showing the correctly sorted array
      const sortedArray = [...initialArrayRef.current].sort((a, b) => a - b);

      // Update the visualization with the sorted array
      setArrayData(sortedArray);
      setSorted(allSorted);
      setComparing([-1, -1]);
      setSwapping([-1, -1]);
    }
  }, [state, arrayData.length]); // Depend on state changes and array length

  // Update swap animation progress
  useFrame(() => {
    if (swapAnimation.active) {
      const elapsed = Date.now() - swapAnimation.startTime;
      const duration = getAnimationDuration(speedRef.current); // Use speed-based animation duration
      const progress = Math.min(elapsed / duration, 1);

      setSwapAnimation(prev => ({
        ...prev,
        progress
      }));

      // If animation is complete, end it automatically
      if (progress >= 1 && !timeoutIdRef.current) {
        // End the animation if it wasn't already ended by the timeout
        setSwapAnimation(prev => ({
          ...prev,
          active: false
        }));
      }
    }
  });

  // Calculate positions for bars with adaptive spacing for larger arrays
  // Using a continuous function instead of discrete thresholds
  const arraySize = arrayData.length;

  // Calculate scale factor based on array size - smoothly scales down as array size increases
  // This matches the calculation in the camera positioning effect
  const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.01));

  // Apply the scale factor to both width and spacing
  const adaptiveWidth = BAR_WIDTH * scaleFactor;
  const adaptiveSpacing = BAR_SPACING * scaleFactor;

  // Adaptive scaling values calculated based on array size
  // const debugInfo = {
  //   arraySize,
  //   scaleFactor,
  //   adaptiveWidth,
  //   adaptiveSpacing
  // };

  const totalWidth = (arrayData.length * (adaptiveWidth + adaptiveSpacing)) - adaptiveSpacing;

  // We no longer need to set scene background color here
  // It's now handled centrally in AlgorithmVisualizer.js

  // Create a reference to the group that will be animated
  const groupRef = useRef();

  // Use useFrame for continuous animation without React state updates
  useFrame(({ clock }) => {
    if (ENABLE_LEVITATION && state === "idle" && groupRef.current) {
      // Get the current time for smooth animation
      const time = clock.getElapsedTime();

      // Apply position changes directly to the group
      groupRef.current.position.x = Math.sin(time * 0.2) * 0.1;
      groupRef.current.position.y = -2 + Math.sin(time * 0.3) * 0.2;
      groupRef.current.position.z = Math.sin(time * 0.15) * 0.1;

      // Apply rotation changes directly to the group
      groupRef.current.rotation.x = Math.sin(time * 0.2) * 0.02;
      groupRef.current.rotation.y = Math.cos(time * 0.15) * 0.02;
      groupRef.current.rotation.z = Math.sin(time * 0.1) * 0.01;
    }
  });

  // We don't need placeholder variables anymore since we're using the ref directly

  return (
    <>
      {/* Fixed Step board - stays at the top of the screen */}
      <FixedStepBoard
        description={currentStepData?.message || 'Bubble Sort Algorithm'}
        currentStep={step > 0 ? step : ''} // Only show step number for steps > 0
        totalSteps={stepsRef.current.length > 0 ? stepsRef.current.length - 1 : 0} // Subtract 1 to exclude initial state from total
        stepData={currentStepData || {}}
        showStepNumber={step > 0} // Don't show step number for initial state
      />

      {/* Fixed Color legend - stays at the bottom of the screen */}
      <FixedColorLegend
        items={legendItems}
        theme={theme}
      />

      {/* Levitating visualization group with fixed center positioning */}
      <group
        ref={groupRef}
        position={[0, -2, 0]} // Initial position
        rotation={[0, 0, 0]} // Initial rotation
      >
        {/* Lighting is handled in the main visualization component */}

        {/* Base - Using enhanced theme-aware SortingBase component */}
        <SortingBase
          width={Math.max(totalWidth + 2, 10)} /* Ensure base is at least 10 units wide */
          color={colors}
          height={BASE_HEIGHT}
          useThemeColors={true}
          metalness={0.3}
          roughness={0.4}
          highlightHeight={0.04}
        />

        {/* Bars - Using SortingBars3D component for 3D bars */}
        <SortingBars3D
          arrayData={arrayData}
          colors={colors}
          maxBarHeight={MAX_BAR_HEIGHT}
          barWidth={adaptiveWidth}
          barSpacing={adaptiveSpacing}
          comparing={comparing}
          swapping={swapping}
          sorted={sorted}
          swapAnimation={swapAnimation}
          showValues={scaleFactor > 0.4} /* Show values only when bars are large enough */
          showIndices={scaleFactor > 0.3} /* Show indices only when bars are large enough */
          showArrows={arrows}
        />
      </group>
    </>
  );
};

export default BubbleSortVisualization;
