// LCSController.js
// This file contains the controller UI for the Longest Common Subsequence algorithm

import React, { useState, useEffect, useRef } from 'react';
import { useSpeed } from '../../../context/SpeedContext';
import {
  Box,
  Typography,
  TextField,
  Button,
  Slider,
  Paper,
  IconButton,
  Tooltip,
  CircularProgress,
  Stack,
  LinearProgress,
  Collapse,
  useTheme
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import SpeedIcon from '@mui/icons-material/Speed';
import CodeIcon from '@mui/icons-material/Code';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PendingIcon from '@mui/icons-material/Pending';
import TimelineIcon from '@mui/icons-material/Timeline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import SettingsIcon from '@mui/icons-material/Settings';
import { generateLCSSteps } from './LCSAlgorithm';

const LCSController = (props) => {
  // Create a ref for the movement sequence container
  const movementContainerRef = useRef(null);

  // Extract properties from the props object with safety checks
  const {
    params = {},
    onParamChange = () => { },
    state = 'idle',
    step = 0,
    setStep = () => { },
    totalSteps = 0,
    setTotalSteps = () => { },
    setState = () => { },
    steps = [],
    setSteps = () => { },
    setMovements = () => { }
  } = props || {};

  // State for collapsible sections
  const [infoExpanded, setInfoExpanded] = useState(false);
  const [paramsExpanded, setParamsExpanded] = useState(true);
  const [controlsExpanded, setControlsExpanded] = useState(true);
  const [progressExpanded, setProgressExpanded] = useState(true);
  const [movementExpanded, setMovementExpanded] = useState(true);

  // Local state for the controller
  const [string1, setString1] = useState(params.string1 || 'ABCBDAB');
  const [string2, setString2] = useState(params.string2 || 'BDCABA');
  const [isCustomInput, setIsCustomInput] = useState(false);
  const [inputError, setInputError] = useState('');

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Get theme
  const theme = useTheme();

  // Initialize steps when component mounts or params change
  useEffect(() => {
    if (params) {
      setString1(params.string1 || 'ABCBDAB');
      setString2(params.string2 || 'BDCABA');
    }
  }, [params]);

  // Generate initial steps when component mounts
  useEffect(() => {
    // Generate steps on initial mount
    generateSteps();
  }, []);

  // Generate steps when parameters change - only when Apply button is clicked
  const generateSteps = () => {
    if (string1 && string2) {
      console.log('Generating steps for', string1, string2);
      const result = generateLCSSteps(string1, string2);

      // Set steps and movements if the parent component provides these functions
      if (typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (typeof setMovements === 'function') {
        setMovements(result.steps.map(step => step.movement));
      }
    }
  };

  // We don't need to update LCS result anymore since we removed the result display

  // Scroll to current step in the movement sequence
  useEffect(() => {
    if (movementContainerRef.current) {
      const container = movementContainerRef.current;
      const activeElement = container.querySelector('.active-step');

      if (activeElement) {
        // Calculate the scroll position to keep it within the container
        const containerRect = container.getBoundingClientRect();
        const activeRect = activeElement.getBoundingClientRect();

        // Check if the active element is outside the visible area
        if (activeRect.top < containerRect.top || activeRect.bottom > containerRect.bottom) {
          // Scroll only within the container
          container.scrollTop = activeElement.offsetTop - container.offsetTop - (containerRect.height / 2) + (activeRect.height / 2);
        }
      }
    }
  }, [step]);

  // Handle string input changes
  const handleString1Change = (e) => {
    setString1(e.target.value);
    setIsCustomInput(true);
    setInputError('');
  };

  const handleString2Change = (e) => {
    setString2(e.target.value);
    setIsCustomInput(true);
    setInputError('');
  };

  // Apply custom strings
  const handleApplyStrings = () => {
    if (!string1 || !string2) {
      setInputError('Both strings are required');
      return;
    }

    // Update params
    onParamChange({
      ...params,
      string1,
      string2
    });

    // Generate steps with the new strings
    generateSteps();

    // Reset state
    setState('idle');
    setStep(0);
  };

  // Handle speed change
  const handleSpeedChange = (_, newValue) => {
    setSpeed(newValue);
  };

  // Handle control button clicks
  const handleStart = () => {
    // If we're at step 0, make sure we're showing the first step
    if (step === 0) {
      setStep(0);
    }

    // Set state to running after a small delay to ensure UI updates
    setTimeout(() => {
      setState('running');
    }, 0);
  };

  const handlePause = () => {
    setState('paused');
  };

  const handleReset = () => {
    setStep(0);
    setState('idle');
  };

  const handleStepForward = () => {
    if (step < totalSteps - 1) {
      setState('paused');
      setStep(step + 1);
    }
  };

  const handleStepBackward = () => {
    if (step > 0) {
      setState('paused');
      setStep(step - 1);
    }
  };

  // Calculate progress percentage
  const progressPercentage = totalSteps > 0 ? ((step + 1) / totalSteps) * 100 : 0;

  // Pseudocode for LCS algorithm
  const pseudocode = [
    "function LCS(X, Y):",
    "    m = length(X)",
    "    n = length(Y)",
    "    Create table L[m+1][n+1] initialized with 0",
    "",
    "    for i = 1 to m:",
    "        for j = 1 to n:",
    "            if X[i-1] == Y[j-1]:",
    "                L[i][j] = L[i-1][j-1] + 1",
    "            else:",
    "                L[i][j] = max(L[i-1][j], L[i][j-1])",
    "",
    "    // Traceback to find the LCS",
    "    i = m, j = n",
    "    lcs = empty string",
    "    while i > 0 and j > 0:",
    "        if X[i-1] == Y[j-1]:",
    "            lcs = X[i-1] + lcs",
    "            i = i - 1",
    "            j = j - 1",
    "        else if L[i-1][j] > L[i][j-1]:",
    "            i = i - 1",
    "        else:",
    "            j = j - 1",
    "",
    "    return lcs"
  ];

  return (
    <Stack spacing={1.5} sx={{ p: 1.5, height: '100%', overflowY: 'auto', bgcolor: 'background.default', borderRadius: 1 }}>
      {/* Algorithm Title */}
      <Typography variant="h6" gutterBottom>
        Longest Common Subsequence
      </Typography>

      {/* Collapsible Information Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            Information
          </Typography>
          <IconButton
            size="small"
            onClick={() => setInfoExpanded(!infoExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {infoExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={infoExpanded} sx={{ mb: 1.5 }}>
          <Box sx={{ p: 1, bgcolor: 'action.hover', borderRadius: 1, fontSize: '0.85rem' }}>
            <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
              About Longest Common Subsequence:
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              The Longest Common Subsequence (LCS) problem is finding the longest subsequence common to all sequences in a set of sequences. It's a classic dynamic programming problem.
            </Typography>
            <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
              Time Complexity:
            </Typography>
            <Typography variant="body2" sx={{ mb: 0.5 }}>
              - O(m*n) where m and n are the lengths of the two strings
            </Typography>
            <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
              Space Complexity:
            </Typography>
            <Typography variant="body2">
              - O(m*n) for the dynamic programming table
            </Typography>
          </Box>
        </Collapse>
      </Paper>

      {/* Parameters Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <ViewArrayIcon fontSize="small" sx={{ mr: 0.5 }} /> Parameters
          </Typography>
          <IconButton
            size="small"
            onClick={() => setParamsExpanded(!paramsExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {paramsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={paramsExpanded}>
          <Stack spacing={2}>
            <Box>
              <TextField
                label="String 1"
                value={string1}
                onChange={handleString1Change}
                fullWidth
                margin="dense"
                variant="outlined"
                error={!!inputError}
                helperText={inputError}
                size="small"
              />
            </Box>

            <Box>
              <TextField
                label="String 2"
                value={string2}
                onChange={handleString2Change}
                fullWidth
                margin="dense"
                variant="outlined"
                error={!!inputError}
                size="small"
              />
            </Box>

            <Button
              variant="contained"
              color="primary"
              onClick={handleApplyStrings}
              disabled={!isCustomInput || state === 'running'}
              sx={{ mt: 1 }}
              fullWidth
            >
              Apply
            </Button>
          </Stack>
        </Collapse>
      </Paper>

      {/* Controls Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <SettingsIcon fontSize="small" sx={{ mr: 0.5 }} /> Controls
          </Typography>
          <IconButton
            size="small"
            onClick={() => setControlsExpanded(!controlsExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {controlsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={controlsExpanded}>
          <Stack spacing={2}>
            {/* Speed Control */}
            <Box>
              <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                <SpeedIcon fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} /> Speed
              </Typography>
              <Slider
                value={speed}
                onChange={handleSpeedChange}
                aria-labelledby="speed-slider"
                valueLabelDisplay="auto"
                step={1}
                marks
                min={1}
                max={10}
              />
            </Box>

            {/* Play/Pause and Reset Buttons */}
            <Stack direction="row" spacing={2} sx={{ '& > span': { flex: 1 }, '& button': { width: '100%' } }}>
              {state === 'running' ? (
                <Tooltip title="Pause">
                  <span>
                    <Button
                      variant="contained"
                      color="warning"
                      startIcon={<PauseIcon />}
                      onClick={handlePause}
                    >
                      Pause
                    </Button>
                  </span>
                </Tooltip>
              ) : (
                <Tooltip title="Play">
                  <span>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon />}
                      onClick={handleStart}
                      disabled={step >= totalSteps - 1}
                    >
                      Play
                    </Button>
                  </span>
                </Tooltip>
              )}

              <Tooltip title="Reset">
                <span>
                  <Button
                    variant="outlined"
                    startIcon={<RestartAltIcon />}
                    onClick={handleReset}
                    disabled={state === 'idle' && step === 0}
                  >
                    Reset
                  </Button>
                </span>
              </Tooltip>
            </Stack>

            {/* Step Forward/Backward Buttons */}
            <Stack direction="row" spacing={2} sx={{ '& > span': { flex: 1 }, '& button': { width: '100%' } }}>
              <Tooltip title="Step Backward">
                <span>
                  <Button
                    variant="outlined"
                    startIcon={<SkipPreviousIcon />}
                    onClick={handleStepBackward}
                    disabled={step <= 0 || state === 'running'}
                  >
                    Back
                  </Button>
                </span>
              </Tooltip>

              <Tooltip title="Step Forward">
                <span>
                  <Button
                    variant="outlined"
                    endIcon={<SkipNextIcon />}
                    onClick={handleStepForward}
                    disabled={step >= totalSteps - 1 || state === 'running'}
                  >
                    Next
                  </Button>
                </span>
              </Tooltip>
            </Stack>
          </Stack>
        </Collapse>
      </Paper>

      {/* Progress Indicator Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <TimelineIcon fontSize="small" sx={{ mr: 0.5 }} /> Progress
          </Typography>
          <IconButton
            size="small"
            onClick={() => setProgressExpanded(!progressExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {progressExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={progressExpanded}>
          <Stack spacing={2}>
            {/* Progress Bar */}
            <Box sx={{ p: 1.5, bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
              <Stack spacing={1.5}>
                {/* Step Counter with Circular Progress */}
                <Stack direction="row" spacing={2} alignItems="center">
                  <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                    <CircularProgress
                      variant="determinate"
                      value={Math.round(progressPercentage) || 0}
                      size={60}
                      thickness={5}
                      sx={{
                        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                        '& .MuiCircularProgress-circle': {
                          strokeLinecap: 'round',
                        },
                      }}
                    />
                    <Box
                      sx={{
                        top: 0,
                        left: 0,
                        bottom: 0,
                        right: 0,
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="caption" component="div" sx={{
                        fontWeight: 'bold',
                        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.dark
                      }}>
                        {`${Math.round(progressPercentage) || 0}%`}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                      Step: {step + 1} / {totalSteps}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.round(progressPercentage) || 0}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                        },
                      }}
                    />
                  </Box>
                </Stack>

                {/* Status Indicator */}
                <Box sx={{
                  p: 1,
                  borderRadius: 1,
                  bgcolor: theme.palette.mode === 'dark' ?
                    (state === 'idle' ? 'rgba(41, 182, 246, 0.2)' :
                      state === 'running' ? 'rgba(76, 175, 80, 0.2)' :
                        state === 'paused' ? 'rgba(255, 152, 0, 0.2)' :
                          state === 'completed' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(0, 0, 0, 0.1)') :
                    (state === 'idle' ? 'rgba(41, 182, 246, 0.1)' :
                      state === 'running' ? 'rgba(76, 175, 80, 0.1)' :
                        state === 'paused' ? 'rgba(255, 152, 0, 0.1)' :
                          state === 'completed' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(0, 0, 0, 0.05)'),
                  color: theme.palette.mode === 'dark' ?
                    (state === 'idle' ? '#29B6F6' :
                      state === 'running' ? '#4CAF50' :
                        state === 'paused' ? '#FF9800' :
                          state === 'completed' ? '#4CAF50' : theme.palette.text.primary) :
                    (state === 'idle' ? '#0288D1' :
                      state === 'running' ? '#2E7D32' :
                        state === 'paused' ? '#E65100' :
                          state === 'completed' ? '#2E7D32' : theme.palette.text.primary),
                }}>
                  <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
                    {state === 'idle' && <HourglassEmptyIcon fontSize="small" />}
                    {state === 'running' && <CircularProgress size={16} thickness={5} sx={{ color: 'inherit' }} />}
                    {state === 'paused' && <PendingIcon fontSize="small" />}
                    {state === 'completed' && <CheckCircleIcon fontSize="small" />}
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      Status: {state.charAt(0).toUpperCase() + state.slice(1)}
                    </Typography>
                  </Stack>
                </Box>


              </Stack>
            </Box>
          </Stack>
        </Collapse>
      </Paper>

      {/* Steps Sequence Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, mb: 1.5 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <FormatListNumberedIcon fontSize="small" sx={{ mr: 0.5 }} /> Steps Sequence
          </Typography>
          <IconButton
            size="small"
            onClick={() => setMovementExpanded(!movementExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {movementExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={movementExpanded}>
          <Box
            ref={movementContainerRef}
            sx={{
              height: '200px', // Fixed height
              overflowY: 'auto',
              p: 1,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
              borderRadius: 1,
              '&::-webkit-scrollbar': {
                width: '8px',
                backgroundColor: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
              },
              // Add shadow to indicate scrollable content
              boxShadow: 'inset 0 -10px 10px -10px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* Only show steps if we've started the algorithm */}
            {(state !== 'idle' ? steps.slice(0, step + 1) : []).map((stepData, index) => (
              <Box
                key={index}
                className={index === step ? 'active-step' : ''}
                sx={{
                  p: 1,
                  mb: 0.5,
                  borderRadius: 1,
                  bgcolor: index === step
                    ? theme.palette.mode === 'dark' ? 'primary.dark' : 'primary.light'
                    : 'transparent',
                  color: index === step
                    ? theme.palette.mode === 'dark' ? 'white' : 'primary.contrastText'
                    : 'text.primary',
                  transition: 'background-color 0.3s',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: index !== step
                      ? theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                      : null
                  }
                }}
                onClick={() => setStep(index)}
              >
                <Typography
                  variant="body2"
                  sx={{ fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace', fontSize: '0.85rem' }}
                >
                  {index + 1}. {
                    stepData.type === 'match' ? (
                      <>
                        Characters match: <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>{stepData.str1[stepData.current.i - 1]}</Box> at positions <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>({stepData.current.i}, {stepData.current.j})</Box>
                      </>
                    ) : stepData.type === 'mismatch' ? (
                      <>
                        Characters don't match: <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold' }}>{stepData.str1[stepData.current.i - 1]}</Box> ≠ <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold' }}>{stepData.str2[stepData.current.j - 1]}</Box>
                      </>
                    ) : stepData.type === 'traceback_match' ? (
                      <>
                        Found matching character <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>{stepData.str1[stepData.current.i]}</Box> in the LCS
                      </>
                    ) : stepData.type === 'traceback_up' || stepData.type === 'traceback_left' ? (
                      <>
                        Move <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold' }}>{stepData.type === 'traceback_up' ? 'up' : 'left'}</Box> to position <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>({stepData.current.i}, {stepData.current.j})</Box>
                      </>
                    ) : stepData.type === 'complete' ? (
                      <>
                        LCS algorithm complete. Result: <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>{stepData.lcs}</Box>
                      </>
                    ) : (
                      stepData.movement
                    )
                  }
                </Typography>
              </Box>
            ))}
          </Box>
        </Collapse>
      </Paper>

      {/* Algorithm Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <CodeIcon fontSize="small" sx={{ mr: 0.5 }} /> Algorithm
          </Typography>
        </Box>
        <Box
          sx={{
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
            p: 1.5,
            borderRadius: 1,
            fontFamily: 'monospace',
            fontSize: '0.9rem',
            overflowX: 'auto',
            mb: 2
          }}
        >
          {pseudocode.map((line, index) => (
            <Typography
              key={index}
              component="div"
              sx={{
                whiteSpace: 'pre',
                color: line.includes('//') ? 'text.secondary' : 'text.primary',
                fontWeight: line.includes('function') ? 'bold' : 'normal',
                ml: line.startsWith(' ') ? 2 : 0
              }}
            >
              {line || ' '}
            </Typography>
          ))}
        </Box>
      </Paper>
    </Stack>
  );
};

export default LCSController;