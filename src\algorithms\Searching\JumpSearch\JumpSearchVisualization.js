// JumpSearchVisualization.js
// This component provides the visualization for the Jump Search algorithm.

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateJumpSearchSteps } from './JumpSearchAlgorithm';
import * as THREE from 'three';

// Import reusable visualization components
import { Bar, ColorLegend, StepBoard, GroundPlane, Arrow } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.8;
const BAR_SPACING = 0.3;
const MAX_BAR_HEIGHT = 3.5;
const CAMERA_POSITION = [0, 6, 12];
const CAMERA_LOOKAT = [0, 0, -2];

// Pointer component for showing the current index
const Pointer = ({ position = [0, 0, 0], label = '', color = '#ffffff', visible = true, size = 0.3 }) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';
  
  return (
    <group position={position} visible={visible}>
      {/* Arrow */}
      <mesh position={[0, 0.5, 0]}>
        <coneGeometry args={[size, size * 2, 16]} />
        <meshStandardMaterial color={color} />
      </mesh>
      
      {/* Label */}
      <Html
        position={[0, 1.2, 0]}
        center
        style={{
          color: color,
          fontSize: '16px',
          fontWeight: 'bold',
          padding: '2px 6px',
          backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
          borderRadius: '4px',
          whiteSpace: 'nowrap',
        }}
      >
        {label}
      </Html>
    </group>
  );
};

// Target indicator component for showing the target value
const TargetIndicator = ({ position = [0, 0, 0], value, visible = true }) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';
  
  return (
    <group position={position} visible={visible}>
      {/* Static ring */}
      <mesh>
        <torusGeometry args={[0.6, 0.05, 16, 32]} />
        <meshStandardMaterial color={isDark ? '#f44336' : '#d32f2f'} />
      </mesh>
      
      {/* Target value without background */}
      <Html
        position={[0, 0, 0]}
        center
        style={{
          color: '#ffffff',
          fontSize: '18px',
          fontWeight: 'bold',
          textShadow: '0 0 3px rgba(0,0,0,0.8), 0 0 5px rgba(0,0,0,0.6)',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
        }}
      >
        {value}
      </Html>
    </group>
  );
};

// Block indicator component for showing the search block
const BlockIndicator = ({ startX, endX, y = -0.5, color = '#ffffff', visible = true }) => {
  const width = endX - startX;
  
  return (
    <group position={[(startX + endX) / 2, y, 0]} visible={visible}>
      <mesh>
        <boxGeometry args={[width, 0.1, 0.1]} />
        <meshStandardMaterial color={color} transparent opacity={0.7} />
      </mesh>
      
      {/* Left cap */}
      <mesh position={[-width / 2, 0, 0]}>
        <boxGeometry args={[0.1, 0.3, 0.1]} />
        <meshStandardMaterial color={color} />
      </mesh>
      
      {/* Right cap */}
      <mesh position={[width / 2, 0, 0]}>
        <boxGeometry args={[0.1, 0.3, 0.1]} />
        <meshStandardMaterial color={color} />
      </mesh>
    </group>
  );
};

// Jump indicator component for showing the jump
const JumpIndicator = ({ startX, endX, y = 4, color = '#ffffff', visible = true, label = '' }) => {
  return (
    <group visible={visible}>
      <Arrow 
        start={[startX, y, 0]} 
        end={[endX, y, 0]} 
        color={color}
        thickness={0.05}
        headSize={0.2}
        label={label}
        showLabel={true}
      />
    </group>
  );
};

// Main visualization component
const JumpSearchVisualization = ({ params = {} }) => {
    const theme = useTheme();
    const { camera } = useThree();
    const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
    const { speed } = useSpeed();

    // Refs for animation control
    const speedRef = useRef(speed);
    const stepsRef = useRef([]);
    const initialArrayRef = useRef([]);
    const lastAppliedStepRef = useRef(-1);
    const animatingRef = useRef(false);
    const timeoutIdRef = useRef(null);
    const stateRef = useRef(state);
    const currentStepRef = useRef(step);

    // State for array data
    const [arrayData, setArrayData] = useState([]);
    const [target, setTarget] = useState(0);

    // State for visualization
    const [currentIndex, setCurrentIndex] = useState(-1);
    const [prevIndex, setPrevIndex] = useState(-1);
    const [jumpSize, setJumpSize] = useState(0);
    const [linearIndex, setLinearIndex] = useState(-1);
    const [found, setFound] = useState(false);
    const [result, setResult] = useState(-1);
    
    // Animation states
    const [prevPointerVisible, setPrevPointerVisible] = useState(false);
    const [currentPointerVisible, setCurrentPointerVisible] = useState(false);
    const [linearPointerVisible, setLinearPointerVisible] = useState(false);
    
    const [blockIndicatorVisible, setBlockIndicatorVisible] = useState(false);
    const [jumpIndicatorVisible, setJumpIndicatorVisible] = useState(false);
    const [jumpStartX, setJumpStartX] = useState(0);
    const [jumpEndX, setJumpEndX] = useState(0);
    const [jumpLabel, setJumpLabel] = useState('');
    
    const [targetIndicatorVisible, setTargetIndicatorVisible] = useState(false);
    const [targetIndicatorPosition, setTargetIndicatorPosition] = useState([0, 0, 0]);
    
    const [highlightedIndices, setHighlightedIndices] = useState([]);
    
    // Animation for the pointer movement
    const [pointerAnimation, setPointerAnimation] = useState({
        active: false,
        fromIndex: -1,
        toIndex: -1,
        progress: 0,
        startTime: 0
    });
    
    // Colors based on theme
    const colors = useMemo(() => {
        const isDark = theme.palette.mode === 'dark';
        return {
            bar: isDark ? '#64b5f6' : '#2196f3',      // Default bar color
            current: isDark ? '#ce93d8' : '#9c27b0',  // Current element being considered
            compare: isDark ? '#ffb74d' : '#ff9800',  // Element being compared
            found: isDark ? '#81c784' : '#4caf50',    // Found element
            notFound: isDark ? '#e57373' : '#f44336', // Not found
            prev: isDark ? '#4fc3f7' : '#03a9f4',     // Previous pointer
            jump: isDark ? '#ff8a65' : '#ff5722',     // Jump indicator
            block: isDark ? '#b39ddb' : '#673ab7',    // Block indicator
            linear: isDark ? '#4db6ac' : '#009688',   // Linear search pointer
            target: isDark ? '#f44336' : '#d32f2f',   // Target value
            base: isDark ? '#1a1a1a' : '#f5f5f5',     // Base color
            ground: isDark ? '#121212' : '#eeeeee',   // Ground color
        };
    }, [theme.palette.mode]);

    // Update refs when state changes
    useEffect(() => {
        stateRef.current = state;
    }, [state]);

    useEffect(() => {
        currentStepRef.current = step;
    }, [step]);

    useEffect(() => {
        speedRef.current = speed;
    }, [speed]);

    // Initialize array when params change
    useEffect(() => {
        // Clear any existing animation timeouts
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state
        animatingRef.current = false;
        setPointerAnimation({
            active: false,
            fromIndex: -1,
            toIndex: -1,
            progress: 0,
            startTime: 0
        });

        // Extract parameters with safety checks
        const { arraySize = 10, randomize = true, customArray = [], target = 42 } = params;

        // Generate array data
        let newArray = [];

        // Use custom array if provided
        if (customArray && customArray.length > 0) {
            newArray = [...customArray];
        } else if (randomize) {
            // Generate random sorted array
            newArray = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 99) + 1
            ).sort((a, b) => a - b);
        } else {
            // Generate sequential array
            newArray = Array.from({ length: arraySize }, (_, i) => (i + 1) * Math.floor(99 / arraySize));
        }

        // Store the initial array
        initialArrayRef.current = [...newArray];
        setArrayData(newArray);
        setTarget(target);

        // Generate steps
        const { steps } = generateJumpSearchSteps(newArray, target);
        stepsRef.current = steps;
        setSteps(steps);
        setTotalSteps(steps.length);
        setAlgorithmSteps(steps.map(step => step.movement));

        // Reset visualization state
        setCurrentIndex(-1);
        setPrevIndex(-1);
        setJumpSize(0);
        setLinearIndex(-1);
        setFound(false);
        setResult(-1);
        
        setPrevPointerVisible(false);
        setCurrentPointerVisible(false);
        setLinearPointerVisible(false);
        setBlockIndicatorVisible(false);
        setJumpIndicatorVisible(false);
        setTargetIndicatorVisible(false);
        setHighlightedIndices([]);

        // Reset step if needed
        setStep(0);
        lastAppliedStepRef.current = -1;

        // Position camera
        if (camera) {
            camera.position.set(...CAMERA_POSITION);
            camera.lookAt(...CAMERA_LOOKAT);
        }

        // Force a small delay to ensure everything is reset
        setTimeout(() => {
            // Apply the initial step
            if (steps.length > 0) {
                applyStep(0);
            }
        }, 50);
    }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

    // Apply a single step of the Jump Search algorithm
    const applyStep = (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
            return;
        }

        const currentStep = stepsRef.current[stepIndex];
        lastAppliedStepRef.current = stepIndex;

        // Update array data if needed
        if (currentStep.array) {
            setArrayData(currentStep.array);
        }

        // Update target value if needed
        if (currentStep.target !== undefined) {
            setTarget(currentStep.target);
        }

        // Update visualization state based on step type
        switch (currentStep.type) {
            case 'init':
                setCurrentIndex(-1);
                setPrevIndex(-1);
                setJumpSize(0);
                setLinearIndex(-1);
                setFound(false);
                setResult(-1);
                
                setPrevPointerVisible(false);
                setCurrentPointerVisible(false);
                setLinearPointerVisible(false);
                setBlockIndicatorVisible(false);
                setJumpIndicatorVisible(false);
                setTargetIndicatorVisible(true);
                setHighlightedIndices([]);
                
                // Position the target indicator
                setTargetIndicatorPosition([0, 5, 0]);
                break;

            case 'checkSorted':
                // No visual changes, just informational
                break;

            case 'setJumpSize':
                setJumpSize(currentStep.jumpSize);
                break;

            case 'startSearch':
                setCurrentIndex(currentStep.current);
                setPrevIndex(currentStep.prev);
                
                setCurrentPointerVisible(true);
                setPrevPointerVisible(true);
                setHighlightedIndices([currentStep.current]);
                break;

            case 'jump':
                setJumpIndicatorVisible(true);
                setJumpStartX(getXPosition(currentStep.current));
                setJumpEndX(getXPosition(currentStep.nextJump));
                setJumpLabel(`Jump: ${currentStep.jumpSize}`);
                
                // Start animation to move the current pointer
                animatingRef.current = true;
                setPointerAnimation({
                    active: true,
                    fromIndex: currentStep.current,
                    toIndex: currentStep.nextJump,
                    progress: 0,
                    startTime: performance.now()
                });
                break;

            case 'compare':
                setCurrentIndex(currentStep.current);
                setPrevIndex(currentStep.prev);
                
                setCurrentPointerVisible(true);
                setPrevPointerVisible(true);
                setJumpIndicatorVisible(false);
                setHighlightedIndices([currentStep.current]);
                break;

            case 'foundBlock':
                setBlockIndicatorVisible(true);
                setHighlightedIndices([]);
                break;

            case 'linearSearch':
                setLinearIndex(currentStep.linearIndex);
                
                setLinearPointerVisible(true);
                setHighlightedIndices([currentStep.linearIndex]);
                break;

            case 'linearCompare':
                setLinearIndex(currentStep.linearIndex);
                
                setLinearPointerVisible(true);
                setHighlightedIndices([currentStep.linearIndex]);
                break;

            case 'found':
                setFound(true);
                setResult(currentStep.result);
                
                setPrevPointerVisible(false);
                setCurrentPointerVisible(false);
                setLinearPointerVisible(false);
                setBlockIndicatorVisible(false);
                setHighlightedIndices([currentStep.linearIndex]);
                
                // Move the target indicator to the found element
                setTargetIndicatorPosition([getXPosition(currentStep.linearIndex), 2, 0]);
                
                // Animate the found element
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;
                    
                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 1000);
                break;

            case 'passedTarget':
                setLinearIndex(currentStep.linearIndex);
                
                setLinearPointerVisible(true);
                setHighlightedIndices([currentStep.linearIndex]);
                break;

            case 'notFound':
                setFound(false);
                
                setPrevPointerVisible(false);
                setCurrentPointerVisible(false);
                setLinearPointerVisible(false);
                setBlockIndicatorVisible(false);
                setHighlightedIndices([]);
                
                // Animate the target indicator to show not found
                animatingRef.current = true;
                setTimeout(() => {
                    animatingRef.current = false;
                    
                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50);
                    }
                }, 1000);
                break;

            case 'complete':
                setFound(currentStep.found);
                setResult(currentStep.result);
                
                if (currentStep.found) {
                    setHighlightedIndices([currentStep.result]);
                    setTargetIndicatorPosition([getXPosition(currentStep.result), 2, 0]);
                } else {
                    setHighlightedIndices([]);
                    setTargetIndicatorPosition([0, 5, 0]);
                }
                break;

            default:
                break;
        }
    };

    // Helper function to get X position for an index
    const getXPosition = (index) => {
        const totalWidth = (BAR_WIDTH + BAR_SPACING) * arrayData.length;
        const startX = -(totalWidth / 2) + (BAR_WIDTH / 2);
        return startX + index * (BAR_WIDTH + BAR_SPACING);
    };

    // Handle step changes
    useEffect(() => {
        // Clear any existing timeout
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state if step changes manually
        if (animatingRef.current && lastAppliedStepRef.current !== step - 1) {
            animatingRef.current = false;
            setPointerAnimation(prev => ({
                ...prev,
                active: false
            }));
        }

        // Don't apply steps if we're still animating and the step is sequential
        if (animatingRef.current && lastAppliedStepRef.current === step - 1) {
            return;
        }

        // Apply the current step
        applyStep(step);
    }, [step]);

    // Handle automatic stepping when state is 'running'
    useEffect(() => {
        // Reset animation state when state changes to idle
        if (state === 'idle') {
            // Clear any existing timeouts
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }

            // Reset animation state
            animatingRef.current = false;
            setPointerAnimation({
                active: false,
                fromIndex: -1,
                toIndex: -1,
                progress: 0,
                startTime: 0
            });

            // Apply the initial step
            if (stepsRef.current && stepsRef.current.length > 0) {
                applyStep(0);
            }

            return;
        }

        // Only proceed if state is 'running'
        if (state !== 'running') {
            return;
        }

        const steps = stepsRef.current || [];

        // Stop if we've reached the end
        if (step >= steps.length) {
            setState('completed');
            return;
        }

        // Don't schedule next step if we're animating
        if (animatingRef.current) {
            return;
        }

        // Schedule the next step with a delay
        const delay = Math.max(200, 800 - (speedRef.current * 80));
        const timeoutId = setTimeout(() => {
            // Only increment if still in running state
            if (stateRef.current === 'running' && !animatingRef.current) {
                setStep(prevStep => prevStep + 1);
            }
        }, delay);

        // Store the timeout ID for cleanup
        timeoutIdRef.current = timeoutId;

        // Cleanup function
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }
        };
    }, [state, step, setStep, setState]);

    // Animation frame for continuous animations
    useFrame(() => {
        // Animate the pointer movement
        if (pointerAnimation.active) {
            // Use performance.now() for more accurate timing
            const now = performance.now();
            const elapsed = now - pointerAnimation.startTime;
            
            // Adjust duration based on speed - slower for better visibility
            const baseDuration = 1000; // Shorter base duration for faster animation
            const duration = Math.max(500, baseDuration - (speedRef.current * 100));
            
            const progress = Math.min(elapsed / duration, 1);

            // Only update if there's a meaningful change to reduce unnecessary renders
            if (Math.abs(progress - pointerAnimation.progress) > 0.01) {
                setPointerAnimation(prev => ({
                    ...prev,
                    progress
                }));
                
                // Update current pointer position
                const fromX = getXPosition(pointerAnimation.fromIndex);
                const toX = getXPosition(pointerAnimation.toIndex);
                const x = fromX + (toX - fromX) * progress;
                
                // Add a slight arc for better visual effect
                const arcHeight = 0.5;
                const midPoint = Math.sin(progress * Math.PI);
                const y = -1 + arcHeight * midPoint;
                
                setCurrentIndex(pointerAnimation.toIndex);
                setCurrentPointerVisible(true);
            }

            // If animation is complete, mark it as inactive
            if (progress >= 1) {
                // Small delay to ensure the animation completes visually
                setTimeout(() => {
                    setPointerAnimation(prev => ({
                        ...prev,
                        active: false
                    }));
                    animatingRef.current = false;
                    
                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50); // Shorter delay before next step
                    }
                }, 50); // Shorter delay for faster transitions
            }
        }
    });

    // Calculate maximum value for scaling
    const maxValue = useMemo(() => {
        if (!arrayData || arrayData.length === 0) return 1;
        return Math.max(...arrayData);
    }, [arrayData]);

    // Get the current step data for the step board
    const currentStepData = useMemo(() => {
        if (step >= 0 && step < stepsRef.current?.length) {
            return stepsRef.current[step];
        }
        return null;
    }, [step]);

    // Render the visualization
    return (
        <group position={[0, -2, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <directionalLight position={[-10, 10, -10]} intensity={0.4} />
            <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

            {/* Ground plane */}
            <GroundPlane 
                position={[0, -0.3, 0]} 
                width={100} 
                depth={100} 
                color={colors.ground} 
                receiveShadow={true} 
            />

            {/* Step Board */}
            <StepBoard 
                position={[0, 6, 0.5]} 
                description={currentStepData?.movement || ''} 
                stepData={currentStepData || {}} 
                currentStep={step + 1} 
                totalSteps={stepsRef.current.length} 
                showBackground={false}
            />

            {/* Color Legend */}
            <ColorLegend 
                position={[0, -3, 0.5]} 
                colors={colors} 
                items={[
                    { label: 'Default', color: colors.bar },
                    { label: 'Current', color: colors.current },
                    { label: 'Previous', color: colors.prev },
                    { label: 'Linear', color: colors.linear },
                    { label: 'Found', color: colors.found },
                    { label: 'Target', color: colors.target }
                ]} 
            />

            {/* Block Indicator */}
            {blockIndicatorVisible && prevIndex >= 0 && currentIndex >= 0 && (
                <BlockIndicator 
                    startX={getXPosition(prevIndex)} 
                    endX={getXPosition(currentIndex)} 
                    y={-0.5} 
                    color={colors.block} 
                    visible={blockIndicatorVisible} 
                />
            )}

            {/* Jump Indicator */}
            {jumpIndicatorVisible && (
                <JumpIndicator 
                    startX={jumpStartX} 
                    endX={jumpEndX} 
                    y={4} 
                    color={colors.jump} 
                    visible={jumpIndicatorVisible} 
                    label={jumpLabel}
                />
            )}

            {/* Array Bars */}
            {arrayData.map((value, index) => {
                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;
                const x = getXPosition(index);
                
                // Determine bar color based on current state
                let barColor = colors.bar; // Default color
                
                if (found && index === result) {
                    barColor = colors.found;
                } else if (highlightedIndices.includes(index)) {
                    barColor = colors.current;
                } else if (index === prevIndex) {
                    barColor = colors.prev;
                } else if (index === linearIndex) {
                    barColor = colors.linear;
                }
                
                // Determine if this bar should be highlighted
                const isHighlighted = highlightedIndices.includes(index);
                
                // Calculate bar scale for animation
                let scaleY = 1;
                if (found && index === result) {
                    // Pulse animation for found element
                    scaleY = 1 + 0.2 * Math.sin(Date.now() * 0.005);
                }
                
                return (
                    <Bar
                        key={`bar-${index}`}
                        position={[x, 0, 0]}
                        height={normalizedHeight}
                        width={BAR_WIDTH}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={true}
                        showIndex={true}
                        showArrow={isHighlighted}
                        depth={BAR_WIDTH}
                        scaleY={scaleY}
                    />
                );
            })}

            {/* Previous Pointer */}
            {prevPointerVisible && prevIndex >= 0 && prevIndex < arrayData.length && (
                <Pointer 
                    position={[getXPosition(prevIndex), -1, 0]} 
                    label="prev" 
                    color={colors.prev} 
                    visible={prevPointerVisible} 
                />
            )}

            {/* Current Pointer */}
            {currentPointerVisible && currentIndex >= 0 && currentIndex < arrayData.length && (
                <Pointer 
                    position={[getXPosition(currentIndex), -1, 0]} 
                    label="curr" 
                    color={colors.current} 
                    visible={currentPointerVisible} 
                />
            )}

            {/* Linear Search Pointer */}
            {linearPointerVisible && linearIndex >= 0 && linearIndex < arrayData.length && (
                <Pointer 
                    position={[getXPosition(linearIndex), -1, 0]} 
                    label="i" 
                    color={colors.linear} 
                    visible={linearPointerVisible} 
                />
            )}

            {/* Target Indicator */}
            <TargetIndicator 
                position={targetIndicatorPosition} 
                value={target} 
                visible={targetIndicatorVisible} 
            />
        </group>
    );
};

export default JumpSearchVisualization;
