// GCDController.js
// This component provides the controls for the Euclidean GCD algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, TextField } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FunctionsIcon from '@mui/icons-material/Functions';
import SettingsIcon from '@mui/icons-material/Settings';

// Import algorithm functions
import { generateGCDSteps } from './GCDAlgorithm';

const GCDController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [a, setA] = useState(params?.a || 48);
  const [b, setB] = useState(params?.b || 18);
  const [method, setMethod] = useState(params?.method || 'division');

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { a, b, method });

    // Update params first
    onParamChange({
      a,
      b,
      method
    });

    // Set steps and movements directly
    try {
      const result = generateGCDSteps({
        a,
        b,
        method
      });
      
      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [a, b, method, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle parameter changes
  const handleAChange = useCallback((event) => {
    const value = parseInt(event.target.value, 10);
    if (!isNaN(value) && value >= 0) {
      setA(value);
    }
  }, []);

  const handleBChange = useCallback((event) => {
    const value = parseInt(event.target.value, 10);
    if (!isNaN(value) && value >= 0) {
      setB(value);
    }
  }, []);

  const handleMethodChange = useCallback((event) => {
    setMethod(event.target.value);
  }, []);

  // Handle apply button click
  const handleApplyClick = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Euclidean GCD algorithm (Division method)
  const divisionPseudocode = [
    { code: "function gcd_division(a, b):", lineNumber: 1, indent: 0 },
    { code: "    // Base case: if b is 0, return a", lineNumber: 2, indent: 0 },
    { code: "    if b == 0:", lineNumber: 3, indent: 1 },
    { code: "        return a", lineNumber: 4, indent: 2 },
    { code: "    // Recursive case: gcd(a, b) = gcd(b, a mod b)", lineNumber: 5, indent: 0 },
    { code: "    remainder = a % b", lineNumber: 6, indent: 1 },
    { code: "    return gcd_division(b, remainder)", lineNumber: 7, indent: 1 },
  ];

  // Pseudocode for Euclidean GCD algorithm (Subtraction method)
  const subtractionPseudocode = [
    { code: "function gcd_subtraction(a, b):", lineNumber: 1, indent: 0 },
    { code: "    // Base case: if b is 0, return a", lineNumber: 2, indent: 0 },
    { code: "    if b == 0:", lineNumber: 3, indent: 1 },
    { code: "        return a", lineNumber: 4, indent: 2 },
    { code: "    // Ensure a >= b", lineNumber: 5, indent: 0 },
    { code: "    if a < b:", lineNumber: 6, indent: 1 },
    { code: "        swap(a, b)", lineNumber: 7, indent: 2 },
    { code: "    // Main loop: subtract the smaller number from the larger", lineNumber: 8, indent: 0 },
    { code: "    while a != b:", lineNumber: 9, indent: 1 },
    { code: "        if a > b:", lineNumber: 10, indent: 2 },
    { code: "            // a is larger, subtract b from a", lineNumber: 11, indent: 0 },
    { code: "            a = a - b", lineNumber: 12, indent: 3 },
    { code: "        else:", lineNumber: 13, indent: 2 },
    { code: "            // b is larger, subtract a from b", lineNumber: 14, indent: 0 },
    { code: "            b = b - a", lineNumber: 15, indent: 3 },
    { code: "    // When a == b, that value is the GCD", lineNumber: 16, indent: 0 },
    { code: "    return a", lineNumber: 17, indent: 1 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Choose the appropriate pseudocode
  const pseudocode = method === 'division' ? divisionPseudocode : subtractionPseudocode;

  // Custom component for method selection
  const MethodSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Method</Typography>
        <RadioGroup
          row
          value={value}
          onChange={onChange}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          <FormControlLabel
            value="division"
            control={<Radio size="small" />}
            label="Division (Faster)"
            disabled={disabled}
          />
          <FormControlLabel
            value="subtraction"
            control={<Radio size="small" />}
            label="Subtraction (Slower)"
            disabled={disabled}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for number input
  const NumberInput = ({ label, value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>{label}</Typography>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          disabled={disabled}
          type="number"
          inputProps={{ min: 0 }}
        />
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Euclidean GCD Algorithm"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Euclidean algorithm is an efficient method for computing the greatest common divisor (GCD) of two integers.
            The GCD of two integers is the largest positive integer that divides both numbers without a remainder.
          </Typography>
          <Typography variant="body2" paragraph>
            There are two main implementations of the Euclidean algorithm:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Division Method:</Typography>
              <Typography variant="body2" paragraph>
                This is the standard and more efficient implementation. It repeatedly divides the larger number by the smaller one
                and replaces the larger number with the smaller one and the smaller number with the remainder.
                The algorithm stops when the remainder becomes zero, and the GCD is the last non-zero remainder.
              </Typography>
              <Typography variant="body2" paragraph>
                Mathematically: gcd(a, b) = gcd(b, a mod b)
              </Typography>
            </li>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Subtraction Method:</Typography>
              <Typography variant="body2" paragraph>
                This is a simpler but less efficient implementation. It repeatedly subtracts the smaller number from the larger one
                until both numbers become equal. The GCD is the value of the equal numbers.
              </Typography>
              <Typography variant="body2" paragraph>
                While this method is easier to understand, it can be much slower for large numbers with a significant difference.
              </Typography>
            </li>
          </ol>
          <Typography variant="body2" paragraph>
            The time complexity of the division method is O(log(min(a, b))), making it very efficient even for large numbers.
            The subtraction method has a time complexity of O(max(a, b)), which can be much slower for certain inputs.
          </Typography>
          <Typography variant="body2" paragraph>
            The Euclidean algorithm was described by Euclid in his Elements (c. 300 BC) and is one of the oldest algorithms still in common use.
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'method',
            type: 'component',
            label: 'Method',
            component: MethodSelector,
            componentProps: {
              value: method,
              onChange: handleMethodChange,
              disabled: state !== 'idle'
            },
            icon: SettingsIcon
          },
          {
            name: 'a',
            type: 'component',
            label: 'First Number (a)',
            component: NumberInput,
            componentProps: {
              label: 'First Number (a)',
              value: a,
              onChange: handleAChange,
              disabled: state !== 'idle'
            },
            icon: FunctionsIcon
          },
          {
            name: 'b',
            type: 'component',
            label: 'Second Number (b)',
            component: NumberInput,
            componentProps: {
              label: 'Second Number (b)',
              value: b,
              onChange: handleBChange,
              disabled: state !== 'idle'
            },
            icon: FunctionsIcon
          }
        ]}
        values={{
          method,
          a,
          b
        }}
        disabled={state === 'running'}
        onApply={handleApplyClick}
        showApplyButton={true}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* No Results Display Section - visualization will handle this */}

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Euclidean GCD Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default GCDController;
