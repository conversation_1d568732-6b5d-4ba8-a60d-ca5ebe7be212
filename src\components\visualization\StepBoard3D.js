// StepBoard3D.js
// A 3D step board component for displaying algorithm steps in a visually appealing way

import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  Html,
  Decal,
  Environment,
  OrbitControls,
  RenderTexture,
  PerspectiveCamera,
  useTexture,
  useCursor,
  Box
} from '@react-three/drei';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';
import { getEnvironmentMap } from '../../utils/environmentMaps';

/**
 * The 3D board that displays the step text
 */
const Board = ({ text, theme, animationSpeed = 1, width = 10, height = 3 }) => {
  const textRef = useRef();
  const boardRef = useRef();
  const isDark = theme?.palette?.mode === 'dark';

  // Colors based on theme
  const backgroundColor = isDark ? '#1e1e1e' : '#f5f5f5';
  const textColor = isDark ? '#ffffff' : '#000000';
  const boardColor = isDark ? '#2a2a2a' : '#ffffff';
  const boardEdgeColor = isDark ? '#3a3a3a' : '#e0e0e0';

  // Animate the board
  useFrame((state) => {
    if (boardRef.current) {
      // Subtle floating animation
      boardRef.current.position.y = Math.sin(state.clock.elapsedTime * animationSpeed * 0.5) * 0.05;
      // Subtle rotation
      boardRef.current.rotation.y = Math.sin(state.clock.elapsedTime * animationSpeed * 0.2) * 0.03;
    }
  });

  return (
    <group>
      {/* The board */}
      <mesh ref={boardRef} castShadow receiveShadow>
        <boxGeometry args={[width * 1.2, height, 0.2]} />
        <meshStandardMaterial color={boardColor} />

        {/* Text decal on the board */}
        <Decal
          position={[0, 0, 0.11]}
          rotation={[0, 0, 0]}
          scale={[width * 1.15, height * 0.95, 1]}
        >
          <meshStandardMaterial
            roughness={0.2}
            transparent
            polygonOffset
            polygonOffsetFactor={-1}
          >
            <RenderTexture attach="map">
              <PerspectiveCamera makeDefault manual aspect={width / height} position={[0, 0, 5]} />
              <color attach="background" args={[backgroundColor]} />
              <ambientLight intensity={Math.PI} />
              <directionalLight position={[10, 10, 5]} />

              {/* Using a div for text instead of Text component */}
              <Html
                position={[-width * 0.45, height * 0.4, 0]}
                transform
                occlude
              >
                <div
                  ref={textRef}
                  style={{
                    fontSize: '16px',
                    fontFamily: '"Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif',
                    fontWeight: 'bold',
                    color: textColor,
                    textAlign: 'left',
                    width: `${width * 10}px`,
                    maxWidth: '1200px',
                    padding: '10px',
                    background: 'transparent',
                    pointerEvents: 'none',
                    userSelect: 'none',
                    lineHeight: '1.6',
                    letterSpacing: '0.01em',
                    textShadow: isDark ? '0 1px 2px rgba(0,0,0,0.5)' : 'none',
                  }}
                >
                  {text}
                </div>
              </Html>
            </RenderTexture>
          </meshStandardMaterial>
        </Decal>

        {/* Board edges */}
        <lineSegments>
          <edgesGeometry attach="geometry" args={[new THREE.BoxGeometry(width, height, 0.2)]} />
          <lineBasicMaterial attach="material" color={boardEdgeColor} linewidth={1} />
        </lineSegments>
      </mesh>
    </group>
  );
};

/**
 * Interactive button for navigating steps
 */
const StepButton = ({ position, onClick, icon, hoverColor = 'hotpink', color = '#4caf50' }) => {
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);
  useCursor(hovered);

  // Animate the button
  useFrame((state, delta) => {
    if (meshRef.current) {
      // Rotate the button
      meshRef.current.rotation.y += delta * 0.5;
      // Scale when hovered
      meshRef.current.scale.setScalar(hovered ? 1.1 : 1);
    }
  });

  return (
    <mesh
      ref={meshRef}
      position={position}
      onClick={onClick}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <boxGeometry args={[0.6, 0.6, 0.6]} />
      <meshStandardMaterial color={hovered ? hoverColor : color} />
      {icon && (
        <Decal position={[0, 0, 0.31]} rotation={[0, 0, 0]} scale={0.4}>
          <meshStandardMaterial transparent polygonOffset polygonOffsetFactor={-1}>
            <RenderTexture attach="map">
              <PerspectiveCamera makeDefault manual position={[0, 0, 5]} />
              <color attach="background" args={['rgba(0,0,0,0)']} />
              <Html transform occlude>
                <div style={{
                  fontSize: '24px',
                  color: 'white',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  pointerEvents: 'none'
                }}>
                  {icon}
                </div>
              </Html>
            </RenderTexture>
          </meshStandardMaterial>
        </Decal>
      )}
    </mesh>
  );
};

/**
 * Main StepBoard3D component
 *
 * @param {Object} props - Component props
 * @param {Array} props.steps - Array of step descriptions
 * @param {number} props.currentStep - Current step index
 * @param {function} props.onStepChange - Function to call when step changes
 * @param {number} props.width - Width of the board
 * @param {number} props.height - Height of the board
 * @param {number} props.animationSpeed - Speed of animations
 */
const StepBoard3D = ({
  steps = [],
  currentStep = 0,
  onStepChange = () => {},
  width = 10,
  height = 3,
  animationSpeed = 1
}) => {
  const theme = useTheme();
  const [step, setStep] = useState(currentStep);

  // Update step when currentStep prop changes
  useEffect(() => {
    console.log('StepBoard3D received steps:', steps, 'currentStep:', currentStep);
    setStep(currentStep);
  }, [currentStep, steps]);

  // Handle step change
  const handleStepChange = (newStep) => {
    if (Array.isArray(steps) && steps.length > 0 && newStep >= 0 && newStep < steps.length) {
      setStep(newStep);
      onStepChange(newStep);
    }
  };

  return (
    <Canvas shadows camera={{ position: [0, 0, 8], fov: 25 }}>
      <color attach="background" args={[theme.palette.mode === 'dark' ? '#121212' : '#f8f8f8']} />
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />

      {/* The step board */}
      <Board
        text={Array.isArray(steps) && steps.length > 0 && step < steps.length ? steps[step] : 'No steps available'}
        theme={theme}
        animationSpeed={animationSpeed}
        width={width}
        height={height}
      />

      {/* Navigation buttons */}
      {Array.isArray(steps) && steps.length > 0 && (
        <>
          <StepButton
            position={[-width/2 * 1.2 - 1, 0, 0]}
            onClick={() => handleStepChange(step - 1)}
            icon="←"
            color="#2196f3"
          />
          <StepButton
            position={[width/2 * 1.2 + 1, 0, 0]}
            onClick={() => handleStepChange(step + 1)}
            icon="→"
            color="#2196f3"
          />
        </>
      )}

      {/* Step indicator */}
      <Html
        position={[0, -height/2 - 0.5, 0]}
        center
        transform
      >
        <div style={{
          fontSize: '15px',
          fontFamily: '"Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif',
          fontWeight: 'bold',
          color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
          textAlign: 'center',
          padding: '8px',
          background: 'transparent',
          pointerEvents: 'none',
          userSelect: 'none',
          letterSpacing: '0.02em',
          textShadow: `0 1px 3px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.8)' : 'rgba(0,0,0,0.3)'}`,
        }}>
          {Array.isArray(steps) && steps.length > 0 ? `Step ${step + 1} of ${steps.length}` : 'No steps available'}
        </div>
      </Html>

      <Environment files={getEnvironmentMap('city')} />
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        minPolarAngle={Math.PI / 3}
        maxPolarAngle={Math.PI / 2.5}
        minAzimuthAngle={-Math.PI / 8}
        maxAzimuthAngle={Math.PI / 8}
      />
    </Canvas>
  );
};

export default StepBoard3D;
