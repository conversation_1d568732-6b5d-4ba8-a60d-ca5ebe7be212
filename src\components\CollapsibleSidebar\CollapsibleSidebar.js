// CollapsibleSidebar.js
// A component that provides a collapsible sidebar with toggle button

import React, { useState, useEffect } from 'react';
import { Box, IconButton, useTheme } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { saveSidebarState, getSidebarState } from '../../utils/layoutStorage';

const CollapsibleSidebar = ({
  children,
  width = 300,
  collapsedWidth = 50,
  position = 'left', // 'left' or 'right'
  bgcolor,
  borderSide = position === 'left' ? 'right' : 'left',
  id, // Optional unique identifier
  ...otherProps
}) => {
  const theme = useTheme();
  // Initialize state from localStorage or default to false
  const [isCollapsed, setIsCollapsed] = useState(() => {
    return getSidebarState();
  });

  // Save collapse state to localStorage when it changes
  useEffect(() => {
    saveSidebarState(isCollapsed);
  }, [isCollapsed]);

  // Toggle sidebar collapse state
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Determine border side
  const borderStyle = {
    [`border${borderSide.charAt(0).toUpperCase() + borderSide.slice(1)}`]: 1,
    borderColor: "divider",
  };

  // Determine toggle button position
  const toggleButtonPosition = position === 'left'
    ? { right: -15 }
    : { left: -15 };

  return (
    <Box
      sx={{
        width: isCollapsed ? collapsedWidth : width,
        bgcolor: bgcolor || (theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.8)'),
        display: "flex",
        flexDirection: "column",
        position: "relative",
        overflow: "hidden",
        boxShadow: theme.palette.mode === 'dark' ? '0px 0px 10px rgba(0, 0, 0, 0.5)' : '0px 0px 10px rgba(0, 0, 0, 0.1)',
        transition: 'width 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease',
        flexShrink: 0, // Prevent the sidebar from shrinking
        flexGrow: 0, // Prevent the sidebar from growing
        zIndex: 10, // Ensure sidebar is above other elements
        ...borderStyle,
        ...otherProps
      }}
    >
      {/* Toggle Button */}
      <IconButton
        onClick={toggleCollapse}
        size="small"
        className="sidebar-toggle" // Add class for event listeners
        sx={{
          position: 'absolute',
          top: 10,
          zIndex: 1200,
          bgcolor: theme.palette.background.paper,
          boxShadow: theme.palette.mode === 'dark' ? '0px 0px 5px rgba(255, 255, 255, 0.2)' : '0px 0px 5px rgba(0, 0, 0, 0.2)',
          '&:hover': {
            bgcolor: theme.palette.action.hover,
          },
          transition: 'all 0.3s ease',
          ...toggleButtonPosition
        }}
      >
        {position === 'left'
          ? (isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />)
          : (isCollapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />)
        }
      </IconButton>

      {/* Content */}
      <Box
        sx={{
          flex: 1,
          opacity: isCollapsed ? 0 : 1,
          visibility: isCollapsed ? 'hidden' : 'visible',
          transition: 'opacity 0.3s ease',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {children}
      </Box>

      {/* Collapsed View Content */}
      {isCollapsed && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            pt: 2,
            gap: 2,
            overflow: 'hidden',
          }}
        >
          {/* You can add collapsed view content here */}
        </Box>
      )}
    </Box>
  );
};

export default CollapsibleSidebar;
