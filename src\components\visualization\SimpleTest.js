import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { Bar, Bucket, ColorLegend, GroundPlane } from './index';

// Simple test scene to verify our components
const SimpleTestScene = () => {
    // Sample data
    const colors = {
        bar: '#2196f3',
        current: '#ff5722',
        sorted: '#4caf50',
        bucket: '#ff9800',
        sorting: '#ffeb3b'
    };

    return (
        <div style={{ width: '100%', height: '100vh' }}>
            <Canvas camera={{ position: [0, 5, 10], fov: 50 }}>
                <ambientLight intensity={0.5} />
                <pointLight position={[10, 10, 10]} intensity={1} />
                
                {/* Ground plane */}
                <GroundPlane 
                    position={[0, -0.5, 0]} 
                    width={50} 
                    depth={50} 
                    color="#f5f5f5" 
                    receiveShadow={true} 
                />
                
                {/* Test bar */}
                <Bar
                    position={[-2, 0, 0]}
                    height={2}
                    width={0.5}
                    color={colors.bar}
                    value={50}
                    index={0}
                    showValue={true}
                    showIndex={true}
                    showArrow={false}
                />
                
                {/* Test bucket */}
                <Bucket
                    position={[2, 0, 0]}
                    width={2}
                    height={2}
                    depth={2}
                    color={colors.bucket}
                    bucketIndex={0}
                    elements={[10, 20, 30]}
                    isHighlighted={false}
                    showLabel={true}
                    showElements={true}
                    animateLid={true}
                />
                
                {/* Color legend */}
                <ColorLegend 
                    colors={colors} 
                    position={[0, 2, 0]} 
                    items={['Default', 'Current', 'Bucket', 'Sorting', 'Sorted']} 
                />
                
                <OrbitControls />
            </Canvas>
        </div>
    );
};

export default SimpleTestScene;
