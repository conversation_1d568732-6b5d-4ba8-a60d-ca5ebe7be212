// NQueensVisualization.js
// 3D visualization component for N-Queens algorithm

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html, shaderMaterial } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { extend } from '@react-three/fiber';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const BASE_CELL_SIZE = 1.0; // Base cell size that will be adjusted dynamically
const BOARD_ELEVATION = 0.2;
const QUEEN_HEIGHT = 1.2;
const QUEEN_BASE_RADIUS = 0.3;
const QUEEN_TOP_RADIUS = 0.2;
const BOARD_GAP = 0.05; // Gap between cells
const LABEL_DISTANCE = 1.5; // Distance of labels from board edge

// Create SDF shader material for smooth 3D cells
const SDFCellMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0xffffff),
    emissive: new THREE.Color(0xffffff),
    emissiveIntensity: 0.3,
    highlighted: 0.0, // 0.0 = not highlighted, 1.0 = highlighted
    isBlack: 0.0, // 0.0 = white cell, 1.0 = black cell
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for crystal-clear cells
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform float highlighted;
    uniform float isBlack;

    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    // SDF for rounded box with sharper edges
    float sdRoundBox(vec3 p, vec3 b, float r) {
      vec3 q = abs(p) - b + r;
      return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }

    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdRoundBox(p, vec3(0.45, 0.05, 0.45), 0.05); // Flat box for cells

      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);

      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);

      // Specular highlight for crystal-like appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 32.0);

      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);

      // Highlight effect - make it glow more when highlighted
      float highlightGlow = highlighted * 0.5 * (1.0 + sin(time * 3.0));

      // Adjust color based on whether it's a black or white cell
      vec3 cellColor = isBlack > 0.5 ? vec3(0.2, 0.2, 0.2) : color;
      vec3 cellEmissive = isBlack > 0.5 ? vec3(0.1, 0.1, 0.1) : emissive;

      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = cellColor * (0.2 + 0.8 * diffuse) +
                        cellEmissive * (emissiveIntensity * (1.0 + pulse + highlightGlow)) +
                        vec3(1.0) * specular * (isBlack > 0.5 ? 0.2 : 0.5);

      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Create SDF shader material for queens
const SDFQueenMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0xf5d442), // Gold color for queens
    emissive: new THREE.Color(0xf5d442),
    emissiveIntensity: 0.5,
    highlighted: 0.0, // 0.0 = not highlighted, 1.0 = highlighted
    active: 0.0, // 0.0 = not active, 1.0 = active
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for queens
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform float highlighted;
    uniform float active;

    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    // SDF for a queen-like shape (simplified as a cylinder with a sphere on top)
    float sdCappedCylinder(vec3 p, float h, float r) {
      vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r, h);
      return min(max(d.x, d.y), 0.0) + length(max(d, 0.0));
    }

    float sdSphere(vec3 p, float r) {
      return length(p) - r;
    }

    float sdQueen(vec3 p) {
      // Base cylinder
      float base = sdCappedCylinder(p - vec3(0.0, -0.1, 0.0), 0.3, 0.2);

      // Top sphere
      float top = sdSphere(p - vec3(0.0, 0.3, 0.0), 0.15);

      // Combine shapes
      return min(base, top);
    }

    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdQueen(p);

      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);

      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);

      // Specular highlight for metallic appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 64.0);

      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);

      // Highlight and active effects
      float glow = (highlighted + active) * 0.5 * (1.0 + sin(time * 3.0));

      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = color * (0.2 + 0.8 * diffuse) +
                        emissive * (emissiveIntensity * (1.0 + pulse + glow)) +
                        vec3(1.0, 0.9, 0.5) * specular * 0.8;

      // Add highlighted tint
      finalColor = mix(finalColor, vec3(1.0, 0.8, 0.0), highlighted * 0.3);

      // Add active tint
      finalColor = mix(finalColor, vec3(1.0, 0.5, 0.0), active * 0.3);

      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom materials
extend({ SDFCellMaterial, SDFQueenMaterial });

// Cell component representing a chessboard cell
const Cell = ({ position, isBlack, isHighlighted, isActive, cellSize, isDark }) => {
  // Use standard materials for better depth and occlusion
  const actualCellSize = cellSize - BOARD_GAP;

  // Define colors based on cell state and theme
  const baseColor = isBlack ?
    (isDark ? '#222222' : '#444444') :
    (isDark ? '#e0e0e0' : '#f0f0f0');
  const highlightColor = isActive ?
    (isDark ? '#ffee58' : '#ffeb3b') :
    (isHighlighted ? (isDark ? '#81c784' : '#4caf50') : baseColor);

  // Calculate a slight hover effect based on whether the cell is active
  const [hoverY, setHoverY] = useState(0);
  useFrame(({ clock }) => {
    if (isActive) {
      setHoverY(Math.sin(clock.getElapsedTime() * 2) * 0.05);
    } else {
      setHoverY(0);
    }
  });

  return (
    <group position={[position[0], position[1] + hoverY, position[2]]}>
      {/* Main cell body */}
      <mesh castShadow receiveShadow>
        <boxGeometry args={[actualCellSize, BOARD_ELEVATION, actualCellSize]} />
        <meshStandardMaterial
          color={highlightColor}
          roughness={0.7}
          metalness={isBlack ? 0.1 : 0.2}
          emissive={isHighlighted || isActive ? highlightColor : '#000000'}
          emissiveIntensity={isHighlighted || isActive ? 0.3 : 0}
        />
      </mesh>

      {/* Subtle edge highlight for better definition */}
      <mesh position={[0, BOARD_ELEVATION/2 + 0.001, 0]}>
        <boxGeometry args={[actualCellSize + 0.02, 0.01, actualCellSize + 0.02]} />
        <meshStandardMaterial
          color={isBlack ?
            (isDark ? '#333333' : '#555555') :
            (isDark ? '#cccccc' : '#ffffff')}
          roughness={0.5}
          metalness={0.3}
          transparent={true}
          opacity={0.7}
        />
      </mesh>
    </group>
  );
};

// Queen component with realistic 3D appearance and proper occlusion
const Queen = ({ position, isActive, isHighlighted, isDark }) => {
  // Define colors based on queen state and theme
  const baseColor = isDark ? '#f5d442' : '#f5d442'; // Gold color for queens
  const activeColor = isDark ? '#ffa726' : '#ff9800'; // Orange for active queens
  const highlightColor = isDark ? '#81c784' : '#4caf50'; // Green for highlighted queens

  const queenColor = isActive ? activeColor : (isHighlighted ? highlightColor : baseColor);

  // Calculate a slight hover effect based on whether the queen is active
  const [hoverY, setHoverY] = useState(0);
  useFrame(({ clock }) => {
    if (isActive) {
      setHoverY(Math.sin(clock.getElapsedTime() * 2) * 0.1);
    } else if (isHighlighted) {
      setHoverY(Math.sin(clock.getElapsedTime() * 1.5) * 0.05);
    } else {
      setHoverY(0);
    }
  });

  // Calculate position with the queen elevated above the board
  const queenPosition = [
    position[0],
    position[1] + BOARD_ELEVATION / 2 + QUEEN_HEIGHT / 2 + hoverY,
    position[2]
  ];

  return (
    <group position={queenPosition}>
      {/* Base of the queen */}
      <mesh castShadow receiveShadow>
        <cylinderGeometry args={[QUEEN_BASE_RADIUS, QUEEN_BASE_RADIUS * 1.2, QUEEN_HEIGHT * 0.5, 16]} />
        <meshStandardMaterial
          color={queenColor}
          roughness={0.3}
          metalness={0.8}
          emissive={queenColor}
          emissiveIntensity={isActive || isHighlighted ? 0.3 : 0.1}
        />
      </mesh>

      {/* Middle section */}
      <mesh castShadow receiveShadow position={[0, QUEEN_HEIGHT * 0.25, 0]}>
        <cylinderGeometry args={[QUEEN_BASE_RADIUS * 0.8, QUEEN_BASE_RADIUS * 0.6, QUEEN_HEIGHT * 0.3, 16]} />
        <meshStandardMaterial
          color={queenColor}
          roughness={0.3}
          metalness={0.8}
          emissive={queenColor}
          emissiveIntensity={isActive || isHighlighted ? 0.3 : 0.1}
        />
      </mesh>

      {/* Crown (sphere on top) */}
      <mesh castShadow receiveShadow position={[0, QUEEN_HEIGHT * 0.45, 0]}>
        <sphereGeometry args={[QUEEN_TOP_RADIUS, 16, 16]} />
        <meshStandardMaterial
          color={queenColor}
          roughness={0.2}
          metalness={0.9}
          emissive={queenColor}
          emissiveIntensity={isActive || isHighlighted ? 0.4 : 0.2}
        />
      </mesh>

      {/* Small decorative point on top */}
      <mesh castShadow position={[0, QUEEN_HEIGHT * 0.45 + QUEEN_TOP_RADIUS * 0.8, 0]}>
        <coneGeometry args={[QUEEN_TOP_RADIUS * 0.3, QUEEN_TOP_RADIUS * 0.6, 8]} />
        <meshStandardMaterial
          color={queenColor}
          roughness={0.2}
          metalness={0.9}
          emissive={queenColor}
          emissiveIntensity={isActive || isHighlighted ? 0.4 : 0.2}
        />
      </mesh>
    </group>
  );
};

// Solution display component with proper occlusion
const SolutionDisplay = ({ solutions, currentSolution, position, isDark }) => {
  if (!solutions || solutions.length === 0) return null;

  // Calculate the size of each mini-board
  const miniSize = 16; // pixels - smaller size
  const gap = 4; // pixels - smaller gap

  return (
    <group position={position}>
      {/* Background plate for better visibility */}
      <mesh castShadow receiveShadow>
        <boxGeometry args={[3, 2, 0.05]} />
        <meshStandardMaterial color={isDark ? "#555555" : "#333333"} opacity={0.8} transparent />
      </mesh>

      {/* HTML content with proper occlusion */}
      <Html
        position={[0, 0, 0.03]}
        center
        occlude
        transform
        distanceFactor={10}
      >
        <div style={{
          color: 'white',
          fontSize: '12px',
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.8)',
          userSelect: 'none',
          background: 'transparent',
          padding: '4px',
          borderRadius: '4px',
          maxWidth: '250px',
          transition: 'opacity 0.2s ease-in-out'
        }}>
          <div style={{ marginBottom: '4px', fontSize: '12px', textAlign: 'center' }}>Solutions: {solutions.length}</div>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: `${gap}px`,
            justifyContent: 'center'
          }}>
            {solutions.map((solution, solutionIndex) => (
              <div
                key={solutionIndex}
                style={{
                  display: 'grid',
                  gridTemplateColumns: `repeat(${solution.length}, ${miniSize}px)`,
                  gridTemplateRows: `repeat(${solution.length}, ${miniSize}px)`,
                  border: solutionIndex === currentSolution ? '2px solid yellow' : '1px solid rgba(255,255,255,0.7)',
                  borderRadius: '2px',
                  overflow: 'hidden',
                  boxShadow: solutionIndex === currentSolution ? '0 0 8px rgba(255,255,0,0.5)' : 'none'
                }}
              >
                {solution.flatMap((row, rowIndex) =>
                  row.map((cell, colIndex) => (
                    <div
                      key={`${rowIndex}-${colIndex}`}
                      style={{
                        width: `${miniSize}px`,
                        height: `${miniSize}px`,
                        backgroundColor: (rowIndex + colIndex) % 2 === 0 ?
                          (isDark ? 'rgba(200,200,200,0.9)' : 'rgba(255,255,255,0.9)') :
                          (isDark ? 'rgba(50,50,50,0.9)' : 'rgba(34,34,34,0.9)'),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '10px',
                        color: (rowIndex + colIndex) % 2 === 0 ? '#000' : '#fff'
                      }}
                    >
                      {cell === 1 && '♛'}
                    </div>
                  ))
                )}
              </div>
            ))}
          </div>
        </div>
      </Html>
    </group>
  );
};

// Main visualization component
const NQueensVisualization = ({
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);

  // Set up initial camera position only once when component mounts
  useEffect(() => {
    if (camera) {
      // Set a reasonable default camera position
      camera.position.set(0, 15, 15);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]); // Only depend on camera, not on currentStepData



  // Define colors based on theme
  const colors = useMemo(() => ({
    whiteCell: isDark ? '#e0e0e0' : '#ffffff',
    blackCell: isDark ? '#222222' : '#444444',
    activeCell: isDark ? '#ffee58' : '#ffeb3b',
    highlightedCell: isDark ? '#81c784' : '#4caf50',
    queen: isDark ? '#f5d442' : '#f5d442',
    activeQueen: isDark ? '#ff9800' : '#ff9800',
    highlightedQueen: isDark ? '#81c784' : '#4caf50',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.whiteCell, label: 'White Cell' },
    { color: colors.blackCell, label: 'Black Cell' },
    { color: colors.activeCell, label: 'Current Cell' },
    { color: colors.highlightedCell, label: 'Valid Position' },
    { color: colors.queen, label: 'Queen' },
    { color: colors.activeQueen, label: 'Current Queen' },
    { color: colors.highlightedQueen, label: 'Solution Queen' },
  ], [colors]);

  // Update state based on current step
  useEffect(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('No steps available or invalid step index');
      return;
    }

    // Store steps in ref for access in animation frame
    stepsRef.current = steps;

    // Get current step data
    const currentStep = steps[step];
    console.log('Current step data:', currentStep);
    setCurrentStepData(currentStep);

    // Update last applied step
    lastAppliedStepRef.current = step;

    // Update total steps
    if (setTotalSteps) {
      setTotalSteps(steps.length);
    }

    // Update movements
    if (setMovements && currentStep.message) {
      setMovements([currentStep.message]);
    }
  }, [step, steps, setTotalSteps, setMovements]);

  // Helper function to calculate delay based on speed
  const calculateDelay = useCallback((speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  }, []);

  // Auto-advance steps when in running state
  useEffect(() => {
    // Update refs
    stateRef.current = state;
    speedRef.current = speed;

    let timeoutId = null;

    if (state === 'running') {
      // If we're at the end, stop the simulation
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached end of steps, stopping simulation');
        setState('paused'); // Change state to paused instead of looping
        return;
      }

      // Calculate next step
      const nextStep = step + 1;
      console.log('Auto-advancing to step:', nextStep);

      // Calculate delay based on speed
      const delay = calculateDelay(speed);

      // Set a timer to advance to the next step
      timeoutId = setTimeout(() => {
        console.log('Timer fired, setting step to:', nextStep);
        setStep(nextStep);
      }, delay);
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, 0, 0];

  // Render the N-Queens visualization
  const renderNQueensVisualization = () => {
    if (!currentStepData) return null;

    const { board, currentRow, currentCol, solutions } = currentStepData;

    // Calculate board size
    const boardSize = board.length;

    // Dynamically adjust cell size based on board size
    // Smaller boards get larger cells, larger boards get smaller cells
    const CELL_SIZE = BASE_CELL_SIZE * Math.max(0.5, 6 / boardSize);

    // Calculate offset to center the board
    const offset = -(boardSize * CELL_SIZE) / 2 + CELL_SIZE / 2;

    // Determine if we're in the solution phase
    const isSolutionPhase = currentStepData.type === 'solution_found' ||
                           currentStepData.type === 'complete';

    // Determine current solution index (if any)
    const currentSolution = solutions && solutions.length > 0 ? solutions.length - 1 : -1;

    // Calculate board dimensions for the frame
    const boardWidth = boardSize * CELL_SIZE;
    const frameThickness = 0.3;
    const frameHeight = 0.4;

    // Calculate label distance based on cell size
    const labelOffset = CELL_SIZE * LABEL_DISTANCE;

    return (
      <group>
        {/* Chessboard with frame */}
        <group position={[0, 0, 0]}>
          {/* Board frame */}
          <group>
            {/* Bottom frame */}
            <mesh
              castShadow
              receiveShadow
              position={[0, -BOARD_ELEVATION/2 - frameHeight/2, 0]}
            >
              <boxGeometry args={[boardWidth + frameThickness*2, frameHeight, boardWidth + frameThickness*2]} />
              <meshStandardMaterial
                color={isDark ? "#3e2723" : "#5d4037"}
                roughness={0.8}
                metalness={0.2}
              />
            </mesh>

            {/* Frame sides */}
            {[[-1, 0], [1, 0], [0, -1], [0, 1]].map((dir, i) => {
              const isXDir = dir[0] !== 0;
              const size = isXDir ?
                [frameThickness, BOARD_ELEVATION + 0.05, boardWidth + frameThickness*2] :
                [boardWidth + frameThickness*2, BOARD_ELEVATION + 0.05, frameThickness];

              const position = [
                dir[0] * (boardWidth/2 + frameThickness/2),
                0,
                dir[1] * (boardWidth/2 + frameThickness/2)
              ];

              return (
                <mesh
                  key={`frame-${i}`}
                  castShadow
                  receiveShadow
                  position={position}
                >
                  <boxGeometry args={size} />
                  <meshStandardMaterial
                    color={isDark ? "#6d4c41" : "#8d6e63"}
                    roughness={0.7}
                    metalness={0.3}
                  />
                </mesh>
              );
            })}
          </group>

          {/* Board cells */}
          {board.map((row, rowIndex) => (
            row.map((cell, colIndex) => {
              const x = offset + colIndex * CELL_SIZE;
              const z = offset + rowIndex * CELL_SIZE;

              // Determine if this cell is highlighted
              const isHighlighted = rowIndex === currentRow && colIndex === currentCol;

              // Determine if this cell is active (being considered in the current step)
              const isActive = (currentStepData.type === 'check_position' ||
                               currentStepData.type === 'place_queen' ||
                               currentStepData.type === 'invalid_position') &&
                              rowIndex === currentRow && colIndex === currentCol;

              // Determine if the cell is black or white (checkerboard pattern)
              const isBlack = (rowIndex + colIndex) % 2 === 1;

              return (
                <Cell
                  key={`cell-${rowIndex}-${colIndex}`}
                  position={[x, 0, z]}
                  isBlack={isBlack}
                  isHighlighted={isHighlighted}
                  isActive={isActive}
                  cellSize={CELL_SIZE}
                  isDark={isDark}
                />
              );
            })
          ))}

          {/* Queens */}
          {board.map((row, rowIndex) => (
            row.map((cell, colIndex) => {
              if (cell !== 1) return null;

              const x = offset + colIndex * CELL_SIZE;
              const z = offset + rowIndex * CELL_SIZE;

              // Determine if this queen is active
              const isActive = (currentStepData.type === 'place_queen' ||
                               currentStepData.type === 'backtrack') &&
                              rowIndex === currentRow && colIndex === currentCol;

              // Determine if this queen is part of a solution
              const isHighlighted = isSolutionPhase;

              return (
                <Queen
                  key={`queen-${rowIndex}-${colIndex}`}
                  position={[x, 0, z]}
                  isActive={isActive}
                  isHighlighted={isHighlighted}
                  isDark={isDark}
                />
              );
            })
          ))}

          {/* Row and column labels with proper visibility */}
          {Array.from({ length: boardSize }).map((_, index) => {
            const position = offset + index * CELL_SIZE;

            return (
              <React.Fragment key={`labels-${index}`}>
                {/* Row labels (letters) - using sprite for guaranteed visibility */}
                <sprite position={[offset - CELL_SIZE - 0.5, 0, position]} scale={[1, 1, 1]}>
                  <spriteMaterial attach="material">
                    <canvasTexture attach="map" args={[(() => {
                      const canvas = document.createElement('canvas');
                      canvas.width = 64;
                      canvas.height = 64;
                      const ctx = canvas.getContext('2d');

                      // Clear canvas
                      ctx.clearRect(0, 0, 64, 64);

                      // Draw text
                      ctx.fillStyle = isDark ? 'white' : 'black';
                      ctx.font = 'bold 48px Arial';
                      ctx.textAlign = 'center';
                      ctx.textBaseline = 'middle';
                      ctx.fillText(String.fromCharCode(65 + index), 32, 32);

                      return canvas;
                    })()]} />
                  </spriteMaterial>
                </sprite>

                {/* Column labels (numbers) - using sprite for guaranteed visibility */}
                <sprite position={[position, 0, offset - CELL_SIZE - 0.5]} scale={[1, 1, 1]}>
                  <spriteMaterial attach="material">
                    <canvasTexture attach="map" args={[(() => {
                      const canvas = document.createElement('canvas');
                      canvas.width = 64;
                      canvas.height = 64;
                      const ctx = canvas.getContext('2d');

                      // Clear canvas
                      ctx.clearRect(0, 0, 64, 64);

                      // Draw text
                      ctx.fillStyle = isDark ? 'white' : 'black';
                      ctx.font = 'bold 48px Arial';
                      ctx.textAlign = 'center';
                      ctx.textBaseline = 'middle';
                      ctx.fillText(String(index + 1), 32, 32);

                      return canvas;
                    })()]} />
                  </spriteMaterial>
                </sprite>
              </React.Fragment>
            );
          })}
        </group>

        {/* Solutions Display */}
        {solutions && solutions.length > 0 && (
          <SolutionDisplay
            solutions={solutions}
            currentSolution={currentSolution}
            position={[boardWidth * 0.8, 0, 0]} // Dynamically positioned based on board width
            isDark={isDark}
          />
        )}
      </group>

    );
  };

  // Return the 3D visualization
  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'N-Queens Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

      {/* Theme-aware fog for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

      {/* Ambient light for overall scene illumination */}
      <ambientLight intensity={isDark ? 0.2 : 0.3} />

      {/* Main directional light with shadows */}
      <directionalLight
        position={[5, 15, 8]}
        intensity={isDark ? 0.7 : 0.8}
        color="#ffffff"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />

      {/* Fill light from opposite direction */}
      <directionalLight
        position={[-8, 10, -5]}
        intensity={0.4}
        color={isDark ? '#a0a0ff' : '#a0d0ff'}
      />

      {/* Rim light for edge definition */}
      <directionalLight
        position={[0, 5, -10]}
        intensity={0.3}
        color={isDark ? '#ffb0b0' : '#ffe0c0'}
      />

      {/* Spotlight to highlight the main visualization */}
      <spotLight
        position={[0, 15, 0]}
        angle={0.5}
        penumbra={0.8}
        intensity={isDark ? 0.5 : 0.6}
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        color={isDark ? '#ffffff' : '#ffffff'}
      />

      {/* Visualization */}
      <group position={position} rotation={rotation}>
        {renderNQueensVisualization()}
      </group>

      {/* Add a subtle fog effect for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

      {/* Add a grid helper for better spatial reference */}
      <gridHelper
        args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
        position={[0, -1, 0]}
        rotation={[0, 0, 0]}
      />
    </group>


    </>
  );
};

export default NQueensVisualization;
