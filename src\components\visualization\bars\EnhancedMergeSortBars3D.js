// EnhancedMergeSortBars3D.js - An improved visualization for merge sort
import React, { useMemo, useState, useEffect } from 'react';
import { useTheme } from '@mui/material/styles';
import { Box, Typography, Paper } from '@mui/material';
import ThemeHtml from '../ThemeHtml';
import Bar3D from './Bar3D';
import MergeSortConfig from '../../../algorithms/Sorting/MergeSort/MergeSortConfig';

// Helper function to create an empty slot indicator
const EmptySlot = ({ position, width, color, opacity = 0.3 }) => {
  // Create a dashed outline to indicate an empty slot
  return (
    <group position={position}>
      {/* Bottom base of the empty slot */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[width, 0.05, width]} />
        <meshStandardMaterial
          color={color}
          transparent={true}
          opacity={opacity * 1.5}
          roughness={0.8}
          metalness={0.2}
        />
      </mesh>

      {/* Vertical lines at the corners to indicate the slot */}
      {[
        [-width / 2, 0.25, -width / 2], // Front left
        [width / 2, 0.25, -width / 2],  // Front right
        [-width / 2, 0.25, width / 2],  // Back left
        [width / 2, 0.25, width / 2]    // Back right
      ].map((cornerPos, i) => (
        <mesh key={`corner-${i}`} position={cornerPos}>
          <boxGeometry args={[0.05, 0.5, 0.05]} />
          <meshStandardMaterial
            color={color}
            transparent={true}
            opacity={opacity}
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>
      ))}
    </group>
  );
};


// Enhanced MergeSortBars3D component
const EnhancedMergeSortBars3D = ({
  arrayData,
  maxBarHeight,
  barWidth,
  barSpacing,
  colors,
  comparing,
  merging,
  sorted,
  showArrows,
  showValues,
  showIndices,
  placeAnimation,
  currentStep,
  onWidthChange,
  barAnimations,
  explanatoryConfig,
}) => {
  const theme = useTheme();

  // We don't need to track viewport dimensions anymore since we're using fixed positions


  // Filter out any undefined or null values
  const validArrayData = arrayData.map(val => (val === undefined || val === null) ? 0 : val);

  // Calculate the maximum value in the array for scaling
  const maxValue = Math.max(...validArrayData, 1); // Ensure we don't divide by zero

  // Calculate the starting position for the first bar
  const startX = -(((arrayData.length - 1) * (barWidth + barSpacing)) / 2);

  // Track all active splits across steps
  const [activeSplits, setActiveSplits] = useState([]);

  // Track all splits that have occurred
  const [allSplits, setAllSplits] = useState([]);

  // Update active splits based on the current step
  useEffect(() => {
    if (!currentStep) return;

    // For initial step, clear all splits
    if (currentStep.type === 'initial' || currentStep.initialArray) {
      setAllSplits([]);
      return;
    }

    console.log("currentStep.type:", currentStep.type);

    // For split steps, add the new split to our collection
    if (currentStep.type === 'split') {
      console.log("currentStep.indices:", currentStep.indices);
      console.log("currentStep.splitInfo:", currentStep.splitInfo);
      const [low, high] = currentStep.indices;
      const mid = currentStep.splitInfo?.mid;

      // Create a new split object
      const newSplit = {
        low,
        high,
        mid,
        active: true,
        isSplit: true,
        splitInfo: currentStep.splitInfo // Store the split info for coloring
      };

      // Add this split to our collection if it doesn't exist
      setAllSplits(prev => {
        // Check if this split already exists
        const exists = prev.some(s =>
          s.low === low && s.high === high && s.mid === mid
        );

        if (exists) {
          return prev;
        }

        return [...prev, newSplit];
      });
    }

    // For merge steps, mark the corresponding split as merging
    // BUT KEEP THE GAPS - don't remove the split yet
    else if (currentStep.type === 'merge') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true };
          }
          return split;
        })
      );
    }

    // For create_temp_arrays steps, mark the corresponding split as merging
    else if (currentStep.type === 'create_temp_arrays') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true, tempArrays: currentStep.tempArrays };
          }
          return split;
        })
      );
    }

    // For create_result_array steps, mark the corresponding split as merging
    else if (currentStep.type === 'create_result_array') {
      const [low, high] = currentStep.indices || [0, 0];



      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true, resultArray: currentStep.resultArray };
          }
          return split;
        })
      );
    }

    // For result_array_complete steps, mark the corresponding split as merging
    else if (currentStep.type === 'result_array_complete') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true, resultArray: currentStep.resultArray };
          }
          return split;
        })
      );
    }

    // For copy_back steps, mark the corresponding split as merging
    else if (currentStep.type === 'copy_back') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true, copyInfo: currentStep.copyInfo };
          }
          return split;
        })
      );
    }

    // For compare steps during a merge, maintain the gaps and update the corresponding split
    else if (currentStep.type === 'compare') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.map(split => {
          if (split.low === low && split.high === high) {
            return { ...split, merging: true, keepGaps: true };
          }
          return split;
        })
      );
    }

    // For place steps during a merge, maintain the gaps
    else if (currentStep.type === 'place') {
      // Do nothing - keep the existing splits and gaps
    }

    // For sorted steps, remove the corresponding split
    // This is when we finally remove the gaps
    else if (currentStep.type === 'sorted') {
      const [low, high] = currentStep.indices || [0, 0];

      setAllSplits(prev =>
        prev.filter(split => !(split.low === low && split.high === high))
      );
    }

    // For complete step, clear all splits
    else if (currentStep.type === 'complete') {
      setAllSplits([]);
    }
  }, [currentStep]);

  // Set active splits based on all splits and current step
  useEffect(() => {
    // For the current active splits, we want to show:
    // 1. The current split if it's a split step
    // 2. All active splits that aren't being merged or sorted

    if (currentStep) {
      // For initial step, clear all active splits
      if (currentStep.type === 'initial' || currentStep.initialArray) {
        setActiveSplits([]);
        return;
      }

      if (currentStep.type === 'split') {
        const [low, high] = currentStep.indices || [0, 0];
        const mid = currentStep.splitInfo?.mid || Math.floor(low + (high - low) / 2);

        // For split steps, show this split and all previous active splits
        setActiveSplits([
          // Include the current split
          {
            low,
            high,
            mid,
            active: true,
            isSplit: true,
            splitInfo: currentStep.splitInfo // Store the split info for coloring
          },
          // Include all other active splits
          ...allSplits.filter(s =>
            !(s.low === low && s.high === high) && // Not the current split
            !s.merging // Not merging
          )
        ]);
      }
      else if (currentStep.type === 'sorted') {
        // For sorted steps, remove the corresponding split
        // This is when we finally remove the gaps
        const [low, high] = currentStep.indices || [0, 0];
        setActiveSplits(allSplits.filter(s => !(s.low === low && s.high === high)));
      }
      else {
        // For ALL other steps, keep all splits to maintain the gaps
        // This includes merge, compare, place, create_temp_arrays, create_result_array, etc.
        setActiveSplits(allSplits);
      }
    }
    else {
      // No current step, show all active splits
      setActiveSplits(allSplits.filter(s => !s.merging));
    }
  }, [allSplits, currentStep]);



  // Calculate the positions and visibility of bars based on the current step and active splits
  const barPositions = useMemo(() => {
    const positions = [];

    // console.log("colors:", colors);

    // Initialize with default positions
    for (let i = 0; i < validArrayData.length; i++) {
      positions.push({
        index: i,
        value: validArrayData[i],
        xPos: 0, // Will be calculated
        yPos: 0, // Base position (will remain constant)
        zPos: 0,
        visible: true,
        placeholder: false,
        color: colors.bar,
        opacity: 1,
        subarray: -1 // Will be assigned
      });
    }

    // Define the gap size between subarrays
    const gapSize = barWidth * 1.5; // Larger gap size for better separation

    // If we have active splits, use them for positioning
    if (activeSplits.length > 0) {
      // Find all split points (mid points of active splits)
      const splitPoints = [];

      // Add all mid points from active splits
      activeSplits.forEach(split => {
        if (split.mid !== undefined) {
          splitPoints.push(split.mid);
        }
      });

      // Sort split points
      splitPoints.sort((a, b) => a - b);

      // Calculate the total width with all gaps
      const totalElements = validArrayData.length;
      const totalGaps = splitPoints.length;
      const totalWidth = (totalElements * (barWidth + barSpacing)) - barSpacing + (totalGaps * gapSize);

      // Calculate the starting position to center everything
      // Add a positive offset to shift the bars to the right
      const visualOffset = 0.3; // Small offset to the right for visual balance
      const startX = -totalWidth / 2 + visualOffset;

      // Calculate positions with gaps
      const elementPositions = [];
      let currentX = startX;

      for (let i = 0; i < validArrayData.length; i++) {
        // Store this element's position
        elementPositions.push(currentX);

        // Move to the next position
        currentX += barWidth + barSpacing;

        // If this is a split point, add a gap
        if (splitPoints.includes(i)) {
          currentX += gapSize;
        }
      }

      // Assign positions and subarrays
      for (let i = 0; i < positions.length; i++) {
        positions[i].xPos = elementPositions[i];

        // Determine which subarray this element belongs to
        // Count how many split points are before this index
        const subarrayIndex = splitPoints.filter(sp => sp < i).length;
        positions[i].subarray = subarrayIndex;
      }
    }
    // No splits - use default centered positioning
    else {
      const totalWidth = (validArrayData.length * (barWidth + barSpacing)) - barSpacing;
      // Add a positive offset to shift the bars to the right
      const visualOffset = 0.3; // Small offset to the right for visual balance
      const startX = -totalWidth / 2 + visualOffset;

      // Position each element
      for (let i = 0; i < positions.length; i++) {
        positions[i].xPos = startX + i * (barWidth + barSpacing);
        positions[i].subarray = 0; // All elements in a single subarray
      }
    }

    // Apply coloring based on the current step
    if (currentStep) {
      // For merge steps, highlight the merging subarray
      if (currentStep.type === 'merge' && currentStep.indices) {
        const [low, high] = currentStep.indices;

        // Highlight the merging subarray
        for (let i = low; i <= high; i++) {
          if (i < positions.length) {
            positions[i].color = colors.merging;
          }
        }
      }

      // For place steps, show the element being placed
      else if (currentStep.type === 'place' && currentStep.indices) {
        const [targetIndex, sourceIndex] = currentStep.indices;

        // Highlight the source and target positions
        if (targetIndex < positions.length) {
          positions[targetIndex].color = colors.merging;
        }

        if (sourceIndex < positions.length && sourceIndex !== targetIndex) {
          positions[sourceIndex].color = colors.comparing;
        }
      }

      // For compare steps, we don't highlight the main array elements anymore
      // The comparison happens in the temp arrays, not the main array
      else if (currentStep.type === 'compare' && currentStep.indices) {
        // Don't highlight any elements in the main array for compare steps
        // The comparison is shown in the temp arrays
      }

      console.log("currentStep.type:", currentStep.type);
      console.log("positions:", positions);
    }

    console.log("placeAnimation.active:", placeAnimation.active);
    // Reset animating flags when animation is not active
    if (!placeAnimation.active) {
      for (let i = 0; i < positions.length; i++) {
        positions[i].animating = false;
      }
    }

    // Apply animation for placing elements
    if (placeAnimation.active && placeAnimation.indices) {
      const [destIndex, sourceIndex] = placeAnimation.indices;
      const progress = placeAnimation.progress;

      // Get the original array and values
      const sourceValue = placeAnimation.sourceValue;

      // Check if this is a swap operation
      const isSwap = currentStep && currentStep.movement && currentStep.movement.includes('Swap');

      // Check if source and destination are the same position (placing at same position)
      const isSamePosition = sourceIndex === destIndex;

      // Find the source and destination positions
      const sourcePosition = positions.find(pos => pos.index === sourceIndex);
      const destPosition = positions.find(pos => pos.index === destIndex);

      // Check if we're working with temp arrays or result arrays
      // If so, we don't want to animate the main bars
      const isWorkingWithTempArrays = currentStep && (
        currentStep.type === 'place_from_left_subarray' ||
        currentStep.type === 'place_from_right_subarray' ||
        currentStep.type === 'compare' ||
        currentStep.type === 'result_array_complete'
      );

      // For copy_back step, we DO want to animate the main bars
      const isCopyingBack = currentStep && currentStep.type === 'copy_back';

      // Only animate if we're not working with temp arrays or result arrays, OR if we're copying back
      if (sourcePosition && destPosition && (!isWorkingWithTempArrays || isCopyingBack)) {
        // Use a simple arc animation
        const arcHeight = 2.0; // Height of the arc

        // Store the original positions
        const originalSourceXPos = sourcePosition.xPos;
        const originalDestXPos = destPosition.xPos;

        // Clear any comparison colors and arrows
        sourcePosition.color = colors.base;
        destPosition.color = colors.base;

        // Mark positions as animating to disable arrows
        sourcePosition.animating = true;
        if (isSwap) {
          destPosition.animating = true;
        }

        if (isSwap) {
          // For swap operations, animate both elements

          // Animate source element to destination position - only change X position
          sourcePosition.xPos = originalSourceXPos + (originalDestXPos - originalSourceXPos) * progress;

          // Only animate the Y position for the bar itself, not the base
          // We'll handle this in the Bar3D component by keeping the base fixed
          sourcePosition.yOffset = 4 * arcHeight * progress * (1 - progress); // Store as offset instead
          sourcePosition.yPos = 0; // Keep base position at 0

          // Gradually bring forward and then back for z-position
          // This creates a smooth transition for the z-position
          // At progress = 0 and progress = 1, zPos will be 0
          // At progress = 0.5, zPos will be 0.5
          sourcePosition.zPos = 0.5 * Math.sin(progress * Math.PI);

          sourcePosition.value = sourceValue;
          sourcePosition.opacity = 0.9;
          sourcePosition.color = colors.merging || '#ff8042';

          // Store the destination value
          const destValue = destPosition.value;

          // Animate destination element to source position (in opposite direction)
          destPosition.xPos = originalDestXPos + (originalSourceXPos - originalDestXPos) * progress;

          // Only animate the Y position for the bar itself, not the base
          destPosition.yOffset = 4 * arcHeight * progress * (1 - progress); // Store as offset instead
          destPosition.yPos = 0; // Keep base position at 0

          // Same smooth z-position transition for destination
          destPosition.zPos = 0.5 * Math.sin(progress * Math.PI);

          destPosition.value = destValue;
          destPosition.opacity = 0.9;
          destPosition.color = colors.merging || '#ff8042';
        }
        else if (isSamePosition) {
          // For placing at the same position, just do a small bounce animation
          // This prevents the "hallucination" effect when placing at the same position

          // Keep the x position the same
          sourcePosition.xPos = originalSourceXPos;

          // Just do a small bounce up and down - only for the bar, not the base
          sourcePosition.yOffset = 2 * arcHeight * progress * (1 - progress); // Store as offset
          sourcePosition.yPos = 0; // Keep base position at 0

          // Gradually bring forward and then back for z-position
          // This creates a smooth transition for the z-position
          sourcePosition.zPos = 0.5 * Math.sin(progress * Math.PI);

          // Keep the original source value
          sourcePosition.value = sourceValue;

          // Keep fully visible
          sourcePosition.opacity = 1.0;

          // Use the placing color
          sourcePosition.color = colors.merging || '#ff8042';
        }
        else {
          // For place operations, just animate the source element

          // Linear interpolation for x position
          sourcePosition.xPos = originalSourceXPos + (originalDestXPos - originalSourceXPos) * progress;

          // Parabolic arc for y position (up and then down) - only for the bar, not the base
          sourcePosition.yOffset = 4 * arcHeight * progress * (1 - progress); // Store as offset
          sourcePosition.yPos = 0; // Keep base position at 0

          // Gradually bring forward and then back for z-position
          // This creates a smooth transition for the z-position
          sourcePosition.zPos = 0.5 * Math.sin(progress * Math.PI);

          // Keep the original source value
          sourcePosition.value = sourceValue;

          // Make the original position semi-transparent
          sourcePosition.opacity = 0.7;

          // Use the placing color
          sourcePosition.color = colors.merging || '#ff8042';
        }

        // We'll handle the placeholder in a different way - we won't modify anything during animation
      }
    }

    // IMPORTANT: We want to remove all effects from the main array
    // So we'll comment out the code that creates placeholders in the main array
    // This ensures that the main array doesn't show any animation or placeholders during any step

    // We're not going to create any placeholders in the main array
    // All the placeholder logic is removed to keep the main array clean

    // The following code is commented out to prevent any placeholders in the main array
    /*
    // Handle placeholders for completed animations
    // This is a better approach than modifying during animation
    // Check if we're working with temp arrays or result arrays
    const isWorkingWithTempArrays = currentStep && (
      currentStep.type === 'place_from_left_subarray' ||
      currentStep.type === 'place_from_right_subarray' ||
      currentStep.type === 'compare' ||
      currentStep.type === 'result_array_complete'
    );

    // Only create placeholders for main array operations, not temp array operations
    // AND NOT for copy_back steps - we don't want placeholders in the main array during copy_back
    if ((!isWorkingWithTempArrays) && !placeAnimation.active && currentStep &&
      (currentStep.type === 'place') &&
      !currentStep.movement?.includes('Swap') &&
      !currentStep.movement?.includes('from left subarray')) {

      // Get the source and target indices from the current step
      const [targetIndex, sourceIndex] = currentStep.indices || [];

      // Only create placeholder if source and target are different
      // This prevents creating placeholders when placing at the same index
      if (sourceIndex !== targetIndex) {
        // Find the source position in the positions array
        const sourcePos = positions.find(pos => pos.index === sourceIndex);

        // If we found the source position, make it a placeholder
        if (sourcePos) {
          sourcePos.placeholder = true;
          sourcePos.value = null;
          sourcePos.yPos = 0;
          sourcePos.zPos = -0.1;
          sourcePos.color = colors.base || '#888888';
          sourcePos.opacity = 0.3;
        }
      }
    }
    */

    // IMPORTANT: We want to remove all effects from the main array
    // So we'll comment out the code that updates the main array during copy_back steps
    // This ensures that the main array doesn't show any animation or placeholders during any step

    // For copy_back steps, we'll only update the values but not the colors or other effects
    if (currentStep && currentStep.type === 'copy_back' && currentStep.copyInfo && currentStep.copyInfo.result) {
      const { low, result } = currentStep.copyInfo;

      // Update the positions with the sorted values
      for (let i = 0; i < result.length; i++) {
        const index = low + i;
        const position = positions.find(pos => pos.index === index);

        if (position) {
          // Update the value but keep the default color
          position.value = result[i];
          position.placeholder = false;
          // Don't change the color - keep it as the default color
          // position.color = colors.sorted;
          position.opacity = 1.0;
        }
      }
    }

    // IMPORTANT: We want to maintain the split and merge colors for the main bars
    // Check if we're in a step that should show colored bars
    const shouldShowColoredBars = currentStep && (
      currentStep.type === 'merge' ||
      currentStep.type === 'split' ||
      currentStep.type === 'sorted'
    );

    // Get the indices that should be colored
    let coloredIndices = [];
    if (shouldShowColoredBars && currentStep.indices) {
      const [low, high] = currentStep.indices;
      coloredIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
    }

    // Apply colors to the bars
    for (let i = 0; i < positions.length; i++) {
      // Skip positions that are placeholders or being animated
      if (positions[i].placeholder || positions[i].animating) {
        continue;
      }

      // Reset any effects
      positions[i].yOffset = 0;
      positions[i].zPos = 0;
      positions[i].opacity = 1.0;

      // Apply colors based on the current step
      if (shouldShowColoredBars && coloredIndices.includes(positions[i].index)) {
        // For create_temp_arrays steps, use the left and right subarray colors from the legend
        if (currentStep.type === 'create_temp_arrays') {
          // Get the mid point to determine left vs right subarray
          const mid = currentStep.tempArrays?.mid ||
            (currentStep.splitInfo?.mid) ||
            (currentStep.indices && Math.floor((currentStep.indices[0] + currentStep.indices[1]) / 2));

          // If this bar is in the left subarray, use the left color
          if (positions[i].index <= mid) {
            positions[i].color = colors.tempStructure.left; // Left subarray (blue)
          }
          // If this bar is in the right subarray, use the right color
          else {
            positions[i].color = colors.tempStructure.right; // Right subarray (orange)
          }
        }
        // For merge and split steps, use the merging color
        else if (currentStep.type === 'merge' || currentStep.type === 'split' || currentStep.type === 'copy_back') {
          positions[i].color = colors.merging;
        }
        // For compare steps, use the comparing color
        else if (currentStep.type === 'compare') {
          positions[i].color = colors.comparing;
        }
        // For place_from_left_subarray, use the left subarray color
        else if (currentStep.type === 'place_from_left_subarray') {
          positions[i].color = colors.tempStructure.left; // Left subarray (blue)
        }
        // For place_from_right_subarray, use the right subarray color
        else if (currentStep.type === 'place_from_right_subarray') {
          positions[i].color = colors.tempStructure.right; // Right subarray (orange)
        }
        // For result array steps, use the result color
        else if (currentStep.type === 'create_result_array' || currentStep.type === 'result_array_complete') {
          positions[i].color = colors.tempStructure.result; // Result array (green)
        }
        // For other steps, use the merging color
        else {
          positions[i].color = colors.merging;
        }
      } else {
        // Set default color for bars not being operated on
        positions[i].color = colors.bar;
      }
    }

    return positions;
  }, [validArrayData, startX, barWidth, barSpacing, colors, comparing, merging, sorted, currentStep, placeAnimation]);



  // Bar height calculation is done inline where needed

  // Calculate the actual total width including all dynamic gaps
  const actualTotalWidth = useMemo(() => {
    if (barPositions.length === 0) return 0;

    // Find the leftmost and rightmost positions
    const leftmost = Math.min(...barPositions.map(pos => pos.xPos - barWidth / 2));
    const rightmost = Math.max(...barPositions.map(pos => pos.xPos + barWidth / 2));

    // Calculate the total width including all dynamic gaps
    const totalWidthWithGaps = rightmost - leftmost;

    // Return the total width with some padding
    return totalWidthWithGaps + barWidth * 2; // Add padding equal to 2x the bar width
  }, [barPositions, barWidth]);

  // Pass the actual total width to the parent component
  useEffect(() => {
    if (onWidthChange && actualTotalWidth > 0) {
      onWidthChange(actualTotalWidth);
    }
  }, [actualTotalWidth, onWidthChange]);

  // Create a fixed array of index positions that don't animate
  const fixedIndexPositions = useMemo(() => {
    // Calculate positions with the same logic as the bars but without animation
    const positions = [];

    // If we have active splits, use them for positioning
    if (activeSplits.length > 0) {
      // Find all split points (mid points of active splits)
      const splitPoints = [];

      // Add all mid points from active splits
      activeSplits.forEach(split => {
        if (split.mid !== undefined) {
          splitPoints.push(split.mid);
        }
      });

      // Sort split points
      splitPoints.sort((a, b) => a - b);

      // Calculate the total width with all gaps
      const totalElements = validArrayData.length;
      const totalGaps = splitPoints.length;
      const gapSize = barWidth * 1.5;
      const totalWidth = (totalElements * (barWidth + barSpacing)) - barSpacing + (totalGaps * gapSize);

      // Calculate the starting position to center everything
      // Add a positive offset to shift the bars to the right
      const visualOffset = 0.3; // Small offset to the right for visual balance
      const startX = -totalWidth / 2 + visualOffset;

      // Calculate positions with gaps
      let currentX = startX;

      for (let i = 0; i < validArrayData.length; i++) {
        // Add this position
        positions.push({
          index: i,
          xPos: currentX
        });

        // Move to the next position
        currentX += barWidth + barSpacing;

        // If this is a split point, add a gap
        if (splitPoints.includes(i)) {
          currentX += gapSize;
        }
      }
    }
    // No splits - use default centered positioning
    else {
      const totalWidth = (validArrayData.length * (barWidth + barSpacing)) - barSpacing;
      // Add a positive offset to shift the bars to the right
      const visualOffset = 0.3; // Small offset to the right for visual balance
      const startX = -totalWidth / 2 + visualOffset;

      // Position each element
      for (let i = 0; i < validArrayData.length; i++) {
        positions.push({
          index: i,
          xPos: startX + i * (barWidth + barSpacing)
        });
      }
    }

    return positions;
  }, [validArrayData, barWidth, barSpacing, activeSplits]);

  // Function to render temporary arrays as 3D bars
  const renderTempArrays = () => {
    // CRITICAL: For result_array_complete and copy_back steps, we should NOT show temp arrays
    // as the merge operation is complete and only the result array should be visible
    if (currentStep && (
      currentStep.type === 'result_array_complete' ||
      currentStep.type === 'copy_back'
    )) {
      return null;
    }

    // CRITICAL: For place_from_left_subarray and place_from_right_subarray steps,
    // we ALWAYS want to show temp arrays regardless of any other condition
    const isPlaceStep = currentStep && (
      currentStep.type === 'place_from_left_subarray' ||
      currentStep.type === 'place_from_right_subarray'
    );

    // Get the current merge operation
    const currentMergeOp = getCurrentMergeOperation();

    // For place steps, we'll create a temporary merge operation if needed
    if (isPlaceStep && !currentMergeOp && currentStep && currentStep.indices) {
      const [low, high] = currentStep.indices;
      // Create a temporary merge operation for this place step
      const tempMergeOp = {
        low,
        high,
        active: true,
        tempArraysCreated: true,
        resultArrayCreated: true,
        step: currentStep
      };

      // Use this temporary merge operation
      return renderTempArraysWithMergeOp(tempMergeOp);
    }

    // If we have a current merge operation, use it
    if (currentMergeOp) {
      return renderTempArraysWithMergeOp(currentMergeOp);
    }

    // If we still don't have a merge operation, we can't render
    return null;
  };

  // Helper function to render temp arrays with a specific merge operation
  const renderTempArraysWithMergeOp = (mergeOp) => {
    if (!mergeOp) return null;

    // CRITICAL: For result_array_complete and copy_back steps, we should NOT show temp arrays
    // as the merge operation is complete and only the result array should be visible
    if (currentStep && (
      currentStep.type === 'result_array_complete' ||
      currentStep.type === 'copy_back'
    )) {
      return null;
    }

    // Find the temp arrays data from the current step or the merge operation
    let tempArraysData = null;

    // If the current step has temp arrays data, use it
    if (currentStep && currentStep.tempArrays) {
      tempArraysData = currentStep.tempArrays;
    }
    // If the merge operation's step has temp arrays data, use it
    else if (mergeOp.step && mergeOp.step.tempArrays) {
      tempArraysData = mergeOp.step.tempArrays;
    }
    // Otherwise, look for temp arrays data in allSplits
    else {
      const currentSplit = allSplits.find(
        split => split.low === mergeOp.low && split.high === mergeOp.high
      );

      if (currentSplit && currentSplit.tempArrays) {
        tempArraysData = currentSplit.tempArrays;
      }
    }

    // CRITICAL: For place_from_left_subarray and place_from_right_subarray steps,
    // we need to ensure we can render temp arrays even if they're not explicitly available
    let leftArray = [];
    let rightArray = [];

    if (tempArraysData && tempArraysData.leftArray && tempArraysData.rightArray) {
      // Use the existing temp arrays data if available
      leftArray = [...tempArraysData.leftArray];
      rightArray = [...tempArraysData.rightArray];

      // IMPORTANT: For place steps, we need to update the temp arrays to reflect
      // which elements have already been placed in the result array
      if (currentStep && (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray')) {
        if (currentStep.placeInfo) {
          const placeInfo = currentStep.placeInfo;

          // For place_from_left_subarray, mark elements that have already been placed as null
          // ONLY in the left subarray
          if (currentStep.type === 'place_from_left_subarray') {
            // Calculate leftArrayIndex if not provided
            const leftArrayIndex = placeInfo.leftArrayIndex !== undefined ?
              placeInfo.leftArrayIndex :
              (placeInfo.sourceIndex !== undefined ? placeInfo.sourceIndex - placeInfo.low : 0);

            // Mark all elements before the current one as null (already placed)
            for (let i = 0; i < leftArrayIndex; i++) {
              if (i < leftArray.length) {
                leftArray[i] = null;
              }
            }

            // Also mark the current element as null since it's being placed
            if (leftArrayIndex < leftArray.length) {
              leftArray[leftArrayIndex] = null;
            }

            // Do NOT modify the right subarray when placing from left subarray
          }

          // For place_from_right_subarray, mark elements that have already been placed as null
          // ONLY in the right subarray
          if (currentStep.type === 'place_from_right_subarray') {
            // Calculate rightArrayIndex if not provided
            const rightArrayIndex = placeInfo.rightArrayIndex !== undefined ?
              placeInfo.rightArrayIndex :
              (placeInfo.sourceIndex !== undefined ? placeInfo.sourceIndex - (placeInfo.mid + 1) : 0);

            // Mark all elements before the current one as null (already placed)
            for (let i = 0; i < rightArrayIndex; i++) {
              if (i < rightArray.length) {
                rightArray[i] = null;
              }
            }

            // Also mark the current element as null since it's being placed
            if (rightArrayIndex < rightArray.length) {
              rightArray[rightArrayIndex] = null;
            }

            // Do NOT modify the left subarray when placing from right subarray
          }
        }
      }
    } else if (currentStep && (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray')) {
      // For place steps, create temp arrays based on the current step's placeInfo
      if (currentStep.placeInfo) {
        const placeInfo = currentStep.placeInfo;

        // For place_from_left_subarray, we need to ensure both left and right arrays are created
        // This ensures both arrays are visible during the place step
        if (currentStep.type === 'place_from_left_subarray') {
          // Calculate leftArrayIndex if not provided
          const leftArrayIndex = placeInfo.leftArrayIndex !== undefined ?
            placeInfo.leftArrayIndex :
            (placeInfo.sourceIndex !== undefined ? placeInfo.sourceIndex - placeInfo.low : 0);

          // Try to get the left array from tempArrays in the step data
          // If not available, create an array of the correct size based on placeInfo
          if (currentStep && currentStep.tempArrays && currentStep.tempArrays.leftArray) {
            leftArray = [...currentStep.tempArrays.leftArray];
          } else if (placeInfo.mid !== undefined && placeInfo.low !== undefined) {
            // Calculate the left array size based on the merge operation bounds
            const leftArraySize = placeInfo.mid - placeInfo.low + 1;
            // Create an array of the correct size with placeholder values
            // Use the actual values from the original array if available
            leftArray = new Array(leftArraySize);
            for (let i = 0; i < leftArraySize; i++) {
              // Try to get the value from the original array
              if (validArrayData && validArrayData[placeInfo.low + i] !== undefined) {
                leftArray[i] = validArrayData[placeInfo.low + i];
              } else {
                // Fallback to a placeholder value
                leftArray[i] = 1;
              }
            }
          } else {
            // Fallback to an empty array only if we can't determine the size
            leftArray = [];
          }

          // Get the merge key for this operation
          const mergeKey = `${placeInfo.low}-${placeInfo.high}`;

          // Get the placed elements for this merge operation
          const currentPlaced = placedElements[mergeKey] || { left: [], right: [] };

          // Mark all placed elements as null
          for (let i = 0; i < leftArray.length; i++) {
            if (currentPlaced.left.includes(i)) {
              leftArray[i] = null;
            } else {
              // Fill with actual values from the step data if available
              leftArray[i] = placeInfo.leftArray && placeInfo.leftArray[i] !== undefined ?
                placeInfo.leftArray[i] : 1; // Use minimal default value if not available
            }
          }

          // We're placing this element, so it should be null in the source array
          if (leftArrayIndex >= 0 && leftArrayIndex < leftArray.length) {
            leftArray[leftArrayIndex] = null;
            console.log(`DEBUG - Setting leftArray[${leftArrayIndex}] to null`);
          }

          // Also mark all previously placed elements as null
          // Use the existing mergeKey and currentPlaced variables
          // Mark all placed left elements as null
          if (currentPlaced && currentPlaced.left && Array.isArray(currentPlaced.left)) {
            for (let i = 0; i < leftArray.length; i++) {
              if (currentPlaced.left.includes(i)) {
                leftArray[i] = null;
                console.log(`DEBUG - Setting leftArray[${i}] to null (from placedElements)`);
              }
            }
          }

          // Try to get the right array from tempArrays in the step data
          // If not available, create an array of the correct size based on placeInfo
          if (currentStep && currentStep.tempArrays && currentStep.tempArrays.rightArray) {
            rightArray = [...currentStep.tempArrays.rightArray];
          } else if (placeInfo.high !== undefined && placeInfo.mid !== undefined) {
            // Calculate the right array size based on the merge operation bounds
            const rightArraySize = placeInfo.high - placeInfo.mid;
            // Create an array of the correct size with placeholder values
            // Use the actual values from the original array if available
            rightArray = new Array(rightArraySize);
            for (let i = 0; i < rightArraySize; i++) {
              // Try to get the value from the original array
              if (validArrayData && validArrayData[placeInfo.mid + 1 + i] !== undefined) {
                rightArray[i] = validArrayData[placeInfo.mid + 1 + i];
              } else {
                // Fallback to a placeholder value
                rightArray[i] = 1;
              }
            }
          } else {
            // Fallback to an empty array only if we can't determine the size
            rightArray = [];
          }

          // When placing from left subarray, the right subarray should still be shown
          // Get the merge key for this operation
          const rightMergeKey = `${placeInfo.low}-${placeInfo.high}`;

          // Get the placed elements for this merge operation
          const rightCurrentPlaced = placedElements[rightMergeKey] || { left: [], right: [] };

          // Fill with actual values from the step data if available, but mark placed elements as null
          for (let i = 0; i < rightArray.length; i++) {
            if (rightCurrentPlaced.right.includes(i)) {
              rightArray[i] = null;
            } else {
              rightArray[i] = placeInfo.rightArray && placeInfo.rightArray[i] !== undefined ?
                placeInfo.rightArray[i] : 1; // Use minimal default value if not available
            }
          }
        }

        // For place_from_right_subarray, we need to ensure both left and right arrays are created
        // This ensures both arrays are visible during the place step
        if (currentStep.type === 'place_from_right_subarray') {
          // Calculate rightArrayIndex if not provided
          const rightArrayIndex = placeInfo.rightArrayIndex !== undefined ?
            placeInfo.rightArrayIndex :
            (placeInfo.sourceIndex !== undefined && placeInfo.mid !== undefined ?
              placeInfo.sourceIndex - (placeInfo.mid + 1) : 0);

          // Try to get the right array from tempArrays in the step data
          // If not available, create an array of the correct size based on placeInfo
          if (currentStep && currentStep.tempArrays && currentStep.tempArrays.rightArray) {
            rightArray = [...currentStep.tempArrays.rightArray];
          } else if (placeInfo.high !== undefined && placeInfo.mid !== undefined) {
            // Calculate the right array size based on the merge operation bounds
            const rightArraySize = placeInfo.high - placeInfo.mid;
            // Create an array of the correct size with placeholder values
            // Use the actual values from the original array if available
            rightArray = new Array(rightArraySize);
            for (let i = 0; i < rightArraySize; i++) {
              // Try to get the value from the original array
              if (validArrayData && validArrayData[placeInfo.mid + 1 + i] !== undefined) {
                rightArray[i] = validArrayData[placeInfo.mid + 1 + i];
              } else {
                // Fallback to a placeholder value
                rightArray[i] = 1;
              }
            }
          } else {
            // Fallback to an empty array only if we can't determine the size
            rightArray = [];
          }

          // Get the merge key for this operation
          const mergeKey = `${placeInfo.low}-${placeInfo.high}`;

          // Get the placed elements for this merge operation
          const currentPlaced = placedElements[mergeKey] || { left: [], right: [] };

          // Mark all placed elements as null
          for (let i = 0; i < rightArray.length; i++) {
            if (currentPlaced.right.includes(i)) {
              rightArray[i] = null;
            } else {
              // Fill with actual values from the step data if available
              rightArray[i] = placeInfo.rightArray && placeInfo.rightArray[i] !== undefined ?
                placeInfo.rightArray[i] : 1; // Use minimal default value if not available
            }
          }

          // We're placing this element, so it should be null in the source array
          if (rightArrayIndex >= 0 && rightArrayIndex < rightArray.length) {
            rightArray[rightArrayIndex] = null;
            console.log(`DEBUG - Setting rightArray[${rightArrayIndex}] to null`);
          }

          // Also mark all previously placed elements as null
          // Use the existing mergeKey and currentPlaced variables
          // Mark all placed right elements as null
          if (currentPlaced && currentPlaced.right && Array.isArray(currentPlaced.right)) {
            for (let i = 0; i < rightArray.length; i++) {
              if (currentPlaced.right.includes(i)) {
                rightArray[i] = null;
                console.log(`DEBUG - Setting rightArray[${i}] to null (from placedElements)`);
              }
            }
          }

          // Try to get the left array from tempArrays in the step data
          // If not available, create an array of the correct size based on placeInfo
          if (currentStep && currentStep.tempArrays && currentStep.tempArrays.leftArray) {
            leftArray = [...currentStep.tempArrays.leftArray];
          } else if (placeInfo.mid !== undefined && placeInfo.low !== undefined) {
            // Calculate the left array size based on the merge operation bounds
            const leftArraySize = placeInfo.mid - placeInfo.low + 1;
            // Create an array of the correct size with placeholder values
            // Use the actual values from the original array if available
            leftArray = new Array(leftArraySize);
            for (let i = 0; i < leftArraySize; i++) {
              // Try to get the value from the original array
              if (validArrayData && validArrayData[placeInfo.low + i] !== undefined) {
                leftArray[i] = validArrayData[placeInfo.low + i];
              } else {
                // Fallback to a placeholder value
                leftArray[i] = 1;
              }
            }
          } else {
            // Fallback to an empty array only if we can't determine the size
            leftArray = [];
          }

          // When placing from right subarray, the left subarray should still be shown
          // Get the merge key for this operation
          const leftMergeKey = `${placeInfo.low}-${placeInfo.high}`;

          // Get the placed elements for this merge operation
          const leftCurrentPlaced = placedElements[leftMergeKey] || { left: [], right: [] };

          // Fill with actual values from the step data if available, but mark placed elements as null
          for (let i = 0; i < leftArray.length; i++) {
            if (leftCurrentPlaced.left.includes(i)) {
              leftArray[i] = null;
            } else {
              leftArray[i] = placeInfo.leftArray && placeInfo.leftArray[i] !== undefined ?
                placeInfo.leftArray[i] : 1; // Use minimal default value if not available
            }
          }
        }
      } else {
        // If we don't have placeInfo, we should not be rendering this component
        // Log an error and return null
        console.error("Missing placeInfo from controller");
        return null;
      }
    } else {
      // If we still don't have valid temp arrays, we can't render
      return null;
    }

    // Get the configuration for the left side
    const { leftSubarray, rightSubarray } = explanatoryConfig.leftSide;

    // Since we're now inside a positioned container, we can use simpler positioning
    // The container is already positioned at the Y position from config
    const tempArrayY = 0; // Relative to container

    // Get the positions for the left and right subarrays from the config
    const leftArrayX = leftSubarray.labelPosition[0]; // Left subarray position
    const rightArrayX = rightSubarray.labelPosition[0]; // Right subarray position

    // Calculate the maximum value for scaling the bar heights
    const maxTempValue = Math.max(
      ...leftArray.filter(val => val !== undefined && val !== null),
      ...rightArray.filter(val => val !== undefined && val !== null),
      1 // Ensure we don't divide by zero
    );

    // Get the bar scale from the config
    const barScale = explanatoryConfig.common.barScale;

    // Calculate the spacing between elements in the temporary arrays
    const tempBarWidth = barWidth * barScale; // Smaller bars for the temporary arrays
    const tempBarSpacing = barSpacing * barScale * 1.5; // Increased spacing to prevent label overlap

    // Reduce the height of temporary bars
    const tempHeightRatio = barScale; // Scale the height by the same factor as the width

    // Check if we're in a compare step to show comparison arrows
    const isCompareStep = currentStep && currentStep.type === 'compare';

    // Get the indices being compared in the temp arrays
    let leftCompareIndex = -1;
    let rightCompareIndex = -1;

    if (isCompareStep && currentStep.compareInfo) {
      leftCompareIndex = currentStep.compareInfo.leftIndex || 0;
      rightCompareIndex = currentStep.compareInfo.rightIndex || 0;
    }

    return (
      <group>
        {/* Add separate background panels for each array for better visibility */}
        {/* Removed background and border boxes for temp arrays */}

        {/* Left array label */}
        <ThemeHtml position={[leftArrayX, tempArrayY + leftSubarray.labelPosition[1], 0]} center sprite occlude theme={theme}>
          <Paper
            elevation={2}
            sx={{
              px: 1,
              py: 0,
              borderRadius: theme.shape.borderRadius,
              border: 1,
              borderColor: colors.tempStructure.left,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="caption"
              fontWeight="bold"
              color="text.primary"
              align="center"
              sx={{ whiteSpace: 'nowrap' }}
            >
              Left Subarray
            </Typography>
          </Paper>
        </ThemeHtml>

        {/* Right array label */}
        <ThemeHtml position={[rightArrayX, tempArrayY + rightSubarray.labelPosition[1], 0]} center sprite occlude theme={theme}>
          <Paper
            elevation={2}
            sx={{
              px: 1,
              py: 0,
              borderRadius: theme.shape.borderRadius,
              border: 1,
              borderColor: colors.tempStructure.right,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="caption"
              fontWeight="bold"
              color="text.primary"
              align="center"
              sx={{ whiteSpace: 'nowrap' }}
            >
              Right Subarray
            </Typography>
          </Paper>
        </ThemeHtml>

        {/* Left array bars */}
        {/* Check if left subarray is exhausted */}
        {isCompareStep && currentStep.compareInfo && currentStep.compareInfo.leftExhausted ? (
          // If left subarray is exhausted, don't render any bars
          <ThemeHtml position={[leftArrayX, tempArrayY - 0.5, 0]} center sprite occlude theme={theme}>
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.7rem',
                fontStyle: 'italic',
                color: 'text.secondary',
                textAlign: 'center',
                display: 'block'
              }}
            >
              Exhausted
            </Typography>
          </ThemeHtml>
        ) : (
          // Otherwise, render the left array bars
          leftArray.map((value, index) => {
            // Skip rendering bars for null values (already placed elements)
            if (value === null) {
              // Only render the index label for null values
              const totalWidth = leftArray.length * (tempBarWidth + tempBarSpacing) - tempBarSpacing;
              const startX = leftArrayX - totalWidth / 2;
              const xPos = startX + index * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;

              return (
                <group key={`left-temp-${index}`} position={[xPos, tempArrayY, 0]}>
                  {/* Index label only */}
                  <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        border: 1,
                        borderColor: 'divider',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                        boxShadow: 1,
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: '0.5rem',
                          lineHeight: 1,
                          textAlign: 'center',
                          width: '100%'
                        }}
                        fontWeight="bold"
                        color="text.secondary"
                      >
                        {index}
                      </Typography>
                    </Box>
                  </ThemeHtml>
                </group>
              );
            }

          // Calculate the bar height based on the value, but reduced for temporary arrays
          const barHeight = (value / maxTempValue) * maxBarHeight * tempHeightRatio;

          // Calculate the position for this bar
          // Center the bars under the label
          const totalWidth = leftArray.length * (tempBarWidth + tempBarSpacing) - tempBarSpacing;
          const startX = leftArrayX - totalWidth / 2;
          const xPos = startX + index * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;

          // Check if this bar is being compared
          const isBeingCompared = isCompareStep && index === leftCompareIndex;

          return (
            <group key={`left-temp-${index}`} position={[xPos, tempArrayY, 0]}>
              {/* Base of the bar */}
              <mesh position={[0, 0, 0]}>
                <boxGeometry args={[tempBarWidth * 1.0, 0.05, tempBarWidth * 1.0]} />
                <meshStandardMaterial
                  color={colors.tempStructure.left} // Use left subarray color (blue)
                  transparent={true}
                  opacity={0.8}
                  roughness={0.8}
                  metalness={0.2}
                />
              </mesh>

              {/* The bar itself */}
              <mesh position={[0, barHeight / 2, 0]}>
                <boxGeometry args={[tempBarWidth * 0.8, barHeight, tempBarWidth * 0.8]} />
                <meshStandardMaterial
                  color={colors.tempStructure.left} // Use left subarray color (blue)
                  transparent={true}
                  opacity={0.9}
                  roughness={0.5}
                  metalness={0.3}
                />
              </mesh>

              {/* Value label */}
              <ThemeHtml position={[0, barHeight + 0.3, 0]} center sprite occlude theme={theme}>
                <Paper
                  elevation={1}
                  sx={{
                    px: theme.spacing(0.5),
                    py: theme.spacing(0.1),
                    minWidth: '16px',
                    borderRadius: theme.shape.borderRadius / 1.5,
                    border: 1,
                    borderColor: colors.tempStructure.left,
                    bgcolor: 'background.paper',
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.6rem',
                      textAlign: 'center',
                      display: 'block'
                    }}
                    fontWeight="bold"
                    color="text.primary"
                  >
                    {value}
                  </Typography>
                </Paper>
              </ThemeHtml>

              {/* Comparison arrow - positioned at the bottom of the bar and pointing up */}
              {isBeingCompared && (
                <ThemeHtml
                  position={MergeSortConfig.comparisonArrow.position}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <div style={{
                    color: colors.comparing,
                    background: 'transparent',
                    fontSize: MergeSortConfig.comparisonArrow.fontSize,
                    fontWeight: 'bold',
                    textAlign: 'center',
                    textShadow: `0 0 8px ${colors.comparing}, 0 0 16px ${colors.comparing}`,
                    userSelect: 'none',
                    pointerEvents: 'none',
                    transform: `scale(${MergeSortConfig.comparisonArrow.scale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg)`,
                    animation: MergeSortConfig.comparisonArrow.animation.enabled ?
                      `pulse ${MergeSortConfig.comparisonArrow.animation.duration} infinite alternate` : 'none'
                  }}>
                    ↓
                  </div>
                  <style>
                    {`
                    @keyframes pulse {
                      0% { transform: scale(${MergeSortConfig.comparisonArrow.animation.minScale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg); }
                      100% { transform: scale(${MergeSortConfig.comparisonArrow.animation.maxScale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg); }
                    }
                    `}
                  </style>
                </ThemeHtml>
              )}

              {/* Index label */}
              <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    border: 1,
                    borderColor: 'divider',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                    boxShadow: 1,
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.5rem',
                      lineHeight: 1,
                      textAlign: 'center',
                      width: '100%'
                    }}
                    fontWeight="bold"
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </Box>
              </ThemeHtml>
            </group>
          );
          })
        )}

        {/* Right array bars */}
        {/* Check if right subarray is exhausted */}
        {isCompareStep && currentStep.compareInfo && currentStep.compareInfo.rightExhausted ? (
          // If right subarray is exhausted, don't render any bars
          <ThemeHtml position={[rightArrayX, tempArrayY - 0.5, 0]} center sprite occlude theme={theme}>
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.7rem',
                fontStyle: 'italic',
                color: 'text.secondary',
                textAlign: 'center',
                display: 'block'
              }}
            >
              Exhausted
            </Typography>
          </ThemeHtml>
        ) : (
          // Otherwise, render the right array bars
          rightArray.map((value, index) => {
            // Skip rendering bars for null values (already placed elements)
            if (value === null) {
              // Only render the index label for null values
              const totalWidth = rightArray.length * (tempBarWidth + tempBarSpacing) - tempBarSpacing;
              const startX = rightArrayX - totalWidth / 2;
              const xPos = startX + index * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;

              return (
                <group key={`right-temp-${index}`} position={[xPos, tempArrayY, 0]}>
                  {/* Index label only */}
                  <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        border: 1,
                        borderColor: 'divider',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                        boxShadow: 1,
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: '0.5rem',
                          lineHeight: 1,
                          textAlign: 'center',
                          width: '100%'
                        }}
                        fontWeight="bold"
                        color="text.secondary"
                      >
                        {index}
                      </Typography>
                    </Box>
                  </ThemeHtml>
                </group>
              );
            }

          // Calculate the bar height based on the value, but reduced for temporary arrays
          const barHeight = (value / maxTempValue) * maxBarHeight * tempHeightRatio;

          // Calculate the position for this bar
          // Center the bars under the label
          const totalWidth = rightArray.length * (tempBarWidth + tempBarSpacing) - tempBarSpacing;
          const startX = rightArrayX - totalWidth / 2;
          const xPos = startX + index * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;

          // Check if this bar is being compared
          const isBeingCompared = isCompareStep && index === rightCompareIndex;

          return (
            <group key={`right-temp-${index}`} position={[xPos, tempArrayY, 0]}>
              {/* Base of the bar */}
              <mesh position={[0, 0, 0]}>
                <boxGeometry args={[tempBarWidth * 1.0, 0.05, tempBarWidth * 1.0]} />
                <meshStandardMaterial
                  color={colors.tempStructure.right} // Use right subarray color (orange)
                  transparent={true}
                  opacity={0.8}
                  roughness={0.8}
                  metalness={0.2}
                />
              </mesh>

              {/* The bar itself */}
              <mesh position={[0, barHeight / 2, 0]}>
                <boxGeometry args={[tempBarWidth * 0.8, barHeight, tempBarWidth * 0.8]} />
                <meshStandardMaterial
                  color={colors.tempStructure.right} // Use right subarray color (orange)
                  transparent={true}
                  opacity={0.9}
                  roughness={0.5}
                  metalness={0.3}
                />
              </mesh>

              {/* Value label */}
              <ThemeHtml position={[0, barHeight + 0.3, 0]} center sprite occlude theme={theme}>
                <Paper
                  elevation={1}
                  sx={{
                    px: theme.spacing(0.5),
                    py: theme.spacing(0.1),
                    minWidth: '16px',
                    borderRadius: theme.shape.borderRadius / 1.5,
                    border: 1,
                    borderColor: colors.tempStructure.right,
                    bgcolor: 'background.paper',
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.6rem',
                      textAlign: 'center',
                      display: 'block'
                    }}
                    fontWeight="bold"
                    color="text.primary"
                  >
                    {value}
                  </Typography>
                </Paper>
              </ThemeHtml>

              {/* Comparison arrow - positioned at the bottom of the bar and pointing up */}
              {isBeingCompared && (
                <ThemeHtml
                  position={MergeSortConfig.comparisonArrow.position}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <div style={{
                    color: colors.comparing,
                    background: 'transparent',
                    fontSize: MergeSortConfig.comparisonArrow.fontSize,
                    fontWeight: 'bold',
                    textAlign: 'center',
                    textShadow: `0 0 8px ${colors.comparing}, 0 0 16px ${colors.comparing}`,
                    userSelect: 'none',
                    pointerEvents: 'none',
                    transform: `scale(${MergeSortConfig.comparisonArrow.scale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg)`,
                    animation: MergeSortConfig.comparisonArrow.animation.enabled ?
                      `pulse ${MergeSortConfig.comparisonArrow.animation.duration} infinite alternate` : 'none'
                  }}>
                    ↓
                  </div>
                  <style>
                    {`
                    @keyframes pulse {
                      0% { transform: scale(${MergeSortConfig.comparisonArrow.animation.minScale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg); }
                      100% { transform: scale(${MergeSortConfig.comparisonArrow.animation.maxScale}) rotate(${MergeSortConfig.comparisonArrow.rotation}deg); }
                    }
                    `}
                  </style>
                </ThemeHtml>
              )}

              {/* Index label */}
              <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    border: 1,
                    borderColor: 'divider',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                    boxShadow: 1,
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.5rem',
                      lineHeight: 1,
                      textAlign: 'center',
                      width: '100%'
                    }}
                    fontWeight="bold"
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </Box>
              </ThemeHtml>
            </group>
          );
          })
        )}
      </group>
    );
  };

  // Function to render result array for a specific merge operation
  const renderResultArrayForMergeOp = (mergeOp) => {
    if (!mergeOp) {
      return null;
    }

    // Always ensure we have a valid merge operation with low and high indices
    // This ensures we always show a result array with the correct size

    // Create a deep copy of the merge operation to avoid modifying the original
    mergeOp = { ...mergeOp };

    // Get the configuration for the right side
    const { resultArray } = explanatoryConfig.rightSide;

    // Since we're now inside a positioned container, we can use simpler positioning
    // The container is already positioned at the Y position from config
    const resultArrayY = 0; // Relative to container

    // Get the position for the result array from the config
    const resultArrayX = resultArray.labelPosition[0]; // Result array position

    // Get the bar scale from the config
    const barScale = explanatoryConfig.common.barScale;

    // Calculate the spacing between elements in the result array
    const resultBarWidth = barWidth * barScale; // Smaller bars for the result array
    const resultBarSpacing = barSpacing * barScale * 3; // Increased spacing to prevent label overlap

    // Reduce the height of result array bars
    // Use a minimum scale to ensure bars are visible
    const resultHeightRatio = Math.max(barScale, 0.35); // Scale the height by the same factor as the width, but ensure it's at least 0.5
    console.log('DEBUG - resultHeightRatio:', resultHeightRatio, 'barScale:', barScale);

    // Handle the case when we're creating the result array
    // Find the result array data from the current step or previous steps
    let resultArrayData = null;

    // If the current step has result array data, use it
    if (currentStep && currentStep.resultArray) {
      resultArrayData = currentStep.resultArray;
    }
    // If the current step has place info, use it to create result array data
    else if (currentStep && currentStep.placeInfo &&
             currentStep.placeInfo.low !== undefined &&
             currentStep.placeInfo.high !== undefined) {
      resultArrayData = {
        low: currentStep.placeInfo.low,
        high: currentStep.placeInfo.high,
        size: currentStep.placeInfo.high - currentStep.placeInfo.low + 1
      };
    }
    // If the merge operation's step has result array data, use it
    else if (mergeOp.step && mergeOp.step.resultArray) {
      resultArrayData = mergeOp.step.resultArray;
    }
    // Otherwise, look for result array data in allSplits
    else {
      const currentSplit = allSplits.find(
        split => split.low === mergeOp.low && split.high === mergeOp.high
      );

      if (currentSplit && currentSplit.resultArray) {
        resultArrayData = currentSplit.resultArray;
      } else {
        // If we still don't have result array data, create a default one based on the merge operation
        resultArrayData = {
          low: mergeOp.low,
          high: mergeOp.high,
          size: mergeOp.high - mergeOp.low + 1
        };
      }
    }

    // Ensure we have valid low and high values
    if (mergeOp.low === undefined || mergeOp.high === undefined) {
      // Try to get low and high from resultArrayData
      if (resultArrayData && resultArrayData.low !== undefined && resultArrayData.high !== undefined) {
        mergeOp.low = resultArrayData.low;
        mergeOp.high = resultArrayData.high;
      } else {
        // If we still don't have valid low and high values, use default values
        mergeOp.low = 0;
        mergeOp.high = validArrayData.length - 1;
      }
    }

    // Get the size ONLY from the controller data - no fallbacks or calculations
    const size = resultArrayData.size;

    // Log the result array data for debugging
    console.log('DEBUG - Result array data:', {
      resultArrayData,
      size,
      mergeOp,
      forceVisible: mergeOp?.forceResultArrayVisible
    });

    // If we have a forceResultArrayVisible flag, make sure we render the result array
    const shouldForceRender = mergeOp?.forceResultArrayVisible === true;

    // Calculate the starting position for the result array
    // Center the bars under the label
    const totalWidth = size * (resultBarWidth + resultBarSpacing) - resultBarSpacing;
    const resultArrayStartX = resultArrayX - (totalWidth / 2);

    // Debug the condition values
    if (currentStep && (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray')) {
      console.log('DEBUG - Step type match:', currentStep.type);
      console.log('DEBUG - Has placeInfo:', !!currentStep.placeInfo);
      console.log('DEBUG - Has indices:', !!currentStep.indices);
      if (currentStep.indices && mergeOp) {
        console.log('DEBUG - indices vs mergeOp:', {
          'indices[0]': currentStep.indices[0],
          'mergeOp.low': mergeOp.low,
          'indices[1]': currentStep.indices[1],
          'mergeOp.high': mergeOp.high,
          'match': currentStep.indices[0] === mergeOp.low && currentStep.indices[1] === mergeOp.high
        });
      }
    }

    // Check if we're placing elements in the result array
    // IMPORTANT: We're removing the strict indices check to make this more flexible
    if (currentStep && (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray') &&
      currentStep.placeInfo) {

      // Log that we're entering the place step rendering
      console.log('DEBUG - Rendering place step:', currentStep.type);

      // Find the place info data from the current step
      const placeInfo = currentStep.placeInfo;

      // Log the place info for debugging
      console.log('DEBUG - Place info:', {
        ...placeInfo,
        size
      });

      // Create an array to track which positions have been filled
      const filledPositions = new Array(size).fill(false);

      // Get the result index from the current step
      const resultIndex = placeInfo.resultIndex;



      // Check if we're currently animating
      const isAnimating = placeAnimation && placeAnimation.active;

      // Get the source value for the current placement
      const sourceValue = currentStep.sourceValue;

      // Create an array to store the values for each position in the result array
      const resultValues = new Array(size).fill(null);

      // Get the previous result values from the placeInfo if available
      if (placeInfo.previousValues && Array.isArray(placeInfo.previousValues)) {
        // Copy the previous values to our resultValues array
        for (let i = 0; i < placeInfo.previousValues.length && i < size; i++) {
          resultValues[i] = placeInfo.previousValues[i];
        }
        console.log('DEBUG - Using previous values from placeInfo:', placeInfo.previousValues);
      } else {
        console.log('DEBUG - No previous values found in placeInfo');
      }

      // Add the current value being placed
      if (resultIndex !== undefined && resultIndex >= 0 && resultIndex < size) {
        resultValues[resultIndex] = sourceValue;
      }

      // First, fill all positions that should be filled based on previous steps
      if (resultIndex !== undefined && resultIndex > 0) {
        // Fill all positions before the current one (these are already placed)
        for (let i = 0; i < resultIndex; i++) {
          filledPositions[i] = true;
        }
      }

      // Now handle the current position (the one being animated)
      if (resultIndex !== undefined && resultIndex >= 0 && resultIndex < size) {
        // IMPORTANT: Only mark the current position as filled if we're NOT animating
        // This is the key to fixing the issue - during animation, this position should be empty
        filledPositions[resultIndex] = !isAnimating;
      }

      // Log the filled positions and values for debugging
      console.log("Filled positions:", filledPositions, "resultIndex:", resultIndex, "isAnimating:", isAnimating);
      console.log("Result values:", resultValues);

      return (
        <group>
          {/* Removed background and border boxes for result array */}

          {/* Result array label */}
          <ThemeHtml position={[resultArrayX, resultArrayY + resultArray.labelPosition[1], 0]} center sprite occlude theme={theme}>
            <Paper
              elevation={2}
              sx={{
                px: theme.spacing(0.75),
                py: theme.spacing(0.25),
                borderRadius: theme.shape.borderRadius,
                border: 1,
                borderColor: colors.tempStructure?.result || 'success.main',
                bgcolor: 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
            >
              <Typography
                variant="caption"
                fontWeight="bold"
                color="text.primary"
                align="center"
                sx={{ whiteSpace: 'nowrap' }}
              >
                Result Array
              </Typography>
            </Paper>
          </ThemeHtml>

          {/* Result array bars - mix of filled and empty placeholders */}
          {Array.from({ length: size }).map((_, index) => {
            // Calculate the position for this bar
            const xPos = resultArrayStartX + index * (resultBarWidth + resultBarSpacing) + resultBarWidth / 2;

            // Check if this position is filled
            const isFilled = filledPositions[index];

            // Check if this position is currently being animated
            // We need to check if this specific index is the target of the current animation
            const isAnimating = placeAnimation && placeAnimation.active && index === resultIndex;

            // If the position is filled and NOT currently being animated, render a filled bar
            // If it's being animated, we'll render an empty placeholder instead
            if (isFilled && !isAnimating) {
              // Get the value for this position from the resultValues array
              const positionValue = resultValues[index];

              // Calculate the bar height based on the value
              const barHeight = (positionValue / maxValue) * maxBarHeight * resultHeightRatio;

              return (
                <group key={`result-filled-${index}`} position={[xPos, resultArrayY, 0]}>
                  {/* Base of the bar */}
                  <mesh position={[0, 0, 0]}>
                    <boxGeometry args={[resultBarWidth * 1.0, 0.05, resultBarWidth * 1.0]} />
                    <meshStandardMaterial
                      color={colors.sorted}
                      transparent={true}
                      opacity={0.8} // Slightly more transparent for the base
                      roughness={0.8}
                      metalness={0.2}
                    />
                  </mesh>

                  {/* The bar itself */}
                  <mesh position={[0, barHeight / 2, 0]}>
                    <boxGeometry args={[resultBarWidth * 0.8, barHeight, resultBarWidth * 0.8]} />
                    <meshStandardMaterial
                      color={colors.sorted}
                      transparent={true}
                      opacity={0.9}
                      roughness={0.5}
                      metalness={0.3}
                    />
                  </mesh>

                  {/* Value label */}
                  <ThemeHtml position={[0, barHeight + 0.3, 0]} center sprite occlude theme={theme}>
                    <Paper
                      elevation={1}
                      sx={{
                        px: theme.spacing(0.5),
                        py: theme.spacing(0.1),
                        minWidth: '16px',
                        borderRadius: theme.shape.borderRadius / 1.5,
                        border: 1,
                        borderColor: colors.tempStructure?.result || 'success.main',
                        bgcolor: 'background.paper',
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: '0.6rem',
                          textAlign: 'center',
                          display: 'block'
                        }}
                        fontWeight="bold"
                        color="text.primary"
                      >
                        {positionValue}
                      </Typography>
                    </Paper>
                  </ThemeHtml>

                  {/* Index label */}
                  <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        border: 1,
                        borderColor: 'divider',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                        boxShadow: 1,
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: '0.5rem',
                          lineHeight: 1,
                          textAlign: 'center',
                          width: '100%'
                        }}
                        fontWeight="bold"
                        color="text.secondary"
                      >
                        {index}
                      </Typography>
                    </Box>
                  </ThemeHtml>
                </group>
              );
            }

            // If the position is not filled, render an empty placeholder
            return (
              <group key={`result-empty-${index}`} position={[xPos, resultArrayY, 0]}>
                {/* Base of the bar - dimmed for empty slots */}
                <mesh position={[0, 0, 0]}>
                  <boxGeometry args={[resultBarWidth * 1.0, 0.05, resultBarWidth * 1.0]} />
                  <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#444444' : '#cccccc'}
                    transparent={true}
                    opacity={0.5}
                    roughness={0.8}
                    metalness={0.2}
                  />
                </mesh>

                {/* Empty placeholder for the bar */}
                <mesh position={[0, resultHeightRatio / 2, 0]}>
                  <boxGeometry args={[resultBarWidth * 0.8, resultHeightRatio, resultBarWidth * 0.8]} />
                  <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#444444' : '#cccccc'}
                    transparent={true}
                    opacity={0.3}
                    roughness={0.5}
                    metalness={0.3}
                    wireframe={true}
                  />
                </mesh>

                {/* Index label */}
                <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      border: 1,
                      borderColor: 'divider',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                      boxShadow: 1,
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.5rem',
                        lineHeight: 1,
                        textAlign: 'center',
                        width: '100%'
                      }}
                      fontWeight="bold"
                      color="text.secondary"
                    >
                      {index}
                    </Typography>
                  </Box>
                </ThemeHtml>
              </group>
            );
          })}
        </group>
      );
    }

    // Check if the result array is complete or being copied back
    // IMPORTANT: We're removing the strict indices check to make this more flexible
    // Also check if we should force render the result array
    else if ((currentStep &&
      (currentStep.type === 'result_array_complete' || currentStep.type === 'copy_back')) || shouldForceRender) {

      // Log that we're entering the result array complete or copy back rendering
      console.log('DEBUG - Rendering result/copy step:', currentStep.type);

      // Find the result data from the current step
      let resultData = null;
      let result = null;

      // Try to get result data from the current step
      if (currentStep.type === 'result_array_complete') {
        console.log('DEBUG - Result array complete step:', currentStep);
        if (currentStep.resultArray && currentStep.resultArray.result) {
          resultData = currentStep.resultArray;
          result = resultData.result;
          console.log('DEBUG - Found result in resultArray.result:', result);
        } else if (currentStep.resultArray) {
          // If resultArray exists but doesn't have result property, try to create it
          resultData = currentStep.resultArray;
          // Create a result array based on the size
          const size = resultData.high - resultData.low + 1;
          result = new Array(size);
          // Fill with values from the main array
          for (let i = 0; i < size; i++) {
            result[i] = validArrayData[resultData.low + i];
          }
          console.log('DEBUG - Created result from validArrayData:', result);
        } else {
          // If we still don't have result data, use the entire array
          // This is a fallback for when the controller doesn't provide result array data
          console.log('DEBUG - No result array data found, using entire array as fallback');
          result = [...validArrayData];
          resultData = {
            low: 0,
            high: validArrayData.length - 1,
            size: validArrayData.length
          };
        }

        // For result_array_complete steps, always ensure we have a valid size
        if (resultData && !resultData.size && result) {
          resultData.size = result.length;
          console.log('DEBUG - Added missing size property:', resultData.size);
        }
      }
      else if (currentStep.type === 'copy_back' && currentStep.copyInfo && currentStep.copyInfo.result) {
        resultData = currentStep.copyInfo;
        result = resultData.result;
        console.log('DEBUG - Found result in copyInfo.result:', result);
      }

      // If we don't have result data, we can't render
      if (!result) {
        console.log('DEBUG - No result data found, cannot render result array');
        return null;
      }

      // Calculate the maximum value for scaling the bar heights
      // Use the global maxValue instead of calculating a separate maxResultValue
      // This ensures consistent bar heights across all visualizations
      const maxResultValue = maxValue;

      console.log('DEBUG - maxResultValue:', maxResultValue, 'maxValue:', maxValue, 'result:', result);

      // Add animation for copy_back step
      const isCopyingBack = currentStep.type === 'copy_back';

      return (
        <group>
          {/* Removed background and border boxes for result array */}

          {/* Result array label */}
          <ThemeHtml position={[resultArrayX, resultArrayY + resultArray.labelPosition[1], 0]} center sprite occlude theme={theme}>
            <Paper
              elevation={2}
              sx={{
                px: theme.spacing(0.75),
                py: theme.spacing(0.25),
                borderRadius: theme.shape.borderRadius,
                border: 1,
                borderColor: colors.tempStructure?.result || 'success.main',
                bgcolor: 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none',
                whiteSpace: 'nowrap',
                overflow: 'visible'
              }}
            >
              <Typography
                variant="caption"
                fontWeight="bold"
                color="text.primary"
                align="center"
              >
                {isCopyingBack ? 'Copying Back to Original Array' : 'Completed Result Array'}
              </Typography>
            </Paper>
          </ThemeHtml>

          {/* Result array bars in a dedicated area */}
          {result.map((value, index) => {
            // Calculate the position for this bar
            const xPos = resultArrayStartX + index * (resultBarWidth + resultBarSpacing) + resultBarWidth / 2;

            // Calculate the bar height based on the value, but reduced for result arrays
            // Ensure we have a valid value and prevent division by zero
            const normalizedValue = (value !== undefined && value !== null && !isNaN(value)) ? value : 0;
            const barHeight = (normalizedValue / maxResultValue) * maxBarHeight * resultHeightRatio;

            console.log('DEBUG - Bar height calculation:', {
              value: normalizedValue,
              maxResultValue,
              maxBarHeight,
              resultHeightRatio,
              calculatedHeight: barHeight
            });

            // For copy_back step, add animation to show copying
            const yOffset = isCopyingBack ? Math.sin(Date.now() / 500 + index) * 0.2 : 0;

            return (
              <group key={`result-${index}`} position={[xPos, resultArrayY + yOffset, 0]}>
                {/* Base of the bar */}
                <mesh position={[0, 0, 0]}>
                  <boxGeometry args={[resultBarWidth * 1.0, 0.05, resultBarWidth * 1.0]} />
                  <meshStandardMaterial
                    color={colors.sorted}
                    transparent={true}
                    opacity={0.7}
                    roughness={0.8}
                    metalness={0.2}
                  />
                </mesh>

                {/* The bar itself */}
                <mesh position={[0, Math.max(barHeight, 0.1) / 2, 0]}>
                  <boxGeometry args={[resultBarWidth * 0.8, Math.max(barHeight, 0.1), resultBarWidth * 0.8]} />
                  <meshStandardMaterial
                    color={colors.sorted} // Use sorted color (green)
                    transparent={true}
                    opacity={0.9}
                    roughness={0.5}
                    metalness={0.3}
                  />
                </mesh>

                {/* Value label */}
                <ThemeHtml position={[0, Math.max(barHeight, 0.1) + 0.3, 0]} center sprite occlude theme={theme}>
                  <Paper
                    elevation={1}
                    sx={{
                      px: theme.spacing(0.5),
                      py: theme.spacing(0.1),
                      minWidth: '16px',
                      borderRadius: theme.shape.borderRadius / 1.5,
                      border: 1,
                      borderColor: colors.tempStructure?.result || 'success.main',
                      bgcolor: 'background.paper',
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.6rem',
                        textAlign: 'center',
                        display: 'block'
                      }}
                      fontWeight="bold"
                      color="text.primary"
                    >
                      {value}
                    </Typography>
                  </Paper>
                </ThemeHtml>

                {/* Index label */}
                <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      border: 1,
                      borderColor: 'divider',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                      boxShadow: 1,
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.5rem',
                        lineHeight: 1,
                        textAlign: 'center',
                        width: '100%'
                      }}
                      fontWeight="bold"
                      color="text.secondary"
                    >
                      {index}
                    </Typography>
                  </Box>
                </ThemeHtml>

                {/* Removed arrows for copy_back step */}
              </group>
            );
          })}
        </group>
      );
    }

    // Default case: render empty result array
    else {
      // Log that we're entering the default case
      console.log('DEBUG - Rendering default empty result array');

      // Use ONLY the size from the controller - no fallbacks or calculations
      const emptyArray = new Array(resultArrayData.size).fill(null);

      return (
        <group>
          {/* Removed background and border boxes for empty result array */}

          {/* Result array label */}
          <ThemeHtml position={[resultArrayX, resultArrayY + resultArray.labelPosition[1], 0]} center sprite occlude theme={theme}>
            <Paper
              elevation={2}
              sx={{
                px: theme.spacing(0.75),
                py: theme.spacing(0.25),
                borderRadius: theme.shape.borderRadius,
                border: 1,
                borderColor: colors.tempStructure?.result || colors.sorted,
                bgcolor: 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none',
                whiteSpace: 'nowrap'
              }}
            >
              <Typography
                variant="caption"
                fontWeight="bold"
                color="text.primary"
                textAlign="center"
              >
                Result Array
              </Typography>
            </Paper>
          </ThemeHtml>

          {/* Result array bars - empty placeholders in a dedicated area */}
          {emptyArray.map((_, index) => {
            // Calculate the position for this bar
            const xPos = resultArrayStartX + index * (resultBarWidth + resultBarSpacing) + resultBarWidth / 2;

            return (
              <group key={`result-empty-${index}`} position={[xPos, resultArrayY, 0]}>
                {/* Base of the bar - dimmed for empty slots */}
                <mesh position={[0, 0, 0]}>
                  <boxGeometry args={[resultBarWidth * 1.0, 0.05, resultBarWidth * 1.0]} />
                  <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#444444' : '#cccccc'}
                    transparent={true}
                    opacity={0.5}
                    roughness={0.8}
                    metalness={0.2}
                  />
                </mesh>

                {/* Empty placeholder for the bar */}
                <mesh position={[0, resultHeightRatio / 2, 0]}>
                  <boxGeometry args={[resultBarWidth * 0.8, resultHeightRatio, resultBarWidth * 0.8]} />
                  <meshStandardMaterial
                    color={theme.palette.mode === 'dark' ? '#444444' : '#cccccc'}
                    transparent={true}
                    opacity={0.3}
                    roughness={0.5}
                    metalness={0.3}
                    wireframe={true}
                  />
                </mesh>

                {/* Index label */}
                <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      border: 1,
                      borderColor: 'divider',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                      boxShadow: 1,
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.5rem',
                        lineHeight: 1,
                        textAlign: 'center',
                        width: '100%'
                      }}
                      fontWeight="bold"
                      color="text.secondary"
                    >
                      {index}
                    </Typography>
                  </Box>
                </ThemeHtml>
              </group>
            );
          })}
        </group>
      );
    }
  };

  // Function to render result array as 3D bars
  const renderResultArray = () => {
    // For result_array_complete step, always render the result array
    if (currentStep && currentStep.type === 'result_array_complete') {
      console.log('DEBUG - Rendering result array for result_array_complete step');

      // Create a temporary merge operation for this result array complete step
      const tempMergeOp = {
        low: currentStep.resultArray?.low || 0,
        high: currentStep.resultArray?.high || (validArrayData.length - 1),
        resultArrayCreated: true,
        step: currentStep
      };

      // Force the result array to be visible by setting a flag
      tempMergeOp.forceResultArrayVisible = true;

      // Log the merge operation for debugging
      console.log('DEBUG - Created temp merge operation for result_array_complete:', tempMergeOp);

      return renderResultArrayForMergeOp(tempMergeOp);
    }

    // Don't show result array for sorted step, merge step, or copy_back step
    if (currentStep && (currentStep.type === 'sorted' || currentStep.type === 'merge' || currentStep.type === 'copy_back')) {
      return null;
    }

    // Get the current merge operation
    const currentMergeOp = getCurrentMergeOperation();

    // For all active merge operations that have result arrays created or temp arrays created,
    // render their result arrays (even if empty)
    const mergeOpsWithArrays = activeMergeOperations.filter(op =>
      op.resultArrayCreated || op.tempArraysCreated
    );

    if (mergeOpsWithArrays.length > 0) {
      // If we have a current merge operation, prioritize it
      if (currentMergeOp && (currentMergeOp.resultArrayCreated || currentMergeOp.tempArraysCreated)) {
        return renderResultArrayForMergeOp(currentMergeOp);
      }

      // Otherwise, render the first merge operation with a result array or temp arrays
      return renderResultArrayForMergeOp(mergeOpsWithArrays[0]);
    }

    // SPECIAL HANDLING FOR PLACE STEPS
    if (currentStep && (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray')) {
      if (currentStep.indices) {
        const [low, high] = currentStep.indices;

        // Create a temporary merge operation for this place step
        const tempMergeOp = {
          low,
          high,
          resultArrayCreated: true,
          step: currentStep
        };

        // Use this temporary merge operation to render the result array
        return renderResultArrayForMergeOp(tempMergeOp);
      }
    }

    // If we have a current step with indices, always render a result array for it
    if (currentStep && currentStep.indices) {
      const [low, high] = currentStep.indices;
      const tempMergeOp = {
        low,
        high,
        resultArrayCreated: true,
        step: currentStep
      };

      // Use this temporary merge operation to render the result array
      return renderResultArrayForMergeOp(tempMergeOp);
    }

    // If we have a current merge operation, use it
    if (currentMergeOp) {
      return renderResultArrayForMergeOp(currentMergeOp);
    }

    // If we have no current step or merge operation, but we have active merge operations,
    // render the first one
    if (activeMergeOperations.length > 0) {
      return renderResultArrayForMergeOp(activeMergeOperations[0]);
    }

    // If we have nothing else, return null
    return null;
  };

  // Function to render animated arrows for merge sort place operations
  const renderMergeSortPlaceArrows = () => {
    if (!placeAnimation || !placeAnimation.active) return null;

    // Get the current merge operation
    const currentMergeOp = getCurrentMergeOperation();

    if (!currentMergeOp) return null;

    // Get place info from the place animation or use the current merge operation
    let placeInfo = placeAnimation.placeInfo;
    const { progress, isFromLeftSubarray, isFromRightSubarray, resultIndex } = placeAnimation;

    // Log the animation details for debugging
    console.log("Rendering place animation:", {
      placeInfo,
      isFromLeftSubarray,
      isFromRightSubarray,
      resultIndex,
      currentStep: currentStep?.type
    });

    // If we don't have place info, create a default one based on the current merge operation
    if (!placeInfo) {
      // Try to find the mid point from the current step or allSplits
      let mid = null;

      if (currentStep && currentStep.tempArrays && currentStep.tempArrays.mid !== undefined) {
        mid = currentStep.tempArrays.mid;
      } else if (currentMergeOp.step && currentMergeOp.step.tempArrays && currentMergeOp.step.tempArrays.mid !== undefined) {
        mid = currentMergeOp.step.tempArrays.mid;
      } else {
        const currentSplit = allSplits.find(
          split => split.low === currentMergeOp.low && split.high === currentMergeOp.high
        );

        if (currentSplit && currentSplit.mid !== undefined) {
          mid = currentSplit.mid;
        } else {
          // Calculate a default mid point
          mid = Math.floor(currentMergeOp.low + (currentMergeOp.high - currentMergeOp.low) / 2);
        }
      }

      placeInfo = {
        low: currentMergeOp.low,
        mid: mid,
        high: currentMergeOp.high,
        leftArrayIndex: 0,
        rightArrayIndex: 0
      };
    }

    // Additional validation to ensure we have all required data
    if (placeInfo.low === undefined || placeInfo.mid === undefined ||
      placeInfo.high === undefined || resultIndex === undefined) {
      return null;
    }

    // Calculate the spacing between elements in the temporary arrays
    const tempBarWidth = barWidth * 0.6; // Smaller bars for the temporary arrays
    const tempBarSpacing = barSpacing * 0.6;

    // Reduce the height of temporary bars
    const tempHeightRatio = 0.5; // Make temporary bars 50% of the height of normal bars

    // We'll use the container's Y position from the main container

    // We'll use a much simpler approach that's guaranteed to work
    // Instead of trying to calculate the viewport width in Three.js units,
    // we'll use fixed positions for the temporary arrays and result array

    // Since we're using a container approach, we need to calculate positions relative to the container
    // The container is positioned at [0, containerY, 0]

    // Get the positions for the left and right sides from the config
    const leftContainerX = explanatoryConfig.leftSide.position; // Left side position
    const rightContainerX = explanatoryConfig.rightSide.position; // Right side position

    // We'll calculate positions directly when needed

    // Get the source and target positions
    let sourceX, sourceY, sourceZ;
    let targetX, targetY, targetZ;

    // Calculate the source position based on whether it's from left or right subarray
    // We need to account for the container positions
    if (isFromLeftSubarray) {
      // Source is in the left subarray in the dedicated area
      // Calculate the total width needed for the left array
      const leftArrayWidth = (placeInfo.mid - placeInfo.low + 1) * (tempBarWidth + tempBarSpacing);
      // Calculate the starting position for the left array within its container
      const leftArrayStartX = -leftArrayWidth / 2;
      // Get the left subarray position from the config
      const leftSubarrayX = explanatoryConfig.leftSide.leftSubarray.labelPosition[0];
      // Calculate the position for this specific element
      sourceX = leftContainerX + leftSubarrayX + leftArrayStartX + placeInfo.leftArrayIndex * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;
      sourceY = explanatoryConfig.common.yPosition; // Use the absolute Y position from config
      sourceZ = 0;

      console.log("Left subarray source position:", { sourceX, sourceY, sourceZ, leftArrayWidth, leftArrayStartX, leftArrayIndex: placeInfo.leftArrayIndex, leftSubarrayX });
    } else if (isFromRightSubarray) {
      // Source is in the right subarray in the dedicated area
      // Calculate the total width needed for the right array
      const rightArrayWidth = (placeInfo.high - placeInfo.mid) * (tempBarWidth + tempBarSpacing);
      // Calculate the starting position for the right array within its container
      const rightArrayStartX = -rightArrayWidth / 2;
      // Get the right subarray position from the config
      const rightSubarrayX = explanatoryConfig.leftSide.rightSubarray.labelPosition[0];
      // Calculate the position for this specific element
      sourceX = leftContainerX + rightSubarrayX + rightArrayStartX + placeInfo.rightArrayIndex * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;
      sourceY = explanatoryConfig.common.yPosition; // Use the absolute Y position from config
      sourceZ = 0;

      console.log("Right subarray source position:", { sourceX, sourceY, sourceZ, rightArrayWidth, rightArrayStartX, rightArrayIndex: placeInfo.rightArrayIndex, rightSubarrayX });
    } else {
      // Default source position
      sourceX = 0;
      sourceY = 0;
      sourceZ = 0;
    }

    // Calculate the target position in the result array
    // Calculate the total width needed for the result array
    const resultArrayWidth = (placeInfo.high - placeInfo.low + 1) * (tempBarWidth + tempBarSpacing);
    // Calculate the starting position for the result array within its container
    const resultArrayStartX = -resultArrayWidth / 2;
    // Get the result array position from the config
    const resultArrayX = explanatoryConfig.rightSide.resultArray.labelPosition[0];
    // Calculate the position for this specific element
    targetX = rightContainerX + resultArrayX + resultArrayStartX + resultIndex * (tempBarWidth + tempBarSpacing) + tempBarWidth / 2;
    targetY = explanatoryConfig.common.yPosition; // Use the absolute Y position from config
    targetZ = 0;

    console.log("Result array target position:", { targetX, targetY, targetZ, resultArrayWidth, resultArrayStartX, resultIndex });

    // Validate source and target positions
    if (isNaN(sourceX) || isNaN(sourceY) || isNaN(sourceZ) ||
      isNaN(targetX) || isNaN(targetY) || isNaN(targetZ)) {
      return null;
    }

    // Calculate the current position based on the animation progress
    const currentX = sourceX + (targetX - sourceX) * progress;
    const currentY = sourceY + (targetY - sourceY) * progress + Math.sin(progress * Math.PI) * 2; // Add a small arc
    const currentZ = sourceZ + (targetZ - sourceZ) * progress;

    console.log("Animation progress:", { progress, currentX, currentY, currentZ, sourceX, targetX, sourceY, targetY });

    // Calculate the value for the bar height
    const value = placeInfo.sourceValue;

    // Validate the value
    if (value === undefined || value === null || isNaN(value)) {
      return null;
    }

    const maxTempValue = Math.max(
      ...validArrayData.filter(val => val !== undefined && val !== null),
      1 // Ensure we don't divide by zero
    );

    // Ensure maxTempValue is valid
    if (isNaN(maxTempValue) || maxTempValue <= 0) {
      return null;
    }

    const barHeight = (value / maxTempValue) * maxBarHeight * tempHeightRatio;

    // Handle left and right subarray cases separately
    // This ensures the bar maintains its original appearance during animation

    return (
      <group>
        {/* Animated bar moving from source to target */}
        <group position={[currentX, currentY, currentZ]}>
          {/* Base of the bar */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[tempBarWidth * 1.0, 0.05, tempBarWidth * 1.0]} />
            <meshStandardMaterial
              color={isFromLeftSubarray ? colors.tempStructure?.left || '#2196f3' : colors.tempStructure?.right || '#ff9800'} // Use proper legend colors
              transparent={true}
              opacity={0.9}
              roughness={0.8}
              metalness={0.2}
            />
          </mesh>

          {/* The bar itself */}
          <mesh position={[0, barHeight / 2, 0]}>
            <boxGeometry args={[tempBarWidth * 0.8, barHeight, tempBarWidth * 0.8]} />
            <meshStandardMaterial
              color={isFromLeftSubarray ? colors.tempStructure?.left || '#2196f3' : colors.tempStructure?.right || '#ff9800'} // Use proper legend colors
              transparent={true}
              opacity={0.9}
              roughness={0.5}
              metalness={0.3}
            />
          </mesh>

          {/* Value label */}
          <ThemeHtml position={[0, barHeight + 0.4, 0]} center sprite occlude theme={theme}>
            <Paper
              elevation={1}
              sx={{
                px: theme.spacing(0.5),
                py: theme.spacing(0.1),
                minWidth: '16px',
                borderRadius: theme.shape.borderRadius / 1.5,
                border: 1,
                borderColor: isFromLeftSubarray ? colors.tempStructure?.left || 'info.main' : colors.tempStructure?.right || 'warning.main',
                bgcolor: colors.ui?.labelBackground || 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  fontSize: '0.6rem',
                  textAlign: 'center',
                  display: 'block'
                }}
                fontWeight="bold"
                color="text.primary"
              >
                {value}
              </Typography>
            </Paper>
          </ThemeHtml>
        </group>
      </group>
    );
  };

  // Track all active merge operations
  const [activeMergeOperations, setActiveMergeOperations] = useState([]);

  // Track which elements have been placed from each subarray
  const [placedElements, setPlacedElements] = useState({});

  // Reset placed elements for initial step
  useEffect(() => {
    if (currentStep && (currentStep.type === 'initial' || currentStep.initialArray)) {
      setPlacedElements({});
    }
  }, [currentStep]);

  // Update placed elements when an element is placed from left or right subarray
  useEffect(() => {
    if (!currentStep) return;

    // For place_from_left_subarray and place_from_right_subarray steps, update placedElements
    if (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray') {
      if (currentStep.placeInfo) {
        const { low, high, leftArrayIndex, rightArrayIndex } = currentStep.placeInfo;
        const mergeKey = `${low}-${high}`;

        // Get the current placed elements for this merge operation
        // Initialize with empty arrays if it doesn't exist
        const currentPlaced = placedElements[mergeKey]
          ? { ...placedElements[mergeKey] }
          : { left: [], right: [] };

        // Update the placed elements based on the step type
        if (currentStep.type === 'place_from_left_subarray' && leftArrayIndex !== undefined) {
          // Add the left array index to the placed elements if it's not already there
          if (!currentPlaced.left || !currentPlaced.left.includes(leftArrayIndex)) {
            currentPlaced.left = [...(currentPlaced.left || []), leftArrayIndex];
          }
        } else if (currentStep.type === 'place_from_right_subarray' && rightArrayIndex !== undefined) {
          // Add the right array index to the placed elements if it's not already there
          if (!currentPlaced.right || !currentPlaced.right.includes(rightArrayIndex)) {
            currentPlaced.right = [...(currentPlaced.right || []), rightArrayIndex];
          }
        }

        // Update the placedElements state
        setPlacedElements(prev => ({
          ...prev,
          [mergeKey]: currentPlaced
        }));

        console.log('DEBUG - Updated placedElements:', {
          mergeKey,
          currentPlaced,
          leftArrayIndex,
          rightArrayIndex
        });
      }
    }
  }, [currentStep]);

  // Helper function to update or create a merge operation
  const updateOrCreateMergeOperation = (low, high, updateFn, createFn) => {
    setActiveMergeOperations(prev => {
      // Check if this merge operation already exists
      const exists = prev.some(op => op.low === low && op.high === high);

      if (exists) {
        // Update the existing merge operation
        return prev.map(op => {
          if (op.low === low && op.high === high) {
            return updateFn(op);
          }
          return op;
        });
      } else {
        // Create a new merge operation
        return [...prev, createFn()];
      }
    });
  };

  // Update active merge operations based on the step
  useEffect(() => {
    if (!currentStep) return;

    // For initial step, clear all merge operations and splits
    if (currentStep.type === 'initial' || currentStep.initialArray) {
      setActiveMergeOperations([]);
      setAllSplits([]);
      return;
    }

    // For merge steps, add a new merge operation
    if (currentStep.type === 'merge') {
      const [low, high] = currentStep.indices || [0, 0];

      updateOrCreateMergeOperation(
        low,
        high,
        // Update function - if it exists, don't change it
        (op) => op,
        // Create function
        () => ({
          low,
          high,
          active: true,
          tempArraysCreated: false,
          resultArrayCreated: false,
          completed: false,
          step: currentStep
        })
      );
    }

    // For create_temp_arrays steps, mark the corresponding merge operation
    // Only create temporary arrays, NOT the result array
    else if (currentStep.type === 'create_temp_arrays') {
      const [low, high] = currentStep.indices || [0, 0];

      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          tempArraysCreated: true,
          resultArrayCreated: false, // Do NOT show result array yet
          step: currentStep
        }),
        // Create function
        () => ({
          low,
          high,
          active: true,
          tempArraysCreated: true,
          resultArrayCreated: false, // Do NOT show result array yet
          completed: false,
          step: currentStep
        })
      );
    }

    // For create_result_array steps, mark the corresponding merge operation
    else if (currentStep.type === 'create_result_array') {
      const [low, high] = currentStep.indices || [0, 0];

      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          resultArrayCreated: true,
          step: currentStep
        }),
        // Create function
        () => ({
          low,
          high,
          active: true,
          tempArraysCreated: false,
          resultArrayCreated: true,
          completed: false,
          step: currentStep
        })
      );
    }

    // For sorted steps, mark the corresponding merge operation as completed
    // AND set resultArrayCreated to false to hide the result array
    else if (currentStep.type === 'sorted') {
      const [low, high] = currentStep.indices || [0, 0];

      // Mark the operation as completed but keep it in the list
      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          completed: true,
          resultArrayCreated: false, // Hide the result array
          tempArraysCreated: false, // Hide the temp arrays
          step: currentStep
        }),
        // Create function - this should never be called for sorted steps
        () => ({
          low,
          high,
          active: true,
          completed: true,
          tempArraysCreated: false,
          resultArrayCreated: false,
          step: currentStep
        })
      );

      // We'll only remove operations when their parent operation is completed
      // This ensures that temp arrays and result arrays remain visible until
      // the entire subarray is sorted
    }

    // For complete step, clear all merge operations and splits
    else if (currentStep.type === 'complete') {
      setActiveMergeOperations([]);
      setAllSplits([]);
    }

    // For copy_back steps, mark the operation as copying back but DON'T remove child operations yet
    // This ensures that temp arrays and result arrays remain visible during the copy_back step
    else if (currentStep.type === 'copy_back') {
      const [low, high] = currentStep.indices || [0, 0];

      // Mark the current operation as copying back
      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          copyingBack: true,
          step: currentStep
        }),
        // Create function - this should rarely be called for copy_back steps
        () => ({
          low,
          high,
          active: true,
          copyingBack: true,
          tempArraysCreated: false,
          resultArrayCreated: false,
          step: currentStep
        })
      );

      // We'll only remove operations when the parent operation is completed AND copied back
      // This ensures that temp arrays and result arrays remain visible until
      // the entire subarray is sorted and copied back to the main array
    }

    // For place steps, compare steps, result_array_complete, and copy_back steps,
    // ensure the merge operation exists and has result array created
    else if (currentStep.type === 'place_from_left_subarray' ||
      currentStep.type === 'place_from_right_subarray' ||
      currentStep.type === 'compare' ||
      currentStep.type === 'result_array_complete' ||
      currentStep.type === 'copy_back') {
      const [low, high] = currentStep.indices || [0, 0];

      // IMPORTANT: For these steps, we need to ensure the merge operation exists and has both temp arrays and result array created
      // This is critical to prevent temp arrays and result arrays from disappearing during the merge sort lifecycle

      // For result_array_complete and copy_back steps, we should NOT show temp arrays
      const shouldShowTempArrays = !(currentStep.type === 'result_array_complete' || currentStep.type === 'copy_back');

      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          active: true, // Ensure the operation is active
          tempArraysCreated: shouldShowTempArrays, // Only create temp arrays for non-result_array_complete steps
          resultArrayCreated: true, // Ensure result array is created
          step: currentStep
        }),
        // Create function
        () => ({
          low,
          high,
          active: true,
          tempArraysCreated: shouldShowTempArrays, // Only create temp arrays for non-result_array_complete steps
          resultArrayCreated: true, // Ensure result array is created
          completed: false,
          step: currentStep
        })
      );
    }

    // For all other steps, update the current step in the corresponding merge operation
    else if (currentStep.indices) {
      const [low, high] = currentStep.indices;

      updateOrCreateMergeOperation(
        low,
        high,
        // Update function
        (op) => ({
          ...op,
          step: currentStep
        }),
        // Create function - this should rarely be called
        () => ({
          low,
          high,
          active: true,
          tempArraysCreated: false,
          resultArrayCreated: false,
          completed: false,
          step: currentStep
        })
      );
    }
  }, [currentStep]);

  // Find the current active merge operation based on the current step
  const getCurrentMergeOperation = () => {
    if (!currentStep || !currentStep.indices) return null;

    const [low, high] = currentStep.indices;

    // Find the merge operation that matches the current step's indices
    const matchingOp = activeMergeOperations.find(op => op.low === low && op.high === high);

    // If we found a matching operation, return it
    if (matchingOp) {
      // IMPORTANT: For place_from_left_subarray and place_from_right_subarray steps,
      // ensure the merge operation has temp arrays created
      if (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray') {
        matchingOp.tempArraysCreated = true;
      }
      return matchingOp;
    }

    // If we didn't find a matching operation but we're in a place step,
    // create a temporary merge operation for this step
    if (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray') {
      return {
        low,
        high,
        active: true,
        tempArraysCreated: true,
        resultArrayCreated: true,
        step: currentStep
      };
    }

    // Otherwise, return null
    return null;
  };

  // Determine if we should show temporary arrays
  const shouldShowTempArrays = () => {
    if (!currentStep) return false;

    // Step types that should never show temp arrays
    const noTempArraySteps = ['result_array_complete', 'copy_back', 'sorted', 'merge'];
    if (noTempArraySteps.includes(currentStep.type)) {
      return false;
    }

    // Step types that should always show temp arrays
    const alwaysShowTempArraySteps = ['place_from_left_subarray', 'place_from_right_subarray', 'create_temp_arrays', 'compare'];
    if (alwaysShowTempArraySteps.includes(currentStep.type)) {
      // For place steps, ensure the merge operation has temp arrays created
      if (currentStep.type.startsWith('place_from_')) {
        const currentMergeOp = getCurrentMergeOperation();
        if (currentMergeOp) {
          currentMergeOp.tempArraysCreated = true;
        }
      }
      return true;
    }

    // Check if any active merge operation has temp arrays
    if (activeMergeOperations.some(op => op.tempArraysCreated)) {
      return true;
    }

    // Get the current merge operation
    const currentMergeOp = getCurrentMergeOperation();

    // If there's a current merge operation with temp arrays created, show them
    if (currentMergeOp && currentMergeOp.tempArraysCreated) {
      return true;
    }

    // For merge step, do NOT show temp arrays
    if (currentStep.type === 'merge') {
      return false;
    }

    return false;
  };

  // Determine if we should show result array
  const shouldShowResultArray = () => {
    if (!currentStep) return false;

    // Step types that should never show result array
    const noResultArraySteps = ['sorted', 'merge', 'copy_back', 'create_temp_arrays'];
    if (noResultArraySteps.includes(currentStep.type)) {
      return false;
    }

    // Step types that should always show result array
    const alwaysShowResultArraySteps = [
      'create_result_array',
      'place_from_left_subarray',
      'place_from_right_subarray',
      'compare',
      'result_array_complete'
    ];
    if (alwaysShowResultArraySteps.includes(currentStep.type)) {
      return true;
    }

    // Check if any active merge operation has result array
    if (activeMergeOperations.some(op => op.resultArrayCreated)) {
      return true;
    }

    // Get the current merge operation
    const currentMergeOp = getCurrentMergeOperation();

    // If there's a current merge operation with result array created, show it
    // But don't show result array just because temp arrays are created
    if (currentMergeOp && currentMergeOp.resultArrayCreated) {
      return true;
    }

    // For create_temp_arrays step, do NOT show result array
    if (currentStep.type === 'create_temp_arrays') {
      return false;
    }

    return false;
  };

  // We'll follow the natural lifecycle of the merge sort algorithm

  // Determine if we should show temporary arrays and result array
  const showTempArrays = shouldShowTempArrays();
  const showResultArray = shouldShowResultArray();

  // Create a responsive container for explanatory elements
  const renderExplanatoryContainer = () => {
    // CRITICAL: For place_from_left_subarray and place_from_right_subarray steps,
    // we ALWAYS want to show both temp arrays and result array
    const isPlaceStep = currentStep && (
      currentStep.type === 'place_from_left_subarray' ||
      currentStep.type === 'place_from_right_subarray'
    );

    // Force showTempArrays to true for place steps, but never for sorted steps
    const shouldShowTempArraysForce = (isPlaceStep || showTempArrays) && !(currentStep && currentStep.type === 'sorted');

    // Force showResultArray to true for place steps, but never for sorted, merge, or copy_back steps
    const shouldShowResultArrayForce = (isPlaceStep || showResultArray) &&
      !(currentStep && (currentStep.type === 'sorted' || currentStep.type === 'merge' || currentStep.type === 'copy_back'));

    // Only render if we need to show temp arrays or result array
    if (!shouldShowTempArraysForce && !shouldShowResultArrayForce) return null;

    // Get the common configuration
    const { yPosition } = explanatoryConfig.common;

    // Get the positions for the left and right sides from the config
    const leftSideX = explanatoryConfig.leftSide.position;
    const rightSideX = explanatoryConfig.rightSide.position;

    return (
      <group position={[0, yPosition, 0]}>
        {/* Removed container background and border */}

        {/* Left side - Temporary Arrays */}
        <group position={[leftSideX, 0, 0]}>
          {shouldShowTempArraysForce && renderTempArrays()}
        </group>

        {/* Right side - Result Array */}
        <group position={[rightSideX, 0, 0]}>
          {shouldShowResultArrayForce && renderResultArray()}
        </group>
      </group>
    );
  };

  return (
    <group>
      {/* Render the explanatory container with temp arrays and result array */}
      {renderExplanatoryContainer()}

      {/* Render animated arrows for merge sort place operations */}
      {placeAnimation && placeAnimation.active && renderMergeSortPlaceArrows()}

      {/* Removed tracked element display */}

      {/* Render fixed index labels that don't animate */}
      {showIndices && fixedIndexPositions.map((position) => {
        return (
          <mesh key={`fixed-index-${position.index}`} position={[position.xPos, 0, 0]}>
            <ThemeHtml
              position={[0, 0.1, barWidth / 2 + 0.3]}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={3}
                sx={{
                  px: theme.spacing(1),
                  py: theme.spacing(0),
                  minWidth: '24px',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: '50%',
                  border: 2,
                  borderColor: 'primary.main',
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none',
                  opacity: 1,
                  zIndex: 1000
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: '0.75rem',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                    textAlign: 'center'
                  }}
                  fontWeight="bold"
                  color="text.primary"
                >
                  {position.index}
                </Typography>
              </Paper>
            </ThemeHtml>
          </mesh>
        );
      })}

      {/* Render bars */}
      {barPositions.map((position) => {
        // Skip placeholder positions - they're just markers, not actual bars
        if (position.placeholder) {
          // Render a placeholder instead of a bar
          return (
            <EmptySlot
              key={`placeholder-${position.index}`}
              position={[position.xPos, position.yPos, position.zPos]}
              width={barWidth}
              color={position.color}
              opacity={position.opacity}
            />
          );
        }

        // Calculate bar height based on value
        const barHeight = position.value ? (position.value / maxValue) * maxBarHeight : 0;

        // Check if we're in a step that should not show arrows in main array
        const isStepWithTempArrays = currentStep && (
          currentStep.type === 'compare' ||
          currentStep.type === 'place_from_left_subarray' ||
          currentStep.type === 'place_from_right_subarray' ||
          currentStep.type === 'result_array_complete'
        );

        // For copy_back step, we DO want to show arrows in the main array
        const isCopyingBack = currentStep && currentStep.type === 'copy_back';

        const shouldShowArrow = (!isStepWithTempArrays || isCopyingBack) && !position.animating && showArrows.includes(position.index);

        return (
          <Bar3D
            key={`bar-${position.index}`}
            position={[position.xPos, position.yPos, position.zPos]}
            height={barHeight}
            width={barWidth}
            color={position.color}
            value={position.value}
            index={position.index}
            showValue={showValues}
            showIndex={false} // Don't show indices on bars - we're showing them separately
            showArrow={shouldShowArrow}
            opacity={position.opacity}
            yOffset={(barAnimations[position.index]?.active ? barAnimations[position.index]?.yOffset : 0) || position.yOffset || 0} // Use barAnimations yOffset if available
          />
        );
      })}
    </group>
  );
};

export default EnhancedMergeSortBars3D;
