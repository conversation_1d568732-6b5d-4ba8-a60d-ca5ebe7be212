// RatInMazeController.js
// This component provides the controls for the Rat in a Maze algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, Switch, Tooltip, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GridOnIcon from '@mui/icons-material/GridOn';
import RefreshIcon from '@mui/icons-material/Refresh';
import SettingsIcon from '@mui/icons-material/Settings';
import DiagonalArrowRightIcon from '@mui/icons-material/NorthEast';

// Import algorithm functions
import { generateRatInMazeSteps } from './RatInMazeAlgorithm';

const RatInMazeController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [mazeSize, setMazeSize] = useState(params?.mazeSize || 4);
  const [customMaze, setCustomMaze] = useState(params?.customMaze || null);
  const [useCustomMaze, setUseCustomMaze] = useState(params?.useCustomMaze || false);
  const [allowDiagonal, setAllowDiagonal] = useState(params?.allowDiagonal || false);
  const [editableMaze, setEditableMaze] = useState(
    customMaze || Array(mazeSize).fill().map(() => Array(mazeSize).fill(1))
  );

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { mazeSize, useCustomMaze, customMaze: useCustomMaze ? editableMaze : null, allowDiagonal });

    // Update params first
    onParamChange({
      mazeSize,
      customMaze: useCustomMaze ? editableMaze : null,
      useCustomMaze,
      allowDiagonal
    });

    // Set steps and movements directly
    try {
      const result = generateRatInMazeSteps({
        mazeSize,
        customMaze: useCustomMaze ? editableMaze : null,
        allowDiagonal
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [mazeSize, useCustomMaze, editableMaze, allowDiagonal, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update maze when size changes
  useEffect(() => {
    if (!useCustomMaze) {
      // When using a random maze, we don't need to update editableMaze
      return;
    }

    // If maze size changes, create a new maze of the correct size
    if (editableMaze.length !== mazeSize) {
      const newMaze = Array(mazeSize).fill().map(() => Array(mazeSize).fill(1));
      setEditableMaze(newMaze);
    }
  }, [mazeSize, useCustomMaze, editableMaze.length]);

  // Handle maze size change
  const handleMazeSizeChange = useCallback((value) => {
    setMazeSize(parseInt(value, 10));
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle custom maze toggle
  const handleCustomMazeToggle = useCallback((value) => {
    setUseCustomMaze(value);
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle diagonal moves toggle
  const handleDiagonalMovesToggle = useCallback((event) => {
    setAllowDiagonal(event.target.checked);
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle cell value change
  const handleCellChange = useCallback((rowIndex, colIndex) => {
    const newMaze = editableMaze.map(row => [...row]);
    // Toggle between 0 (wall) and 1 (path)
    newMaze[rowIndex][colIndex] = newMaze[rowIndex][colIndex] === 1 ? 0 : 1;

    // Ensure start and end positions are always paths (1)
    newMaze[0][0] = 1; // Start position
    newMaze[mazeSize-1][mazeSize-1] = 1; // End position

    setEditableMaze(newMaze);

    if (useCustomMaze) {
      // Only regenerate steps if we're using a custom maze
      resetAndGenerateSteps();
    }
  }, [editableMaze, useCustomMaze, mazeSize, resetAndGenerateSteps]);

  // Handle randomize maze
  const handleRandomizeMaze = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Rat in a Maze algorithm
  const pseudocode = [
    { code: "function solveMaze(maze, row, col, solution):", lineNumber: 1, indent: 0 },
    { code: "    // If we've reached the destination", lineNumber: 2, indent: 0 },
    { code: "    if row == maze.length - 1 and col == maze[0].length - 1:", lineNumber: 3, indent: 1 },
    { code: "        return true", lineNumber: 4, indent: 1 },
    { code: "    // Try each possible move (right, down, left, up)", lineNumber: 5, indent: 0 },
    { code: "    for each move in possibleMoves:", lineNumber: 6, indent: 1 },
    { code: "        newRow = row + move.row", lineNumber: 7, indent: 2 },
    { code: "        newCol = col + move.col", lineNumber: 8, indent: 2 },
    { code: "        if isValidMove(maze, newRow, newCol) and solution[newRow][newCol] == 0:", lineNumber: 9, indent: 2 },
    { code: "            // Mark this cell as part of the solution path", lineNumber: 10, indent: 0 },
    { code: "            solution[newRow][newCol] = 1", lineNumber: 11, indent: 3 },
    { code: "            // Recursively solve from this new position", lineNumber: 12, indent: 0 },
    { code: "            if solveMaze(maze, newRow, newCol, solution):", lineNumber: 13, indent: 3 },
    { code: "                return true", lineNumber: 14, indent: 4 },
    { code: "            // If we get here, this path didn't work, so backtrack", lineNumber: 15, indent: 0 },
    { code: "            solution[newRow][newCol] = 0", lineNumber: 16, indent: 3 },
    { code: "    // If we've tried all moves and none worked, return false", lineNumber: 17, indent: 0 },
    { code: "    return false", lineNumber: 18, indent: 1 },
    { code: "", lineNumber: 19, indent: 0 },
    { code: "function isValidMove(maze, row, col):", lineNumber: 20, indent: 0 },
    { code: "    // Check if the position is within the maze and is a path (1)", lineNumber: 21, indent: 0 },
    { code: "    return row >= 0 and row < maze.length and", lineNumber: 22, indent: 1 },
    { code: "           col >= 0 and col < maze[0].length and", lineNumber: 23, indent: 1 },
    { code: "           maze[row][col] == 1", lineNumber: 24, indent: 1 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Custom component for maze size selection
  const MazeSizeSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Maze Size</Typography>
        <RadioGroup
          row
          value={value.toString()}
          onChange={(e) => onChange(e.target.value)}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          <FormControlLabel
            value="4"
            control={<Radio size="small" />}
            label="4x4"
            disabled={disabled}
          />
          <FormControlLabel
            value="5"
            control={<Radio size="small" />}
            label="5x5"
            disabled={disabled}
          />
          <FormControlLabel
            value="6"
            control={<Radio size="small" />}
            label="6x6"
            disabled={disabled}
          />
          <FormControlLabel
            value="8"
            control={<Radio size="small" />}
            label="8x8"
            disabled={disabled}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for maze type selection
  const MazeTypeSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Maze Type</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <RadioGroup
            row
            value={value ? 'custom' : 'random'}
            onChange={(e) => onChange(e.target.value === 'custom')}
            sx={{ ml: 1 }}
          >
            <FormControlLabel
              value="random"
              control={<Radio size="small" />}
              label="Random"
              disabled={disabled}
            />
            <FormControlLabel
              value="custom"
              control={<Radio size="small" />}
              label="Custom"
              disabled={disabled}
            />
          </RadioGroup>
          {!value && (
            disabled ? (
              <IconButton
                disabled={true}
                size="small"
                sx={{ ml: 1 }}
              >
                <RefreshIcon />
              </IconButton>
            ) : (
              <Tooltip title="Generate New Random Maze">
                <IconButton
                  onClick={handleRandomizeMaze}
                  size="small"
                  sx={{ ml: 1 }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            )
          )}
        </Box>
      </Box>
    );
  };

  // Custom component for diagonal moves toggle
  const DiagonalMovesToggle = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={value}
              onChange={onChange}
              disabled={disabled}
              color="primary"
            />
          }
          label="Allow Diagonal Moves"
        />
      </Box>
    );
  };

  // Custom component for maze input
  const MazeInput = ({ maze, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2, mb: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Maze Editor (Click cells to toggle between path and wall)
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {maze.map((row, rowIndex) => (
            <Box key={`row-${rowIndex}`} sx={{ display: 'flex', gap: 1 }}>
              {row.map((cell, colIndex) => {
                // Determine if this is the start or end position
                const isStart = rowIndex === 0 && colIndex === 0;
                const isEnd = rowIndex === maze.length - 1 && colIndex === maze[0].length - 1;

                return (
                  <Box
                    key={`cell-${rowIndex}-${colIndex}`}
                    onClick={() => !disabled && !isStart && !isEnd && onChange(rowIndex, colIndex)}
                    sx={{
                      width: '40px',
                      height: '40px',
                      backgroundColor: cell === 1
                        ? (isStart ? 'green' : isEnd ? 'red' : 'white')
                        : '#333',
                      border: '1px solid #ccc',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: disabled || isStart || isEnd ? 'default' : 'pointer',
                      color: cell === 1 ? '#333' : 'white',
                      fontWeight: 'bold',
                      fontSize: '12px',
                      transition: 'background-color 0.2s',
                      '&:hover': {
                        backgroundColor: !disabled && !isStart && !isEnd
                          ? (cell === 1 ? '#e0e0e0' : '#555')
                          : undefined,
                      }
                    }}
                  >
                    {isStart ? 'S' : isEnd ? 'E' : ''}
                  </Box>
                );
              })}
            </Box>
          ))}
        </Box>
        <Typography variant="caption" sx={{ mt: 1, display: 'block', color: 'text.secondary' }}>
          White cells are paths, dark cells are walls. Start (S) and End (E) positions are fixed.
        </Typography>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Rat in a Maze"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Rat in a Maze problem is a classic backtracking problem where we need to find a path from the start (top-left) to the destination (bottom-right) in a maze.
            The maze is represented as a 2D grid where each cell is either a path (1) or a wall (0). The rat can only move through path cells.
          </Typography>
          <Typography variant="body2" gutterBottom>
            The algorithm follows these steps:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2">Start from the initial position (top-left corner).</Typography>
            </li>
            <li>
              <Typography variant="body2">If the current position is the destination, we've found a solution.</Typography>
            </li>
            <li>
              <Typography variant="body2">Try moving in all possible directions (right, down, left, up, and diagonally if allowed).</Typography>
            </li>
            <li>
              <Typography variant="body2">For each direction, check if the move is valid (within bounds and to a path cell).</Typography>
            </li>
            <li>
              <Typography variant="body2">If valid, mark the new position as part of the solution path and recursively try to solve from there.</Typography>
            </li>
            <li>
              <Typography variant="body2">If the recursive call returns true, we've found a solution.</Typography>
            </li>
            <li>
              <Typography variant="body2">If not, we backtrack by unmarking the position and try other directions.</Typography>
            </li>
            <li>
              <Typography variant="body2">If all directions fail, return false to trigger backtracking.</Typography>
            </li>
          </ol>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'mazeSize',
            type: 'component',
            label: 'Maze Size',
            component: MazeSizeSelector,
            componentProps: {
              value: mazeSize,
              onChange: handleMazeSizeChange,
              disabled: state !== 'idle'
            },
            icon: GridOnIcon
          },
          {
            name: 'mazeType',
            type: 'component',
            label: 'Maze Type',
            component: MazeTypeSelector,
            componentProps: {
              value: useCustomMaze,
              onChange: handleCustomMazeToggle,
              disabled: state !== 'idle'
            },
            icon: SettingsIcon
          },
          {
            name: 'allowDiagonal',
            type: 'component',
            label: 'Diagonal Moves',
            component: DiagonalMovesToggle,
            componentProps: {
              value: allowDiagonal,
              onChange: handleDiagonalMovesToggle,
              disabled: state !== 'idle'
            },
            icon: DiagonalArrowRightIcon
          },
          {
            name: 'maze',
            type: 'component',
            label: 'Maze',
            component: MazeInput,
            componentProps: {
              maze: editableMaze,
              onChange: handleCellChange,
              disabled: state !== 'idle' || !useCustomMaze
            }
          }
        ]}
        values={{
          mazeSize,
          mazeType: useCustomMaze,
          allowDiagonal,
          maze: editableMaze
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Rat in a Maze Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default RatInMazeController;
