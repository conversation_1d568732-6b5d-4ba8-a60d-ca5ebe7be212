// InformationSection.js
// A reusable information section component with consistent styling

import React, { useState } from 'react';
import { Box, Typography, Stack, Paper, Collapse, useTheme } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import PropTypes from 'prop-types';

/**
 * A reusable information section component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The content to display in the information section
 * @param {string} props.title - Optional custom title for the section (defaults to "Information")
 * @param {boolean} props.defaultExpanded - Whether the section is expanded by default
 */
const InformationSection = ({
  children,
  title = 'Information',
  defaultExpanded = false,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(defaultExpanded);

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <InfoIcon fontSize="small" sx={{ mr: 0.5 }} /> {title}
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{
          p: 1.5,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          borderRadius: 1,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
        }}>
          {children}
        </Box>
      </Collapse>
    </Paper>
  );
};

InformationSection.propTypes = {
  children: PropTypes.node.isRequired,
  title: PropTypes.string,
  defaultExpanded: PropTypes.bool,
};

export default InformationSection;
