/**
 * Centralized color utility for algorithm visualizations
 * This file provides consistent theme-aware colors for all algorithm visualizations
 */

/**
 * Get theme-aware colors for algorithm visualizations
 * @param {Object} theme - MUI theme object
 * @param {string} algorithm - Optional algorithm name for algorithm-specific colors
 * @returns {Object} Object containing color values for different algorithm states
 */
export const getAlgorithmColors = (theme, algorithm = null) => {
  const isDark = theme?.palette?.mode === 'dark';

  // Algorithm-specific color overrides
  const algorithmColors = {
    mergeSort: {
      // Dedicated colors for merge sort subarrays
      leftSubarray: isDark ? '#9575cd' : '#673ab7', // Left subarray (purple)
      rightSubarray: isDark ? '#ff8a65' : '#e64a19', // Right subarray (distinct orange)

      // Keep the tempStructure colors consistent with the global ones
      tempStructure: {
        left: isDark ? '#9575cd' : '#673ab7', // Left subarray (purple)
        right: isDark ? '#ff8a65' : '#e64a19', // Right subarray (distinct orange)
        result: isDark ? '#81c784' : '#4caf50', // Result array (green)
      }
    },
    quickSort: {
      pivot: isDark ? '#ce93d8' : '#9c27b0', // Pivot element (purple)
      leftPartition: isDark ? '#7986cb' : '#3f51b5', // Left partition (indigo - distinct from comparing)
      rightPartition: isDark ? '#f06292' : '#e91e63', // Right partition (pink - distinct from merging)
    },
    heapSort: {
      heap: isDark ? '#ba68c8' : '#9c27b0', // Heap structure (purple)
      root: isDark ? '#ef5350' : '#d32f2f', // Root element (red)
      heapified: isDark ? '#ba68c8' : '#9c27b0', // Heapified elements (purple)
    }
  };

  // Base colors that work for all algorithms
  const colors = {
    // Base colors
    base: isDark ? '#1a1a1a' : '#f5f5f5', // Darker/lighter base that matches theme better
    surface: isDark ? '#212121' : '#ffffff', // Surface color for scene background

    // Bar colors - more muted and theme-appropriate
    bar: isDark ? '#42a5f5' : '#1976d2', // Blue that works in both themes
    comparing: isDark ? '#ffb74d' : '#f57c00', // Orange for bars being compared
    swapping: isDark ? '#ef5350' : '#d32f2f', // Red for bars being swapped
    merging: isDark ? '#ba68c8' : '#9c27b0', // Purple for merging operations
    sorted: isDark ? '#66bb6a' : '#388e3c', // Green for sorted bars

    // Text and effects
    text: isDark ? '#ffffff' : '#000000',
    shadow: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.3)', // Shadow color
    highlight: isDark ? '#424242' : '#e0e0e0', // Subtle highlight color

    // Scene colors
    ground: isDark ? '#121212' : '#eeeeee', // Ground color

    // UI component colors
    ui: {
      // Label backgrounds
      labelBackground: isDark ? 'rgba(30,30,30,0.8)' : 'rgba(255,255,255,0.8)',
      // Label text
      labelText: isDark ? 'rgba(255,255,255,0.9)' : 'rgba(0,0,0,0.9)',
      // Index label background
      indexBackground: isDark ? 'rgba(30,30,30,0.8)' : 'rgba(240,240,240,0.8)',
      // Panel background
      panelBackground: isDark ? '#222222' : '#f0f0f0',
      // Border colors
      border: theme.palette.divider,
      primaryBorder: theme.palette.primary.main,
    },

    // Temporary structure colors for algorithms like merge sort
    tempStructure: {
      left: isDark ? '#9575cd' : '#673ab7', // Left subarray (purple)
      right: isDark ? '#ff8a65' : '#e64a19', // Right subarray (distinct orange)
      result: isDark ? '#81c784' : '#4caf50', // Result array (green)
      placeholder: isDark ? '#9e9e9e' : '#bdbdbd', // Empty placeholder (gray)
    },

    // Graph algorithm specific colors
    node: {
      default: isDark ? '#64b5f6' : '#1976d2', // Default node color
      visited: isDark ? '#ffb74d' : '#f57c00', // Visited node
      active: isDark ? '#ef5350' : '#d32f2f', // Active node
      completed: isDark ? '#66bb6a' : '#388e3c', // Completed node
    },
    edge: {
      default: isDark ? '#9e9e9e' : '#757575', // Default edge color
      active: isDark ? '#ffb74d' : '#f57c00', // Active edge
      selected: isDark ? '#ef5350' : '#d32f2f', // Selected edge
      completed: isDark ? '#66bb6a' : '#388e3c', // Completed edge
    },

    // Search algorithm specific colors
    search: {
      default: isDark ? '#64b5f6' : '#1976d2', // Default element color
      current: isDark ? '#ce93d8' : '#9c27b0', // Current element being considered
      comparing: isDark ? '#ffb74d' : '#f57c00', // Element being compared
      found: isDark ? '#81c784' : '#4caf50', // Found element
      notFound: isDark ? '#e57373' : '#f44336', // Not found element
      target: isDark ? '#f44336' : '#d32f2f', // Target value
    }
  };

  // If an algorithm is specified, merge the base colors with algorithm-specific colors
  if (algorithm && algorithmColors[algorithm]) {
    // Create a deep merge of the base colors and algorithm-specific colors
    return {
      ...colors,
      ...algorithmColors[algorithm]
    };
  }

  // Otherwise, just return the base colors
  return colors;
};

export default getAlgorithmColors;
