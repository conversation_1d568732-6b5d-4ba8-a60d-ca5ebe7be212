// SieveVisualization.js
// 3D visualization component for the Sieve of Eratosthenes algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const NUMBER_SIZE = 0.8;
const NUMBER_SPACING = 1.2;
const GRID_OFFSET = -10;

const SieveVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 20, 20);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();
  
  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);
      
      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty data to show when no steps are available
      setCurrentStepData({
        isPrime: Array(30).fill(true),
        currentNumber: -1,
        currentMultiple: -1,
        primes: []
      });
    }
    
    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;
      
      // Get current time
      const currentTime = clock.getElapsedTime();
      
      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);
        
        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Number colors
    prime: isDark ? '#00b894' : '#55efc4',
    nonPrime: isDark ? '#636e72' : '#b2bec3',
    current: isDark ? '#0984e3' : '#74b9ff',
    multiple: isDark ? '#e84118' : '#ff7675',
    
    // Text colors
    primeText: isDark ? '#ffffff' : '#2d3436',
    nonPrimeText: isDark ? '#b2bec3' : '#636e72',
    currentText: isDark ? '#ffffff' : '#ffffff',
    multipleText: isDark ? '#ffffff' : '#ffffff',
    
    // Background colors
    background: isDark ? '#1e272e' : '#f5f6fa',
    
    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.prime, label: 'Prime Number' },
    { color: colors.nonPrime, label: 'Non-Prime Number' },
    { color: colors.current, label: 'Current Number' },
    { color: colors.multiple, label: 'Multiple (Being Marked)' },
  ], [colors]);

  // Use fixed position for stability
  const position = [0, 0, 0];

  // Create a number cube
  const NumberCube = ({ position, number, isPrime, isCurrent, isMultiple }) => {
    // Determine color based on state
    let color, textColor;
    if (isCurrent) {
      color = colors.current;
      textColor = colors.currentText;
    } else if (isMultiple) {
      color = colors.multiple;
      textColor = colors.multipleText;
    } else if (isPrime) {
      color = colors.prime;
      textColor = colors.primeText;
    } else {
      color = colors.nonPrime;
      textColor = colors.nonPrimeText;
    }
    
    // Animation for current number and multiples
    const [scale, setScale] = useState(1);
    const [rotation, setRotation] = useState(0);
    
    useEffect(() => {
      if (isCurrent || isMultiple) {
        // Pulse animation
        const interval = setInterval(() => {
          setScale(prev => (prev === 1 ? 1.2 : 1));
        }, 500);
        
        return () => clearInterval(interval);
      } else {
        setScale(1);
      }
    }, [isCurrent, isMultiple]);
    
    useFrame(() => {
      if (isCurrent) {
        // Rotate current number
        setRotation(prev => prev + 0.02);
      } else if (isMultiple) {
        // Rotate multiples slower
        setRotation(prev => prev + 0.01);
      }
    });
    
    return (
      <group position={position} scale={[scale, scale, scale]} rotation={[0, rotation, 0]}>
        {/* Number cube */}
        <mesh castShadow>
          <boxGeometry args={[NUMBER_SIZE, NUMBER_SIZE, NUMBER_SIZE]} />
          <meshStandardMaterial color={color} />
        </mesh>
        
        {/* Number text */}
        <Html
          position={[0, 0, NUMBER_SIZE/2 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: textColor,
            fontSize: `${NUMBER_SIZE * 20}px`,
            fontWeight: 'bold',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {number}
          </div>
        </Html>
      </group>
    );
  };

  // Render the Sieve visualization
  const renderSieveVisualization = () => {
    if (!currentStepData) return null;

    const { isPrime, currentNumber, currentMultiple } = currentStepData;
    
    // Calculate grid dimensions
    const gridSize = Math.ceil(Math.sqrt(isPrime.length));
    
    // Create number cubes
    const numberCubes = [];
    for (let i = 0; i < isPrime.length; i++) {
      // Skip 0 and 1 as they're not part of the sieve
      if (i < 2) continue;
      
      // Calculate grid position
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;
      
      // Calculate 3D position
      const x = (col - gridSize / 2) * NUMBER_SPACING;
      const z = (row - gridSize / 2) * NUMBER_SPACING;
      
      numberCubes.push(
        <NumberCube
          key={`number-${i}`}
          position={[x, 0, z]}
          number={i}
          isPrime={isPrime[i]}
          isCurrent={i === currentNumber}
          isMultiple={i === currentMultiple}
        />
      );
    }
    
    return (
      <group position={position}>
        {/* Number grid */}
        {numberCubes}
        
        {/* Connection line between current number and its multiple */}
        {currentNumber > 0 && currentMultiple > 0 && (
          <ConnectionLine
            from={[
              ((currentNumber % gridSize) - gridSize / 2) * NUMBER_SPACING,
              0,
              (Math.floor(currentNumber / gridSize) - gridSize / 2) * NUMBER_SPACING
            ]}
            to={[
              ((currentMultiple % gridSize) - gridSize / 2) * NUMBER_SPACING,
              0,
              (Math.floor(currentMultiple / gridSize) - gridSize / 2) * NUMBER_SPACING
            ]}
            color={colors.multiple}
          />
        )}
      </group>
    );
  };

  // Create a connection line between two points
  const ConnectionLine = ({ from, to, color }) => {
    const ref = useRef();
    
    useEffect(() => {
      if (ref.current) {
        // Create a direction vector
        const direction = new THREE.Vector3(
          to[0] - from[0],
          to[1] - from[1],
          to[2] - from[2]
        );
        
        // Calculate length
        const length = direction.length();
        
        // Normalize direction
        direction.normalize();
        
        // Position at midpoint
        ref.current.position.set(
          (from[0] + to[0]) / 2,
          (from[1] + to[1]) / 2,
          (from[2] + to[2]) / 2
        );
        
        // Orient along direction
        ref.current.quaternion.setFromUnitVectors(
          new THREE.Vector3(0, 1, 0),
          direction
        );
        
        // Scale to length
        ref.current.scale.set(0.1, length, 0.1);
      }
    }, [from, to]);
    
    return (
      <mesh ref={ref} castShadow>
        <cylinderGeometry args={[1, 1, 1, 8]} />
        <meshStandardMaterial color={color} />
      </mesh>
    );
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Sieve of Eratosthenes Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position}>
          {renderSieveVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default SieveVisualization;
