// ShellSortVisualization.js
// This component provides the visualization for the Shell Sort algorithm.

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateShellSortSteps } from './ShellSortAlgorithm';

// Import reusable visualization components
import { Bar, ColorLegend, StepBoard, GroundPlane, Arrow } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.5;
const BAR_SPACING = 0.5;
const MAX_BAR_HEIGHT = 3.8;
const CAMERA_POSITION = [0, 5, 8];
const CAMERA_LOOKAT = [0, 0, -2];

// Main visualization component
const ShellSortVisualization = ({ params = {} }) => {
    const theme = useTheme();
    const { camera } = useThree();
    const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
    const { speed } = useSpeed();

    // Refs for animation control
    const speedRef = useRef(speed);
    const stepsRef = useRef([]);
    const initialArrayRef = useRef([]);
    const lastAppliedStepRef = useRef(-1);
    const animatingRef = useRef(false);
    const timeoutIdRef = useRef(null);
    const stateRef = useRef(state);
    const currentStepRef = useRef(step);

    // State for array data
    const [arrayData, setArrayData] = useState([]);

    // State for visualization
    const [currentIndex, setCurrentIndex] = useState(-1);
    const [currentValue, setCurrentValue] = useState(null);
    const [compareIndex, setCompareIndex] = useState(-1);
    const [insertIndex, setInsertIndex] = useState(-1);
    const [gap, setGap] = useState(0);
    const [sorted, setSorted] = useState([]);

    // Animation state
    const [moveAnimation, setMoveAnimation] = useState({
        active: false,
        fromIndex: -1,
        toIndex: -1,
        progress: 0,
        startTime: 0
    });

    // Colors based on theme - using the same colors as other sorting algorithms
    const colors = useMemo(() => {
        const isDark = theme.palette.mode === 'dark';
        return {
            bar: isDark ? '#64b5f6' : '#2196f3',      // Default bar color
            current: isDark ? '#ce93d8' : '#9c27b0',  // Current element being considered
            compare: isDark ? '#ffb74d' : '#ff9800',  // Element being compared
            sorted: isDark ? '#81c784' : '#4caf50',   // Sorted element
            base: isDark ? '#1a1a1a' : '#f5f5f5',     // Base color
            ground: isDark ? '#121212' : '#eeeeee',   // Ground color
            gap: isDark ? '#f06292' : '#e91e63'       // Gap indicator color
        };
    }, [theme.palette.mode]);

    // Update refs when state changes
    useEffect(() => {
        stateRef.current = state;
    }, [state]);

    useEffect(() => {
        currentStepRef.current = step;
    }, [step]);

    useEffect(() => {
        speedRef.current = speed;
    }, [speed]);

    // Initialize array when params change
    useEffect(() => {
        // Clear any existing animation timeouts
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state
        animatingRef.current = false;
        setMoveAnimation({
            active: false,
            fromIndex: -1,
            toIndex: -1,
            progress: 0,
            startTime: 0
        });

        // Extract parameters with safety checks
        const { arraySize = 10, randomize = true, customArray = [] } = params;

        // Generate array data
        let newArray = [];

        // Use custom array if provided
        if (customArray && customArray.length > 0) {
            newArray = [...customArray];
        } else if (randomize) {
            // Generate random array
            newArray = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 99) + 1
            );
        } else {
            // Generate reverse sorted array
            newArray = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        // Store the initial array
        initialArrayRef.current = [...newArray];
        setArrayData(newArray);

        // Generate steps
        const { steps } = generateShellSortSteps(newArray);
        stepsRef.current = steps;
        setSteps(steps);
        setTotalSteps(steps.length);
        setAlgorithmSteps(steps.map(step => step.movement));

        // Reset visualization state
        setCurrentIndex(-1);
        setCurrentValue(null);
        setCompareIndex(-1);
        setInsertIndex(-1);
        setGap(0);
        setSorted([]);

        // Reset step if needed
        setStep(0);
        lastAppliedStepRef.current = -1;

        // Position camera
        if (camera) {
            camera.position.set(...CAMERA_POSITION);
            camera.lookAt(...CAMERA_LOOKAT);
        }

        // Force a small delay to ensure everything is reset
        setTimeout(() => {
            // Apply the initial step
            if (steps.length > 0) {
                applyStep(0);
            }
        }, 50);
    }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

    // Apply a single step of the Shell Sort algorithm
    const applyStep = (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
            return;
        }

        const currentStep = stepsRef.current[stepIndex];
        lastAppliedStepRef.current = stepIndex;

        // Update array data if needed
        if (currentStep.array) {
            setArrayData(currentStep.array);
        }

        // Update visualization state based on step type
        switch (currentStep.type) {
            case 'init':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                setGap(0);
                setSorted([]);
                break;

            case 'setGap':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                setGap(currentStep.gap);
                break;

            case 'considerElement':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'startComparison':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'compare':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(currentStep.compareIndex);
                setInsertIndex(-1);
                break;

            case 'shift':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(currentStep.shiftFromIndex);
                setInsertIndex(currentStep.shiftToIndex);

                // Start animation to move item
                if (!animatingRef.current) {
                    animatingRef.current = true;
                    setMoveAnimation({
                        active: true,
                        fromIndex: currentStep.shiftFromIndex,
                        toIndex: currentStep.shiftToIndex,
                        progress: 0,
                        startTime: performance.now()
                    });
                }
                break;

            case 'insert':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(-1);
                setInsertIndex(currentStep.insertIndex);
                break;

            case 'complete':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                setGap(0);
                setSorted(currentStep.sorted || []);
                break;

            default:
                break;
        }
    };

    // Handle step changes
    useEffect(() => {
        // Clear any existing timeout
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state if step changes manually
        if (animatingRef.current && lastAppliedStepRef.current !== step - 1) {
            animatingRef.current = false;
            setMoveAnimation(prev => ({
                ...prev,
                active: false
            }));
        }

        // Don't apply steps if we're still animating and the step is sequential
        if (animatingRef.current && lastAppliedStepRef.current === step - 1) {
            return;
        }

        // Apply the current step
        applyStep(step);
    }, [step]);

    // Handle automatic stepping when state is 'running'
    useEffect(() => {
        // Reset animation state when state changes to idle
        if (state === 'idle') {
            // Clear any existing timeouts
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }

            // Reset animation state
            animatingRef.current = false;
            setMoveAnimation({
                active: false,
                fromIndex: -1,
                toIndex: -1,
                progress: 0,
                startTime: 0
            });

            // Reset visualization state
            setCurrentIndex(-1);
            setCurrentValue(null);
            setCompareIndex(-1);
            setInsertIndex(-1);
            setGap(0);
            setSorted([]);

            // Apply the initial step
            if (stepsRef.current && stepsRef.current.length > 0) {
                applyStep(0);
            }

            return;
        }

        // Only proceed if state is 'running'
        if (state !== 'running') {
            return;
        }

        const steps = stepsRef.current || [];

        // Stop if we've reached the end
        if (step >= steps.length) {
            setState('completed');
            return;
        }

        // Don't schedule next step if we're animating
        if (animatingRef.current) {
            return;
        }

        // Schedule the next step with a delay
        const delay = Math.max(200, 800 - (speedRef.current * 80));
        const timeoutId = setTimeout(() => {
            // Only increment if still in running state
            if (stateRef.current === 'running' && !animatingRef.current) {
                setStep(prevStep => prevStep + 1);
            }
        }, delay);

        // Store the timeout ID for cleanup
        timeoutIdRef.current = timeoutId;

        // Cleanup function
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }
        };
    }, [state, step, setStep, setState]);

    // Animation frame for move animation
    useFrame(() => {
        if (moveAnimation.active) {
            // Use performance.now() for more accurate timing
            const now = performance.now();
            const elapsed = now - moveAnimation.startTime;

            // Adjust duration based on speed - slower for better visibility
            const baseDuration = 1000; // Shorter base duration for faster animation
            const duration = Math.max(500, baseDuration - (speedRef.current * 100));

            const progress = Math.min(elapsed / duration, 1);

            // Only update if there's a meaningful change to reduce unnecessary renders
            if (Math.abs(progress - moveAnimation.progress) > 0.01) {
                setMoveAnimation(prev => ({
                    ...prev,
                    progress
                }));
            }

            // If animation is complete, mark it as inactive
            if (progress >= 1) {
                // Small delay to ensure the animation completes visually
                setTimeout(() => {
                    setMoveAnimation(prev => ({
                        ...prev,
                        active: false
                    }));
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50); // Shorter delay before next step
                    }
                }, 50); // Shorter delay for faster transitions
            }
        }
    });

    // Calculate maximum value for scaling
    const maxValue = useMemo(() => {
        if (!arrayData || arrayData.length === 0) return 1;
        return Math.max(...arrayData);
    }, [arrayData]);

    // Calculate adaptive width and spacing based on array size
    const adaptiveWidth = Math.max(0.2, BAR_WIDTH - (arrayData.length * 0.01));
    const adaptiveSpacing = Math.max(0.1, BAR_SPACING - (arrayData.length * 0.01));

    // Calculate total width of all bars
    const totalWidth = (adaptiveWidth + adaptiveSpacing) * arrayData.length;

    // Calculate starting X position to center the array
    const startX = -(totalWidth / 2) + (adaptiveWidth / 2);

    // Get the current step data for the step board
    const currentStepData = useMemo(() => {
        if (step >= 0 && step < stepsRef.current?.length) {
            return stepsRef.current[step];
        }
        return null;
    }, [step]);

    // Render the visualization
    return (
        <group position={[0, -2, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <directionalLight position={[-10, 10, -10]} intensity={0.4} />
            <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

            {/* Ground plane */}
            <GroundPlane
                position={[0, -0.3, 0]}
                width={100}
                depth={100}
                color={colors.ground}
                receiveShadow={true}
            />

            {/* Step Board */}
            <StepBoard
                position={[0, 6, 0.5]}
                description={currentStepData?.movement || ''}
                stepData={currentStepData || {}}
                currentStep={step + 1}
                totalSteps={stepsRef.current.length}
                showBackground={false}
            />

            {/* Color Legend */}
            <ColorLegend
                position={[0, -3, 0.5]}
                colors={colors}
                items={[
                    { label: 'Default', color: colors.bar },
                    { label: 'Current', color: colors.current },
                    { label: 'Compare', color: colors.compare },
                    { label: 'Gap', color: colors.gap },
                    { label: 'Sorted', color: colors.sorted }
                ]}
            />

            {/* Gap Visualization */}
            {gap > 0 && (
                <group>
                    {Array.from({ length: Math.floor(arrayData.length / gap) }, (_, i) => {
                        const startIndex = i * gap;
                        const endIndex = Math.min(startIndex + gap, arrayData.length - 1);

                        if (startIndex === endIndex) return null;

                        const startPos = startX + startIndex * (adaptiveWidth + adaptiveSpacing);
                        const endPos = startX + endIndex * (adaptiveWidth + adaptiveSpacing);

                        return (
                            <Arrow
                                key={`gap-${i}`}
                                start={[startPos, 4, 0]}
                                end={[endPos, 4, 0]}
                                color={colors.gap}
                                thickness={0.05}
                                headSize={0.2}
                                label={`Gap: ${gap}`}
                                showLabel={i === 0}
                            />
                        );
                    })}
                </group>
            )}

            {/* Array Bars */}
            {arrayData.map((value, index) => {
                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

                // Calculate position with animation
                let x = startX + index * (adaptiveWidth + adaptiveSpacing);
                let y = 0;
                let z = 0;

                // Apply animation for items being moved
                if (moveAnimation.active) {
                    if (index === moveAnimation.fromIndex) {
                        // Hide the source bar during animation
                        return null;
                    }

                    if (index === moveAnimation.toIndex) {
                        // This is where the animated bar will end up
                        // We'll render the animated bar separately
                    }
                }

                // Determine bar color based on current state
                let barColor = colors.bar; // Default color
                let showArrow = false; // Flag to show arrow indicator

                // Use a priority system for coloring and arrow indicators
                if (sorted.includes(index)) {
                    barColor = colors.sorted;
                } else if (index === currentIndex) {
                    barColor = colors.current;
                    showArrow = true;
                } else if (index === compareIndex) {
                    barColor = colors.compare;
                    showArrow = true;
                } else if (index === insertIndex) {
                    barColor = colors.current;
                    showArrow = true;
                }

                // Return the bar component
                return (
                    <Bar
                        key={`bar-${index}`}
                        position={[x, y, z]}
                        height={normalizedHeight}
                        width={adaptiveWidth}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={arrayData.length <= 20}
                        showIndex={true}
                        showArrow={showArrow}
                        depth={adaptiveWidth}
                    />
                );
            })}

            {/* Animated Bar */}
            {moveAnimation.active && (
                (() => {
                    const fromIndex = moveAnimation.fromIndex;
                    const toIndex = moveAnimation.toIndex;
                    const value = arrayData[toIndex]; // The value that was moved

                    const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

                    // Calculate source and target positions
                    const sourceX = startX + fromIndex * (adaptiveWidth + adaptiveSpacing);
                    const targetX = startX + toIndex * (adaptiveWidth + adaptiveSpacing);

                    // Use a smoother easing function for more natural movement
                    const { progress } = moveAnimation;
                    const easedProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);

                    // Calculate current position
                    const x = sourceX + (targetX - sourceX) * easedProgress;

                    // Add a slight arc for better visual effect
                    const arcHeight = 0.5;
                    const midPoint = Math.sin(easedProgress * Math.PI);
                    const y = arcHeight * midPoint;

                    return (
                        <Bar
                            key="animated-bar"
                            position={[x, y, 0]}
                            height={normalizedHeight}
                            width={adaptiveWidth}
                            color={colors.current}
                            value={value}
                            index={toIndex}
                            showValue={arrayData.length <= 20}
                            showIndex={false}
                            showArrow={true}
                            depth={adaptiveWidth}
                        />
                    );
                })()
            )}
        </group>
    );
};

export default ShellSortVisualization;
