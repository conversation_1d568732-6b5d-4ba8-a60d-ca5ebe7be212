// LabelManager.js
// A component to manage persistent labels in 3D space

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Html } from '@react-three/drei';
import { useThree } from '@react-three/fiber';

// Create a context for the label manager
const LabelManagerContext = createContext({
  registerLabel: () => {},
  unregisterLabel: () => {},
  labels: []
});

// Custom hook to use the label manager
export const useLabels = () => useContext(LabelManagerContext);

// Label Manager Provider component
export const LabelManagerProvider = ({ children }) => {
  // State to store all registered labels
  const [labels, setLabels] = useState([]);

  // Function to register a new label
  const registerLabel = (id, position, content, options = {}) => {
    setLabels(prevLabels => {
      // Check if label with this ID already exists
      const existingLabelIndex = prevLabels.findIndex(label => label.id === id);

      // Create new label object
      const newLabel = {
        id,
        position,
        content,
        options: {
          color: 'white',
          backgroundColor: 'rgba(0,0,0,0.6)',
          fontSize: '12px',
          ...options
        }
      };

      // If label exists, update it, otherwise add new one
      if (existingLabelIndex >= 0) {
        const updatedLabels = [...prevLabels];
        updatedLabels[existingLabelIndex] = newLabel;
        return updatedLabels;
      } else {
        return [...prevLabels, newLabel];
      }
    });
  };

  // Function to unregister a label
  const unregisterLabel = (id) => {
    setLabels(prevLabels => prevLabels.filter(label => label.id !== id));
  };

  return (
    <LabelManagerContext.Provider value={{ registerLabel, unregisterLabel, labels }}>
      {children}
      <PersistentLabels />
    </LabelManagerContext.Provider>
  );
};

// Component to render all persistent labels
const PersistentLabels = () => {
  const { labels } = useLabels();
  const { camera } = useThree();

  // Use all labels without filtering

  // Render all valid registered labels
  return (
    <>
      {labels.map(label => (
        <Html
          key={label.id}
          position={label.position}
          center
          distanceFactor={15}
          occlude={false} // Never occlude labels
          zIndexRange={[0, 100]}
          sprite={true} // Always face camera
          transform
          portal={null}
        >
          <div style={{
            color: label.options.color,
            backgroundColor: label.options.backgroundColor,
            padding: '2px 4px',
            borderRadius: '50%',
            fontSize: label.options.fontSize,
            fontWeight: 'bold',
            userSelect: 'none',
            pointerEvents: 'none',
            border: label.options.border || '0.5px solid rgba(255,255,255,0.3)',
            boxShadow: label.options.boxShadow || '0 0 2px rgba(0,0,0,0.5)',
            textShadow: label.options.textShadow || '0 0.5px 0.5px rgba(0,0,0,1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 1, // Always fully visible
            width: label.options.width || 'auto',
            height: label.options.height || 'auto',
            minWidth: label.options.minWidth || '16px',
            minHeight: label.options.minHeight || '16px',
          }}>
            {label.content}
          </div>
        </Html>
      ))}
    </>
  );
};

// Hook to register a label
export const useRegisterLabel = (id, position, content, options = {}) => {
  const { registerLabel, unregisterLabel } = useLabels();

  // Register label on mount and update when dependencies change
  useEffect(() => {
    console.log('Registering label:', id, position, content);
    registerLabel(id, position, content, options);

    // Unregister on unmount
    return () => {
      console.log('Unregistering label:', id);
      unregisterLabel(id);
    };
  }, [id, position, content, options, registerLabel, unregisterLabel]);
};

export default LabelManagerProvider;
