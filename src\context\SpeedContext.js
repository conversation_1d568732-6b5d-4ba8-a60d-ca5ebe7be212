import React, { createContext, useState, useContext, useEffect, useCallback, useMemo } from 'react';

// Create a context for animation speed
export const SpeedContext = createContext();

// Create a provider component
export const SpeedProvider = ({ children }) => {
  // CRITICAL FIX: Always initialize with speed 2
  const [speed, setSpeed] = useState(2); // Default speed (1-10)

  // Log speed changes for debugging
  // useEffect(() => {
  //   console.log('SpeedContext: Current speed is', speed);
  // }, [speed]);

  // Provide a safe setter that ensures speed is always a valid number
  const setSpeedSafe = useCallback((newSpeed) => {
    // Special case: if newSpeed is an empty string, don't update the speed
    if (newSpeed === '') {
      // console.log('SpeedContext: Ignoring empty string input');
      return;
    }

    // Convert to number
    const numSpeed = Number(newSpeed);

    // Validate the input
    if (isNaN(numSpeed)) {
      // console.log('SpeedContext: Ignoring invalid input:', newSpeed);
      return;
    }

    // Ensure speed is between 1-10
    const validSpeed = Math.max(1, Math.min(10, numSpeed));

    // console.log(`SpeedContext: Setting speed to ${validSpeed} (from input ${newSpeed})`);
    setSpeed(validSpeed);
  }, []);

  // Create a stable context value object that doesn't change on every render
  const contextValue = useMemo(() => ({
    speed,
    setSpeed: setSpeedSafe
  }), [speed, setSpeedSafe]);

  return (
    <SpeedContext.Provider value={contextValue}>
      {children}
    </SpeedContext.Provider>
  );
};

// Custom hook to use the speed context
export const useSpeed = () => {
  const context = useContext(SpeedContext);
  if (!context) {
    throw new Error('useSpeed must be used within a SpeedProvider');
  }
  return context;
};
