// AlgorithmControls.js
// Base component for algorithm controls

import React from 'react';
import { Box, Grid, Typography, Divider } from '@mui/material';
import PropTypes from 'prop-types';
import { But<PERSON>, Section, Slider } from '../common';
import { useAlgorithm } from '../../context/AlgorithmContext';
import { useSpeed } from '../../context/SpeedContext';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import SpeedIcon from '@mui/icons-material/Speed';
import SettingsIcon from '@mui/icons-material/Settings';
import InfoIcon from '@mui/icons-material/Info';
import ListIcon from '@mui/icons-material/List';

/**
 * Base component for algorithm controls
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Algorithm title
 * @param {React.ReactNode} props.children - Additional controls
 * @param {React.ReactNode} props.infoContent - Algorithm information content
 * @param {boolean} props.showStepControls - Whether to show step controls
 */
const AlgorithmControls = ({
  title,
  children,
  infoContent,
  showStepControls = true,
}) => {
  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    movements,
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Handle play/pause
  const handlePlayPause = () => {
    if (state === 'running') {
      setState('paused');
    } else {
      setState('running');
    }
  };

  // Handle reset
  const handleReset = () => {
    setState('idle');
    setStep(0);
  };

  // Handle step forward
  const handleStepForward = () => {
    if (step < totalSteps - 1) {
      setStep(step + 1);
    }
  };

  // Handle step backward
  const handleStepBackward = () => {
    if (step > 0) {
      setStep(step - 1);
    }
  };

  // Calculate progress percentage
  const progressPercentage = totalSteps > 0 ? (step / (totalSteps - 1)) * 100 : 0;

  return (
    <Box sx={{ p: 2 }}>
      {/* Algorithm Title */}
      <Typography variant="h5" gutterBottom>
        {title}
      </Typography>

      <Divider sx={{ my: 2 }} />

      {/* Algorithm Information */}
      {infoContent && (
        <Section
          title="Information"
          icon={<InfoIcon />}
          collapsible
          defaultExpanded
        >
          {infoContent}
        </Section>
      )}

      {/* Algorithm Parameters */}
      <Section
        title="Parameters"
        icon={<SettingsIcon />}
        collapsible
        defaultExpanded
      >
        {children}
      </Section>

      {/* Controls Section */}
      <Section
        title="Controls"
        icon={<SpeedIcon />}
      >
        <Grid container spacing={2}>
          {/* Speed Slider */}
          <Grid item xs={12}>
            <Slider
              label="Speed"
              value={speed}
              onChange={setSpeed}
              min={1}
              max={10}
              step={1}
              valueLabelFormat={(value) => `${value}x`}
            />
          </Grid>

          {/* Control Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
              {showStepControls && (
                <Button
                  label="Previous"
                  onClick={handleStepBackward}
                  variant="outlined"
                  disabled={state === 'running' || step === 0}
                  startIcon={<SkipPreviousIcon />}
                  sx={{ flex: 1 }}
                />
              )}

              <Button
                label={state === 'running' ? 'Pause' : 'Start'}
                onClick={handlePlayPause}
                variant="contained"
                color={state === 'running' ? 'secondary' : 'primary'}
                startIcon={state === 'running' ? <PauseIcon /> : <PlayArrowIcon />}
                sx={{ flex: 1 }}
              />

              <Button
                label="Reset"
                onClick={handleReset}
                variant="outlined"
                color="error"
                startIcon={<RestartAltIcon />}
                sx={{ flex: 1 }}
              />

              {showStepControls && (
                <Button
                  label="Next"
                  onClick={handleStepForward}
                  variant="outlined"
                  disabled={state === 'running' || step === totalSteps - 1}
                  endIcon={<SkipNextIcon />}
                  sx={{ flex: 1 }}
                />
              )}
            </Box>
          </Grid>
        </Grid>
      </Section>

      {/* Progress Section */}
      <Section
        title="Progress"
        icon={<ListIcon />}
      >
        <Grid container spacing={2}>
          {/* Progress Bar */}
          <Grid item xs={12}>
            <Box sx={{ width: '100%', bgcolor: 'background.paper', borderRadius: 1, p: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Step
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {step} / {totalSteps - 1}
                </Typography>
              </Box>
              <Box
                sx={{
                  width: '100%',
                  height: 8,
                  bgcolor: 'grey.200',
                  borderRadius: 4,
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    width: `${progressPercentage}%`,
                    height: '100%',
                    bgcolor: 'primary.main',
                    transition: 'width 0.3s ease',
                  }}
                />
              </Box>
            </Box>
          </Grid>

          {/* Current Step Description */}
          <Grid item xs={12}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Current Step:
            </Typography>
            <Box
              sx={{
                p: 1.5,
                bgcolor: 'background.paper',
                borderRadius: 1,
                border: 1,
                borderColor: 'divider',
                maxHeight: 150,
                overflow: 'auto',
              }}
            >
              <Typography variant="body2">
                {movements && movements[step] ? movements[step] : 'No step information available.'}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Section>
    </Box>
  );
};

AlgorithmControls.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  infoContent: PropTypes.node,
  showStepControls: PropTypes.bool,
};

export default AlgorithmControls;
