// Sorting/index.js
// Export all sorting algorithms

import BubbleSort from './BubbleSort';
import MergeSort from './MergeSort';
import QuickSort from './QuickSort';
import HeapSort from './HeapSort';
import InsertionSort from './InsertionSort';
import SelectionSort from './SelectionSort';
import RadixSort from './RadixSort';
import CountingSort from './CountingSort';
import BucketSort from './BucketSort';
import ShellSort from './ShellSort';
import TimSort from './TimSort';

export {
    BubbleSort,
    MergeSort,
    QuickSort,
    HeapSort,
    InsertionSort,
    SelectionSort,
    RadixSort,
    CountingSort,
    BucketSort,
    ShellSort,
    TimSort
};

export default {
    BubbleSort,
    MergeSort,
    QuickSort,
    HeapSort,
    InsertionSort,
    SelectionSort,
    RadixSort,
    CountingSort,
    BucketSort,
    ShellSort,
    TimSort
};
