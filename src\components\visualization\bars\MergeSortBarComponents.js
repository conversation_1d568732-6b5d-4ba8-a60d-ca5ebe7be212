// MergeSortBarComponents.js - Reusable components for merge sort visualization
import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import ThemeHtml from '../ThemeHtml';
import { getTempArrayColor } from '../../../algorithms/Sorting/MergeSort/mergeSortColors';

/**
 * A reusable 3D bar component with value and index labels
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the bar
 * @param {number} props.value - Value of the bar (determines height)
 * @param {number} props.maxValue - Maximum value in the array (for scaling)
 * @param {number} props.index - Index of the bar in the array
 * @param {string} props.color - Color of the bar
 * @param {number} props.barWidth - Width of the bar
 * @param {number} props.maxBarHeight - Maximum height of bars
 * @param {number} props.scale - Scale factor for the bar (default: 1.0)
 * @param {Object} props.theme - MUI theme object
 * @param {boolean} props.showValue - Whether to show the value label
 * @param {boolean} props.showIndex - Whether to show the index label
 * @param {boolean} props.isPlaceholder - Whether this is an empty placeholder
 * @param {string} props.borderColor - Color for the value label border
 * @returns {JSX.Element} The 3D bar with labels
 */
export const Bar3DWithLabels = ({
  position,
  value,
  maxValue,
  index,
  color,
  barWidth,
  maxBarHeight,
  scale = 1.0,
  theme,
  showValue = true,
  showIndex = true,
  isPlaceholder = false,
  borderColor = null
}) => {
  // Calculate the bar height based on the value
  const barHeight = isPlaceholder
    ? maxBarHeight * scale // Fixed height for placeholders
    : (value / maxValue) * maxBarHeight * scale;

  // Use placeholder styling for empty slots
  const barColor = isPlaceholder
    ? (theme.palette.mode === 'dark' ? '#444444' : '#cccccc')
    : color;

  const barOpacity = isPlaceholder ? 0.3 : 0.9;
  const baseOpacity = isPlaceholder ? 0.5 : 0.7;

  return (
    <group position={position}>
      {/* Base of the bar */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[barWidth * 1.0, 0.05, barWidth * 1.0]} />
        <meshStandardMaterial
          color={barColor}
          transparent={true}
          opacity={baseOpacity}
          roughness={0.8}
          metalness={0.2}
          wireframe={isPlaceholder}
        />
      </mesh>

      {/* The bar itself */}
      <mesh position={[0, barHeight / 2, 0]}>
        <boxGeometry args={[barWidth * 0.8, barHeight, barWidth * 0.8]} />
        <meshStandardMaterial
          color={barColor}
          transparent={true}
          opacity={barOpacity}
          roughness={0.5}
          metalness={0.3}
          wireframe={isPlaceholder}
        />
      </mesh>

      {/* Value label */}
      {showValue && !isPlaceholder && (
        <ThemeHtml position={[0, barHeight + 0.2, 0]} center sprite occlude theme={theme}>
          <Paper
            elevation={1}
            sx={{
              px: theme.spacing(0.5),
              py: theme.spacing(0.1),
              minWidth: '16px',
              borderRadius: theme.shape.borderRadius / 1.5,
              border: 1,
              borderColor: borderColor || color,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="caption"
              fontSize="0.6rem"
              fontWeight="bold"
              color="text.primary"
              textAlign="center"
            >
              {value}
            </Typography>
          </Paper>
        </ThemeHtml>
      )}

      {/* Index label */}
      {showIndex && (
        <ThemeHtml position={[0, -0.3, 0]} center sprite occlude theme={theme}>
          <Box
            sx={{
              px: theme.spacing(0.4),
              py: theme.spacing(0.1),
              minWidth: '14px',
              borderRadius: theme.shape.borderRadius / 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
              boxShadow: 1,
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="caption"
              fontSize="0.5rem"
              fontWeight="bold"
              color="text.secondary"
              textAlign="center"
            >
              {index}
            </Typography>
          </Box>
        </ThemeHtml>
      )}
    </group>
  );
};

/**
 * A reusable array visualization component
 * @param {Object} props - Component props
 * @param {string} props.title - Title of the array
 * @param {Array} props.values - Array of values to visualize
 * @param {Array} props.position - [x, y, z] position of the array
 * @param {Object} props.colors - Colors object
 * @param {string} props.arrayType - Type of array ('main', 'left', 'right', 'result')
 * @param {number} props.barWidth - Width of each bar
 * @param {number} props.barSpacing - Spacing between bars
 * @param {number} props.maxBarHeight - Maximum height of bars
 * @param {number} props.scale - Scale factor for the array (default: 1.0)
 * @param {Object} props.theme - MUI theme object
 * @param {boolean} props.showValues - Whether to show value labels
 * @param {boolean} props.showIndices - Whether to show index labels
 * @param {Array} props.labelPosition - [x, y, z] position of the label relative to the array
 * @returns {JSX.Element} The array visualization
 */
export const ArrayVisualization = ({
  title,
  values,
  position,
  colors,
  arrayType,
  barWidth,
  barSpacing,
  maxBarHeight,
  scale = 1.0,
  theme,
  showValues = true,
  showIndices = true,
  labelPosition = [0, 2.0, 0]
}) => {
  // If values is null or undefined, create an empty array
  const arrayValues = values || [];

  // Calculate the maximum value for scaling
  const maxValue = Math.max(
    ...arrayValues.filter(val => val !== undefined && val !== null),
    1 // Ensure we don't divide by zero
  );

  // Get the appropriate color for this array type
  const getColor = () => {
    switch (arrayType) {
      case 'left':
        return getTempArrayColor('left', colors);
      case 'right':
        return getTempArrayColor('right', colors);
      case 'result':
        return getTempArrayColor('result', colors);
      case 'sorted':
        return colors.sorted;
      default:
        return colors.bar;
    }
  };

  // Get border color for the array label
  const getBorderColor = () => {
    switch (arrayType) {
      case 'left':
        return getTempArrayColor('left', colors);
      case 'right':
        return getTempArrayColor('right', colors);
      case 'result':
        return getTempArrayColor('result', colors);
      default:
        return 'primary.main';
    }
  };

  return (
    <group position={position}>
      {/* Array label */}
      <ThemeHtml position={labelPosition} center sprite occlude theme={theme}>
        <Paper
          elevation={2}
          sx={{
            px: theme.spacing(0.75),
            py: theme.spacing(0.25),
            borderRadius: theme.shape.borderRadius,
            border: 1,
            borderColor: getBorderColor(),
            bgcolor: 'background.paper',
            userSelect: 'none',
            pointerEvents: 'none',
            whiteSpace: 'nowrap'
          }}
        >
          <Typography
            variant="caption"
            fontWeight="bold"
            color="text.primary"
            textAlign="center"
          >
            {title}
          </Typography>
        </Paper>
      </ThemeHtml>

      {/* Array bars */}
      <group>
        {arrayValues.map((value, index) => {
          // Calculate the position for this bar
          const xPos = index * (barWidth + barSpacing) - (arrayValues.length * (barWidth + barSpacing)) / 2 + barWidth / 2;

          // Check if this is a placeholder (empty slot)
          // For create_result_array step, we want to show empty slots with minimal height
          const isPlaceholder = value === null || value === undefined;

          // For values that are 0, we still want to show a small bar
          const displayValue = value === 0 ? 1 : value;

          return (
            <Bar3DWithLabels
              key={`${arrayType}-${index}`}
              position={[xPos, 0, 0]}
              value={displayValue}
              maxValue={maxValue}
              index={index}
              color={getColor()}
              barWidth={barWidth}
              maxBarHeight={maxBarHeight}
              scale={scale}
              theme={theme}
              showValue={showValues}
              showIndex={showIndices}
              isPlaceholder={isPlaceholder}
              borderColor={getBorderColor()}
            />
          );
        })}
      </group>
    </group>
  );
};

export default {
  Bar3DWithLabels,
  ArrayVisualization
};
