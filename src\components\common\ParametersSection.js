// ParametersSection.js
// A reusable parameters section component with consistent styling

import React, { useState } from 'react';
import { Box, Typography, Stack, Paper, Collapse, useTheme } from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import PropTypes from 'prop-types';

// Import common components
import { Slider, Switch, TextField, Select, CustomArrayInput, NumberInput } from './index';

/**
 * A reusable parameters section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {Array} props.parameters - Array of parameter objects
 * @param {Object} props.values - Current parameter values
 * @param {function} props.onChange - Function to handle parameter changes
 * @param {boolean} props.disabled - Whether the parameters are disabled
 */
const ParametersSection = ({
  parameters = [],
  values = {},
  onChange = () => {},
  disabled = false,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(true);

  // Handle parameter change
  const handleParameterChange = (name, value) => {
    onChange({ ...values, [name]: value });
  };

  // Render parameter based on its type
  const renderParameter = (param) => {
    const { name, type, label, min, max, step, options, icon: Icon, ...rest } = param;
    const value = values[name] !== undefined ? values[name] : (param.defaultValue || '');

    switch (type) {
      case 'slider':
        // Extract non-standard props that shouldn't be passed to the Slider component
        const { disableWhen, showOnlyWhen, ...sliderRest } = rest;

        // Calculate if the slider should be disabled
        const isSliderDisabled = disabled || (disableWhen && values[disableWhen]);

        return (
          <Box key={name}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                {Icon && <Icon fontSize="small" sx={{ mr: 0.5 }} />} {label}
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {value}
              </Typography>
            </Box>
            <Slider
              label=""
              value={value}
              onChange={(newValue) => handleParameterChange(name, newValue)}
              min={typeof min === 'function' ? min(values) : (min || 0)}
              max={typeof max === 'function' ? max(values) : (max || 100)}
              step={step || 1}
              marks={true}
              showValue={false}
              disabled={isSliderDisabled}
              {...sliderRest}
            />
          </Box>
        );

      case 'switch':
        // Extract non-standard props that shouldn't be passed to the Switch component
        const { disableWhen: switchDisableWhen, showOnlyWhen: switchShowOnlyWhen, ...switchRest } = rest;

        // Calculate if the switch should be disabled
        const isSwitchDisabled = disabled || (switchDisableWhen && values[switchDisableWhen]);

        return (
          <Switch
            key={name}
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {Icon && <Icon fontSize="small" sx={{ mr: 0.5 }} />} {label}
              </Box>
            }
            checked={value}
            onChange={(newValue) => handleParameterChange(name, newValue)}
            disabled={isSwitchDisabled}
            {...switchRest}
          />
        );

      case 'text':
        // Extract non-standard props that shouldn't be passed to the TextField component
        const { disableWhen: textDisableWhen, showOnlyWhen: textShowOnlyWhen, ...textRest } = rest;

        // Calculate if the text field should be disabled
        const isTextFieldDisabled = disabled || (textDisableWhen && values[textDisableWhen]);

        return (
          <TextField
            key={name}
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {Icon && <Icon fontSize="small" sx={{ mr: 0.5 }} />} {label}
              </Box>
            }
            value={value}
            onChange={(newValue) => handleParameterChange(name, newValue)}
            disabled={isTextFieldDisabled}
            {...textRest}
          />
        );

      case 'select':
        // Extract non-standard props that shouldn't be passed to the Select component
        const { disableWhen: selectDisableWhen, showOnlyWhen: selectShowOnlyWhen, ...selectRest } = rest;

        // Calculate if the select should be disabled
        const isSelectDisabled = disabled || (selectDisableWhen && values[selectDisableWhen]);

        return (
          <Select
            key={name}
            label={label}
            value={value}
            onChange={(newValue) => handleParameterChange(name, newValue)}
            options={options || []}
            disabled={isSelectDisabled}
            {...selectRest}
          />
        );

      case 'customArray':
        // Extract non-standard props
        const { disableWhen: arrayDisableWhen, showOnlyWhen: arrayShowOnlyWhen, onApply: arrayOnApply, error: arrayError, helperText: arrayHelperText, placeholder: arrayPlaceholder } = rest;

        // Calculate if the component should be disabled
        const isArrayDisabled = disabled || (arrayDisableWhen && values[arrayDisableWhen]);

        return (
          <Box key={name} sx={{ mt: arrayShowOnlyWhen ? 1 : 0 }}>
            {(!arrayShowOnlyWhen || values[arrayShowOnlyWhen]) && (
              <CustomArrayInput
                value={value}
                onChange={(newValue) => handleParameterChange(name, newValue)}
                onApply={arrayOnApply}
                error={arrayError}
                helperText={arrayHelperText}
                placeholder={arrayPlaceholder}
                disabled={isArrayDisabled}
                // Don't spread rest props to avoid passing showOnlyWhen and other non-standard props
              />
            )}
          </Box>
        );

      case 'numberInput':
        // Extract non-standard props
        const { disableWhen: numberDisableWhen, showOnlyWhen: numberShowOnlyWhen, onApply: numberOnApply, error: numberError, helperText: numberHelperText, placeholder: numberPlaceholder } = rest;

        // Calculate if the component should be disabled
        const isNumberDisabled = disabled || (numberDisableWhen && values[numberDisableWhen]);

        return (
          <Box key={name} sx={{ mt: numberShowOnlyWhen ? 1 : 0 }}>
            {(!numberShowOnlyWhen || values[numberShowOnlyWhen]) && (
              <NumberInput
                value={value !== undefined ? String(value) : ''}
                onChange={(newValue) => handleParameterChange(name, newValue)}
                onApply={() => {
                  // Default onApply implementation if none provided
                  const numValue = parseInt(value, 10);
                  if (!isNaN(numValue)) {
                    const minVal = typeof min === 'function' ? min(values) : (min || 0);
                    const maxVal = typeof max === 'function' ? max(values) : (max || 100);
                    if (numValue >= minVal && numValue <= maxVal) {
                      handleParameterChange(name, numValue);
                    }
                  }
                  if (numberOnApply) numberOnApply();
                }}
                error={numberError}
                helperText={numberHelperText || `Min: ${typeof min === 'function' ? min(values) : (min || 0)}, Max: ${typeof max === 'function' ? max(values) : (max || 100)}`}
                placeholder={numberPlaceholder}
                disabled={isNumberDisabled}
                min={typeof min === 'function' ? min(values) : (min || 0)}
                max={typeof max === 'function' ? max(values) : (max || 100)}
                label={label}
                // Don't spread rest props to avoid passing showOnlyWhen and other non-standard props
              />
            )}
          </Box>
        );

      case 'component':
        // Extract non-standard props
        const { disableWhen: componentDisableWhen, showOnlyWhen: componentShowOnlyWhen, component: Component, componentProps = {} } = rest;

        // Calculate if the component should be disabled
        const isComponentDisabled = disabled || (componentDisableWhen && values[componentDisableWhen]);

        // If the component should be shown based on showOnlyWhen
        if (componentShowOnlyWhen && !values[componentShowOnlyWhen]) {
          return null;
        }

        // Render the component with the provided props
        return (
          <Box key={name} sx={{ mt: 1, mb: 1 }}>
            {Component && <Component {...componentProps} disabled={isComponentDisabled} key={JSON.stringify(componentProps)} />}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <SettingsIcon fontSize="small" sx={{ mr: 0.5 }} /> Parameters
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{
          p: 1.5,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          borderRadius: 1,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
        }}>
          <Stack spacing={2}>
            {parameters.map(renderParameter)}
          </Stack>
        </Box>
      </Collapse>
    </Paper>
  );
};

ParametersSection.propTypes = {
  parameters: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      type: PropTypes.oneOf(['slider', 'switch', 'text', 'select', 'customArray', 'numberInput', 'component']).isRequired,
      label: PropTypes.string.isRequired,
      min: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),
      max: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),
      step: PropTypes.number,
      options: PropTypes.array,
      defaultValue: PropTypes.any,
      icon: PropTypes.elementType,
      showOnlyWhen: PropTypes.string,
      disableWhen: PropTypes.string,
      onApply: PropTypes.func,
      error: PropTypes.string,
      helperText: PropTypes.string,
      placeholder: PropTypes.string,
      component: PropTypes.elementType,
      componentProps: PropTypes.object,
    })
  ),
  values: PropTypes.object,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};

export default ParametersSection;
