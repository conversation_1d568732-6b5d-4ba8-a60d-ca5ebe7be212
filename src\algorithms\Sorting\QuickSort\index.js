// QuickSort/index.js
// Export only what's needed from this algorithm

import QuickSortVisualization from './QuickSortVisualization';
import QuickSortController from './QuickSortController';
import QuickSortAlgorithm from './QuickSortAlgorithm';

export const metadata = {
  id: 'QuickSort',
  name: 'Quick Sort',
  description: 'A divide-and-conquer sorting algorithm that uses a pivot element to partition the array.',
  timeComplexity: 'O(n log n) average, O(n²) worst case',
  spaceComplexity: 'O(log n)',
  defaultParams: {
    arraySize: 10,
    randomize: true,
    customArray: [],
  },
};

export const components = {
  visualization: QuickSortVisualization,
  controller: QuickSortController,
  algorithm: QuickSortAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
