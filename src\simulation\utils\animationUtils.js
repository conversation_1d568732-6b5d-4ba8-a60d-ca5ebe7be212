// animationUtils.js
// Utility functions for animations

/**
 * Linear interpolation between two values
 * 
 * @param {number} a - Start value
 * @param {number} b - End value
 * @param {number} t - Interpolation factor (0-1)
 * @returns {number} Interpolated value
 */
export const lerp = (a, b, t) => {
  return a + (b - a) * t;
};

/**
 * Linear interpolation between two 3D vectors
 * 
 * @param {Object} a - Start vector {x, y, z}
 * @param {Object} b - End vector {x, y, z}
 * @param {number} t - Interpolation factor (0-1)
 * @returns {Object} Interpolated vector {x, y, z}
 */
export const lerpVector = (a, b, t) => {
  return {
    x: lerp(a.x, b.x, t),
    y: lerp(a.y, b.y, t),
    z: lerp(a.z, b.z, t)
  };
};

/**
 * Easing function: Ease In Quad
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeInQuad = (t) => {
  return t * t;
};

/**
 * Easing function: Ease Out Quad
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeOutQuad = (t) => {
  return t * (2 - t);
};

/**
 * Easing function: Ease In Out Quad
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeInOutQuad = (t) => {
  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
};

/**
 * Easing function: Ease In Cubic
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeInCubic = (t) => {
  return t * t * t;
};

/**
 * Easing function: Ease Out Cubic
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeOutCubic = (t) => {
  return (--t) * t * t + 1;
};

/**
 * Easing function: Ease In Out Cubic
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const easeInOutCubic = (t) => {
  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
};

/**
 * Easing function: Elastic
 * 
 * @param {number} t - Input value (0-1)
 * @returns {number} Eased value
 */
export const elastic = (t) => {
  return (33 * t * t * t * t * t - 106 * t * t * t * t + 126 * t * t * t - 67 * t * t + 15 * t);
};

/**
 * Calculate animation duration based on step type and speed
 * 
 * @param {string} stepType - The type of step
 * @param {number} speed - The animation speed (1-10)
 * @returns {number} The animation duration in milliseconds
 */
export const calculateDuration = (stepType, speed = 5) => {
  // Base duration calculation
  // Speed 1 = 2000ms, Speed 10 = 200ms
  const baseDuration = 2200 - (speed * 200);
  
  // Adjust duration based on step type
  switch (stepType) {
    case 'comparison':
      return baseDuration * 0.7;
    case 'swap':
      return baseDuration * 1.2;
    case 'merge':
      return baseDuration * 1.5;
    case 'split':
      return baseDuration * 0.8;
    case 'place':
      return baseDuration * 1.0;
    default:
      return baseDuration;
  }
};

/**
 * Calculate a levitation animation value
 * 
 * @param {number} time - Current time value
 * @param {number} amplitude - Amplitude of the levitation
 * @param {number} frequency - Frequency of the levitation
 * @returns {number} The levitation offset
 */
export const calculateLevitation = (time, amplitude = 0.1, frequency = 1) => {
  return amplitude * Math.sin(time * frequency);
};

/**
 * Calculate a rotation animation value
 * 
 * @param {number} progress - Animation progress (0-1)
 * @param {number} startAngle - Starting angle in radians
 * @param {number} endAngle - Ending angle in radians
 * @returns {number} The current rotation angle
 */
export const calculateRotation = (progress, startAngle = 0, endAngle = Math.PI * 2) => {
  return lerp(startAngle, endAngle, progress);
};

/**
 * Calculate a scale animation value
 * 
 * @param {number} progress - Animation progress (0-1)
 * @param {number} startScale - Starting scale
 * @param {number} endScale - Ending scale
 * @param {Function} easingFn - Easing function to use
 * @returns {number} The current scale
 */
export const calculateScale = (progress, startScale = 1, endScale = 1.2, easingFn = easeInOutQuad) => {
  return lerp(startScale, endScale, easingFn(progress));
};

/**
 * Calculate a color interpolation
 * 
 * @param {Array} startColor - Starting RGB color [r, g, b]
 * @param {Array} endColor - Ending RGB color [r, g, b]
 * @param {number} progress - Animation progress (0-1)
 * @returns {Array} The interpolated RGB color [r, g, b]
 */
export const interpolateColor = (startColor, endColor, progress) => {
  return [
    Math.round(lerp(startColor[0], endColor[0], progress)),
    Math.round(lerp(startColor[1], endColor[1], progress)),
    Math.round(lerp(startColor[2], endColor[2], progress))
  ];
};

/**
 * Convert RGB color to hex string
 * 
 * @param {Array} rgb - RGB color [r, g, b]
 * @returns {string} Hex color string
 */
export const rgbToHex = (rgb) => {
  return '#' + rgb.map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
};
