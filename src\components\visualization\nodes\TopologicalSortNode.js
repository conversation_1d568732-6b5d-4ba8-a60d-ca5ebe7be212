// TopologicalSortNode.js
// A specialized node component for Topological Sort visualization

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

const TopologicalSortNode = ({
  position = [0, 0, 0],
  label = '',
  color = '#2196f3',
  isVisited = false,
  isInStack = false,
  isSorted = false,
  isCurrent = false,
  isNeighbor = false,
  radius = 0.8,
  segments = 32,
  onClick,
}) => {
  // Reference to the mesh for animations
  const nodeRef = useRef();
  const glowRef = useRef();

  // Define colors based on node state
  const nodeColors = useMemo(() => {
    // Base colors
    const baseColor = new THREE.Color(color);
    const visitedColor = new THREE.Color('#69f0ae'); // Green
    const stackColor = new THREE.Color('#ba68c8'); // Purple
    const sortedColor = new THREE.Color('#ff5252'); // Red
    const currentColor = new THREE.Color('#ffeb3b'); // Yellow
    const neighborColor = new THREE.Color('#ff9800'); // Orange

    // Determine the color based on node state
    let finalColor = baseColor;
    if (isCurrent) {
      finalColor = currentColor;
    } else if (isNeighbor) {
      finalColor = neighborColor;
    } else if (isSorted) {
      finalColor = sortedColor;
    } else if (isInStack) {
      finalColor = stackColor;
    } else if (isVisited) {
      finalColor = visitedColor;
    }

    return {
      base: finalColor,
      glow: finalColor.clone().multiplyScalar(1.5),
      emissive: finalColor.clone().multiplyScalar(0.8),
    };
  }, [color, isVisited, isInStack, isSorted, isCurrent, isNeighbor]);

  // Determine scale based on node state
  const nodeScale = useMemo(() => {
    if (isCurrent) return 1.3;
    if (isNeighbor) return 1.2;
    if (isSorted) return 1.2;
    if (isInStack) return 1.1;
    return 1.0;
  }, [isCurrent, isNeighbor, isSorted, isInStack]);

  // Animate node if it's the current node or neighbor
  useFrame(({ clock }) => {
    if (nodeRef.current && (isCurrent || isNeighbor)) {
      // Pulse effect for highlighted nodes
      const pulse = nodeScale + Math.sin(clock.getElapsedTime() * 3) * 0.1;
      nodeRef.current.scale.set(pulse, pulse, pulse);
    } else if (nodeRef.current) {
      // Set static scale for non-highlighted nodes
      nodeRef.current.scale.set(nodeScale, nodeScale, nodeScale);
    }
    
    if (glowRef.current && (isCurrent || isNeighbor)) {
      // Glow effect for highlighted nodes
      const glowPulse = nodeScale + Math.sin(clock.getElapsedTime() * 2) * 0.15;
      glowRef.current.scale.set(glowPulse, glowPulse, glowPulse);
      
      if (glowRef.current.material) {
        glowRef.current.material.opacity = 0.6 + Math.sin(clock.getElapsedTime() * 4) * 0.2;
      }
    }
  });

  // Create a canvas texture for the label
  const labelTexture = useMemo(() => {
    if (!label) return null;
    
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const size = 128;
    canvas.width = size;
    canvas.height = size / 2;
    
    // Clear canvas
    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw text
    context.font = 'bold 64px Arial';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillStyle = 'white';
    context.fillText(label, canvas.width / 2, canvas.height / 2);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, [label]);

  return (
    <group position={position} onClick={onClick}>
      {/* Glow sphere */}
      {(isCurrent || isNeighbor) && (
        <mesh ref={glowRef} scale={[1.3, 1.3, 1.3]}>
          <sphereGeometry args={[radius, segments, segments]} />
          <meshBasicMaterial
            color={nodeColors.glow}
            transparent={true}
            opacity={0.4}
          />
        </mesh>
      )}
      
      {/* Main node sphere */}
      <mesh ref={nodeRef}>
        <sphereGeometry args={[radius, segments, segments]} />
        <meshStandardMaterial
          color={nodeColors.base}
          emissive={nodeColors.emissive}
          emissiveIntensity={0.5}
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Node label */}
      {labelTexture && (
        <mesh position={[0, radius * 1.2, 0]} scale={[radius * 2, radius, 1]}>
          <planeGeometry args={[1, 0.5]} />
          <meshBasicMaterial
            map={labelTexture}
            transparent={true}
            depthTest={false}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}
    </group>
  );
};

export default TopologicalSortNode;
