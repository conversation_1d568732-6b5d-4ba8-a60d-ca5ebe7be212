// GraphEdgeCanvas.js
// A reusable component for graph edges in 3D visualizations with canvas-based weight labels

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

const GraphEdgeCanvas = ({
  start,
  end,
  weight,
  color = '#757575',
  isHighlighted = false,
  isRelaxed = false,
  isNegativeCycle = false,
  curved = false,
  curveHeight = 1.5,
  thickness = 0.1,
  nodeRadius = 0.8,
  onClick,
  showWeight = true,
  negativeWeightColor = '#f44336'
}) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';
  const edgeRef = useRef();
  const weightRef = useRef();

  // Calculate edge direction
  const direction = useMemo(() => {
    return new THREE.Vector3().subVectors(
      new THREE.Vector3(...end),
      new THREE.Vector3(...start)
    ).normalize();
  }, [start, end]);

  // Calculate edge length
  const length = useMemo(() => {
    return new THREE.Vector3(...start).distanceTo(new THREE.Vector3(...end));
  }, [start, end]);

  // Calculate midpoint for weight label
  const midpoint = useMemo(() => {
    if (curved) {
      // For curved edges, calculate a point along the curve
      const startVec = new THREE.Vector3(...start);
      const endVec = new THREE.Vector3(...end);
      const midVec = new THREE.Vector3().addVectors(startVec, endVec).multiplyScalar(0.5);

      // Add height to the midpoint
      const perpendicular = new THREE.Vector3().crossVectors(
        direction,
        new THREE.Vector3(0, 1, 0)
      ).normalize();

      if (perpendicular.length() === 0) {
        perpendicular.set(1, 0, 0);
      }

      midVec.add(perpendicular.multiplyScalar(curveHeight));
      return midVec;
    } else {
      // For straight edges, just use the midpoint
      return new THREE.Vector3().addVectors(
        new THREE.Vector3(...start),
        new THREE.Vector3(...end)
      ).multiplyScalar(0.5);
    }
  }, [start, end, curved, direction, curveHeight]);

  // Calculate points for the edge curve
  const points = useMemo(() => {
    const startVec = new THREE.Vector3(...start);
    const endVec = new THREE.Vector3(...end);

    if (curved) {
      // Create a curved path with a control point
      const midVec = new THREE.Vector3().addVectors(startVec, endVec).multiplyScalar(0.5);

      // Calculate perpendicular direction for the curve
      const perpendicular = new THREE.Vector3().crossVectors(
        direction,
        new THREE.Vector3(0, 1, 0)
      ).normalize();

      if (perpendicular.length() === 0) {
        perpendicular.set(1, 0, 0);
      }

      // Add height to the control point
      const controlPoint = midVec.clone().add(perpendicular.multiplyScalar(curveHeight));

      // Create a quadratic curve
      const curve = new THREE.QuadraticBezierCurve3(
        startVec,
        controlPoint,
        endVec
      );

      // Sample points along the curve
      return curve.getPoints(10);
    } else {
      // For straight edges, just use start and end points
      return [startVec, endVec];
    }
  }, [start, end, curved, direction, curveHeight]);

  // Create canvas texture for weight label
  const weightTexture = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 128; // Increased resolution
    canvas.height = 64; // Increased resolution
    const context = canvas.getContext('2d');

    // Background with border
    context.fillStyle = isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)';
    context.beginPath();
    context.roundRect(0, 0, canvas.width, canvas.height, 16);
    context.fill();

    // Add border
    context.strokeStyle = isDark ? '#ffffff' : '#000000';
    context.lineWidth = 2;
    context.beginPath();
    context.roundRect(2, 2, canvas.width - 4, canvas.height - 4, 14);
    context.stroke();

    // Text with outline for better visibility
    const textColor = weight < 0 ? negativeWeightColor : (isDark ? '#ffffff' : '#000000');

    // Outline
    context.strokeStyle = isDark ? '#000000' : '#ffffff';
    context.lineWidth = 4;
    context.font = 'bold 32px Arial'; // Larger font
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.strokeText(weight.toString(), canvas.width / 2, canvas.height / 2);

    // Fill
    context.fillStyle = textColor;
    context.fillText(weight.toString(), canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, [weight, isDark, negativeWeightColor]);

  // Animate highlighted edges
  useFrame(() => {
    if (edgeRef.current && (isHighlighted || isRelaxed || isNegativeCycle)) {
      const material = edgeRef.current.material;
      material.emissiveIntensity = 0.5 + Math.sin(Date.now() * 0.005) * 0.3;
    }

    // Make weight label always face the camera
    if (weightRef.current) {
      weightRef.current.lookAt(weightRef.current.parent.worldToLocal(
        new THREE.Vector3(0, 0, 0)
      ));
    }
  });

  return (
    <group>
      {/* Edge tube */}
      <mesh ref={edgeRef} onClick={onClick}>
        <tubeGeometry args={[
          new THREE.CatmullRomCurve3(points),
          64,
          thickness * (isHighlighted || isRelaxed || isNegativeCycle ? 1.5 : 1),
          8,
          false
        ]} />
        <meshStandardMaterial
          color={color}
          emissive={isHighlighted || isRelaxed || isNegativeCycle ? color : '#000000'}
          emissiveIntensity={isHighlighted || isRelaxed || isNegativeCycle ? 0.5 : 0}
          transparent={true}
          opacity={0.8}
        />
      </mesh>

      {/* Weight label using canvas texture */}
      {showWeight && (
        <mesh
          ref={weightRef}
          position={midpoint}
          scale={[1, 0.5, 1]} // Larger scale for better visibility
        >
          <planeGeometry args={[1, 1]} />
          <meshBasicMaterial
            map={weightTexture}
            transparent={true}
            depthTest={false}
            side={THREE.DoubleSide}
            alphaTest={0.1} // Prevent transparency issues
          />
        </mesh>
      )}
    </group>
  );
};

export default GraphEdgeCanvas;
