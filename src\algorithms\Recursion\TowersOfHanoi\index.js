// TowersOfHanoi/index.js
// Export only what's needed from this algorithm

import TowersOfHanoiVisualization from './TowersOfHanoiVisualization';
import TowersOfHanoiController from './TowersOfHanoiController';
import TowersOfHanoiAlgorithm from './TowersOfHanoiAlgorithm';

export const metadata = {
  id: 'TowersOfHanoi',
  name: 'Towers of Hanoi',
  description: 'A classic recursive algorithm for solving the Tower of Hanoi puzzle.',
  timeComplexity: 'O(2^n)',
  spaceComplexity: 'O(n)',
  defaultParams: {
    numDiscs: 3,
  },
};

export const components = {
  visualization: TowersOfHanoiVisualization,
  controller: TowersOfHanoiController,
  algorithm: TowersOfHanoiAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
