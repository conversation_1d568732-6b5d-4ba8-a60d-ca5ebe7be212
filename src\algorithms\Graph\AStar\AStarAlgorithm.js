// AStarAlgorithm.js
// Implementation of the A* pathfinding algorithm

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { getSyntaxColors } from '../../../utils/syntaxHighlighting';

/**
 * Generate a graph with random weights and positions
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @returns {Object} - Graph representation with node positions
 */
const generateRandomGraph = (numNodes, density = 0.5) => {
  // Create an empty adjacency list
  const graph = {};
  const nodePositions = {};

  // Generate node positions in a grid-like layout
  const gridSize = Math.ceil(Math.sqrt(numNodes));
  const spacing = 10;

  for (let i = 0; i < numNodes; i++) {
    const row = Math.floor(i / gridSize);
    const col = i % gridSize;

    // Add some randomness to positions
    const xOffset = (Math.random() - 0.5) * 3;
    const yOffset = (Math.random() - 0.5) * 3;

    nodePositions[i] = {
      x: col * spacing + xOffset,
      y: row * spacing + yOffset
    };

    // Initialize empty adjacency list for this node
    graph[i] = {};
  }

  // Add random edges with weights based on Euclidean distance
  for (let i = 0; i < numNodes; i++) {
    for (let j = 0; j < numNodes; j++) {
      if (i !== j && Math.random() < density) {
        // Calculate Euclidean distance for weight
        const dx = nodePositions[i].x - nodePositions[j].x;
        const dy = nodePositions[i].y - nodePositions[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Round to one decimal place
        const weight = Math.round(distance * 10) / 10;

        graph[i][j] = weight;
      }
    }
  }

  // Ensure the graph is connected by connecting neighbors
  for (let i = 0; i < numNodes; i++) {
    const row = Math.floor(i / gridSize);
    const col = i % gridSize;

    // Connect to right neighbor if exists
    if (col < gridSize - 1 && i + 1 < numNodes) {
      const dx = nodePositions[i].x - nodePositions[i + 1].x;
      const dy = nodePositions[i].y - nodePositions[i + 1].y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const weight = Math.round(distance * 10) / 10;

      graph[i][i + 1] = weight;
      graph[i + 1][i] = weight;
    }

    // Connect to bottom neighbor if exists
    if (row < gridSize - 1 && i + gridSize < numNodes) {
      const dx = nodePositions[i].x - nodePositions[i + gridSize].x;
      const dy = nodePositions[i].y - nodePositions[i + gridSize].y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const weight = Math.round(distance * 10) / 10;

      graph[i][i + gridSize] = weight;
      graph[i + gridSize][i] = weight;
    }
  }

  return { graph, nodePositions };
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edges - Array of edges [from, to, weight]
 * @param {Object} nodePositions - Optional custom node positions
 * @returns {Object} - Graph representation
 */
const generateCustomGraph = (numNodes, edges, nodePositions = null) => {
  // Create an empty adjacency list
  const graph = {};

  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = {};
  }

  // Add edges
  edges.forEach(([from, to, weight]) => {
    if (from >= 0 && from < numNodes && to >= 0 && to < numNodes) {
      graph[from][to] = weight;
    }
  });

  // Generate node positions if not provided
  const positions = nodePositions || {};
  if (!nodePositions) {
    const gridSize = Math.ceil(Math.sqrt(numNodes));
    const spacing = 10;

    for (let i = 0; i < numNodes; i++) {
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;

      positions[i] = {
        x: col * spacing,
        y: row * spacing
      };
    }
  }

  return { graph, nodePositions: positions };
};

/**
 * Calculate heuristic (Euclidean distance) between two nodes
 * @param {Object} nodePositions - Node positions
 * @param {number} node - Current node
 * @param {number} goal - Goal node
 * @returns {number} - Heuristic value
 */
const calculateHeuristic = (nodePositions, node, goal) => {
  const dx = nodePositions[node].x - nodePositions[goal].x;
  const dy = nodePositions[node].y - nodePositions[goal].y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Generate steps for A* algorithm visualization
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and shortest path
 */
const generateAStarSteps = (params) => {
  const { nodes: numNodes = 9, startNode = 0, endNode = 8, density = 0.5, customEdges = [] } = params;

  // Generate graph and node positions
  let graph, nodePositions;

  if (customEdges && customEdges.length > 0) {
    const result = generateCustomGraph(numNodes, customEdges);
    graph = result.graph;
    nodePositions = result.nodePositions;
  } else {
    const result = generateRandomGraph(numNodes, density);
    graph = result.graph;
    nodePositions = result.nodePositions;
  }

  const steps = [];
  const nodes = Object.keys(graph).map(Number);

  // Initialize data structures
  const openSet = new Set([startNode]);
  const closedSet = new Set();
  const gScore = {}; // Cost from start to current node
  const fScore = {}; // Estimated total cost (g + h)
  const previous = {};

  // Initialize all scores as Infinity
  nodes.forEach(node => {
    gScore[node] = Infinity;
    fScore[node] = Infinity;
    previous[node] = null;
  });

  // Cost from start to start is 0
  gScore[startNode] = 0;

  // Estimate total cost for start node
  fScore[startNode] = calculateHeuristic(nodePositions, startNode, endNode);

  // Add initialization step
  steps.push({
    type: 'initialize',
    graph: { ...graph },
    nodePositions: { ...nodePositions },
    openSet: [...openSet],
    closedSet: [...closedSet],
    gScore: { ...gScore },
    fScore: { ...fScore },
    previous: { ...previous },
    current: null,
    neighbor: null,
    startNode,
    endNode,
    message: `Initialize A*: Start from node ${startNode}, target is node ${endNode}`
  });

  // Main algorithm loop
  while (openSet.size > 0) {
    // Find the node in openSet with the lowest fScore
    let current = null;
    let lowestFScore = Infinity;

    openSet.forEach(node => {
      if (fScore[node] < lowestFScore) {
        lowestFScore = fScore[node];
        current = node;
      }
    });

    // Add step for selecting current node
    steps.push({
      type: 'select',
      graph: { ...graph },
      nodePositions: { ...nodePositions },
      openSet: [...openSet],
      closedSet: [...closedSet],
      gScore: { ...gScore },
      fScore: { ...fScore },
      previous: { ...previous },
      current,
      neighbor: null,
      startNode,
      endNode,
      message: `Select node ${current} with lowest f-score ${fScore[current].toFixed(2)}`
    });

    // If we've reached the end node, we're done
    if (current === endNode) {
      steps.push({
        type: 'found',
        graph: { ...graph },
        nodePositions: { ...nodePositions },
        openSet: [...openSet],
        closedSet: [...closedSet],
        gScore: { ...gScore },
        fScore: { ...fScore },
        previous: { ...previous },
        current,
        neighbor: null,
        startNode,
        endNode,
        message: `Found path to target node ${endNode} with cost ${gScore[endNode].toFixed(2)}`
      });
      break;
    }

    // Move current from openSet to closedSet
    openSet.delete(current);
    closedSet.add(current);

    // Add step for moving to closed set
    steps.push({
      type: 'close',
      graph: { ...graph },
      nodePositions: { ...nodePositions },
      openSet: [...openSet],
      closedSet: [...closedSet],
      gScore: { ...gScore },
      fScore: { ...fScore },
      previous: { ...previous },
      current,
      neighbor: null,
      startNode,
      endNode,
      message: `Move node ${current} to closed set`
    });

    // Check all neighbors of the current node
    for (const neighbor in graph[current]) {
      const neighborNode = parseInt(neighbor);

      // Skip if neighbor is in closed set
      if (closedSet.has(neighborNode)) {
        steps.push({
          type: 'skip',
          graph: { ...graph },
          nodePositions: { ...nodePositions },
          openSet: [...openSet],
          closedSet: [...closedSet],
          gScore: { ...gScore },
          fScore: { ...fScore },
          previous: { ...previous },
          current,
          neighbor: neighborNode,
          startNode,
          endNode,
          message: `Skip neighbor ${neighborNode} as it's already in closed set`
        });
        continue;
      }

      // Calculate tentative gScore
      const edgeWeight = graph[current][neighborNode];
      const tentativeGScore = gScore[current] + edgeWeight;

      // Add step for checking neighbor
      steps.push({
        type: 'check',
        graph: { ...graph },
        nodePositions: { ...nodePositions },
        openSet: [...openSet],
        closedSet: [...closedSet],
        gScore: { ...gScore },
        fScore: { ...fScore },
        previous: { ...previous },
        current,
        neighbor: neighborNode,
        edgeWeight,
        tentativeGScore,
        startNode,
        endNode,
        message: `Check neighbor ${neighborNode}: current g-score ${gScore[neighborNode] === Infinity ? '∞' : gScore[neighborNode].toFixed(2)}, new potential g-score ${tentativeGScore.toFixed(2)}`
      });

      // If this path is better than the previous one
      if (tentativeGScore < gScore[neighborNode]) {
        // Update path and scores
        previous[neighborNode] = current;
        gScore[neighborNode] = tentativeGScore;
        fScore[neighborNode] = gScore[neighborNode] + calculateHeuristic(nodePositions, neighborNode, endNode);

        // Add step for updating scores
        steps.push({
          type: 'update',
          graph: { ...graph },
          nodePositions: { ...nodePositions },
          openSet: [...openSet],
          closedSet: [...closedSet],
          gScore: { ...gScore },
          fScore: { ...fScore },
          previous: { ...previous },
          current,
          neighbor: neighborNode,
          edgeWeight,
          tentativeGScore,
          startNode,
          endNode,
          message: `Update neighbor ${neighborNode}: g-score = ${gScore[neighborNode].toFixed(2)}, f-score = ${fScore[neighborNode].toFixed(2)}`
        });

        // Add to open set if not already there
        if (!openSet.has(neighborNode)) {
          openSet.add(neighborNode);

          // Add step for adding to open set
          steps.push({
            type: 'open',
            graph: { ...graph },
            nodePositions: { ...nodePositions },
            openSet: [...openSet],
            closedSet: [...closedSet],
            gScore: { ...gScore },
            fScore: { ...fScore },
            previous: { ...previous },
            current,
            neighbor: neighborNode,
            startNode,
            endNode,
            message: `Add neighbor ${neighborNode} to open set`
          });
        }
      }
    }
  }

  // If we didn't find a path
  if (!closedSet.has(endNode)) {
    steps.push({
      type: 'no-path',
      graph: { ...graph },
      nodePositions: { ...nodePositions },
      openSet: [...openSet],
      closedSet: [...closedSet],
      gScore: { ...gScore },
      fScore: { ...fScore },
      previous: { ...previous },
      current: null,
      neighbor: null,
      startNode,
      endNode,
      message: `No path found from node ${startNode} to node ${endNode}`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    graph: { ...graph },
    nodePositions: { ...nodePositions },
    openSet: [...openSet],
    closedSet: [...closedSet],
    gScore: { ...gScore },
    fScore: { ...fScore },
    previous: { ...previous },
    current: null,
    neighbor: null,
    startNode,
    endNode,
    message: `A* algorithm complete`
  });

  // Reconstruct the path
  let path = [];
  if (previous[endNode] !== null) {
    let current = endNode;
    while (current !== null) {
      path.unshift(current);
      current = previous[current];
    }
  }

  return {
    steps,
    gScore,
    fScore,
    previous,
    path,
    graph,
    nodePositions
  };
};

/**
 * A* Algorithm visualization component
 */
const AStarAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Define the steps of the algorithm
  const steps = [
    { line: 0, description: "Initialize open set, closed set, and scores" },
    { line: 1, description: "While open set is not empty" },
    { line: 2, description: "Find node in open set with lowest f-score" },
    { line: 3, description: "If current node is target, we've found the path" },
    { line: 4, description: "Move current node from open set to closed set" },
    { line: 5, description: "For each neighbor of current node" },
    { line: 6, description: "Skip if neighbor is in closed set" },
    { line: 7, description: "Calculate tentative g-score" },
    { line: 8, description: "If new path is better, update scores and previous node" },
    { line: 9, description: "Add neighbor to open set if not already there" },
  ];

  // Get current step
  const currentStep = steps[Math.min(step % steps.length, steps.length - 1)];

  // A* algorithm pseudocode
  const pseudocode = [
    { code: "function aStar(graph, nodePositions, startNode, endNode):", highlight: currentStep.line === 0 },
    { code: "  // Initialize data structures", highlight: false },
    { code: "  openSet = new Set([startNode])", highlight: currentStep.line === 0 },
    { code: "  closedSet = new Set()", highlight: currentStep.line === 0 },
    { code: "  gScore = {}, fScore = {}, previous = {}", highlight: currentStep.line === 0 },
    { code: "  for each node in graph:", highlight: false },
    { code: "    gScore[node] = Infinity", highlight: currentStep.line === 0 },
    { code: "    fScore[node] = Infinity", highlight: currentStep.line === 0 },
    { code: "    previous[node] = null", highlight: currentStep.line === 0 },
    { code: "  gScore[startNode] = 0", highlight: currentStep.line === 0 },
    { code: "  fScore[startNode] = heuristic(startNode, endNode)", highlight: currentStep.line === 0 },
    { code: "", highlight: false },
    { code: "  // Main algorithm loop", highlight: false },
    { code: "  while openSet is not empty:", highlight: currentStep.line === 1 },
    { code: "    // Find node in openSet with lowest fScore", highlight: false },
    { code: "    current = node in openSet with lowest fScore[node]", highlight: currentStep.line === 2 },
    { code: "", highlight: false },
    { code: "    // If we've reached the end node, we're done", highlight: false },
    { code: "    if current == endNode:", highlight: currentStep.line === 3 },
    { code: "      return reconstructPath(previous, endNode)", highlight: currentStep.line === 3 },
    { code: "", highlight: false },
    { code: "    // Move current from openSet to closedSet", highlight: false },
    { code: "    openSet.remove(current)", highlight: currentStep.line === 4 },
    { code: "    closedSet.add(current)", highlight: currentStep.line === 4 },
    { code: "", highlight: false },
    { code: "    // Check all neighbors of the current node", highlight: false },
    { code: "    for each neighbor of current:", highlight: currentStep.line === 5 },
    { code: "      // Skip if neighbor is in closed set", highlight: false },
    { code: "      if neighbor in closedSet:", highlight: currentStep.line === 6 },
    { code: "        continue", highlight: currentStep.line === 6 },
    { code: "", highlight: false },
    { code: "      // Calculate tentative gScore", highlight: false },
    { code: "      tentativeGScore = gScore[current] + distance(current, neighbor)", highlight: currentStep.line === 7 },
    { code: "", highlight: false },
    { code: "      // If this path is better than the previous one", highlight: false },
    { code: "      if tentativeGScore < gScore[neighbor]:", highlight: currentStep.line === 8 },
    { code: "        // Update path and scores", highlight: false },
    { code: "        previous[neighbor] = current", highlight: currentStep.line === 8 },
    { code: "        gScore[neighbor] = tentativeGScore", highlight: currentStep.line === 8 },
    { code: "        fScore[neighbor] = gScore[neighbor] + heuristic(neighbor, endNode)", highlight: currentStep.line === 8 },
    { code: "", highlight: false },
    { code: "        // Add to open set if not already there", highlight: false },
    { code: "        if neighbor not in openSet:", highlight: currentStep.line === 9 },
    { code: "          openSet.add(neighbor)", highlight: currentStep.line === 9 },
    { code: "", highlight: false },
    { code: "  // If we get here, no path was found", highlight: false },
    { code: "  return null", highlight: currentStep.line === 1 },
  ];

  return (
    <div style={{ fontFamily: 'monospace', fontSize: '14px', padding: '10px', overflowX: 'auto' }}>
      {pseudocode.map((line, index) => (
        <div
          key={index}
          style={{
            backgroundColor: line.highlight ? colors.lineHighlight : 'transparent',
            padding: '2px 5px',
            whiteSpace: 'pre',
            color: line.highlight ? colors.highlightedText : colors.text,
          }}
        >
          {line.code}
        </div>
      ))}
    </div>
  );
};

export default AStarAlgorithm;

// Export helper functions for use in other files
AStarAlgorithm.generateAStarSteps = generateAStarSteps;
AStarAlgorithm.generateRandomGraph = generateRandomGraph;
AStarAlgorithm.generateCustomGraph = generateCustomGraph;
AStarAlgorithm.calculateHeuristic = calculateHeuristic;
