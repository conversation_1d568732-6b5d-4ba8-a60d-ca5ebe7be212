// CountingSortController.js
// This component provides the controls for the Counting Sort algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import the detailed steps generator
import { generateCountingSortDetailedSteps } from './CountingSortDetailedSteps';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const CountingSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setAlgorithmArray
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Generate array and steps when parameters change
    useEffect(() => {
        // Generate array based on current settings
        let array = [];

        if (customArray && customArray.length > 0) {
            // Use custom array if provided
            array = [...customArray];
        } else {
            // Generate array based on settings
            if (randomize) {
                // Generate random array with values 0-9 for counting sort
                array = Array.from({ length: arraySize }, () =>
                    Math.floor(Math.random() * 10)
                );
            } else {
                // Generate reverse sorted array
                array = Array.from({ length: arraySize }, (_, i) => arraySize - i - 1);
            }
        }

        console.log('CountingSortController - Generated array:', array);

        // IMPORTANT: Set the array in the context so visualization can use it
        setAlgorithmArray(array);

        // Generate steps
        try {
            const generatedSteps = generateCountingSortDetailedSteps(array);
            console.log('CountingSortController - Generated steps:', generatedSteps.length);

            // Set total steps
            setTotalSteps(generatedSteps.length);

            // Set steps for visualization
            setSteps(generatedSteps);

            // Reset to initial state
            setState('idle');
            setStep(0);

            // Log completion
            console.log(`CountingSortController - Total steps: ${generatedSteps.length}`);
        } catch (error) {
            console.error('CountingSortController - Error generating steps:', error);
            setSteps([]);
            setTotalSteps(0);
        }
    }, [arraySize, randomize, customArray, setAlgorithmArray, setSteps, setTotalSteps, setState, setStep]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: []
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    if (num < 0) {
                        throw new Error(`Counting Sort requires positive numbers: ${item}`);
                    }
                    if (num > 9) {
                        throw new Error(`For better visualization, please use numbers between 0-9: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // IMPORTANT: Set the array in the context directly
            console.log('CountingSortController - Setting custom array in context:', parsedArray);
            setAlgorithmArray(parsedArray);

            // Generate steps for the custom array
            const generatedSteps = generateCountingSortDetailedSteps(parsedArray);
            console.log('CountingSortController - Generated steps for custom array:', generatedSteps.length);

            // Set the steps in the context
            setSteps(generatedSteps);
            setTotalSteps(generatedSteps.length);

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box>

            {/* Information Section */}
            <InformationSection title={"Counting Sort"}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Counting Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Counting Sort is a non-comparative sorting algorithm that works well when the range of input values is not significantly larger than the number of elements to be sorted. It counts the occurrences of each element and uses this information to place elements in their correct positions.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n+k) where n is the number of elements and k is the range of input
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n+k)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n+k)
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(n+k) where n is the number of elements and k is the range of input
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated numbers between 0-9 (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.movement || ''
                }))}
                currentStep={step}
                defaultExpanded
                renderStep={(_, index) => {
                    const currentStep = steps && steps[index];
                    const isCurrentStep = index === step - 1;

                    if (!currentStep) return null;

                    return (
                        <Typography
                            variant="body2"
                            component="div"
                            sx={{
                                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                                fontSize: '0.85rem',
                                fontWeight: isCurrentStep ? 'bold' : 'normal',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                mb: 0.75,
                                pb: 0.75,
                                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                    bgcolor: 'action.hover',
                                    borderRadius: '4px',
                                }
                            }}
                        >
                            <Box
                                component="span"
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: '24px',
                                    height: '24px',
                                    borderRadius: '12px',
                                    bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                                    color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                                    mr: 1.5,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold',
                                    flexShrink: 0,
                                    boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {index + 1}
                            </Box>
                            {currentStep.movement}
                        </Typography>
                    );
                }}
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Counting Sort Algorithm"
                defaultExpanded
                currentStep={step > 0 && steps && steps.length > 0 ?
                    // Map the current step to the corresponding line number
                    steps[step - 1]?.type === 'init' ? 0 :
                        steps[step - 1]?.type === 'findMax' ? 1 :
                            steps[step - 1]?.type === 'initCount' ? 2 :
                                steps[step - 1]?.type === 'counting' || steps[step - 1]?.type === 'incrementCount' ? 3 :
                                    steps[step - 1]?.type === 'calculatePosition' || steps[step - 1]?.type === 'updatePosition' ? 4 :
                                        steps[step - 1]?.type === 'initOutput' ? 5 :
                                            steps[step - 1]?.type === 'placing' || steps[step - 1]?.type === 'placed' ? 6 :
                                                steps[step - 1]?.type === 'copying' || steps[step - 1]?.type === 'copied' ? 7 :
                                                    steps[step - 1]?.type === 'complete' ? 8 : 0
                    : 0
                }
                algorithm={[
                    { code: "function countingSort(arr):", lineNumber: 0, indent: 0 },
                    { code: "max = findMaximum(arr)", lineNumber: 1, indent: 1 },
                    { code: "count = new Array(max + 1).fill(0)", lineNumber: 2, indent: 1 },
                    { code: "for i = 0 to length(arr) - 1:", lineNumber: 3, indent: 1 },
                    { code: "    count[arr[i]]++", lineNumber: 4, indent: 2 },
                    { code: "for i = 1 to max:", lineNumber: 5, indent: 1 },
                    { code: "    count[i] += count[i - 1]", lineNumber: 6, indent: 2 },
                    { code: "output = new Array(length(arr))", lineNumber: 7, indent: 1 },
                    { code: "for i = length(arr) - 1 to 0:", lineNumber: 8, indent: 1 },
                    { code: "    output[count[arr[i]] - 1] = arr[i]", lineNumber: 9, indent: 2 },
                    { code: "    count[arr[i]]--", lineNumber: 10, indent: 2 },
                    { code: "for i = 0 to length(arr) - 1:", lineNumber: 11, indent: 1 },
                    { code: "    arr[i] = output[i]", lineNumber: 12, indent: 2 },
                    { code: "return arr", lineNumber: 13, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default CountingSortController;
