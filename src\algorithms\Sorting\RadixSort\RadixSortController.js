// RadixSortController.js
// Controller component for RadixSort algorithm following the new architecture

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import step generation
import { generateRadixSortDetailedSteps } from './RadixSortDetailedSteps';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const RadixSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        setTotalSteps,
        steps,
        setSteps,
        setAlgorithmArray
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Generate array and steps when parameters change
    useEffect(() => {
        let array;

        if (customArray && customArray.length > 0) {
            // Use custom array
            array = [...customArray];
        } else if (randomize) {
            // Generate random array
            array = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 999) + 1
            );
        } else {
            // Generate reverse sorted array (worst case for radix sort)
            array = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        // // console.log('RadixSortController - Generated array:', array);

        // IMPORTANT: Set the array in the context so visualization can use it
        setAlgorithmArray(array);

        // Generate steps
        try {
            const result = generateRadixSortDetailedSteps(array);
            const generatedSteps = result.steps;
            // // console.log('RadixSortController - Generated steps:', generatedSteps.length);

            if (!generatedSteps || generatedSteps.length === 0) {
                console.error('RadixSortController - No steps generated!');
                return;
            }

            // Set total steps
            setTotalSteps(result.totalSteps);

            // Set steps for visualization
            setSteps(generatedSteps);
        } catch (error) {
            console.error('RadixSortController - Error generating steps:', error);
            return;
        }

        // Reset to initial state
        setState('idle');
        setStep(0);

        // Log completion
        // // console.log(`RadixSortController - Total steps: ${totalSteps}`);
    }, [arraySize, randomize, customArray, setAlgorithmArray, setSteps, setTotalSteps, setState, setStep]);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: []
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    if (num < 0) {
                        throw new Error(`Radix Sort requires positive numbers: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // IMPORTANT: Set the array in the context directly
            // // console.log('RadixSortController - Setting custom array in context:', parsedArray);
            setAlgorithmArray(parsedArray);

            // Generate steps for the custom array
            const result = generateRadixSortDetailedSteps(parsedArray);
            const generatedSteps = result.steps;
            // console.log('RadixSortController - Generated steps for custom array:', generatedSteps.length);

            // Set the steps in the context
            setSteps(generatedSteps);
            setTotalSteps(result.totalSteps);

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title={"Radix Sort"}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Radix Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Radix Sort is a non-comparative sorting algorithm that sorts integers by processing individual digits. It sorts numbers digit by digit, starting from the least significant digit (rightmost) to the most significant digit (leftmost).
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n·k) where n is the number of elements and k is the number of digits
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n·k)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n·k)
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(n+k) where n is the number of elements and k is the range of input (typically 10 for decimal digits)
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated positive numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={step > 0 ? (steps || []).slice(1).map(step => ({
                    description: step.statement || step.movement || ''
                })) : []}
                currentStep={step > 0 ? step - 1 : 0}
                defaultExpanded
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Radix Sort Algorithm"
                defaultExpanded
                currentStep={step > 0 && steps && steps.length > 0 ?
                    // Map the current step to the corresponding line number
                    steps[step - 1]?.type === 'initial' ? 0 :
                        steps[step - 1]?.type === 'find_max' ? 1 :
                            steps[step - 1]?.type === 'digit_start' ? 2 :
                                steps[step - 1]?.type === 'process_element' || steps[step - 1]?.type === 'extract_digit' || steps[step - 1]?.type === 'place_in_bucket' ? 3 :
                                    steps[step - 1]?.type === 'buckets_filled' ? 4 :
                                        steps[step - 1]?.type === 'collect_bucket_start' || steps[step - 1]?.type === 'place_in_array' || steps[step - 1]?.type === 'collect_bucket_end' ? 5 :
                                            steps[step - 1]?.type === 'digit_complete' ? 6 :
                                                steps[step - 1]?.type === 'complete' ? 7 : 0
                    : 0
                }
                algorithm={[
                    { code: "function radixSort(arr):", lineNumber: 0, indent: 0 },
                    { code: "max = findMaximum(arr)", lineNumber: 1, indent: 1 },
                    { code: "for digit = 0 to numberOfDigits(max):", lineNumber: 2, indent: 1 },
                    { code: "place each number in bucket according to its digit", lineNumber: 3, indent: 2 },
                    { code: "when all numbers are in buckets", lineNumber: 4, indent: 2 },
                    { code: "collect numbers from buckets back into array", lineNumber: 5, indent: 2 },
                    { code: "end digit loop", lineNumber: 6, indent: 1 },
                    { code: "return arr", lineNumber: 7, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default RadixSortController;
