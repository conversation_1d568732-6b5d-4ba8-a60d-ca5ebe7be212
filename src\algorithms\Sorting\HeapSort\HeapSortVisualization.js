// HeapSortVisualization.js - Clean HeapSort visualization following QuickSort pattern
// Uses HeapSortSimulation component and controller-driven architecture

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import HeapSortSimulation from '../../../components/visualization/bars/HeapSortSimulation';
import getAlgorithmColors from '../../../utils/algorithmColors';
import * as THREE from 'three';
import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './HeapSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

// Extract constants from comprehensive configuration
const BAR_WIDTH = CONFIG.mainArray.bars.width;
const BAR_SPACING = CONFIG.mainArray.bars.spacing;

// Main HeapSort Visualization Component
const HeapSortVisualization = (props) => {
  const {
    state,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    theme,
    steps,
    array
  } = props;

  // Get speed from context
  const { speed } = useSpeed();

  // Local state to track current step data
  const [currentStep, setCurrentStep] = useState(null);

  // Use array from props as input data
  const inputData = array;

  // Get theme-aware colors with algorithm-specific overrides
  const colors = useMemo(() => getAlgorithmColors(theme, 'heapSort'), [theme]);

  // Generate color legend items specific to heap sort
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Default' },
    { color: colors.comparing, label: 'Comparing Elements' },
    { color: colors.swapping, label: 'Swapping Elements' },
    { color: colors.heapified, label: 'Heapified Elements' },
    { color: colors.sorted, label: 'Sorted Elements' }
  ], [colors]);

  // Get camera from Three.js context
  const { camera } = useThree();

  // Store speed in a ref for animation frame access
  const speedRef = useRef(speed);
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // State for visualization
  const [arrayData, setArrayData] = useState([]);

  // Simple animation state management
  const [displayStep, setDisplayStep] = useState(null);       // What the visualization currently shows

  // Swap animation state
  const [swapAnimation, setSwapAnimation] = useState({
    active: false,
    indices: [-1, -1],
    progress: 0,
    startTime: 0
  });

  // Refs for animation control
  const timeoutIdRef = useRef(null);
  const isAnimatingRef = useRef(false);
  const animatingRef = useRef(false); // For swap animations
  const initialArrayRef = useRef([]);
  const groupRef = useRef(null);

  // Store the camera initialization state
  const cameraInitializedRef = useRef(false);

  // Set camera position dynamically based on array size and total width
  useEffect(() => {
    if (!camera || !arrayData.length) return;

    if (cameraInitializedRef.current) {
      return;
    }

    const arraySize = arrayData.length;
    const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.025));
    const currentBarWidth = BAR_WIDTH * scaleFactor;
    const currentBarSpacing = BAR_SPACING * scaleFactor;
    const totalWidth = (arraySize * (currentBarWidth + currentBarSpacing)) - currentBarSpacing;

    const fov = camera.fov * (Math.PI / 180);
    const padding = 2;
    const minDistance = (totalWidth / 2) / Math.tan(fov / 2) * padding;

    if (!cameraInitializedRef.current) {
      const cameraDistance = CONFIG.camera.dynamicPositioning.enabled
        ? Math.max(CONFIG.camera.dynamicPositioning.minDistance, minDistance)
        : CONFIG.camera.position[2];

      camera.position.set(
        CONFIG.camera.position[0],
        CONFIG.camera.position[1] + CONFIG.camera.dynamicPositioning.heightOffset,
        cameraDistance
      );
      camera.lookAt(new THREE.Vector3(...CONFIG.camera.lookAt));
      camera.updateProjectionMatrix();
      cameraInitializedRef.current = true;
    }
  }, [camera, arrayData]);

  // Add an effect to handle camera controls
  useEffect(() => {
    if (!camera) return;

    const handleCameraChange = () => {
      cameraInitializedRef.current = true;
    };

    const controls = camera.userData.controls;
    if (controls) {
      controls.addEventListener('change', handleCameraChange);
    }

    return () => {
      if (controls) {
        controls.removeEventListener('change', handleCameraChange);
      }
    };
  }, [camera]);

  // Initialize array data from inputData
  useEffect(() => {
    if (!inputData || inputData.length === 0 || isAnimatingRef.current) {
      return;
    }

    const newArray = [...inputData];
    console.log('HeapSortVisualization - Using input data:', newArray);

    setArrayData(newArray);
    initialArrayRef.current = [...newArray];
  }, [inputData]);

  // Simple step handling using previous and current step comparison
  useEffect(() => {
    console.log('HeapSortVisualization - Step changed to:', step);
    console.log('HeapSortVisualization - Steps array length:', steps?.length);
    console.log('HeapSortVisualization - Current state:', state);

    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('HeapSortVisualization - No steps available or invalid step index');
      return;
    }

    const currentStepData = steps[step];
    const previousStepData = step > 0 ? steps[step - 1] : null;

    // For step 0, create a special "initial" step type
    if (step === 0) {
      const initialStep = {
        ...steps[step],
        type: 'initial',
        initialArray: true,
        message: 'Heap Sort: Initial Array'
      };
      setDisplayStep(initialStep);
      setCurrentStep(initialStep);
      return;
    }

    // Check if we need animation: previous step was 'swap' and current step is 'swapped'
    const needsAnimation = previousStepData?.type === 'swap' &&
                          currentStepData?.type === 'swapped' &&
                          CONFIG.animation.types.swap.enabled;

    if (needsAnimation) {
      const swappingIndices = previousStepData.visualizationData?.mainArray?.swappingIndices;

      if (swappingIndices && swappingIndices.length === 2) {
        console.log('=== SWAP ANIMATION STARTING ===');
        console.log('Previous step (swap announcement):', {
          type: previousStepData.type,
          statement: previousStepData.statement,
          values: previousStepData.visualizationData?.mainArray?.values,
          swappingIndices: previousStepData.visualizationData?.mainArray?.swappingIndices
        });
        console.log('Current step (swap result):', {
          type: currentStepData.type,
          statement: currentStepData.statement,
          values: currentStepData.visualizationData?.mainArray?.values,
          swappingIndices: currentStepData.visualizationData?.mainArray?.swappingIndices
        });
        console.log('Animation will swap indices:', swappingIndices);

        // Keep showing the previous step (with original array) during animation
        setDisplayStep(previousStepData);
        console.log('Set displayStep to previous step (original array)');

        // Start animation
        animatingRef.current = true;
        setSwapAnimation({
          active: true,
          indices: swappingIndices,
          progress: 0,
          startTime: performance.now()
        });

        // End animation after duration and show current step
        const animationDuration = CONFIG.animation.types.swap.duration;
        setTimeout(() => {
          console.log('=== SWAP ANIMATION COMPLETED ===');
          console.log('Applying final step (swapped array):', {
            type: currentStepData.type,
            values: currentStepData.visualizationData?.mainArray?.values
          });

          // Now show the current step (with swapped array)
          setDisplayStep(currentStepData);
          setCurrentStep(currentStepData);

          setSwapAnimation(prev => ({
            ...prev,
            active: false
          }));
          animatingRef.current = false;
          console.log('HeapSortVisualization - Swap animation completed, showing swapped result');
        }, animationDuration);

        // Exit early - don't update display step until animation completes
        return;
      }
    }

    // Direct update (no animation needed)
    setDisplayStep(currentStepData);
    setCurrentStep(currentStepData);
  }, [steps, step]);

  // Function to safely clear any existing timeout
  const clearExistingTimeout = useCallback(() => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }
  }, []);

  // Calculate adaptive dimensions based on array size using config
  const { scaleFactor, adaptiveSpacing } = useMemo(() => {
    const arraySize = arrayData.length;

    // Determine scale factor based on config breakpoints
    let factor;
    if (arraySize <= CONFIG.responsive.breakpoints.arraySize.small) {
      factor = CONFIG.responsive.scaleFactors.small;
    } else if (arraySize <= CONFIG.responsive.breakpoints.arraySize.medium) {
      factor = CONFIG.responsive.scaleFactors.medium;
    } else {
      factor = CONFIG.responsive.scaleFactors.large;
    }

    // Calculate adaptive spacing for stage dimensions
    const spacing = CONFIG.responsive.adaptive.bars.enabled
      ? Math.max(CONFIG.responsive.adaptive.bars.minSpacing, BAR_SPACING * factor)
      : BAR_SPACING * factor;

    return {
      scaleFactor: factor,
      adaptiveSpacing: spacing
    };
  }, [arrayData.length]);

  // State to track the actual total width from bars for proper platform sizing
  const [actualTotalWidth, setActualTotalWidth] = useState(0);

  // Calculate stage dimensions using actual bar width from simulation
  const stageDimensions = useMemo(() => {
    // Use the initial array length for fallback calculation
    const arrayLength = arrayData.length || 5;

    // Calculate fallback width if actualTotalWidth is not available yet
    const adaptiveBarWidth = CONFIG.mainArray.bars.width * scaleFactor;
    const fallbackTotalWidth = (arrayLength * (adaptiveBarWidth + adaptiveSpacing)) - adaptiveSpacing;

    // Use actual width from bars if available, otherwise use fallback
    const totalBarsWidth = actualTotalWidth > 0 ? actualTotalWidth : fallbackTotalWidth;

    return {
      width: Math.max(
        totalBarsWidth + CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right,
        CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right + 4 // Minimum width
      ),
      height: CONFIG.basePlatform.dimensions.height,
      depth: CONFIG.basePlatform.dimensions.depth
    };
  }, [arrayData.length, adaptiveSpacing, scaleFactor, actualTotalWidth]);

  // Enhanced auto-advance functionality
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      console.log('Auto-advance stopped - state is not running:', state);
      return;
    }

    // Stop if we've reached the end
    if (step >= totalSteps) {
      console.log('Auto-advance stopped - reached end of steps:', step, '>=', totalSteps);
      return;
    }

    // Don't advance if we're currently animating
    if (animatingRef.current) {
      console.log('Auto-advance stopped - currently animating');
      return;
    }

    console.log('Auto-advance conditions met - should advance to next step soon');

    // Calculate delay based on speed using enhanced delay function
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    console.log(`Scheduling next step with enhanced speed-based delay: ${speedBasedDelay}ms (speed: ${speedRef.current})`);

    const timeoutId = setTimeout(() => {
      console.log('Auto-advancing to next step with enhanced speed-based timing');
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef, steps]);

  // Animation frame for swap animation and levitation
  useFrame(({ clock }) => {
    // Update swap animation progress
    if (swapAnimation.active) {
      const now = performance.now();
      const elapsed = now - swapAnimation.startTime;
      const duration = CONFIG.animation.types.swap.duration;
      const progress = Math.min(elapsed / duration, 1);

      // Only update if there's a meaningful change to reduce unnecessary renders
      if (Math.abs(progress - swapAnimation.progress) > 0.01) {
        setSwapAnimation(prev => ({
          ...prev,
          progress
        }));
      }
    }

    // Levitation animation - stay at origin, let child components handle their own positioning
    if (CONFIG.visual.levitation.enabled &&
      (!CONFIG.visual.levitation.disableDuringSimulation || state === 'idle') &&
      groupRef.current) {

      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.visual.levitation;

      // Apply levitation relative to base platform position from config
      const basePosition = CONFIG.basePlatform.position;

      if (levitationConfig.movement.y.enabled) {
        groupRef.current.position.y = basePosition[1] + Math.sin(time * levitationConfig.movement.y.frequency) * levitationConfig.movement.y.amplitude;
      } else {
        groupRef.current.position.y = basePosition[1];
      }

      if (levitationConfig.movement.x.enabled) {
        groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.movement.x.frequency) * levitationConfig.movement.x.amplitude;
      } else {
        groupRef.current.position.x = basePosition[0];
      }

      if (levitationConfig.movement.z.enabled) {
        groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.movement.z.frequency) * levitationConfig.movement.z.amplitude;
      } else {
        groupRef.current.position.z = basePosition[2];
      }

      // Apply rotation effects
      if (levitationConfig.rotation.enabled) {
        if (levitationConfig.rotation.x.enabled) {
          groupRef.current.rotation.x = Math.cos(time * levitationConfig.rotation.x.frequency) * levitationConfig.rotation.x.amplitude;
        }

        if (levitationConfig.rotation.y.enabled) {
          groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
        }

        if (levitationConfig.rotation.z.enabled) {
          groupRef.current.rotation.z = Math.sin(time * levitationConfig.rotation.z.frequency) * levitationConfig.rotation.z.amplitude;
        }
      }
    } else if (groupRef.current) {
      // When levitation is disabled, use base platform position from config
      const basePosition = CONFIG.basePlatform.position;
      groupRef.current.position.set(basePosition[0], basePosition[1], basePosition[2]);
      groupRef.current.rotation.set(0, 0, 0);
    }
  });

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      clearExistingTimeout();
    };
  }, [clearExistingTimeout]);

  return (
    <>
      {/* Step board */}
      {CONFIG.stepBoard.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''} // Only show step number for steps > 0
          totalSteps={totalSteps > 0 ? totalSteps - 1 : 0} // Subtract 1 to exclude initial state from total
          description={currentStep?.statement || currentStep?.message || 'Heap Sort Algorithm'}
          stepData={currentStep}
          showStepNumber={step > 0} // Don't show step number for initial state
          config={{
            stepNumberFormat: 'Step {current}: ' // Only show current step number, not total
          }}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {/* Color legend */}
      {CONFIG.colorLegend.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      {/* Main Array Group - Position controlled by levitation animation in useFrame */}
      <group ref={groupRef}>
        {/* Base platform - positioned at origin since group handles basePlatform.position */}
        <SortingBase
          width={stageDimensions.width}
          height={stageDimensions.height}
          depth={stageDimensions.depth}
          color={colors.base}
          position={[0, -stageDimensions.height / 2, 0]}
        />

        {/* Bars and labels - positioned relative to the base platform using config offset */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <HeapSortSimulation
            currentStep={displayStep || currentStep}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width * scaleFactor}
            barSpacing={adaptiveSpacing}
            showValues={(() => {
              const shouldShow = CONFIG.visual.labels.values.adaptiveVisibility
                ? (scaleFactor > CONFIG.visual.labels.values.visibilityThreshold)
                : CONFIG.visual.labels.values.enabled;
              return shouldShow;
            })()}
            showIndices={CONFIG.visual.labels.indices.adaptiveVisibility
              ? (scaleFactor > CONFIG.visual.labels.indices.visibilityThreshold)
              : CONFIG.visual.labels.indices.enabled}
            config={CONFIG}
            state={state}
            swapAnimation={swapAnimation}
            onWidthChange={setActualTotalWidth}
          />
        </group>
      </group>
    </>
  );
};

export default HeapSortVisualization;
