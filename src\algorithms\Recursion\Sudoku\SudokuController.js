// SudokuController.js
// This component provides the controls for the Sudoku solver algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, TextField, Tooltip, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GridOnIcon from '@mui/icons-material/GridOn';
import RefreshIcon from '@mui/icons-material/Refresh';
import SettingsIcon from '@mui/icons-material/Settings';

// Import algorithm functions
import SudokuAlgorithm, { generateSudokuSteps, getBoardByDifficulty } from './SudokuAlgorithm';

const SudokuController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [difficulty, setDifficulty] = useState(params?.difficulty || 'medium');
  const [customBoard, setCustomBoard] = useState(false);
  const [editableBoard, setEditableBoard] = useState(
    params?.board || getBoardByDifficulty(difficulty)
  );

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { difficulty, customBoard, editableBoard });

    // Update params first
    onParamChange({
      difficulty,
      board: customBoard ? editableBoard : getBoardByDifficulty(difficulty)
    });

    // Set steps and movements directly
    try {
      const result = generateSudokuSteps({
        board: customBoard ? editableBoard : getBoardByDifficulty(difficulty),
        difficulty
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [difficulty, customBoard, editableBoard, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update board when difficulty changes
  useEffect(() => {
    if (!customBoard) {
      const newBoard = getBoardByDifficulty(difficulty);
      setEditableBoard(newBoard.map(row => [...row]));
    }
  }, [difficulty, customBoard]);

  // Handle difficulty change
  const handleDifficultyChange = useCallback((value) => {
    setDifficulty(value);
    if (!customBoard) {
      resetAndGenerateSteps();
    }
  }, [customBoard, resetAndGenerateSteps]);

  // Handle custom board toggle
  const handleCustomBoardToggle = useCallback((value) => {
    setCustomBoard(value === 'custom');
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle cell value change
  const handleCellChange = useCallback((rowIndex, colIndex, value) => {
    const newValue = value === '' ? 0 : parseInt(value, 10);
    if (isNaN(newValue) || newValue < 0 || newValue > 9) return;

    const newBoard = editableBoard.map(row => [...row]);
    newBoard[rowIndex][colIndex] = newValue;
    setEditableBoard(newBoard);

    if (customBoard) {
      // Only regenerate steps if we're using a custom board
      resetAndGenerateSteps();
    }
  }, [editableBoard, customBoard, resetAndGenerateSteps]);

  // Handle randomize board
  const handleRandomizeBoard = useCallback(() => {
    const newBoard = getBoardByDifficulty(difficulty);
    setEditableBoard(newBoard.map(row => [...row]));
    resetAndGenerateSteps();
  }, [difficulty, resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Sudoku solver algorithm
  const pseudocode = [
    { code: "function solveSudoku(board, row, col):", lineNumber: 1, indent: 0 },
    { code: "    // If we've gone through all rows, we've found a solution", lineNumber: 2, indent: 0 },
    { code: "    if row == 9 return true", lineNumber: 3, indent: 1 },
    { code: "    // If we've gone through all columns in this row, move to the next row", lineNumber: 4, indent: 0 },
    { code: "    if col == 9 return solveSudoku(board, row + 1, 0)", lineNumber: 5, indent: 1 },
    { code: "    // If this cell is already filled, move to the next cell", lineNumber: 6, indent: 0 },
    { code: "    if board[row][col] != 0 return solveSudoku(board, row, col + 1)", lineNumber: 7, indent: 1 },
    { code: "    // Try each number from 1 to 9", lineNumber: 8, indent: 0 },
    { code: "    for num = 1 to 9:", lineNumber: 9, indent: 1 },
    { code: "        // Check if it's valid to place this number", lineNumber: 10, indent: 0 },
    { code: "        if isValid(board, row, col, num):", lineNumber: 11, indent: 2 },
    { code: "            // Place the number", lineNumber: 12, indent: 0 },
    { code: "            board[row][col] = num", lineNumber: 13, indent: 3 },
    { code: "            // Recursively solve the rest of the puzzle", lineNumber: 14, indent: 0 },
    { code: "            if solveSudoku(board, row, col + 1) return true", lineNumber: 15, indent: 3 },
    { code: "            // If we get here, this number didn't work, so backtrack", lineNumber: 16, indent: 0 },
    { code: "            board[row][col] = 0", lineNumber: 17, indent: 3 },
    { code: "    // If we've tried all numbers and none worked, return false", lineNumber: 18, indent: 0 },
    { code: "    return false", lineNumber: 19, indent: 1 },
    { code: "", lineNumber: 20, indent: 0 },
    { code: "function isValid(board, row, col, num):", lineNumber: 21, indent: 0 },
    { code: "    // Check row", lineNumber: 22, indent: 0 },
    { code: "    for x = 0 to 8:", lineNumber: 23, indent: 1 },
    { code: "        if board[row][x] == num return false", lineNumber: 24, indent: 2 },
    { code: "    // Check column", lineNumber: 25, indent: 0 },
    { code: "    for x = 0 to 8:", lineNumber: 26, indent: 1 },
    { code: "        if board[x][col] == num return false", lineNumber: 27, indent: 2 },
    { code: "    // Check 3x3 box", lineNumber: 28, indent: 0 },
    { code: "    boxRow = Math.floor(row / 3) * 3", lineNumber: 29, indent: 1 },
    { code: "    boxCol = Math.floor(col / 3) * 3", lineNumber: 30, indent: 1 },
    { code: "    for i = 0 to 2:", lineNumber: 31, indent: 1 },
    { code: "        for j = 0 to 2:", lineNumber: 32, indent: 2 },
    { code: "            if board[boxRow + i][boxCol + j] == num return false", lineNumber: 33, indent: 3 },
    { code: "    return true", lineNumber: 34, indent: 1 },
  ];

  // Map pseudocodeLine values from SudokuAlgorithm.js to our pseudocode array
  const getPseudocodeLine = (step) => {
    if (!step || !steps || step <= 0 || step > steps.length) return 0;

    const pseudocodeLine = steps[step - 1]?.pseudocodeLine || 0;

    // Map the pseudocodeLine values from SudokuAlgorithm.js to our pseudocode array
    switch (pseudocodeLine) {
      case 1: return 1;  // Init
      case 3: return 3;  // Found solution
      case 5: return 7;  // Skip filled cell
      case 7: return 11; // Invalid placement
      case 9: return 13; // Place number
      case 14: return 17; // Backtrack
      default: return 0;
    }
  };

  // Calculate current line based on step
  const currentLine = getPseudocodeLine(step);

  // Custom component for difficulty selection
  const DifficultySelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Difficulty</Typography>
        <RadioGroup
          row
          value={value}
          onChange={(e) => onChange(e.target.value)}
          sx={{ ml: 1 }}
          disabled={disabled || customBoard}
        >
          <FormControlLabel
            value="easy"
            control={<Radio size="small" />}
            label="Easy"
            disabled={disabled || customBoard}
          />
          <FormControlLabel
            value="medium"
            control={<Radio size="small" />}
            label="Medium"
            disabled={disabled || customBoard}
          />
          <FormControlLabel
            value="hard"
            control={<Radio size="small" />}
            label="Hard"
            disabled={disabled || customBoard}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for board type selection
  const BoardTypeSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Board Type</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <RadioGroup
            row
            value={value === true ? 'custom' : 'predefined'}
            onChange={(e) => onChange(e.target.value)}
            sx={{ ml: 1 }}
          >
            <FormControlLabel
              value="predefined"
              control={<Radio size="small" />}
              label="Predefined"
              disabled={disabled}
            />
            <FormControlLabel
              value="custom"
              control={<Radio size="small" />}
              label="Custom"
              disabled={disabled}
            />
          </RadioGroup>
          {(disabled || value === true) ? (
            <IconButton
              disabled={true}
              size="small"
              sx={{ ml: 1 }}
            >
              <RefreshIcon />
            </IconButton>
          ) : (
            <Tooltip title="Randomize Board">
              <IconButton
                onClick={handleRandomizeBoard}
                size="small"
                sx={{ ml: 1 }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>
    );
  };

  // Custom component for board input
  const BoardInput = ({ board, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {board.map((row, rowIndex) => (
            <Box key={`row-${rowIndex}`} sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {row.map((cell, colIndex) => (
                  <Box key={`cell-${rowIndex}-${colIndex}`} sx={{ flex: 1 }}>
                    <TextField
                      size="small"
                      value={cell === 0 ? '' : cell}
                      onChange={(e) => onChange(rowIndex, colIndex, e.target.value)}
                      disabled={disabled}
                      inputProps={{
                        min: 0,
                        max: 9,
                        style: { textAlign: 'center', padding: '4px' }
                      }}
                      sx={{
                        width: '100%',
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: (rowIndex % 3 === 0 && colIndex % 3 === 0) ? 'primary.main' : 'divider',
                            borderWidth: (Math.floor(rowIndex / 3) * 3 + Math.floor(colIndex / 3)) % 2 === 0 ? 1 : 1,
                          },
                        },
                        backgroundColor: (Math.floor(rowIndex / 3) * 3 + Math.floor(colIndex / 3)) % 2 === 0 ? 'rgba(0, 0, 0, 0.03)' : 'transparent',
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Sudoku Solver"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Sudoku solver uses a backtracking algorithm to find a solution to a given Sudoku puzzle.
            Backtracking is a recursive algorithm that tries different values for each empty cell and
            backtracks when it finds that a value doesn't lead to a solution.
          </Typography>
          <Typography variant="body2" gutterBottom>
            The algorithm follows these steps:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2">Find an empty cell in the Sudoku grid.</Typography>
            </li>
            <li>
              <Typography variant="body2">Try placing digits 1-9 in this cell.</Typography>
            </li>
            <li>
              <Typography variant="body2">Check if the digit is valid in the current position according to Sudoku rules.</Typography>
            </li>
            <li>
              <Typography variant="body2">If valid, recursively try to fill the rest of the grid.</Typography>
            </li>
            <li>
              <Typography variant="body2">If the recursive call is successful, we've found a solution.</Typography>
            </li>
            <li>
              <Typography variant="body2">If not, we undo the choice (backtrack) and try another digit.</Typography>
            </li>
            <li>
              <Typography variant="body2">If no digit works, we return false to trigger backtracking.</Typography>
            </li>
          </ol>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'difficulty',
            type: 'component',
            label: 'Difficulty',
            component: DifficultySelector,
            componentProps: {
              value: difficulty,
              onChange: handleDifficultyChange,
              disabled: state !== 'idle' || customBoard
            },
            icon: SettingsIcon
          },
          {
            name: 'boardType',
            type: 'component',
            label: 'Board Type',
            component: BoardTypeSelector,
            componentProps: {
              value: customBoard,
              onChange: handleCustomBoardToggle,
              disabled: state !== 'idle'
            },
            icon: GridOnIcon
          },
          {
            name: 'board',
            type: 'component',
            label: 'Board',
            component: BoardInput,
            componentProps: {
              board: editableBoard,
              onChange: handleCellChange,
              disabled: state !== 'idle'
            }
          }
        ]}
        values={{
          difficulty,
          boardType: customBoard,
          board: editableBoard
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Sudoku Solver Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default SudokuController;
