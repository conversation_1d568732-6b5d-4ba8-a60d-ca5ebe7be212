import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

/**
 * Reusable color legend component for algorithm visualizations using canvas textures
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the legend
 * @param {Array} props.items - Array of legend items to display
 * @returns {JSX.Element} - The rendered color legend component
 */
const ColorLegendCanvas = ({
    position = [0, 0, 0],
    items = [
        { color: '#ff0000', label: 'Default' },
        { color: '#00ff00', label: 'Current' },
        { color: '#0000ff', label: 'Sorted' }
    ],
    animate = true
}) => {
    const theme = useTheme();
    const isDark = theme.palette.mode === 'dark';
    const groupRef = useRef();

    // Text color based on theme
    const textColor = isDark ? '#ffffff' : '#000000';

    // Animation
    useFrame(() => {
        if (animate && groupRef.current) {
            // Subtle floating animation
            groupRef.current.position.y = position[1] + Math.sin(Date.now() * 0.001) * 0.05;
        }
    });

    // Calculate the total width needed for all items
    const itemWidth = 1.8; // Reduced width per item
    const totalWidth = items.length * itemWidth;

    return (
        <group ref={groupRef} position={position}>
            {/* Background plane */}
            <mesh position={[0, 0, -0.05]}>
                <planeGeometry args={[totalWidth + 0.5, 0.8]} /> {/* Reduced height */}
                <meshStandardMaterial
                    color={isDark ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.5)'}
                    transparent
                    opacity={0.7}
                    roughness={0.7}
                />
            </mesh>

            {/* Legend items */}
            {items.map((item, index) => {
                const xPos = -totalWidth / 2 + itemWidth / 2 + index * itemWidth;
                return (
                    <group key={index} position={[xPos, 0, 0]}>
                        {/* Color box */}
                        <mesh position={[-0.5, 0, 0]}>
                            <boxGeometry args={[0.4, 0.4, 0.1]} /> {/* Smaller box */}
                            <meshStandardMaterial color={item.color} />
                        </mesh>

                        {/* Label */}
                        <mesh position={[0.3, 0, 0]}>
                            <planeGeometry args={[1.2, 0.4]} /> {/* Smaller plane */}
                            <meshStandardMaterial
                                color={textColor}
                                transparent
                                opacity={0.9}
                            >
                                <canvasTexture
                                    attach="map"
                                    image={(() => {
                                        const canvas = document.createElement('canvas');
                                        canvas.width = 256;
                                        canvas.height = 64;
                                        const context = canvas.getContext('2d');
                                        context.fillStyle = textColor;
                                        context.font = '16px Roboto'; {/* Smaller font */}
                                        context.textAlign = 'left';
                                        context.textBaseline = 'middle';
                                        context.fillText(item.label, 10, canvas.height / 2);
                                        return canvas;
                                    })()}
                                />
                            </meshStandardMaterial>
                        </mesh>
                    </group>
                );
            })}
        </group>
    );
};

export default ColorLegendCanvas;
