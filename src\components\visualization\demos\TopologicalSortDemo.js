// TopologicalSortDemo.js
// A demo component that shows how to use the TopologicalSortScene

import React, { useState, useEffect, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { Box, Typography, useTheme } from '@mui/material';

// Import the scene component
import TopologicalSortScene from '../scenes/TopologicalSortScene';

// Sample graph data
const sampleGraphData = {
  nodes: [
    { id: 0 },
    { id: 1 },
    { id: 2 },
    { id: 3 },
    { id: 4 },
    { id: 5 },
  ],
  edges: [
    { id: 0, source: 0, target: 1 },
    { id: 1, source: 0, target: 2 },
    { id: 2, source: 1, target: 3 },
    { id: 3, source: 2, target: 3 },
    { id: 4, source: 2, target: 4 },
    { id: 5, source: 3, target: 5 },
    { id: 6, source: 4, target: 5 },
  ],
};

// Generate node positions in a circular layout
const generateNodePositions = (nodes) => {
  const positions = {};
  const numNodes = nodes.length;
  const radius = 5;

  nodes.forEach((node, index) => {
    const angle = (index / numNodes) * Math.PI * 2;
    const x = Math.cos(angle) * radius;
    const y = Math.sin(angle) * radius;
    positions[node.id] = [x, y, 0];
  });

  return positions;
};

// Demo component
const TopologicalSortDemo = () => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  // State for the demo
  const [graphData] = useState(sampleGraphData);
  const [nodePositions, setNodePositions] = useState({});
  const [visitedNodes, setVisitedNodes] = useState([]);
  const [stackNodes, setStackNodes] = useState([]);
  const [sortedNodes, setSortedNodes] = useState([]);
  const [currentNode, setCurrentNode] = useState(null);
  const [neighborNode, setNeighborNode] = useState(null);
  const [step, setStep] = useState(0);

  // Generate node positions
  useEffect(() => {
    setNodePositions(generateNodePositions(graphData.nodes));
  }, [graphData.nodes]);

  // Simulation steps for the demo
  const simulationSteps = useMemo(() => [
    {
      message: 'Initialize topological sort algorithm.',
      visited: [],
      stack: [],
      sorted: [],
      current: null,
      neighbor: null,
    },
    {
      message: 'Start DFS from node 0.',
      visited: [0],
      stack: [],
      sorted: [],
      current: 0,
      neighbor: null,
    },
    {
      message: 'Visit neighbor node 1.',
      visited: [0, 1],
      stack: [],
      sorted: [],
      current: 1,
      neighbor: null,
    },
    {
      message: 'Visit neighbor node 3.',
      visited: [0, 1, 3],
      stack: [],
      sorted: [],
      current: 3,
      neighbor: null,
    },
    {
      message: 'No unvisited neighbors for node 3. Add to stack.',
      visited: [0, 1, 3],
      stack: [3],
      sorted: [],
      current: 1,
      neighbor: null,
    },
    {
      message: 'No more unvisited neighbors for node 1. Add to stack.',
      visited: [0, 1, 3],
      stack: [3, 1],
      sorted: [],
      current: 0,
      neighbor: null,
    },
    {
      message: 'Visit neighbor node 2.',
      visited: [0, 1, 3, 2],
      stack: [3, 1],
      sorted: [],
      current: 2,
      neighbor: null,
    },
    {
      message: 'Node 3 is already visited. Check next neighbor.',
      visited: [0, 1, 3, 2],
      stack: [3, 1],
      sorted: [],
      current: 2,
      neighbor: 3,
    },
    {
      message: 'Visit neighbor node 4.',
      visited: [0, 1, 3, 2, 4],
      stack: [3, 1],
      sorted: [],
      current: 4,
      neighbor: null,
    },
    {
      message: 'Visit neighbor node 5.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [3, 1],
      sorted: [],
      current: 5,
      neighbor: null,
    },
    {
      message: 'No unvisited neighbors for node 5. Add to stack.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [3, 1, 5],
      sorted: [],
      current: 4,
      neighbor: null,
    },
    {
      message: 'No more unvisited neighbors for node 4. Add to stack.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [3, 1, 5, 4],
      sorted: [],
      current: 2,
      neighbor: null,
    },
    {
      message: 'No more unvisited neighbors for node 2. Add to stack.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [3, 1, 5, 4, 2],
      sorted: [],
      current: 0,
      neighbor: null,
    },
    {
      message: 'No more unvisited neighbors for node 0. Add to stack.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [3, 1, 5, 4, 2, 0],
      sorted: [],
      current: null,
      neighbor: null,
    },
    {
      message: 'Reverse the stack to get the topological order.',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [],
      sorted: [0, 2, 4, 5, 1, 3],
      current: null,
      neighbor: null,
    },
    {
      message: 'Topological sort complete: [0, 2, 4, 5, 1, 3]',
      visited: [0, 1, 3, 2, 4, 5],
      stack: [],
      sorted: [0, 2, 4, 5, 1, 3],
      current: null,
      neighbor: null,
    },
  ], []);

  // Update state based on current step
  useEffect(() => {
    if (step < 0 || step >= simulationSteps.length) return;
    
    const currentStep = simulationSteps[step];
    setVisitedNodes(currentStep.visited);
    setStackNodes(currentStep.stack);
    setSortedNodes(currentStep.sorted);
    setCurrentNode(currentStep.current);
    setNeighborNode(currentStep.neighbor);
  }, [step, simulationSteps]);

  // Auto-advance steps for the demo
  useEffect(() => {
    const timer = setTimeout(() => {
      if (step < simulationSteps.length - 1) {
        setStep(step + 1);
      } else {
        setStep(0); // Reset to beginning
      }
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [step, simulationSteps.length]);

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Step description */}
      <Box sx={{ p: 2, bgcolor: 'background.paper', boxShadow: 1 }}>
        <Typography variant="h6">
          Step {step + 1} of {simulationSteps.length}
        </Typography>
        <Typography variant="body1">
          {simulationSteps[step]?.message || 'Initializing...'}
        </Typography>
      </Box>
      
      {/* 3D Canvas */}
      <Box sx={{ flexGrow: 1 }}>
        <Canvas camera={{ position: [0, 0, 15], fov: 50 }}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={0.8} />
          
          <TopologicalSortScene
            graphData={graphData}
            nodePositions={nodePositions}
            visitedNodes={visitedNodes}
            stackNodes={stackNodes}
            sortedNodes={sortedNodes}
            currentNode={currentNode}
            neighborNode={neighborNode}
            isDark={isDark}
          />
          
          <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
        </Canvas>
      </Box>
    </Box>
  );
};

export default TopologicalSortDemo;
