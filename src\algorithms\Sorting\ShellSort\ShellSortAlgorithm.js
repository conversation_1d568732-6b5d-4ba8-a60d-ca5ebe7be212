// ShellSortAlgorithm.js
// Implementation of the Shell Sort algorithm with step generation

/**
 * Generates steps for the Shell Sort algorithm
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateShellSortSteps = (arr) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    movement: 'Initialize Shell Sort'
  });

  const n = inputArray.length;
  
  // Start with a large gap, then reduce the gap
  // Using the sequence: n/2, n/4, n/8, ..., 1
  let gap = Math.floor(n / 2);
  
  // Add step to show the initial gap
  steps.push({
    type: 'setGap',
    array: [...inputArray],
    gap,
    movement: `Set initial gap to ${gap}`
  });

  // Continue until gap is 0
  while (gap > 0) {
    // Add step to show the current gap
    if (gap < Math.floor(n / 2)) {
      steps.push({
        type: 'setGap',
        array: [...inputArray],
        gap,
        movement: `Reduce gap to ${gap}`
      });
    }

    // Do a gapped insertion sort for this gap size
    for (let i = gap; i < n; i++) {
      // Add step to show the current element being considered
      steps.push({
        type: 'considerElement',
        array: [...inputArray],
        gap,
        currentIndex: i,
        currentValue: inputArray[i],
        movement: `Consider element ${inputArray[i]} at index ${i}`
      });

      // Save the current element
      const temp = inputArray[i];
      
      // Shift earlier gap-sorted elements up until the correct location for inputArray[i] is found
      let j;
      
      // Add step to start the comparison process
      steps.push({
        type: 'startComparison',
        array: [...inputArray],
        gap,
        currentIndex: i,
        currentValue: temp,
        movement: `Start comparing ${temp} with elements at gap ${gap} positions apart`
      });
      
      for (j = i; j >= gap && inputArray[j - gap] > temp; j -= gap) {
        // Add step to show the comparison
        steps.push({
          type: 'compare',
          array: [...inputArray],
          gap,
          currentIndex: i,
          compareIndex: j - gap,
          compareValue: inputArray[j - gap],
          currentValue: temp,
          movement: `Compare ${inputArray[j - gap]} > ${temp}`
        });
        
        // Shift the element
        inputArray[j] = inputArray[j - gap];
        
        // Add step to show the shift
        steps.push({
          type: 'shift',
          array: [...inputArray],
          gap,
          currentIndex: i,
          shiftFromIndex: j - gap,
          shiftToIndex: j,
          shiftValue: inputArray[j],
          movement: `Shift ${inputArray[j]} from index ${j - gap} to index ${j}`
        });
      }
      
      // Put the saved element in its correct location
      inputArray[j] = temp;
      
      // Add step to show the insertion
      steps.push({
        type: 'insert',
        array: [...inputArray],
        gap,
        currentIndex: i,
        insertIndex: j,
        insertValue: temp,
        movement: `Insert ${temp} at index ${j}`
      });
    }
    
    // Reduce the gap for the next iteration
    gap = Math.floor(gap / 2);
  }
  
  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    sorted: Array.from({ length: inputArray.length }, (_, i) => i),
    movement: 'Shell Sort complete'
  });

  return { steps, sortedArray: inputArray };
};

// Default export
const ShellSortAlgorithm = {
  generateSteps: generateShellSortSteps
};

export default ShellSortAlgorithm;
