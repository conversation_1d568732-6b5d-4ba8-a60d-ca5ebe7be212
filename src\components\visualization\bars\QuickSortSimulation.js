// QuickSortSimulation.js - Clean quick sort visualization component
// Consumes visualizationData from controller with minimal complexity

import React, { useMemo, useEffect } from 'react';
import { Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ThemeHtml from '../ThemeHtml';
import QuickSortConfig from '../../../algorithms/Sorting/QuickSort/QuickSortConfig';

const QuickSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true,
  onWidthChange
}) => {
  const theme = useTheme();

  // Get configuration for positioning
  const config = QuickSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // SIMPLIFIED STATIC POSITIONING - No dynamic gaps but proper total width calculation
  const layoutData = useMemo(() => {
    if (!vizData?.mainArray?.values) return { barPositions: [], totalWidth: 0 };

    const values = vizData.mainArray.values;
    const baseSpacing = barWidth + barSpacing;
    const visualOffset = config.mainArray.bars.visualOffset;

    // Calculate STATIC bar positions - these never change regardless of partitioning
    const staticPositions = [];
    const barsOnlyWidth = (values.length * baseSpacing) - barSpacing;
    const startX = -barsOnlyWidth / 2 + visualOffset;

    for (let i = 0; i < values.length; i++) {
      staticPositions.push({
        index: i,
        xPos: startX + i * baseSpacing  // Static position - never changes
      });
    }

    // Calculate the ACTUAL total width covered by bars (from leftmost to rightmost edge)
    const leftmostEdge = staticPositions[0].xPos - barWidth / 2;
    const rightmostEdge = staticPositions[staticPositions.length - 1].xPos + barWidth / 2;
    const actualTotalWidth = rightmostEdge - leftmostEdge;

    // DEBUG: Log the calculations
    console.log('QuickSort Static Layout:', {
      values: values.map((v, i) => `${i}:${v}`),
      barsOnlyWidth,
      actualTotalWidth,
      leftmostEdge: leftmostEdge.toFixed(2),
      rightmostEdge: rightmostEdge.toFixed(2),
      startX: startX.toFixed(2),
      positions: staticPositions.map(p => `${p.index}:${p.xPos.toFixed(1)}`)
    });

    return {
      barPositions: staticPositions,
      totalWidth: actualTotalWidth
    };
  }, [vizData?.mainArray?.values, barWidth, barSpacing]);

  // Extract values
  const { barPositions, totalWidth: actualTotalWidth } = layoutData;

  // Notify parent of width changes for platform sizing
  useEffect(() => {
    if (onWidthChange && actualTotalWidth > 0) {
      onWidthChange(actualTotalWidth);
    }
  }, [actualTotalWidth, onWidthChange]);

  // If no visualization data, return null
  if (!vizData) {
    return null;
  }

  // Render the main array bars with partitioning gaps and highlights
  const renderMainArray = () => {
    if (!vizData.mainArray?.values) return null;

    // Use current step data directly - no animation
    const {
      values,
      sortedIndices = [],
      pivotIndex = -1,
      partitionRange = null,
      leftPartitionEnd = -1,
      rightPartitionStart = -1,
      comparingIndices = [],
      swappingIndices = []
    } = vizData.mainArray;

    const maxValue = Math.max(...values);

    return values.map((value, index) => {
      const isPivot = index === pivotIndex;
      const isComparing = comparingIndices.includes(index);
      const isSwapping = swappingIndices.includes(index);
      const isSorted = sortedIndices.some(range => {
        if (typeof range === 'number') return range === index;
        return index >= range.start && index <= range.end;
      });

      // Determine if this element is in left partition, right partition, or unpartitioned
      let partitionType = 'unpartitioned';
      if (partitionRange && index >= partitionRange.start && index <= partitionRange.end) {
        if (leftPartitionEnd >= 0 && index <= leftPartitionEnd) {
          partitionType = 'left';
        } else if (rightPartitionStart >= 0 && index >= rightPartitionStart) {
          partitionType = 'right';
        } else if (index === pivotIndex) {
          partitionType = 'pivot';
        } else {
          partitionType = 'unpartitioned';
        }
      }

      const barHeight = (value / maxValue) * maxBarHeight;

      // Determine bar color based on state and partition
      let barColor;
      if (isPivot) {
        barColor = colors.pivot;
      } else if (isSwapping) {
        barColor = colors.swapping;
      } else if (isComparing) {
        barColor = colors.comparing;
      } else if (isSorted) {
        barColor = colors.sorted;
      } else if (partitionType === 'left') {
        barColor = colors.leftPartition; // Left partition color (distinct from comparing)
      } else if (partitionType === 'right') {
        barColor = colors.rightPartition; // Right partition color (distinct from merging)
      } else {
        barColor = colors.default || colors.bar;
      }

      // Use pre-calculated bar position - no animation
      const barPosition = barPositions.find(pos => pos.index === index);
      const xPos = barPosition ? barPosition.xPos : 0;
      const yPos = 0;
      const zPos = 0;

      return (
        <group key={`main-${index}-${value}`} position={[xPos, yPos, zPos]}>
          {/* Base of the bar (like MergeSort) */}
          {config.mainArray.bars.base.enabled && (
            <mesh
              position={[0, 0, 0]}
              castShadow={config.visual.effects.shadows}
              receiveShadow={config.visual.effects.shadows}
            >
              <boxGeometry args={[
                barWidth * config.mainArray.bars.base.widthScale,
                config.mainArray.bars.base.height,
                barWidth * config.mainArray.bars.base.depthScale
              ]} />
              <meshStandardMaterial
                color={barColor}
                transparent={config.mainArray.bars.base.material.transparent}
                opacity={config.mainArray.bars.base.material.opacity}
                roughness={config.mainArray.bars.base.material.roughness}
                metalness={config.mainArray.bars.base.material.metalness}
              />
            </mesh>
          )}

          {/* The bar itself (narrower, sitting on top of base) */}
          <mesh
            position={[0, barHeight / 2, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              barWidth * config.mainArray.bars.geometry.widthScale,
              barHeight,
              barWidth * config.mainArray.bars.geometry.depthScale
            ]} />
            <meshStandardMaterial
              color={barColor}
              transparent={config.mainArray.bars.material.transparent}
              opacity={config.mainArray.bars.material.opacity}
              roughness={config.mainArray.bars.material.roughness}
              metalness={config.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {(() => {
            if (index === 0) {
              console.log('QuickSortSimulation showValues:', showValues, 'for value:', value);
            }
            return showValues;
          })() && (
            <ThemeHtml position={[config.mainArray.valueLabels.offset[0], barHeight + config.mainArray.valueLabels.offset[1], config.mainArray.valueLabels.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.valueLabels.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.valueLabels.padding.horizontal),
                  py: theme.spacing(config.mainArray.valueLabels.padding.vertical),
                  minWidth: config.mainArray.valueLabels.minWidth,
                  borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                  border: 1,
                  borderColor: isPivot ? colors.pivot : 'divider',
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.valueLabels.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: isPivot ? "bold" : config.mainArray.valueLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Index label */}
          {showIndices && (
            <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.indexLabels.elevation}
                sx={{
                  width: config.mainArray.indexLabels.size.width,
                  height: config.mainArray.indexLabels.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: isPivot ? colors.pivot : 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.indexLabels.fontSize,
                    fontWeight: isPivot ? "bold" : config.mainArray.indexLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Pivot indicator */}
          {isPivot && (
            <ThemeHtml position={[config.mainArray.pivotIndicator.offset[0], barHeight + config.mainArray.pivotIndicator.offset[1], config.mainArray.pivotIndicator.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.pivotIndicator.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.pivotIndicator.padding.horizontal),
                  py: theme.spacing(config.mainArray.pivotIndicator.padding.vertical),
                  borderRadius: theme.shape.borderRadius / config.mainArray.pivotIndicator.borderRadius,
                  border: 1,
                  borderColor: colors.pivot,
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.pivotIndicator.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: config.mainArray.pivotIndicator.fontWeight
                  }}
                  color="text.primary"
                >
                  {config.mainArray.pivotIndicator.text}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };


  // Render left partition if it exists
  const renderLeftPartition = () => {
    // Use data from partitionExplanation to avoid conflicts
    if (!vizData.partitionExplanation?.leftPartition || vizData.partitionExplanation.leftPartition.length === 0) {
      return null;
    }

    const { leftPartition, pivotValue } = vizData.partitionExplanation;
    const maxValue = Math.max(...vizData.mainArray.values);

    // Calculate positions from common base position + offset
    const basePosition = config.explanatoryElements.common.basePosition;
    const offset = config.explanatoryElements.leftPartition.container.offset;
    const leftSideX = basePosition[0] + offset[0];
    const yPosition = basePosition[1] + offset[1];
    const zPosition = basePosition[2] + offset[2];
    const labelPosition = config.explanatoryElements.leftPartition.label.position;

    return (
      <group position={[leftSideX, yPosition, zPosition]}>
        {/* Label */}
        <ThemeHtml position={labelPosition} center sprite occlude theme={theme}>
          <Paper
            elevation={config.explanatoryElements.leftPartition.label.elevation}
            sx={{
              px: theme.spacing(config.explanatoryElements.leftPartition.label.padding.horizontal),
              py: theme.spacing(config.explanatoryElements.leftPartition.label.padding.vertical),
              borderRadius: theme.shape.borderRadius / config.explanatoryElements.leftPartition.label.borderRadius,
              border: 1,
              borderColor: colors.leftPartition,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant={config.explanatoryElements.leftPartition.label.fontSize}
              fontWeight={config.explanatoryElements.leftPartition.label.fontWeight}
              color="text.primary"
              align="center"
              sx={{
                whiteSpace: config.explanatoryElements.leftPartition.label.whiteSpace,
                fontSize: config.explanatoryElements.leftPartition.label.fontSizeOverride
              }}
            >
              Left Partition (≤ {pivotValue})
            </Typography>
          </Paper>
        </ThemeHtml>

        {/* Bars */}
        {leftPartition.map((value, index) => {
          const barHeight = (value / maxValue) * maxBarHeight * config.explanatoryElements.common.barScale;
          const barsOffset = config.explanatoryElements.leftPartition.bars.offset;

          // Calculate proper spacing with visual offset from config
          const partitionBarWidth = config.explanatoryElements.leftPartition.bars.width;
          const partitionBarSpacing = config.explanatoryElements.leftPartition.bars.spacing;
          const totalWidth = leftPartition.length * (partitionBarWidth + partitionBarSpacing) - partitionBarSpacing;
          const visualOffset = config.explanatoryElements.leftPartition.bars.visualOffset;
          const startX = -totalWidth / 2 + visualOffset;

          return (
            <group key={`left-${index}`} position={[
              barsOffset[0] + startX + index * (partitionBarWidth + partitionBarSpacing),
              barsOffset[1],
              barsOffset[2]
            ]}>
              {/* Base of the partition bar */}
              {config.explanatoryElements.leftPartition.bars.base.enabled && (
                <mesh
                  position={[0, 0, 0]}
                  castShadow={config.visual.effects.shadows}
                  receiveShadow={config.visual.effects.shadows}
                >
                  <boxGeometry args={[
                    config.explanatoryElements.leftPartition.bars.width * config.explanatoryElements.leftPartition.bars.base.widthScale,
                    config.explanatoryElements.leftPartition.bars.base.height,
                    config.explanatoryElements.leftPartition.bars.width * config.explanatoryElements.leftPartition.bars.base.widthScale
                  ]} />
                  <meshStandardMaterial
                    color={colors.leftPartition}
                    transparent={config.explanatoryElements.leftPartition.bars.base.material.transparent}
                    opacity={config.explanatoryElements.leftPartition.bars.base.material.opacity}
                    roughness={config.explanatoryElements.leftPartition.bars.base.material.roughness}
                    metalness={config.explanatoryElements.leftPartition.bars.base.material.metalness}
                  />
                </mesh>
              )}

              {/* The partition bar itself */}
              <mesh
                position={[0, barHeight / 2, 0]}
                castShadow={config.visual.effects.shadows}
                receiveShadow={config.visual.effects.shadows}
              >
                <boxGeometry args={[
                  config.explanatoryElements.leftPartition.bars.width,
                  barHeight,
                  config.explanatoryElements.leftPartition.bars.width
                ]} />
                <meshStandardMaterial
                  color={colors.leftPartition}
                  transparent={config.explanatoryElements.leftPartition.bars.material.transparent}
                  opacity={config.explanatoryElements.leftPartition.bars.material.opacity}
                  roughness={config.explanatoryElements.leftPartition.bars.material.roughness}
                  metalness={config.explanatoryElements.leftPartition.bars.material.metalness}
                />
              </mesh>

              {showValues && (
                <ThemeHtml position={[config.explanatoryElements.leftPartition.valueLabels.offset[0], barHeight + config.explanatoryElements.leftPartition.valueLabels.offset[1], config.explanatoryElements.leftPartition.valueLabels.offset[2]]} center sprite occlude theme={theme}>
                  <Paper
                    elevation={config.explanatoryElements.leftPartition.valueLabels.elevation}
                    sx={{
                      px: theme.spacing(config.explanatoryElements.leftPartition.valueLabels.padding.horizontal),
                      py: theme.spacing(config.explanatoryElements.leftPartition.valueLabels.padding.vertical),
                      minWidth: config.explanatoryElements.leftPartition.valueLabels.minWidth,
                      borderRadius: theme.shape.borderRadius / config.explanatoryElements.leftPartition.valueLabels.borderRadius,
                      border: 1,
                      borderColor: colors.leftPartition,
                      bgcolor: 'background.paper',
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: config.explanatoryElements.leftPartition.valueLabels.fontSize,
                        textAlign: 'center',
                        display: 'block',
                        fontWeight: config.explanatoryElements.leftPartition.valueLabels.fontWeight
                      }}
                      color="text.primary"
                    >
                      {value}
                    </Typography>
                  </Paper>
                </ThemeHtml>
              )}
            </group>
          );
        })}
      </group>
    );
  };

  // Render right partition if it exists
  const renderRightPartition = () => {
    // Use data from partitionExplanation to avoid conflicts
    if (!vizData.partitionExplanation?.rightPartition || vizData.partitionExplanation.rightPartition.length === 0) {
      return null;
    }

    const { rightPartition, pivotValue } = vizData.partitionExplanation;
    const maxValue = Math.max(...vizData.mainArray.values);

    // Calculate positions from common base position + offset
    const basePosition = config.explanatoryElements.common.basePosition;
    const offset = config.explanatoryElements.rightPartition.container.offset;
    const rightSideX = basePosition[0] + offset[0];
    const yPosition = basePosition[1] + offset[1];
    const zPosition = basePosition[2] + offset[2];
    const labelPosition = config.explanatoryElements.rightPartition.label.position;

    return (
      <group position={[rightSideX, yPosition, zPosition]}>
        {/* Label */}
        <ThemeHtml position={labelPosition} center sprite occlude theme={theme}>
          <Paper
            elevation={config.explanatoryElements.rightPartition.label.elevation}
            sx={{
              px: theme.spacing(config.explanatoryElements.rightPartition.label.padding.horizontal),
              py: theme.spacing(config.explanatoryElements.rightPartition.label.padding.vertical),
              borderRadius: theme.shape.borderRadius / config.explanatoryElements.rightPartition.label.borderRadius,
              border: 1,
              borderColor: colors.rightPartition,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant={config.explanatoryElements.rightPartition.label.fontSize}
              fontWeight={config.explanatoryElements.rightPartition.label.fontWeight}
              color="text.primary"
              align="center"
              sx={{
                whiteSpace: config.explanatoryElements.rightPartition.label.whiteSpace,
                fontSize: config.explanatoryElements.rightPartition.label.fontSizeOverride
              }}
            >
              Right Partition (&gt; {pivotValue})
            </Typography>
          </Paper>
        </ThemeHtml>

        {/* Bars */}
        {rightPartition.map((value, index) => {
          const barHeight = (value / maxValue) * maxBarHeight * config.explanatoryElements.common.barScale;
          const barsOffset = config.explanatoryElements.rightPartition.bars.offset;

          // Calculate proper spacing with visual offset from config
          const partitionBarWidth = config.explanatoryElements.rightPartition.bars.width;
          const partitionBarSpacing = config.explanatoryElements.rightPartition.bars.spacing;
          const totalWidth = rightPartition.length * (partitionBarWidth + partitionBarSpacing) - partitionBarSpacing;
          const visualOffset = config.explanatoryElements.rightPartition.bars.visualOffset;
          const startX = -totalWidth / 2 + visualOffset;

          return (
            <group key={`right-${index}`} position={[
              barsOffset[0] + startX + index * (partitionBarWidth + partitionBarSpacing),
              barsOffset[1],
              barsOffset[2]
            ]}>
              {/* Base of the partition bar */}
              {config.explanatoryElements.rightPartition.bars.base.enabled && (
                <mesh
                  position={[0, 0, 0]}
                  castShadow={config.visual.effects.shadows}
                  receiveShadow={config.visual.effects.shadows}
                >
                  <boxGeometry args={[
                    config.explanatoryElements.rightPartition.bars.width * config.explanatoryElements.rightPartition.bars.base.widthScale,
                    config.explanatoryElements.rightPartition.bars.base.height,
                    config.explanatoryElements.rightPartition.bars.width * config.explanatoryElements.rightPartition.bars.base.widthScale
                  ]} />
                  <meshStandardMaterial
                    color={colors.rightPartition}
                    transparent={config.explanatoryElements.rightPartition.bars.base.material.transparent}
                    opacity={config.explanatoryElements.rightPartition.bars.base.material.opacity}
                    roughness={config.explanatoryElements.rightPartition.bars.base.material.roughness}
                    metalness={config.explanatoryElements.rightPartition.bars.base.material.metalness}
                  />
                </mesh>
              )}

              {/* The partition bar itself */}
              <mesh
                position={[0, barHeight / 2, 0]}
                castShadow={config.visual.effects.shadows}
                receiveShadow={config.visual.effects.shadows}
              >
                <boxGeometry args={[
                  config.explanatoryElements.rightPartition.bars.width,
                  barHeight,
                  config.explanatoryElements.rightPartition.bars.width
                ]} />
                <meshStandardMaterial
                  color={colors.rightPartition}
                  transparent={config.explanatoryElements.rightPartition.bars.material.transparent}
                  opacity={config.explanatoryElements.rightPartition.bars.material.opacity}
                  roughness={config.explanatoryElements.rightPartition.bars.material.roughness}
                  metalness={config.explanatoryElements.rightPartition.bars.material.metalness}
                />
              </mesh>

              {showValues && (
                <ThemeHtml position={[config.explanatoryElements.rightPartition.valueLabels.offset[0], barHeight + config.explanatoryElements.rightPartition.valueLabels.offset[1], config.explanatoryElements.rightPartition.valueLabels.offset[2]]} center sprite occlude theme={theme}>
                  <Paper
                    elevation={config.explanatoryElements.rightPartition.valueLabels.elevation}
                    sx={{
                      px: theme.spacing(config.explanatoryElements.rightPartition.valueLabels.padding.horizontal),
                      py: theme.spacing(config.explanatoryElements.rightPartition.valueLabels.padding.vertical),
                      minWidth: config.explanatoryElements.rightPartition.valueLabels.minWidth,
                      borderRadius: theme.shape.borderRadius / config.explanatoryElements.rightPartition.valueLabels.borderRadius,
                      border: 1,
                      borderColor: colors.rightPartition,
                      bgcolor: 'background.paper',
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: config.explanatoryElements.rightPartition.valueLabels.fontSize,
                        textAlign: 'center',
                        display: 'block',
                        fontWeight: config.explanatoryElements.rightPartition.valueLabels.fontWeight
                      }}
                      color="text.primary"
                    >
                      {value}
                    </Typography>
                  </Paper>
                </ThemeHtml>
              )}
            </group>
          );
        })}
      </group>
    );
  };





  // Render pivot element if it exists separately
  const renderPivotElement = () => {
    if (!vizData.pivotElement) return null;

    const { value, originalIndex } = vizData.pivotElement;
    const maxValue = Math.max(...vizData.mainArray.values);

    // Calculate positions from common base position + offset
    const basePosition = config.explanatoryElements.common.basePosition;
    const offset = config.explanatoryElements.pivotElement.container.offset;
    const centerX = basePosition[0] + offset[0];
    const yPosition = basePosition[1] + offset[1];
    const zPosition = basePosition[2] + offset[2];
    const labelPosition = config.explanatoryElements.pivotElement.label.position;
    const barOffset = config.explanatoryElements.pivotElement.bar.offset;

    const barHeight = (value / maxValue) * maxBarHeight * config.explanatoryElements.common.barScale;

    return (
      <group position={[centerX, yPosition, zPosition]}>
        {/* Label */}
        <ThemeHtml position={labelPosition} center sprite occlude theme={theme}>
          <Paper
            elevation={config.explanatoryElements.pivotElement.label.elevation}
            sx={{
              px: theme.spacing(config.explanatoryElements.pivotElement.label.padding.horizontal),
              py: theme.spacing(config.explanatoryElements.pivotElement.label.padding.vertical),
              borderRadius: theme.shape.borderRadius / config.explanatoryElements.pivotElement.label.borderRadius,
              border: 1,
              borderColor: colors.pivot,
              bgcolor: 'background.paper',
              userSelect: 'none',
              pointerEvents: 'none',
              whiteSpace: config.explanatoryElements.pivotElement.label.whiteSpace
            }}
          >
            <Typography
              variant={config.explanatoryElements.pivotElement.label.fontSize}
              fontWeight={config.explanatoryElements.pivotElement.label.fontWeight}
              color="text.primary"
              align="center"
            >
              {config.explanatoryElements.pivotElement.label.text}
            </Typography>
          </Paper>
        </ThemeHtml>

        {/* Pivot Bar */}
        <group position={barOffset}>
          {/* Base of the pivot bar */}
          {config.explanatoryElements.pivotElement.bar.base.enabled && (
            <mesh
              position={[0, 0, 0]}
              castShadow={config.visual.effects.shadows}
              receiveShadow={config.visual.effects.shadows}
            >
              <boxGeometry args={[
                config.explanatoryElements.pivotElement.bar.width * config.explanatoryElements.pivotElement.bar.base.widthScale,
                config.explanatoryElements.pivotElement.bar.base.height,
                config.explanatoryElements.pivotElement.bar.width * config.explanatoryElements.pivotElement.bar.base.widthScale
              ]} />
              <meshStandardMaterial
                color={colors.pivot}
                transparent={config.explanatoryElements.pivotElement.bar.base.material.transparent}
                opacity={config.explanatoryElements.pivotElement.bar.base.material.opacity}
                roughness={config.explanatoryElements.pivotElement.bar.base.material.roughness}
                metalness={config.explanatoryElements.pivotElement.bar.base.material.metalness}
              />
            </mesh>
          )}

          {/* The pivot bar itself */}
          <mesh
            position={[0, barHeight / 2, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              config.explanatoryElements.pivotElement.bar.width,
              barHeight,
              config.explanatoryElements.pivotElement.bar.width
            ]} />
            <meshStandardMaterial
              color={colors.pivot}
              transparent={config.explanatoryElements.pivotElement.bar.material.transparent}
              opacity={config.explanatoryElements.pivotElement.bar.material.opacity}
              roughness={config.explanatoryElements.pivotElement.bar.material.roughness}
              metalness={config.explanatoryElements.pivotElement.bar.material.metalness}
            />
          </mesh>

          {showValues && (
            <ThemeHtml position={[config.explanatoryElements.pivotElement.valueLabel.offset[0], barHeight + config.explanatoryElements.pivotElement.valueLabel.offset[1], config.explanatoryElements.pivotElement.valueLabel.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.explanatoryElements.pivotElement.valueLabel.elevation}
                sx={{
                  px: theme.spacing(config.explanatoryElements.pivotElement.valueLabel.padding.horizontal),
                  py: theme.spacing(config.explanatoryElements.pivotElement.valueLabel.padding.vertical),
                  minWidth: config.explanatoryElements.pivotElement.valueLabel.minWidth,
                  borderRadius: theme.shape.borderRadius / config.explanatoryElements.pivotElement.valueLabel.borderRadius,
                  border: 1,
                  borderColor: colors.pivot,
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.explanatoryElements.pivotElement.valueLabel.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: config.explanatoryElements.pivotElement.valueLabel.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {showIndices && (
            <ThemeHtml position={config.explanatoryElements.pivotElement.indexLabel.offset} center sprite occlude theme={theme}>
              <Paper
                elevation={config.explanatoryElements.pivotElement.indexLabel.elevation}
                sx={{
                  width: config.explanatoryElements.pivotElement.indexLabel.size.width,
                  height: config.explanatoryElements.pivotElement.indexLabel.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: colors.pivot,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography variant="caption" sx={{ fontSize: config.explanatoryElements.pivotElement.indexLabel.fontSize, fontWeight: config.explanatoryElements.pivotElement.indexLabel.fontWeight }} color="text.primary">
                  {originalIndex}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      </group>
    );
  };

  // Render comparison arrows (exactly like MergeSort - simple ↓ arrows below bars)
  const renderComparisonArrows = () => {
    if (!vizData.mainArray?.comparingIndices || vizData.mainArray.comparingIndices.length === 0) {
      return null;
    }

    const { comparingIndices } = vizData.mainArray;

    return comparingIndices.map((index) => {
      // Use pre-calculated bar position (same as main bars)
      const barPosition = barPositions.find(pos => pos.index === index);
      const xPos = barPosition ? barPosition.xPos : 0;

      return (
        <group key={`arrow-${index}`} position={[xPos, 0, 0]}>
          {/* Comparison arrow - exactly like MergeSort */}
          <ThemeHtml
            position={[0, maxBarHeight + 1, 0]} // Above the bars
            center
            sprite
            occlude
            theme={theme}
          >
            <div style={{
              color: colors.comparing,
              background: 'transparent',
              fontSize: '1.4rem', // Same as MergeSort
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: `0 0 8px ${colors.comparing}, 0 0 16px ${colors.comparing}`,
              userSelect: 'none',
              pointerEvents: 'none',
              transform: 'scale(1) rotate(0deg)', // Point down to bars
              animation: 'pulse 1s infinite alternate' // Same as MergeSort
            }}>
              ↓
            </div>
            <style>
              {`
              @keyframes pulse {
                0% { transform: scale(1.0) rotate(0deg); }
                100% { transform: scale(1.3) rotate(0deg); }
              }
              `}
            </style>
          </ThemeHtml>
        </group>
      );
    });
  };

  return (
    <group>
      {renderMainArray()}
      {renderComparisonArrows()}
      {renderLeftPartition()}
      {renderRightPartition()}
      {renderPivotElement()}
    </group>
  );
};

export default QuickSortSimulation;
