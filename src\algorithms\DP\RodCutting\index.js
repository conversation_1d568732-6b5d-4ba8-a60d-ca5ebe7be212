// RodCutting/index.js
// Export only what's needed from this algorithm

import RodCuttingVisualization from './RodCuttingVisualization';
import Rod<PERSON>uttingController from './RodCuttingController';
import RodCuttingAlgorithm from './RodCuttingAlgorithm';

export const metadata = {
  id: '<PERSON><PERSON>utt<PERSON>',
  name: '<PERSON> Cutting',
  description: 'A dynamic programming algorithm that determines the maximum value obtainable by cutting a rod of a given length into smaller pieces, where each piece has a specific price.',
  timeComplexity: 'O(n²)',
  spaceComplexity: 'O(n)',
  defaultParams: {
    prices: [0, 1, 5, 8, 9, 10, 17, 17, 20, 24, 30],
    rodLength: 8,
  },
};

export const components = {
  visualization: RodCuttingVisualization,
  controller: Rod<PERSON>uttingController,
  algorithm: RodCuttingAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
