// ControlsSection.js
// A reusable controls section component with consistent styling

import React, { useState } from 'react';
import { Box, Typography, Stack, Paper, Collapse, useTheme } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SpeedIcon from '@mui/icons-material/Speed';
import PropTypes from 'prop-types';

// Import common components
import { Button, Slider } from './index';

/**
 * A reusable controls section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string} props.state - Current state of the algorithm (idle, running, paused, completed)
 * @param {number} props.step - Current step
 * @param {number} props.totalSteps - Total number of steps
 * @param {number} props.speed - Current speed
 * @param {function} props.setSpeed - Function to set speed
 * @param {function} props.onStart - Function to start the algorithm
 * @param {function} props.onPause - Function to pause the algorithm
 * @param {function} props.onReset - Function to reset the algorithm
 * @param {function} props.onStepForward - Function to step forward
 * @param {function} props.onStepBackward - Function to step backward
 * @param {boolean} props.showStepControls - Whether to show step controls
 */
const ControlsSection = ({
  state = 'idle',
  step = 0,
  totalSteps = 0,
  speed = 5,
  setSpeed = () => {},
  onStart = () => {},
  onPause = () => {},
  onReset = () => {},
  onStepForward = () => {},
  onStepBackward = () => {},
  showStepControls = true,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(true);

  // Handle play/pause
  const handlePlayPause = () => {
    if (state === 'running') {
      onPause();
    } else {
      onStart();
    }
  };

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <TuneIcon fontSize="small" sx={{ mr: 0.5 }} /> Controls
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{
          p: 1.5,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          borderRadius: 1,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
        }}>
          <Stack spacing={2}>
            {/* Speed Slider */}
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <SpeedIcon fontSize="small" sx={{ mr: 0.5 }} /> Speed
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {speed}x
                </Typography>
              </Box>
              <Slider
                label="" // Empty label to hide it
                value={speed}
                onChange={setSpeed}
                min={1}
                max={10}
                step={1}
                marks={true}
                showValue={false}
                valueLabelFormat={(value) => `${value}x`}
              />
            </Box>

            {/* Control Buttons */}
            <Stack direction="row" spacing={1}>
              <Button
                label={state === 'running' ? 'Pause' : 'Start'}
                onClick={handlePlayPause}
                variant="contained"
                color={state === 'running' ? 'warning' : 'primary'} // Warning color for pause button
                disabled={step >= totalSteps}
                startIcon={state === 'running' ? <PauseIcon /> : <PlayArrowIcon />}
                sx={{ flex: 1 }}
              />

              <Button
                label="Reset"
                onClick={onReset}
                variant="outlined"
                color="error"
                startIcon={<RestartAltIcon />}
                disabled={(state === 'running' && step < totalSteps) || (state === 'idle' && step === 0)}
                sx={{ flex: 1 }}
              />
            </Stack>

            {/* Step Controls */}
            {showStepControls && (
              <Stack direction="row" spacing={1}>
                <Button
                  label="Previous"
                  onClick={onStepBackward}
                  variant="outlined"
                  startIcon={<SkipPreviousIcon />}
                  disabled={state === 'running' || step <= 0}
                  sx={{ flex: 1 }}
                />

                <Button
                  label="Next"
                  onClick={onStepForward}
                  variant="outlined"
                  endIcon={<SkipNextIcon />}
                  disabled={state === 'running' || step >= totalSteps}
                  sx={{ flex: 1 }}
                />
              </Stack>
            )}
          </Stack>
        </Box>
      </Collapse>
    </Paper>
  );
};

ControlsSection.propTypes = {
  state: PropTypes.oneOf(['idle', 'running', 'paused', 'completed']),
  step: PropTypes.number,
  totalSteps: PropTypes.number,
  speed: PropTypes.number,
  setSpeed: PropTypes.func,
  onStart: PropTypes.func,
  onPause: PropTypes.func,
  onReset: PropTypes.func,
  onStepForward: PropTypes.func,
  onStepBackward: PropTypes.func,
  showStepControls: PropTypes.bool,
};

export default ControlsSection;
