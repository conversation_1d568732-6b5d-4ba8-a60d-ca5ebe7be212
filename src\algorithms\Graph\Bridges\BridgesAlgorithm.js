// BridgesAlgorithm.js
// Implementation of algorithm to find bridges in an undirected graph

/**
 * Generate a random undirected graph
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @param {number} minWeight - Minimum edge weight
 * @param {number} maxWeight - Maximum edge weight
 * @returns {Object} - Graph representation with nodes and edges
 */
const generateRandomGraph = (numNodes, density = 0.5, minWeight = 1, maxWeight = 10) => {
  console.log(`Generating random undirected graph with ${numNodes} nodes, density ${density}`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges
  const edges = [];
  let edgeId = 0;

  // Create a connected graph first to ensure all nodes are reachable
  // Use a simple spanning tree approach
  const visited = new Set([0]); // Start with node 0
  const unvisited = new Set(Array.from({ length: numNodes - 1 }, (_, i) => i + 1));

  while (unvisited.size > 0) {
    // Pick a random visited node
    const fromNode = Array.from(visited)[Math.floor(Math.random() * visited.size)];
    
    // Pick a random unvisited node
    const toNode = Array.from(unvisited)[Math.floor(Math.random() * unvisited.size)];
    
    // Add an undirected edge with random weight
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source: fromNode,
      target: toNode,
      weight
    });
    
    // Mark the target node as visited
    visited.add(toNode);
    unvisited.delete(toNode);
  }

  // Add additional random edges based on density
  const maxPossibleEdges = numNodes * (numNodes - 1) / 2; // Undirected graph, so n(n-1)/2 possible edges
  const targetEdgeCount = Math.floor(maxPossibleEdges * density);
  const additionalEdges = Math.max(0, targetEdgeCount - (numNodes - 1));

  // Keep track of existing edges to avoid duplicates
  const existingEdges = new Set();
  edges.forEach(edge => {
    existingEdges.add(`${edge.source}-${edge.target}`);
    existingEdges.add(`${edge.target}-${edge.source}`); // Undirected graph
  });

  // Add random edges until we reach the target density
  for (let i = 0; i < additionalEdges; i++) {
    let source, target;
    let edgeKey;

    // Find a new edge that doesn't already exist
    do {
      source = Math.floor(Math.random() * numNodes);
      target = Math.floor(Math.random() * numNodes);
      edgeKey = `${source}-${target}`;
    } while (source === target || existingEdges.has(edgeKey));

    // Add the edge with random weight
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source,
      target,
      weight
    });
    existingEdges.add(edgeKey);
    existingEdges.add(`${target}-${source}`); // Undirected graph
  }

  return { nodes, edges };
};

/**
 * Generate a custom graph from edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} customEdges - Array of edges [from, to, weight]
 * @returns {Object} - Graph representation with nodes and edges
 */
const generateCustomGraph = (numNodes, customEdges) => {
  console.log(`Generating custom graph with ${numNodes} nodes and ${customEdges.length} edges`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges from custom edges
  const edges = [];
  let edgeId = 0;

  // Keep track of existing edges to avoid duplicates
  const existingEdges = new Set();

  customEdges.forEach(([source, target, weight = 1]) => {
    if (source >= 0 && source < numNodes && target >= 0 && target < numNodes && source !== target) {
      // Check if this edge already exists
      const edgeKey = `${source}-${target}`;
      const reverseEdgeKey = `${target}-${source}`;
      
      if (!existingEdges.has(edgeKey) && !existingEdges.has(reverseEdgeKey)) {
        edges.push({
          id: edgeId++,
          source,
          target,
          weight
        });
        
        existingEdges.add(edgeKey);
        existingEdges.add(reverseEdgeKey);
      }
    }
  });

  return { nodes, edges };
};

/**
 * Generate steps for finding bridges in an undirected graph
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateBridgesSteps = (params) => {
  console.log('generateBridgesSteps called with params:', params);
  const { nodes: numNodes, density, minWeight = 1, maxWeight = 10, customEdges } = params;
  const steps = [];

  // Generate graph
  let graphData;
  if (customEdges && customEdges.length > 0) {
    console.log('Generating custom graph with edges:', customEdges);
    graphData = generateCustomGraph(numNodes, customEdges);
  } else {
    console.log('Generating random graph with params:', { numNodes, density, minWeight, maxWeight });
    graphData = generateRandomGraph(numNodes, density, minWeight, maxWeight);
  }
  console.log('Generated graph:', graphData);

  // Extract edges from graph data
  const { edges } = graphData;

  // Create adjacency list representation of the graph
  const adjList = {};
  
  // Initialize adjacency list
  for (let i = 0; i < numNodes; i++) {
    adjList[i] = [];
  }
  
  // Fill adjacency list
  edges.forEach(edge => {
    adjList[edge.source].push({ node: edge.target, edge: edge.id });
    adjList[edge.target].push({ node: edge.source, edge: edge.id }); // Undirected graph
  });

  // Add initial step
  steps.push({
    type: 'initialize',
    message: 'Initialize algorithm to find bridges',
    graph: graphData,
    visited: [],
    discoveryTime: {},
    lowTime: {},
    parent: {},
    bridges: [],
    currentNode: null,
    currentEdge: null,
    time: 0,
    pseudocodeLine: 1
  });

  // Find bridges using DFS
  const visited = new Set();
  const discoveryTime = {};
  const lowTime = {};
  const parent = {};
  const bridges = [];
  let time = 0;

  // DFS function to find bridges
  const dfs = (u) => {
    // Mark the current node as visited
    visited.add(u);
    time++;
    discoveryTime[u] = time;
    lowTime[u] = time;
    
    // Add step for visiting the node
    steps.push({
      type: 'visit',
      message: `Visit node ${u}`,
      graph: graphData,
      visited: Array.from(visited),
      discoveryTime: { ...discoveryTime },
      lowTime: { ...lowTime },
      parent: { ...parent },
      bridges: [...bridges],
      currentNode: u,
      time,
      pseudocodeLine: 5
    });

    // Recur for all adjacent vertices
    for (const neighbor of adjList[u]) {
      const v = neighbor.node;
      const edgeId = neighbor.edge;
      
      // Add step for exploring edge
      steps.push({
        type: 'explore',
        message: `Explore edge from ${u} to ${v}`,
        graph: graphData,
        visited: Array.from(visited),
        discoveryTime: { ...discoveryTime },
        lowTime: { ...lowTime },
        parent: { ...parent },
        bridges: [...bridges],
        currentNode: u,
        currentEdge: { source: u, target: v },
        time,
        pseudocodeLine: 6
      });
      
      // If v is not visited yet, then make it a child of u in DFS tree
      if (!visited.has(v)) {
        parent[v] = u;
        
        // Add step for setting parent
        steps.push({
          type: 'set_parent',
          message: `Set parent of ${v} to ${u}`,
          graph: graphData,
          visited: Array.from(visited),
          discoveryTime: { ...discoveryTime },
          lowTime: { ...lowTime },
          parent: { ...parent },
          bridges: [...bridges],
          currentNode: u,
          currentEdge: { source: u, target: v },
          time,
          pseudocodeLine: 8
        });
        
        // Recursive call
        dfs(v);
        
        // Check if the subtree rooted with v has a connection to one of the ancestors of u
        if (lowTime[v] > discoveryTime[u]) {
          // This is a bridge
          bridges.push({ source: u, target: v, edgeId });
          
          // Add step for finding bridge
          steps.push({
            type: 'bridge',
            message: `Edge (${u}, ${v}) is a bridge (lowTime[${v}] = ${lowTime[v]} > discoveryTime[${u}] = ${discoveryTime[u]})`,
            graph: graphData,
            visited: Array.from(visited),
            discoveryTime: { ...discoveryTime },
            lowTime: { ...lowTime },
            parent: { ...parent },
            bridges: [...bridges],
            currentNode: u,
            currentEdge: { source: u, target: v },
            time,
            pseudocodeLine: 12
          });
        }
        
        // Update low value of u
        lowTime[u] = Math.min(lowTime[u], lowTime[v]);
        
        // Add step for updating low time
        steps.push({
          type: 'update_low',
          message: `Update lowTime[${u}] = min(lowTime[${u}], lowTime[${v}]) = ${Math.min(lowTime[u], lowTime[v])}`,
          graph: graphData,
          visited: Array.from(visited),
          discoveryTime: { ...discoveryTime },
          lowTime: { ...lowTime },
          parent: { ...parent },
          bridges: [...bridges],
          currentNode: u,
          time,
          pseudocodeLine: 14
        });
      }
      // Update low value of u for parent function calls
      else if (v !== parent[u]) {
        lowTime[u] = Math.min(lowTime[u], discoveryTime[v]);
        
        // Add step for updating low time (back edge)
        steps.push({
          type: 'update_low_back',
          message: `Update lowTime[${u}] = min(lowTime[${u}], discoveryTime[${v}]) = ${Math.min(lowTime[u], discoveryTime[v])} (back edge)`,
          graph: graphData,
          visited: Array.from(visited),
          discoveryTime: { ...discoveryTime },
          lowTime: { ...lowTime },
          parent: { ...parent },
          bridges: [...bridges],
          currentNode: u,
          currentEdge: { source: u, target: v },
          time,
          pseudocodeLine: 16
        });
      }
    }
  };

  // Call DFS for all unvisited nodes
  for (let i = 0; i < numNodes; i++) {
    if (!visited.has(i)) {
      // Add step for starting DFS from a new root
      steps.push({
        type: 'start_dfs',
        message: `Start DFS from node ${i} (new component)`,
        graph: graphData,
        visited: Array.from(visited),
        discoveryTime: { ...discoveryTime },
        lowTime: { ...lowTime },
        parent: { ...parent },
        bridges: [...bridges],
        currentNode: i,
        time,
        pseudocodeLine: 3
      });
      
      dfs(i);
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Found ${bridges.length} bridges: ${bridges.map(b => `(${b.source}, ${b.target})`).join(', ')}`,
    graph: graphData,
    visited: Array.from(visited),
    discoveryTime,
    lowTime,
    parent,
    bridges,
    time,
    pseudocodeLine: 19
  });

  console.log('Generated steps:', steps.length, 'steps');
  console.log('Found bridges:', bridges);
  
  const result = {
    steps,
    graph: graphData,
    bridges
  };
  
  console.log('Returning result:', result);
  return result;
};

// Create the algorithm object with helper functions
const BridgesAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateBridgesSteps,
  generateRandomGraph,
  generateCustomGraph
};

export default BridgesAlgorithm;
