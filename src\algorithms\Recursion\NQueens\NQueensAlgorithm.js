// NQueensAlgorithm.js
// Implementation of N-Queens algorithm using backtracking

/**
 * Generate steps for solving the N-Queens problem using backtracking
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateNQueensSteps = (params) => {
  console.log('generateNQueensSteps called with params:', params);
  const { boardSize = 4, findAllSolutions = false } = params;
  const steps = [];

  // Validate input
  if (typeof boardSize !== 'number' || boardSize < 1 || boardSize > 12) {
    throw new Error('Board size must be a number between 1 and 12');
  }

  // Initialize the board (n x n matrix filled with 0s)
  const board = Array(boardSize).fill().map(() => Array(boardSize).fill(0));
  
  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize ${boardSize}x${boardSize} board for N-Queens problem`,
    board: JSON.parse(JSON.stringify(board)),
    currentRow: 0,
    currentCol: 0,
    solutions: [],
    pseudocodeLine: 1
  });

  // Array to store solutions
  const solutions = [];

  // Function to check if a queen can be placed at board[row][col]
  const isSafe = (board, row, col) => {
    // Check this row on left side
    for (let i = 0; i < col; i++) {
      if (board[row][i] === 1) {
        return false;
      }
    }

    // Check upper diagonal on left side
    for (let i = row, j = col; i >= 0 && j >= 0; i--, j--) {
      if (board[i][j] === 1) {
        return false;
      }
    }

    // Check lower diagonal on left side
    for (let i = row, j = col; i < boardSize && j >= 0; i++, j--) {
      if (board[i][j] === 1) {
        return false;
      }
    }

    return true;
  };

  // Recursive function to solve N-Queens problem
  const solveNQueensUtil = (board, col) => {
    // Base case: If all queens are placed, add the solution
    if (col >= boardSize) {
      // Create a copy of the current solution
      const solution = board.map(row => [...row]);
      solutions.push(solution);
      
      // Add solution found step
      steps.push({
        type: 'solution_found',
        message: `Solution ${solutions.length} found!`,
        board: JSON.parse(JSON.stringify(board)),
        currentRow: -1,
        currentCol: col,
        solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
        pseudocodeLine: 5
      });
      
      return true;
    }

    // Add step for considering column
    steps.push({
      type: 'consider_column',
      message: `Considering column ${col}`,
      board: JSON.parse(JSON.stringify(board)),
      currentRow: -1,
      currentCol: col,
      solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
      pseudocodeLine: 8
    });

    let solutionFound = false;

    // Try placing queen in all rows of this column
    for (let row = 0; row < boardSize; row++) {
      // Add step for checking position
      steps.push({
        type: 'check_position',
        message: `Checking if queen can be placed at position (${row}, ${col})`,
        board: JSON.parse(JSON.stringify(board)),
        currentRow: row,
        currentCol: col,
        solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
        pseudocodeLine: 10
      });

      // Check if queen can be placed at board[row][col]
      if (isSafe(board, row, col)) {
        // Place the queen
        board[row][col] = 1;
        
        // Add step for placing queen
        steps.push({
          type: 'place_queen',
          message: `Placing queen at position (${row}, ${col})`,
          board: JSON.parse(JSON.stringify(board)),
          currentRow: row,
          currentCol: col,
          solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
          pseudocodeLine: 12
        });

        // Recur to place rest of the queens
        const result = solveNQueensUtil(board, col + 1);
        
        // If we're looking for all solutions, continue even if a solution is found
        if (result && !findAllSolutions) {
          return true;
        }
        
        if (result) {
          solutionFound = true;
        }

        // Backtrack: remove the queen
        board[row][col] = 0;
        
        // Add step for backtracking
        steps.push({
          type: 'backtrack',
          message: `Backtracking: removing queen from position (${row}, ${col})`,
          board: JSON.parse(JSON.stringify(board)),
          currentRow: row,
          currentCol: col,
          solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
          pseudocodeLine: 16
        });
      } else {
        // Add step for invalid position
        steps.push({
          type: 'invalid_position',
          message: `Cannot place queen at position (${row}, ${col})`,
          board: JSON.parse(JSON.stringify(board)),
          currentRow: row,
          currentCol: col,
          solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
          pseudocodeLine: 18
        });
      }
    }

    // Add step for column complete
    steps.push({
      type: 'column_complete',
      message: `Completed column ${col}${solutionFound ? ' (solution found)' : ' (no solution found)'}`,
      board: JSON.parse(JSON.stringify(board)),
      currentRow: -1,
      currentCol: col,
      solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
      pseudocodeLine: 20
    });

    return solutionFound;
  };

  // Start solving from the first column
  solveNQueensUtil(board, 0);

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Found ${solutions.length} solution(s).`,
    board: JSON.parse(JSON.stringify(board)),
    currentRow: -1,
    currentCol: -1,
    solutions: solutions.map(sol => JSON.parse(JSON.stringify(sol))),
    pseudocodeLine: 22
  });

  return { 
    steps, 
    solutions,
    result: solutions.length
  };
};

// Create the algorithm object with helper functions
const NQueensAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateNQueensSteps
};

export default NQueensAlgorithm;
