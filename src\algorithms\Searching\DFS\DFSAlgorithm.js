// DFSAlgorithm.js
// This file contains the implementation of Depth-First Search algorithm and the visualization component.

import React from 'react';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    background: isDark ? '#1e1e1e' : '#f5f5f5',
    text: isDark ? '#d4d4d4' : '#333333',
    comment: isDark ? '#6a9955' : '#008000',
    keyword: isDark ? '#569cd6' : '#0000ff',
    string: isDark ? '#ce9178' : '#a31515',
    number: isDark ? '#b5cea8' : '#098658',
    function: isDark ? '#dcdcaa' : '#795e26',
    variable: isDark ? '#9cdcfe' : '#001080',
    property: isDark ? '#4fc1ff' : '#0070c1',
    operator: isDark ? '#d4d4d4' : '#000000',
    punctuation: isDark ? '#d4d4d4' : '#000000',
    border: isDark ? '#444444' : '#cccccc',
    highlight: isDark ? 'rgba(14, 99, 156, 0.3)' : 'rgba(173, 214, 255, 0.3)',
    activeHighlight: isDark ? 'rgba(14, 99, 156, 0.8)' : 'rgba(0, 120, 215, 0.4)',
    stepBackground: isDark ? '#252525' : '#f0f0f0',
  };
};

/**
 * Generate a graph with random connections
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @returns {Object} - Graph representation
 */
export const generateRandomGraph = (numNodes, density = 0.5) => {
  // Create an empty adjacency list
  const graph = {};
  
  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = [];
  }
  
  // Add random edges
  for (let i = 0; i < numNodes; i++) {
    for (let j = 0; j < numNodes; j++) {
      if (i !== j && Math.random() < density) {
        graph[i].push(j);
      }
    }
  }
  
  // Ensure the graph is connected
  for (let i = 0; i < numNodes - 1; i++) {
    // If there's no edge from i to i+1, add one
    if (!graph[i].includes(i+1)) {
      graph[i].push(i+1);
    }
    // If there's no edge from i+1 to i, add one
    if (!graph[i+1].includes(i)) {
      graph[i+1].push(i);
    }
  }
  
  return graph;
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edges - Array of edges [from, to]
 * @returns {Object} - Graph representation
 */
export const generateCustomGraph = (numNodes, edges) => {
  // Create an empty adjacency list
  const graph = {};
  
  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = [];
  }
  
  // Add edges
  edges.forEach(([from, to]) => {
    if (from >= 0 && from < numNodes && to >= 0 && to < numNodes) {
      if (!graph[from].includes(to)) {
        graph[from].push(to);
      }
    }
  });
  
  return graph;
};

/**
 * Generate steps for DFS algorithm visualization
 * @param {Object} graph - Graph representation (adjacency list)
 * @param {number} startNode - Starting node
 * @param {number} targetNode - Target node (optional)
 * @returns {Object} - Object containing steps and path
 */
export const generateDFSSteps = (graph, startNode, targetNode = null) => {
  const steps = [];
  const nodes = Object.keys(graph).map(Number);
  
  // Initialize visited array and stack
  const visited = new Array(nodes.length).fill(false);
  const stack = [];
  const parent = {};
  
  // Add initialization step
  steps.push({
    type: 'initialize',
    visited: [...visited],
    stack: [...stack],
    current: null,
    movement: `Initialize DFS: Start from node ${startNode}`
  });
  
  // Push the start node to the stack
  stack.push(startNode);
  
  // Add push step
  steps.push({
    type: 'push',
    visited: [...visited],
    stack: [...stack],
    current: startNode,
    movement: `Push start node ${startNode} onto the stack`
  });
  
  // Main DFS loop
  while (stack.length > 0) {
    // Pop a node from the stack
    const current = stack.pop();
    
    // Add pop step
    steps.push({
      type: 'pop',
      visited: [...visited],
      stack: [...stack],
      current,
      movement: `Pop node ${current} from the stack`
    });
    
    // If the node is not visited, mark it as visited
    if (!visited[current]) {
      visited[current] = true;
      
      // Add visit step
      steps.push({
        type: 'visit',
        visited: [...visited],
        stack: [...stack],
        current,
        movement: `Mark node ${current} as visited`
      });
      
      // If we found the target node, we can stop
      if (targetNode !== null && current === targetNode) {
        steps.push({
          type: 'found',
          visited: [...visited],
          stack: [...stack],
          current,
          movement: `Found target node ${targetNode}!`
        });
        break;
      }
      
      // Visit all adjacent nodes in reverse order (to maintain correct DFS order when popping)
      const neighbors = [...graph[current]].reverse();
      
      for (const neighbor of neighbors) {
        // Add check step
        steps.push({
          type: 'check',
          visited: [...visited],
          stack: [...stack],
          current,
          neighbor,
          movement: `Check neighbor ${neighbor} of node ${current}`
        });
        
        // If the neighbor is not visited, push it to the stack
        if (!visited[neighbor]) {
          stack.push(neighbor);
          parent[neighbor] = current;
          
          // Add push step
          steps.push({
            type: 'push',
            visited: [...visited],
            stack: [...stack],
            current,
            neighbor,
            movement: `Push neighbor ${neighbor} onto the stack`
          });
        } else {
          // Add skip step
          steps.push({
            type: 'skip',
            visited: [...visited],
            stack: [...stack],
            current,
            neighbor,
            movement: `Skip neighbor ${neighbor} as it is already visited`
          });
        }
      }
    }
  }
  
  // Add final step
  if (steps[steps.length - 1].type !== 'found') {
    if (targetNode !== null) {
      steps.push({
        type: 'complete',
        visited: [...visited],
        stack: [...stack],
        movement: `DFS complete. Target node ${targetNode} not found.`
      });
    } else {
      steps.push({
        type: 'complete',
        visited: [...visited],
        stack: [...stack],
        movement: `DFS complete. Visited all reachable nodes from ${startNode}.`
      });
    }
  }
  
  // Reconstruct the path if target node is found
  let path = [];
  if (targetNode !== null && visited[targetNode]) {
    let current = targetNode;
    while (current !== undefined) {
      path.unshift(current);
      current = parent[current];
    }
  }
  
  return { steps, visited, path };
};

/**
 * DFS Algorithm visualization component
 */
const DFSAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();
  
  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);
  
  // Define the steps of the algorithm
  const steps = [
    { line: 0, description: "Initialize DFS: Create visited array and stack" },
    { line: 1, description: "Push start node onto the stack" },
    { line: 2, description: "While stack is not empty" },
    { line: 3, description: "Pop a node from the stack" },
    { line: 4, description: "If node is not visited, mark it as visited" },
    { line: 5, description: "If current node is the target, we've found it" },
    { line: 6, description: "Visit all adjacent nodes of current node" },
    { line: 7, description: "Check if neighbor is not visited" },
    { line: 8, description: "Push neighbor onto the stack" },
    { line: 9, description: "DFS complete: All reachable nodes visited" },
  ];
  
  // Get the current step
  const currentStep = steps[Math.min(step % steps.length, steps.length - 1)];
  
  // DFS pseudocode
  const pseudocode = [
    { code: "function dfs(graph, startNode, targetNode):", highlight: currentStep.line === 0 },
    { code: "  // Initialize visited array and stack", highlight: false },
    { code: "  visited = new Array(graph.length).fill(false)", highlight: currentStep.line === 0 },
    { code: "  stack = []", highlight: currentStep.line === 0 },
    { code: "  parent = {}", highlight: currentStep.line === 0 },
    { code: "", highlight: false },
    { code: "  // Push the start node to the stack", highlight: false },
    { code: "  stack.push(startNode)", highlight: currentStep.line === 1 },
    { code: "", highlight: false },
    { code: "  // Main DFS loop", highlight: false },
    { code: "  while stack is not empty:", highlight: currentStep.line === 2 },
    { code: "    // Pop a node from the stack", highlight: false },
    { code: "    current = stack.pop()", highlight: currentStep.line === 3 },
    { code: "", highlight: false },
    { code: "    // If the node is not visited", highlight: false },
    { code: "    if not visited[current]:", highlight: currentStep.line === 4 },
    { code: "      // Mark it as visited", highlight: false },
    { code: "      visited[current] = true", highlight: currentStep.line === 4 },
    { code: "", highlight: false },
    { code: "      // If we found the target node, we can stop", highlight: false },
    { code: "      if targetNode is not null and current equals targetNode:", highlight: currentStep.line === 5 },
    { code: "        return reconstructPath(parent, targetNode)", highlight: currentStep.line === 5 },
    { code: "", highlight: false },
    { code: "      // Visit all adjacent nodes in reverse order", highlight: false },
    { code: "      for each neighbor of current (in reverse):", highlight: currentStep.line === 6 },
    { code: "        // If the neighbor is not visited", highlight: false },
    { code: "        if not visited[neighbor]:", highlight: currentStep.line === 7 },
    { code: "          // Push it to the stack", highlight: false },
    { code: "          stack.push(neighbor)", highlight: currentStep.line === 8 },
    { code: "          parent[neighbor] = current", highlight: currentStep.line === 8 },
    { code: "", highlight: false },
    { code: "  // If we get here, we've visited all reachable nodes", highlight: false },
    { code: "  return { visited, path: [] }", highlight: currentStep.line === 9 },
  ];
  
  return (
    <Box
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
        '&::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
          },
        },
      }}
    >
      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {currentStep?.description || "Algorithm complete"}
        </Typography>
      </Paper>

      {/* Pseudocode */}
      <Box
        sx={{
          fontFamily: "monospace",
          fontSize: "0.9rem",
          lineHeight: 1.5,
          whiteSpace: "pre",
          p: 1,
          overflowX: "auto",
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        {pseudocode.map((line, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              bgcolor: line.highlight ? colors.highlight : "transparent",
              borderRadius: 1,
              px: 1,
              py: 0.25,
            }}
          >
            {line.highlight && (
              <ArrowRightIcon
                sx={{
                  color: colors.function,
                  mr: 1,
                  fontSize: "1.2rem",
                }}
              />
            )}
            <Box sx={{ ml: line.highlight ? 0 : 3 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  color: colors.text,
                  fontWeight: line.highlight ? 600 : 400,
                }}
              >
                {line.code}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default DFSAlgorithm;
