// EditDistanceAlgorithm.js
// Implementation of Edit Distance algorithm using dynamic programming

/**
 * Generate steps for solving the Edit Distance problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateEditDistanceSteps = (params) => {
  console.log('generateEditDistanceSteps called with params:', params);
  const { string1 = "kitten", string2 = "sitting" } = params;
  const steps = [];

  // Validate input
  if (typeof string1 !== 'string' || typeof string2 !== 'string') {
    throw new Error('Both inputs must be strings');
  }

  const m = string1.length;
  const n = string2.length;

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Edit Distance algorithm with strings "${string1}" and "${string2}"`,
    string1,
    string2,
    dp: null,
    currentRow: null,
    currentCol: null,
    operations: [],
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // Initialize first row and column
  for (let i = 0; i <= m; i++) {
    dp[i][0] = i;
  }
  for (let j = 0; j <= n; j++) {
    dp[0][j] = j;
  }

  // Add DP table initialization step
  steps.push({
    type: 'initialize_table',
    message: `Initialize the DP table. First row and column represent deletions and insertions.`,
    string1,
    string2,
    dp: JSON.parse(JSON.stringify(dp)),
    currentRow: null,
    currentCol: null,
    operations: [],
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      // Add step for current cell consideration
      steps.push({
        type: 'consider_cell',
        message: `Consider cell (${i}, ${j}): comparing "${string1[i-1]}" with "${string2[j-1]}"`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        operations: [],
        result: null,
        pseudocodeLine: 7
      });

      // Calculate the cost of operations
      const deleteCost = dp[i-1][j] + 1;
      const insertCost = dp[i][j-1] + 1;
      const replaceCost = dp[i-1][j-1] + (string1[i-1] === string2[j-1] ? 0 : 1);

      // Add step for comparing costs
      steps.push({
        type: 'compare_costs',
        message: `Compare costs: Delete (${deleteCost}), Insert (${insertCost}), Replace/Match (${replaceCost})`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        deleteCost,
        insertCost,
        replaceCost,
        operations: [],
        result: null,
        pseudocodeLine: 9
      });

      // Choose the minimum cost operation
      dp[i][j] = Math.min(deleteCost, insertCost, replaceCost);

      // Determine which operation was chosen
      let operation;
      if (dp[i][j] === replaceCost) {
        operation = string1[i-1] === string2[j-1] ? 'match' : 'replace';
      } else if (dp[i][j] === deleteCost) {
        operation = 'delete';
      } else {
        operation = 'insert';
      }

      // Add step for decision
      steps.push({
        type: 'decision',
        message: `Decision for cell (${i}, ${j}): ${operation} with cost ${dp[i][j]}`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        operation,
        operations: [],
        result: null,
        pseudocodeLine: 10
      });
    }
  }

  // Trace back to find the operations
  let i = m;
  let j = n;
  const operations = [];

  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    message: 'Start tracing back to find the sequence of operations',
    string1,
    string2,
    dp: JSON.parse(JSON.stringify(dp)),
    currentRow: i,
    currentCol: j,
    operations: [],
    result: dp[m][n],
    pseudocodeLine: 13
  });

  while (i > 0 || j > 0) {
    if (i > 0 && j > 0 && dp[i][j] === dp[i-1][j-1] + (string1[i-1] === string2[j-1] ? 0 : 1)) {
      // Match or replace
      const op = string1[i-1] === string2[j-1] ? 'match' : 'replace';
      operations.unshift({
        type: op,
        pos1: i - 1,
        pos2: j - 1,
        char1: string1[i-1],
        char2: string2[j-1]
      });

      // Add step for match/replace operation
      steps.push({
        type: 'traceback_operation',
        message: `${op === 'match' ? 'Match' : 'Replace'} "${string1[i-1]}" ${op === 'match' ? 'with' : 'by'} "${string2[j-1]}" at positions ${i-1} and ${j-1}`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        operations: [...operations],
        result: dp[m][n],
        pseudocodeLine: op === 'match' ? 16 : 17
      });

      i--;
      j--;
    } else if (i > 0 && dp[i][j] === dp[i-1][j] + 1) {
      // Delete
      operations.unshift({
        type: 'delete',
        pos1: i - 1,
        pos2: null,
        char1: string1[i-1],
        char2: null
      });

      // Add step for delete operation
      steps.push({
        type: 'traceback_operation',
        message: `Delete "${string1[i-1]}" at position ${i-1}`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        operations: [...operations],
        result: dp[m][n],
        pseudocodeLine: 18
      });

      i--;
    } else {
      // Insert
      operations.unshift({
        type: 'insert',
        pos1: null,
        pos2: j - 1,
        char1: null,
        char2: string2[j-1]
      });

      // Add step for insert operation
      steps.push({
        type: 'traceback_operation',
        message: `Insert "${string2[j-1]}" at position ${j-1}`,
        string1,
        string2,
        dp: JSON.parse(JSON.stringify(dp)),
        currentRow: i,
        currentCol: j,
        operations: [...operations],
        result: dp[m][n],
        pseudocodeLine: 19
      });

      j--;
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Edit Distance: ${dp[m][n]}`,
    string1,
    string2,
    dp: JSON.parse(JSON.stringify(dp)),
    currentRow: null,
    currentCol: null,
    operations,
    result: dp[m][n],
    pseudocodeLine: 22
  });

  return { 
    steps, 
    result: dp[m][n],
    operations
  };
};

// Create the algorithm object with helper functions
const EditDistanceAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateEditDistanceSteps
};

export default EditDistanceAlgorithm;
