// MergeSortSteps.js
// This file contains the step generation logic for the Merge Sort algorithm

import {
  createInitialStep,
  createCompletionStep,
  createSplitStep,
  createMergeStep,
  createPlaceStep,
  deepClone
} from '../../../simulation/utils/stepUtils';

/**
 * Generate steps for the Merge Sort algorithm
 * 
 * @param {Array} array - The array to sort
 * @returns {Array} An array of steps for the algorithm
 */
export const generateMergeSortSteps = (array) => {
  // Validate input
  if (!Array.isArray(array) || array.length === 0) {
    return [];
  }

  // Create a deep copy of the original array
  const originalArray = deepClone(array);
  
  // Create an auxiliary array for the merge sort algorithm
  const auxArray = new Array(originalArray.length).fill(null);
  
  // Track sorted subarrays to avoid duplicate steps
  const sortedSubarrays = new Set();
  
  // Initialize steps array
  const steps = [];
  
  // Add initial step
  steps.push(
    createInitialStep(
      deepClone(originalArray),
      `Initial array: [${originalArray.join(', ')}]`
    )
  );

  // Main merge sort function
  const mergeSort = (mainArray, aux, low, high) => {
    // Base case: if the subarray has 1 or 0 elements, it's already sorted
    if (high <= low) {
      // For single-element subarrays, mark as sorted immediately
      if (high === low && !sortedSubarrays.has(`${low}-${high}`)) {
        sortedSubarrays.add(`${low}-${high}`);
      }
      return;
    }

    // Find the middle point
    const mid = Math.floor(low + (high - low) / 2);

    // Add step for splitting the array
    steps.push(
      createSplitStep(
        deepClone(mainArray),
        { low, mid, high },
        `Split array from index ${low} to ${high} at middle index ${mid}`
      )
    );

    // Sort the left half
    mergeSort(mainArray, aux, low, mid);

    // Sort the right half
    mergeSort(mainArray, aux, mid + 1, high);

    // Merge the sorted halves
    merge(mainArray, aux, low, mid, high);
  };

  // Merge function to combine two sorted subarrays
  const merge = (mainArray, aux, low, mid, high) => {
    // Add step for starting the merge
    steps.push(
      createMergeStep(
        deepClone(mainArray),
        { low, mid, high },
        `Merging subarrays from index ${low} to ${mid} and from ${mid + 1} to ${high}`
      )
    );

    // Copy elements to auxiliary array
    for (let k = low; k <= high; k++) {
      aux[k] = mainArray[k];
    }

    // Merge back to the main array
    let i = low;      // Pointer for the left subarray
    let j = mid + 1;  // Pointer for the right subarray
    
    // For each position in the merged subarray
    for (let k = low; k <= high; k++) {
      // If left pointer is exhausted, take from right
      if (i > mid) {
        // Add step for placing element from right subarray
        steps.push(
          createPlaceStep(
            deepClone(mainArray),
            { 
              sourceIndex: j,
              targetIndex: k,
              sourceValue: aux[j],
              isFromRightSubarray: true
            },
            `Place element ${aux[j]} from right subarray at index ${k}`
          )
        );
        
        mainArray[k] = aux[j++];
      }
      // If right pointer is exhausted, take from left
      else if (j > high) {
        // Add step for placing element from left subarray
        steps.push(
          createPlaceStep(
            deepClone(mainArray),
            { 
              sourceIndex: i,
              targetIndex: k,
              sourceValue: aux[i],
              isFromLeftSubarray: true
            },
            `Place element ${aux[i]} from left subarray at index ${k}`
          )
        );
        
        mainArray[k] = aux[i++];
      }
      // If left element is smaller, take it
      else if (aux[i] <= aux[j]) {
        // Add step for placing element from left subarray after comparison
        steps.push(
          createPlaceStep(
            deepClone(mainArray),
            { 
              sourceIndex: i,
              targetIndex: k,
              sourceValue: aux[i],
              comparedWith: aux[j],
              comparedWithIndex: j,
              isFromLeftSubarray: true
            },
            `Compare ${aux[i]} and ${aux[j]}: Place smaller element ${aux[i]} at index ${k}`
          )
        );
        
        mainArray[k] = aux[i++];
      }
      // Otherwise, take the right element
      else {
        // Add step for placing element from right subarray after comparison
        steps.push(
          createPlaceStep(
            deepClone(mainArray),
            { 
              sourceIndex: j,
              targetIndex: k,
              sourceValue: aux[j],
              comparedWith: aux[i],
              comparedWithIndex: i,
              isFromRightSubarray: true
            },
            `Compare ${aux[i]} and ${aux[j]}: Place smaller element ${aux[j]} at index ${k}`
          )
        );
        
        mainArray[k] = aux[j++];
      }
    }

    // Mark this subarray as sorted
    if (!sortedSubarrays.has(`${low}-${high}`)) {
      sortedSubarrays.add(`${low}-${high}`);
      
      // Add step for completed merge
      steps.push(
        createMergeStep(
          deepClone(mainArray),
          { 
            low, 
            high,
            isSorted: true
          },
          `Merged subarray from index ${low} to ${high} is now sorted`
        )
      );
    }
  };

  // Start the merge sort
  mergeSort(originalArray, auxArray, 0, originalArray.length - 1);

  // Add final step
  steps.push(
    createCompletionStep(
      deepClone(originalArray),
      `Merge Sort complete: [${originalArray.join(', ')}]`
    )
  );

  return steps;
};
