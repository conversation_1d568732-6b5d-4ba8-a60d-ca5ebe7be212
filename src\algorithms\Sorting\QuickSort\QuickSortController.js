// QuickSortController.js - Controller-driven QuickSort following MergeSort pattern
// Generates visualizationData for each step to drive the visualization

import React, { useEffect, useState, useMemo } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// Import QuickSort algorithm
import { generateDetailedQuickSortSteps } from './QuickSortDetailedSteps';

const QuickSortController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => { } } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    steps,
    setSteps
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Import configuration
  const CONFIG = require('./QuickSortConfig').default;

  // Extract parameters with safety checks using config defaults
  const arraySize = params?.arraySize || CONFIG.performance.arraySize.default;
  const randomize = params?.randomize !== undefined ? params.randomize : true;
  const customArray = params?.customArray || [];
  const useCustomArray = params?.useCustomArray || false;

  // State for custom array input
  const [customArrayInput, setCustomArrayInput] = useState('');
  const [customArrayError, setCustomArrayError] = useState('');

  // Generate the input array based on parameters using config
  const inputArray = useMemo(() => {
    if (useCustomArray && customArray.length > 0) {
      return [...customArray];
    } else if (randomize) {
      // Generate random array using config values
      const minValue = CONFIG.algorithm.arrayGeneration.randomValues.min;
      const maxValue = CONFIG.algorithm.arrayGeneration.randomValues.max;
      const range = maxValue - minValue + 1;

      return Array.from({ length: arraySize }, () =>
        Math.floor(Math.random() * range) + minValue
      );
    } else {
      // Generate reverse sorted array (worst case for quick sort) or use config
      if (CONFIG.algorithm.arrayGeneration.reverseArray.useForWorstCase) {
        const startValue = CONFIG.algorithm.arrayGeneration.reverseArray.startValue || arraySize;
        return Array.from({ length: arraySize }, (_, i) => startValue - i);
      } else {
        // Generate sequential array using config
        const startValue = CONFIG.algorithm.arrayGeneration.sequentialArray.startValue;
        const increment = CONFIG.algorithm.arrayGeneration.sequentialArray.increment;
        return Array.from({ length: arraySize }, (_, i) => startValue + (i * increment));
      }
    }
  }, [arraySize, randomize, customArray, useCustomArray]);

  // Generate steps with visualization data when input array changes
  useEffect(() => {
    if (inputArray.length === 0) return;

    console.log('QuickSortController - Generating steps for array:', inputArray);

    try {
      // Generate detailed algorithm steps (like MergeSort)
      const { steps: algorithmSteps } = generateDetailedQuickSortSteps(inputArray);

      // Enhance steps with visualization data
      const enhancedSteps = algorithmSteps.map((step, index) => ({
        ...step,
        stepNumber: index + 1,
        visualizationData: generateVisualizationData(step, inputArray)
      }));

      console.log('QuickSortController - Generated enhanced steps:', enhancedSteps);

      setSteps(enhancedSteps);
      setTotalSteps(enhancedSteps.length);

      setStep(0); // Reset to initial step
      setState('idle');
    } catch (error) {
      console.error('QuickSortController - Error generating steps:', error);
      setSteps([]);
      setTotalSteps(0);
    }
  }, [inputArray, setSteps, setTotalSteps, setStep, setState]);

  // Set state to completed when step reaches totalSteps (like other algorithms)
  useEffect(() => {
    // Only update if we have valid steps and we're not in idle state
    if (totalSteps > 0 && state !== 'idle') {
      // If we've reached the last step, mark as completed
      if (step >= totalSteps && state !== 'completed') {
        console.log('QuickSortController - Reached final step, setting state to completed');
        setState('completed');
      }
      // If we were in completed state but stepped back, go to paused
      else if (state === 'completed' && step < totalSteps) {
        console.log('QuickSortController - Stepped back from completion, setting state to paused');
        setState('paused');
      }
    }
  }, [step, totalSteps, setState, state]);

  // Generate visualization data for a step (main array with gaps + explanatory partitions)
  const generateVisualizationData = (step, originalArray) => {
    const vizData = {
      mainArray: {
        values: [...step.array],
        size: step.array.length,
        sortedIndices: [...step.sorted],
        pivotIndex: step.pivot || -1,
        partitionRange: null,
        leftPartitionEnd: -1,
        rightPartitionStart: -1,
        comparingIndices: step.comparing || [],
        swappingIndices: step.swapping || []
      },
      partitionExplanation: null,  // For showing left/right partitions between step board and main array
      explanatoryElements: {}  // For other explanatory elements
    };

    // Add step-specific visualization data (main array with gaps + partition explanations)
    switch (step.type) {
      case 'initial':
        // Initial state - main array only
        break;

      case 'start_partition':
      case 'select_pivot':
      case 'initialize_pointers':
        // Show partition range in main array
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };
        }
        break;

      case 'examine_element':
      case 'compare_with_pivot':
        // Show partition range and comparing elements in main array
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };

          // Show partition explanation if we have boundary info
          if (step.partitionInfo?.leftBoundary !== undefined && step.partitionInfo.leftBoundary >= step.left) {
            const leftElements = step.array.slice(step.left, step.partitionInfo.leftBoundary + 1);
            const rightElements = step.array.slice(step.partitionInfo.leftBoundary + 1, step.right);

            vizData.partitionExplanation = {
              leftPartition: leftElements,
              rightPartition: rightElements,
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'move_left_boundary':
      case 'already_in_left_partition':
      case 'stays_in_right_partition':
        // Show partition range with updated boundaries
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };
          // Show partition boundaries in main array
          if (step.partitionInfo?.leftBoundary !== undefined) {
            vizData.mainArray.leftPartitionEnd = step.partitionInfo.leftBoundary;
          }

          // Show partition explanation consistently
          if (step.partitionInfo?.leftBoundary >= step.left) {
            const leftElements = step.array.slice(step.left, step.partitionInfo.leftBoundary + 1);
            const rightElements = step.array.slice(step.partitionInfo.leftBoundary + 1, step.right);

            vizData.partitionExplanation = {
              leftPartition: leftElements,
              rightPartition: rightElements,
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'swap_to_left_partition':
      case 'swapped_to_left_partition':
        // Show partition range and swapping in main array
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };
          if (step.partitionInfo?.leftBoundary !== undefined) {
            vizData.mainArray.leftPartitionEnd = step.partitionInfo.leftBoundary;
          }
        }

        // Show explanatory elements if available
        if (step.explanatoryElements) {
          vizData.explanatoryElements = step.explanatoryElements;

          // Extract partition data for explanation display
          if (step.explanatoryElements.leftPartition && step.explanatoryElements.rightPartition) {
            vizData.partitionExplanation = {
              leftPartition: step.explanatoryElements.leftPartition.map(item => item.value),
              rightPartition: step.explanatoryElements.rightPartition.map(item => item.value),
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'partition_state':
        // Show partition explanation with left and right partitions
        if (step.explanatoryElements) {
          vizData.explanatoryElements = step.explanatoryElements;

          // Also show partition explanation between step board and main array
          if (step.explanatoryElements.leftPartition && step.explanatoryElements.rightPartition) {
            vizData.partitionExplanation = {
              leftPartition: step.explanatoryElements.leftPartition.values || [],
              rightPartition: step.explanatoryElements.rightPartition.values || [],
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'place_pivot':
      case 'pivot_placed':
        // Show partition range during pivot placement
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };
          if (step.partitionInfo?.leftBoundary !== undefined) {
            vizData.mainArray.leftPartitionEnd = step.partitionInfo.leftBoundary;
          }
        }

        // Show explanatory elements if available
        if (step.explanatoryElements) {
          vizData.explanatoryElements = step.explanatoryElements;

          // Extract partition data for explanation display
          if (step.explanatoryElements.leftPartition && step.explanatoryElements.rightPartition) {
            vizData.partitionExplanation = {
              leftPartition: step.explanatoryElements.leftPartition.map(item => item.value),
              rightPartition: step.explanatoryElements.rightPartition.map(item => item.value),
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'pivot_sorted':
      case 'single_element_sorted':
        // Pivot is finally placed - show in main array
        vizData.mainArray.sortedIndices = [...step.sorted];
        break;

      case 'partition_complete':
        // Show final partition results in explanatory elements
        if (step.explanatoryElements) {
          vizData.explanatoryElements = step.explanatoryElements;

          // Show final partition explanation
          if (step.explanatoryElements.leftPartition && step.explanatoryElements.rightPartition) {
            vizData.partitionExplanation = {
              leftPartition: step.explanatoryElements.leftPartition.values || [],
              rightPartition: step.explanatoryElements.rightPartition.values || [],
              pivotValue: step.array[step.pivot]
            };
          }
        }
        break;

      case 'recurse_left':
      case 'recurse_right':
        // Show the range being recursively sorted
        if (step.left !== null && step.right !== null) {
          vizData.mainArray.partitionRange = {
            start: step.left,
            end: step.right
          };
        }
        break;

      case 'complete':
        // All elements are sorted
        vizData.mainArray.sortedIndices = Array.from({ length: step.array.length }, (_, i) => i);
        break;

      default:
        break;
    }

    return vizData;
  };

  // Handle array size change using config limits
  const handleArraySizeChange = (value) => {
    if (value >= CONFIG.performance.arraySize.min && value <= CONFIG.performance.arraySize.max) {
      onParamChange({ ...params, arraySize: value });
      setState('idle');
      setStep(0);
    }
  };

  // Handle randomize toggle
  const handleRandomizeChange = (value) => {
    onParamChange({ ...params, randomize: value });
    setState('idle');
    setStep(0);
  };

  // Handle custom array toggle
  const handleCustomArrayChange = (value) => {
    onParamChange({ ...params, useCustomArray: value });
    setState('idle');
    setStep(0);
  };

  // Handle custom array input change
  const handleCustomArrayInputChange = (value) => {
    setCustomArrayInput(value);
    setCustomArrayError('');
  };

  // Apply custom array
  const applyCustomArray = () => {
    try {
      const parsedArray = customArrayInput
        .split(',')
        .map(item => item.trim())
        .filter(item => item !== '')
        .map(item => {
          const num = Number(item);
          if (isNaN(num)) throw new Error('Invalid number');
          return num;
        });

      if (parsedArray.length < CONFIG.performance.arraySize.min) {
        setCustomArrayError(`Please enter at least ${CONFIG.performance.arraySize.min} numbers`);
        return false;
      } else if (parsedArray.length > CONFIG.performance.arraySize.max) {
        setCustomArrayError(`Maximum ${CONFIG.performance.arraySize.max} numbers allowed`);
        return false;
      } else {
        setCustomArrayError('');
        onParamChange({
          ...params,
          customArray: parsedArray,
          arraySize: parsedArray.length,
          useCustomArray: true
        });
        setState('idle');
        setStep(0);
        return true;
      }
    } catch (error) {
      setCustomArrayError('Please enter valid numbers separated by commas');
      return false;
    }
  };

  // Update custom array input when customArray changes
  useEffect(() => {
    if (customArray && customArray.length > 0) {
      setCustomArrayInput(customArray.join(', '));
    }
  }, [customArray]);

  return (
    <Box>
      {/* Information Section */}
      <InformationSection title={"Quick Sort"}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Quick Sort:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Quick Sort is a divide-and-conquer algorithm that picks an element as a pivot and partitions the array around the pivot. Random arrays contain values from {CONFIG.algorithm.arrayGeneration.randomValues.min} to {CONFIG.algorithm.arrayGeneration.randomValues.max}.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Best Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Average Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            - Worst Case: O(n²) when the array is already sorted
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Space Complexity:
          </Typography>
          <Typography variant="body2">
            O(log n) - Quick Sort requires space for the recursive call stack
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'arraySize',
            type: 'slider',
            label: 'Array Size',
            min: CONFIG.performance.arraySize.min,
            max: CONFIG.performance.arraySize.max,
            step: 1,
            defaultValue: arraySize,
            icon: ViewArrayIcon,
            disabled: useCustomArray
          },
          {
            name: 'randomize',
            type: 'switch',
            label: 'Randomize Array',
            defaultValue: randomize,
            icon: ShuffleIcon,
            disabled: useCustomArray
          },
          {
            name: 'useCustomArray',
            type: 'switch',
            label: 'Use Custom Array',
            defaultValue: useCustomArray,
            icon: FormatListNumberedIcon
          },
          {
            name: 'customArrayInput',
            type: 'customArray',
            label: 'Custom Array',
            showOnlyWhen: 'useCustomArray',
            error: customArrayError,
            helperText: `Enter comma-separated numbers (e.g., 5, 3, 8, 1). Values can range from ${CONFIG.algorithm.arrayGeneration.randomValues.min} to ${CONFIG.algorithm.arrayGeneration.randomValues.max}. Maximum ${CONFIG.performance.arraySize.max} numbers allowed.`,
            placeholder: `e.g., 5, 3, 8, 4, 2 (min ${CONFIG.performance.arraySize.min}, max ${CONFIG.performance.arraySize.max} numbers)`,
            onApply: applyCustomArray,
            icon: FormatListNumberedIcon
          }
        ]}
        values={{
          arraySize,
          randomize,
          useCustomArray,
          customArrayInput
        }}
        onChange={(newValues) => {
          if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize && !useCustomArray) {
            handleArraySizeChange(newValues.arraySize);
          }
          if (newValues.randomize !== undefined && newValues.randomize !== randomize && !useCustomArray) {
            handleRandomizeChange(newValues.randomize);
          }
          if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
            handleCustomArrayChange(newValues.useCustomArray);
          }
          if (newValues.customArrayInput !== undefined) {
            handleCustomArrayInputChange(newValues.customArrayInput);
          }
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setStep(0);
          setTimeout(() => {
            setState('idle');
          }, 50);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            setStep(step + 1);
            if (step + 1 >= totalSteps) {
              setState('completed');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            if (state === 'completed') {
              setState('paused');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.movement || ''
        }))}
        currentStep={step}
        defaultExpanded
        renderStep={(_, index) => {
          const currentStep = steps && steps[index];
          const isCurrentStep = index === step - 1;

          if (!currentStep) return null;

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
                mb: 0.75,
                pb: 0.75,
                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  bgcolor: 'action.hover',
                  borderRadius: '4px',
                }
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '24px',
                  height: '24px',
                  borderRadius: '12px',
                  bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                  mr: 1.5,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                  boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {index + 1}
              </Box>
              {currentStep.movement || 'Processing...'}
            </Typography>
          );
        }}
        emptyMessage="No steps yet. Start the algorithm to see the sequence."
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Quick Sort Algorithm"
        defaultExpanded
        currentStep={step > 0 && steps && steps.length > 0 ?
          // Map the detailed step types to corresponding line numbers
          steps[step - 1]?.type === 'initial' ? 0 :
            steps[step - 1]?.type === 'start_partition' ? 1 :
              steps[step - 1]?.type === 'select_pivot' ? 3 :
                steps[step - 1]?.type === 'initialize_pointers' ? 4 :
                  steps[step - 1]?.type === 'examine_element' ? 5 :
                    steps[step - 1]?.type === 'compare_with_pivot' ? 6 :
                      steps[step - 1]?.type === 'move_left_boundary' ? 7 :
                        steps[step - 1]?.type === 'swap_to_left_partition' || steps[step - 1]?.type === 'swapped_to_left_partition' ? 8 :
                          steps[step - 1]?.type === 'already_in_left_partition' || steps[step - 1]?.type === 'stays_in_right_partition' ? 9 :
                            steps[step - 1]?.type === 'partition_state' ? 10 :
                              steps[step - 1]?.type === 'place_pivot' || steps[step - 1]?.type === 'pivot_placed' ? 11 :
                                steps[step - 1]?.type === 'pivot_sorted' ? 12 :
                                  steps[step - 1]?.type === 'partition_complete' ? 13 :
                                    steps[step - 1]?.type === 'recurse_left' ? 14 :
                                      steps[step - 1]?.type === 'recurse_right' ? 15 :
                                        steps[step - 1]?.type === 'single_element_sorted' ? 2 :
                                          steps[step - 1]?.type === 'complete' ? 16 : 0
          : 0
        }
        algorithm={[
          { code: "function detailedQuickSort(arr, low, high):", lineNumber: 0, indent: 0 },
          { code: "# Start partitioning array segment", lineNumber: 1, indent: 1 },
          { code: "if low >= high: return  # Single element or empty", lineNumber: 2, indent: 1 },
          { code: "pivot = arr[high]  # Select last element as pivot", lineNumber: 3, indent: 1 },
          { code: "i = low - 1  # Initialize left partition boundary", lineNumber: 4, indent: 1 },
          { code: "for j in range(low, high):  # Examine each element", lineNumber: 5, indent: 1 },
          { code: "if arr[j] < pivot:  # Compare with pivot", lineNumber: 6, indent: 2 },
          { code: "i += 1  # Move left boundary", lineNumber: 7, indent: 3 },
          { code: "swap arr[i], arr[j]  # Swap to left partition", lineNumber: 8, indent: 3 },
          { code: "else: # Element stays in right partition", lineNumber: 9, indent: 2 },
          { code: "# Show current partition state", lineNumber: 10, indent: 2 },
          { code: "swap arr[i+1], arr[high]  # Place pivot correctly", lineNumber: 11, indent: 1 },
          { code: "# Mark pivot as sorted in final position", lineNumber: 12, indent: 1 },
          { code: "# Partition complete: Left | Pivot | Right", lineNumber: 13, indent: 1 },
          { code: "detailedQuickSort(arr, low, i)  # Sort left partition", lineNumber: 14, indent: 1 },
          { code: "detailedQuickSort(arr, i+2, high)  # Sort right partition", lineNumber: 15, indent: 1 },
          { code: "# Algorithm complete", lineNumber: 16, indent: 1 }
        ]}
      />
    </Box>
  );
};

export default QuickSortController;
