// BFSAlgorithm.js
// This file contains the implementation of Breadth-First Search algorithm and the visualization component.

import React from 'react';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    background: isDark ? '#1e1e1e' : '#f5f5f5',
    text: isDark ? '#d4d4d4' : '#333333',
    comment: isDark ? '#6a9955' : '#008000',
    keyword: isDark ? '#569cd6' : '#0000ff',
    string: isDark ? '#ce9178' : '#a31515',
    number: isDark ? '#b5cea8' : '#098658',
    function: isDark ? '#dcdcaa' : '#795e26',
    variable: isDark ? '#9cdcfe' : '#001080',
    property: isDark ? '#4fc1ff' : '#0070c1',
    operator: isDark ? '#d4d4d4' : '#000000',
    punctuation: isDark ? '#d4d4d4' : '#000000',
    border: isDark ? '#444444' : '#cccccc',
    highlight: isDark ? 'rgba(14, 99, 156, 0.3)' : 'rgba(173, 214, 255, 0.3)',
    activeHighlight: isDark ? 'rgba(14, 99, 156, 0.8)' : 'rgba(0, 120, 215, 0.4)',
    stepBackground: isDark ? '#252525' : '#f0f0f0',
  };
};

/**
 * Generate a graph with random connections
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @returns {Object} - Graph representation
 */
export const generateRandomGraph = (numNodes, density = 0.5) => {
  // Create an empty adjacency list
  const graph = {};
  
  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = [];
  }
  
  // Add random edges
  for (let i = 0; i < numNodes; i++) {
    for (let j = 0; j < numNodes; j++) {
      if (i !== j && Math.random() < density) {
        graph[i].push(j);
      }
    }
  }
  
  // Ensure the graph is connected
  for (let i = 0; i < numNodes - 1; i++) {
    // If there's no edge from i to i+1, add one
    if (!graph[i].includes(i+1)) {
      graph[i].push(i+1);
    }
    // If there's no edge from i+1 to i, add one
    if (!graph[i+1].includes(i)) {
      graph[i+1].push(i);
    }
  }
  
  return graph;
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edges - Array of edges [from, to]
 * @returns {Object} - Graph representation
 */
export const generateCustomGraph = (numNodes, edges) => {
  // Create an empty adjacency list
  const graph = {};
  
  // Initialize nodes
  for (let i = 0; i < numNodes; i++) {
    graph[i] = [];
  }
  
  // Add edges
  edges.forEach(([from, to]) => {
    if (from >= 0 && from < numNodes && to >= 0 && to < numNodes) {
      if (!graph[from].includes(to)) {
        graph[from].push(to);
      }
    }
  });
  
  return graph;
};

/**
 * Generate steps for BFS algorithm visualization
 * @param {Object} graph - Graph representation (adjacency list)
 * @param {number} startNode - Starting node
 * @param {number} targetNode - Target node (optional)
 * @returns {Object} - Object containing steps and path
 */
export const generateBFSSteps = (graph, startNode, targetNode = null) => {
  const steps = [];
  const nodes = Object.keys(graph).map(Number);
  
  // Initialize visited array and queue
  const visited = new Array(nodes.length).fill(false);
  const queue = [];
  const parent = {};
  
  // Add initialization step
  steps.push({
    type: 'initialize',
    visited: [...visited],
    queue: [...queue],
    current: null,
    movement: `Initialize BFS: Start from node ${startNode}`
  });
  
  // Mark the start node as visited and enqueue it
  visited[startNode] = true;
  queue.push(startNode);
  
  // Add enqueue step
  steps.push({
    type: 'enqueue',
    visited: [...visited],
    queue: [...queue],
    current: startNode,
    movement: `Enqueue start node ${startNode} and mark it as visited`
  });
  
  // Main BFS loop
  while (queue.length > 0) {
    // Dequeue a node
    const current = queue.shift();
    
    // Add dequeue step
    steps.push({
      type: 'dequeue',
      visited: [...visited],
      queue: [...queue],
      current,
      movement: `Dequeue node ${current} from the queue`
    });
    
    // If we found the target node, we can stop
    if (targetNode !== null && current === targetNode) {
      steps.push({
        type: 'found',
        visited: [...visited],
        queue: [...queue],
        current,
        movement: `Found target node ${targetNode}!`
      });
      break;
    }
    
    // Visit all adjacent nodes
    for (const neighbor of graph[current]) {
      // Add check step
      steps.push({
        type: 'check',
        visited: [...visited],
        queue: [...queue],
        current,
        neighbor,
        movement: `Check neighbor ${neighbor} of node ${current}`
      });
      
      // If the neighbor is not visited, mark it as visited and enqueue it
      if (!visited[neighbor]) {
        visited[neighbor] = true;
        queue.push(neighbor);
        parent[neighbor] = current;
        
        // Add enqueue step
        steps.push({
          type: 'enqueue',
          visited: [...visited],
          queue: [...queue],
          current,
          neighbor,
          movement: `Enqueue neighbor ${neighbor} and mark it as visited`
        });
        
        // If we found the target node, we can stop
        if (targetNode !== null && neighbor === targetNode) {
          steps.push({
            type: 'found',
            visited: [...visited],
            queue: [...queue],
            current: neighbor,
            movement: `Found target node ${targetNode}!`
          });
          break;
        }
      } else {
        // Add skip step
        steps.push({
          type: 'skip',
          visited: [...visited],
          queue: [...queue],
          current,
          neighbor,
          movement: `Skip neighbor ${neighbor} as it is already visited`
        });
      }
    }
    
    // If we found the target node, we can stop
    if (targetNode !== null && steps[steps.length - 1].type === 'found') {
      break;
    }
  }
  
  // Add final step
  if (steps[steps.length - 1].type !== 'found') {
    if (targetNode !== null) {
      steps.push({
        type: 'complete',
        visited: [...visited],
        queue: [...queue],
        movement: `BFS complete. Target node ${targetNode} not found.`
      });
    } else {
      steps.push({
        type: 'complete',
        visited: [...visited],
        queue: [...queue],
        movement: `BFS complete. Visited all reachable nodes from ${startNode}.`
      });
    }
  }
  
  // Reconstruct the path if target node is found
  let path = [];
  if (targetNode !== null && visited[targetNode]) {
    let current = targetNode;
    while (current !== undefined) {
      path.unshift(current);
      current = parent[current];
    }
  }
  
  return { steps, visited, path };
};

/**
 * BFS Algorithm visualization component
 */
const BFSAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();
  
  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);
  
  // Define the steps of the algorithm
  const steps = [
    { line: 0, description: "Initialize BFS: Create visited array and queue" },
    { line: 1, description: "Mark start node as visited and enqueue it" },
    { line: 2, description: "While queue is not empty" },
    { line: 3, description: "Dequeue a node from the queue" },
    { line: 4, description: "If current node is the target, we've found it" },
    { line: 5, description: "Visit all adjacent nodes of current node" },
    { line: 6, description: "Check if neighbor is not visited" },
    { line: 7, description: "Mark neighbor as visited and enqueue it" },
    { line: 8, description: "If neighbor is the target, we've found it" },
    { line: 9, description: "BFS complete: All reachable nodes visited" },
  ];
  
  // Get the current step
  const currentStep = steps[Math.min(step % steps.length, steps.length - 1)];
  
  // BFS pseudocode
  const pseudocode = [
    { code: "function bfs(graph, startNode, targetNode):", highlight: currentStep.line === 0 },
    { code: "  // Initialize visited array and queue", highlight: false },
    { code: "  visited = new Array(graph.length).fill(false)", highlight: currentStep.line === 0 },
    { code: "  queue = []", highlight: currentStep.line === 0 },
    { code: "  parent = {}", highlight: currentStep.line === 0 },
    { code: "", highlight: false },
    { code: "  // Mark the start node as visited and enqueue it", highlight: false },
    { code: "  visited[startNode] = true", highlight: currentStep.line === 1 },
    { code: "  queue.push(startNode)", highlight: currentStep.line === 1 },
    { code: "", highlight: false },
    { code: "  // Main BFS loop", highlight: false },
    { code: "  while queue is not empty:", highlight: currentStep.line === 2 },
    { code: "    // Dequeue a node", highlight: false },
    { code: "    current = queue.shift()", highlight: currentStep.line === 3 },
    { code: "", highlight: false },
    { code: "    // If we found the target node, we can stop", highlight: false },
    { code: "    if targetNode is not null and current equals targetNode:", highlight: currentStep.line === 4 },
    { code: "      return reconstructPath(parent, targetNode)", highlight: currentStep.line === 4 },
    { code: "", highlight: false },
    { code: "    // Visit all adjacent nodes", highlight: false },
    { code: "    for each neighbor of current:", highlight: currentStep.line === 5 },
    { code: "      // If the neighbor is not visited", highlight: false },
    { code: "      if not visited[neighbor]:", highlight: currentStep.line === 6 },
    { code: "        // Mark it as visited and enqueue it", highlight: false },
    { code: "        visited[neighbor] = true", highlight: currentStep.line === 7 },
    { code: "        queue.push(neighbor)", highlight: currentStep.line === 7 },
    { code: "        parent[neighbor] = current", highlight: currentStep.line === 7 },
    { code: "", highlight: false },
    { code: "        // If we found the target node, we can stop", highlight: false },
    { code: "        if targetNode is not null and neighbor equals targetNode:", highlight: currentStep.line === 8 },
    { code: "          return reconstructPath(parent, targetNode)", highlight: currentStep.line === 8 },
    { code: "", highlight: false },
    { code: "  // If we get here, we've visited all reachable nodes", highlight: false },
    { code: "  return { visited, path: [] }", highlight: currentStep.line === 9 },
  ];
  
  return (
    <Box
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
        '&::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
          },
        },
      }}
    >
      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {currentStep?.description || "Algorithm complete"}
        </Typography>
      </Paper>

      {/* Pseudocode */}
      <Box
        sx={{
          fontFamily: "monospace",
          fontSize: "0.9rem",
          lineHeight: 1.5,
          whiteSpace: "pre",
          p: 1,
          overflowX: "auto",
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        {pseudocode.map((line, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              bgcolor: line.highlight ? colors.highlight : "transparent",
              borderRadius: 1,
              px: 1,
              py: 0.25,
            }}
          >
            {line.highlight && (
              <ArrowRightIcon
                sx={{
                  color: colors.function,
                  mr: 1,
                  fontSize: "1.2rem",
                }}
              />
            )}
            <Box sx={{ ml: line.highlight ? 0 : 3 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  color: colors.text,
                  fontWeight: line.highlight ? 600 : 400,
                }}
              >
                {line.code}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default BFSAlgorithm;
