# Algorithms Implementation Guide

This directory contains all the algorithm implementations for the visualization project. Each algorithm is organized in its own folder with a consistent structure.

## Directory Structure

```
src/algorithms/
├── AlgorithmName/
│   ├── AlgorithmNameAlgorithm.js  # Core algorithm logic and step generation
│   ├── AlgorithmNameVisualization.js  # 3D visualization component
│   └── AlgorithmNameController.js  # UI controls and parameters
├── AlgorithmRegistry.js  # Central registry for all algorithms
└── README.md  # This file
```

## Implemented Algorithms

### Towers of Hanoi

A classic recursive algorithm that demonstrates the solution to the Towers of Hanoi puzzle.

**Implementation Details:**
- Recursive solution with step-by-step visualization
- Configurable number of disks
- 3D visualization of disk movements between three towers

**Files:**
- `TowersOfHanoi/TowersOfHanoiAlgorithm.js`
- `TowersOfHanoi/TowersOfHanoiVisualization.js`
- `TowersOfHanoi/TowersOfHanoiController.js`

### Bubble Sort

A simple sorting algorithm that repeatedly steps through the list, compares adjacent elements, and swaps them if they are in the wrong order.

**Implementation Details:**
- O(n²) time complexity
- In-place sorting with minimal space requirements
- Visualization shows comparisons and swaps

**Files:**
- `BubbleSort/BubbleSortAlgorithm.js`
- `BubbleSort/BubbleSortVisualization.js`
- `BubbleSort/BubbleSortController.js`

### Merge Sort

A divide-and-conquer sorting algorithm that divides the input array into two halves, recursively sorts them, and then merges the sorted halves.

**Implementation Details:**
- O(n log n) time complexity
- Visualization shows the divide, sort, and merge steps
- Recursive implementation with clear step tracking

**Files:**
- `MergeSort/MergeSortAlgorithm.js`
- `MergeSort/MergeSortVisualization.js`
- `MergeSort/MergeSortController.js`

### Quick Sort

A divide-and-conquer sorting algorithm that picks a pivot element and partitions the array around the pivot.

**Implementation Details:**
- Average O(n log n) time complexity
- In-place partitioning
- Visualization shows pivot selection, partitioning, and recursive sorting

**Files:**
- `QuickSort/QuickSortAlgorithm.js`
- `QuickSort/QuickSortVisualization.js`
- `QuickSort/QuickSortController.js`

**Known Issues:**
- Array size changes don't properly reset the visualization
- Steps count doesn't update correctly when array size changes
- Animation and state management need improvement for array size changes

### Longest Common Subsequence (LCS)

A dynamic programming algorithm that finds the longest subsequence common to two sequences.

**Implementation Details:**
- O(m*n) time and space complexity
- 2D table visualization showing the dynamic programming approach
- Traceback visualization to find the actual subsequence
- Configurable input strings

**Files:**
- `LCS/LCSAlgorithm.js`
- `LCS/LCSVisualization.js`
- `LCS/LCSController.js`

### Floyd-Warshall Algorithm

An all-pairs shortest path algorithm that works on weighted graphs, including those with negative edge weights (but not negative cycles).

**Implementation Details:**
- O(V³) time complexity
- O(V²) space complexity
- Visualization shows both the graph and the distance matrix
- Highlights intermediate vertices and path comparisons
- Detects negative cycles
- Configurable graph parameters (vertices, density, weights)

**Files:**
- `FloydWarshall/FloydWarshallAlgorithm.js`
- `FloydWarshall/FloydWarshallVisualization.js`
- `FloydWarshall/FloydWarshallController.js`

## Adding a New Algorithm

To add a new algorithm to the project:

1. Create a new folder with the algorithm name: `src/algorithms/NewAlgorithm/`

2. Create the three required files:
   - `NewAlgorithmAlgorithm.js` - Core logic and step generation
   - `NewAlgorithmVisualization.js` - 3D visualization
   - `NewAlgorithmController.js` - UI controls

3. Register the algorithm in `AlgorithmRegistry.js`:
   ```javascript
   import NewAlgorithmVisualization from './NewAlgorithm/NewAlgorithmVisualization';
   import NewAlgorithmController from './NewAlgorithm/NewAlgorithmController';

   // Add to algorithms object
   const algorithms = {
     // ... existing algorithms
     NewAlgorithm: {
       name: "New Algorithm",
       visualization: NewAlgorithmVisualization,
       controller: NewAlgorithmController,
       defaultParams: {
         // Default parameters for your algorithm
         arraySize: 10,
         speed: 5,
         // ... other parameters
       }
     }
   };
   ```

## Implementation Guidelines

### Algorithm Logic (`*Algorithm.js`)

- Implement the core algorithm logic
- Generate steps for visualization with clear state at each step
- Include pseudocode or explanation for educational purposes
- Return an object with steps array and any other necessary data

Example step generation:
```javascript
export const generateAlgorithmSteps = (input) => {
  const steps = [];

  // Initial state
  steps.push({
    type: 'init',
    state: [...input],
    // Other relevant data
  });

  // Algorithm implementation with step tracking
  // ...

  // Final state
  steps.push({
    type: 'complete',
    state: [...result],
    // Other relevant data
  });

  return { steps, result };
};
```

### Visualization Component (`*Visualization.js`)

- Use React Three Fiber for 3D rendering
- Implement animations for algorithm steps
- Handle camera positioning and scene setup
- Respond to state changes (play, pause, reset)

### Controller Component (`*Controller.js`)

- Implement UI controls for the algorithm
- Handle parameter changes (array size, speed, etc.)
- Provide step navigation (next, previous, play, pause)
- Display algorithm information and current state
