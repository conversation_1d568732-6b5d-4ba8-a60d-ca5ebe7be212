// PrimalityTestAlgorithm.js
// Implementation of various primality test algorithms

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for the primality test algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generatePrimalityTestSteps = (params) => {
  console.log('generatePrimalityTestSteps called with params:', params);
  const { number = 17, method = 'trial_division' } = params;

  const steps = [];
  let isPrime = false;

  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize primality test for number ${number} using ${getMethodName(method)} method.`,
    number,
    method,
    currentDivisor: null,
    isPrime: null,
    progressStep: 'init',
    pseudocodeLine: 1
  });

  // Perform primality test using the selected method
  if (method === 'trial_division') {
    isPrime = trialDivision(number, steps);
  } else if (method === 'fermat') {
    isPrime = fermatTest(number, steps);
  } else if (method === 'miller_rabin') {
    isPrime = millerRabinTest(number, steps);
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `${number} is ${isPrime ? 'prime' : 'not prime'}.`,
    number,
    method,
    currentDivisor: null,
    isPrime,
    progressStep: 'complete',
    pseudocodeLine: 0
  });

  return { steps, isPrime };
};

/**
 * Get the human-readable name of the primality test method
 * @param {String} method - Method identifier
 * @returns {String} - Human-readable method name
 */
const getMethodName = (method) => {
  switch (method) {
    case 'trial_division':
      return 'Trial Division';
    case 'fermat':
      return 'Fermat Primality Test';
    case 'miller_rabin':
      return 'Miller-Rabin Primality Test';
    default:
      return method;
  }
};

/**
 * Perform primality test using trial division
 * @param {Number} number - Number to test
 * @param {Array} steps - Array to store steps
 * @returns {Boolean} - Whether the number is prime
 */
const trialDivision = (number, steps) => {
  // Handle special cases
  if (number <= 1) {
    steps.push({
      type: 'special_case',
      message: `${number} is not prime because it is less than or equal to 1.`,
      number,
      method: 'trial_division',
      currentDivisor: null,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return false;
  }

  if (number <= 3) {
    steps.push({
      type: 'special_case',
      message: `${number} is prime because it is 2 or 3.`,
      number,
      method: 'trial_division',
      currentDivisor: null,
      isPrime: true,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return true;
  }

  // Check if number is divisible by 2 or 3
  if (number % 2 === 0) {
    steps.push({
      type: 'divisible',
      message: `${number} is divisible by 2, so it is not prime.`,
      number,
      method: 'trial_division',
      currentDivisor: 2,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 5
    });

    return false;
  }

  if (number % 3 === 0) {
    steps.push({
      type: 'divisible',
      message: `${number} is divisible by 3, so it is not prime.`,
      number,
      method: 'trial_division',
      currentDivisor: 3,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 5
    });

    return false;
  }

  // Check all numbers of the form 6k ± 1 up to sqrt(number)
  const limit = Math.floor(Math.sqrt(number));

  steps.push({
    type: 'info',
    message: `We only need to check divisors up to the square root of ${number}, which is approximately ${limit.toFixed(2)}.`,
    number,
    method: 'trial_division',
    currentDivisor: null,
    isPrime: null,
    progressStep: 'process',
    pseudocodeLine: 7
  });

  for (let i = 5; i <= limit; i += 6) {
    // Check if number is divisible by i
    steps.push({
      type: 'check',
      message: `Checking if ${number} is divisible by ${i}...`,
      number,
      method: 'trial_division',
      currentDivisor: i,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 8
    });

    if (number % i === 0) {
      steps.push({
        type: 'divisible',
        message: `${number} is divisible by ${i}, so it is not prime.`,
        number,
        method: 'trial_division',
        currentDivisor: i,
        isPrime: false,
        progressStep: 'process',
        pseudocodeLine: 9
      });

      return false;
    }

    // Check if number is divisible by i + 2
    steps.push({
      type: 'check',
      message: `Checking if ${number} is divisible by ${i + 2}...`,
      number,
      method: 'trial_division',
      currentDivisor: i + 2,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 8
    });

    if (number % (i + 2) === 0) {
      steps.push({
        type: 'divisible',
        message: `${number} is divisible by ${i + 2}, so it is not prime.`,
        number,
        method: 'trial_division',
        currentDivisor: i + 2,
        isPrime: false,
        progressStep: 'process',
        pseudocodeLine: 9
      });

      return false;
    }
  }

  // If we reach here, the number is prime
  steps.push({
    type: 'prime',
    message: `${number} is not divisible by any number up to ${limit}, so it is prime.`,
    number,
    method: 'trial_division',
    currentDivisor: null,
    isPrime: true,
    progressStep: 'process',
    pseudocodeLine: 12
  });

  return true;
};

/**
 * Perform primality test using Fermat's Little Theorem
 * @param {Number} number - Number to test
 * @param {Array} steps - Array to store steps
 * @returns {Boolean} - Whether the number is probably prime
 */
const fermatTest = (number, steps) => {
  // Handle special cases
  if (number <= 1) {
    steps.push({
      type: 'special_case',
      message: `${number} is not prime because it is less than or equal to 1.`,
      number,
      method: 'fermat',
      currentDivisor: null,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return false;
  }

  if (number <= 3) {
    steps.push({
      type: 'special_case',
      message: `${number} is prime because it is 2 or 3.`,
      number,
      method: 'fermat',
      currentDivisor: null,
      isPrime: true,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return true;
  }

  // Fermat's test: if a^(n-1) ≡ 1 (mod n) for several random values of a, then n is probably prime
  const numTests = Math.min(5, number - 2); // Number of random witnesses to test

  steps.push({
    type: 'info',
    message: `Fermat's Little Theorem states that if n is prime, then for any integer a where 1 < a < n, a^(n-1) = 1 (mod n).`,
    number,
    method: 'fermat',
    currentDivisor: null,
    isPrime: null,
    progressStep: 'process',
    pseudocodeLine: 5
  });

  steps.push({
    type: 'info',
    message: `We will test ${numTests} random values of a to check if ${number} is probably prime.`,
    number,
    method: 'fermat',
    currentDivisor: null,
    isPrime: null,
    progressStep: 'process',
    pseudocodeLine: 6
  });

  // Use a deterministic set of witnesses for demonstration purposes
  const witnesses = [];
  for (let i = 0; i < numTests; i++) {
    // Choose witnesses deterministically for visualization
    witnesses.push(2 + i);
  }

  for (let i = 0; i < witnesses.length; i++) {
    const a = witnesses[i];

    steps.push({
      type: 'witness',
      message: `Testing witness a = ${a}...`,
      number,
      method: 'fermat',
      currentDivisor: a,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 8
    });

    // Calculate a^(n-1) mod n
    const power = number - 1;
    const result = modPow(a, power, number);

    steps.push({
      type: 'calculation',
      message: `Calculating ${a}^(${number}-1) mod ${number} = ${a}^${power} mod ${number} = ${result}`,
      number,
      method: 'fermat',
      currentDivisor: a,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 9
    });

    if (result !== 1) {
      steps.push({
        type: 'composite',
        message: `${a}^(${number}-1) mod ${number} = ${result}, which is not 1. Therefore, ${number} is not prime.`,
        number,
        method: 'fermat',
        currentDivisor: a,
        isPrime: false,
        progressStep: 'process',
        pseudocodeLine: 10
      });

      return false;
    }
  }

  // If all tests pass, the number is probably prime
  steps.push({
    type: 'probably_prime',
    message: `All ${numTests} Fermat tests passed. ${number} is probably prime.`,
    number,
    method: 'fermat',
    currentDivisor: null,
    isPrime: true,
    progressStep: 'process',
    pseudocodeLine: 13
  });

  return true;
};

/**
 * Perform primality test using Miller-Rabin test
 * @param {Number} number - Number to test
 * @param {Array} steps - Array to store steps
 * @returns {Boolean} - Whether the number is probably prime
 */
const millerRabinTest = (number, steps) => {
  // Handle special cases
  if (number <= 1) {
    steps.push({
      type: 'special_case',
      message: `${number} is not prime because it is less than or equal to 1.`,
      number,
      method: 'miller_rabin',
      currentDivisor: null,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return false;
  }

  if (number <= 3) {
    steps.push({
      type: 'special_case',
      message: `${number} is prime because it is 2 or 3.`,
      number,
      method: 'miller_rabin',
      currentDivisor: null,
      isPrime: true,
      progressStep: 'process',
      pseudocodeLine: 3
    });

    return true;
  }

  if (number % 2 === 0) {
    steps.push({
      type: 'divisible',
      message: `${number} is divisible by 2, so it is not prime.`,
      number,
      method: 'miller_rabin',
      currentDivisor: 2,
      isPrime: false,
      progressStep: 'process',
      pseudocodeLine: 5
    });

    return false;
  }

  // Write n-1 as 2^r * d where d is odd
  let r = 0;
  let d = number - 1;

  while (d % 2 === 0) {
    d /= 2;
    r++;
  }

  steps.push({
    type: 'info',
    message: `Express ${number}-1 as 2^r * d where d is odd: ${number}-1 = 2^${r} * ${d}`,
    number,
    method: 'miller_rabin',
    currentDivisor: null,
    isPrime: null,
    progressStep: 'process',
    pseudocodeLine: 7
  });

  // Number of witnesses to test
  const numTests = Math.min(4, number - 4);

  steps.push({
    type: 'info',
    message: `We will test ${numTests} random witnesses to check if ${number} is probably prime.`,
    number,
    method: 'miller_rabin',
    currentDivisor: null,
    isPrime: null,
    progressStep: 'process',
    pseudocodeLine: 8
  });

  // Use a deterministic set of witnesses for demonstration purposes
  const witnesses = [];
  for (let i = 0; i < numTests; i++) {
    // Choose witnesses deterministically for visualization
    witnesses.push(2 + i);
  }

  for (let i = 0; i < witnesses.length; i++) {
    const a = witnesses[i];

    steps.push({
      type: 'witness',
      message: `Testing witness a = ${a}...`,
      number,
      method: 'miller_rabin',
      currentDivisor: a,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 10
    });

    // Calculate a^d mod n
    let x = modPow(a, d, number);

    steps.push({
      type: 'calculation',
      message: `Calculating ${a}^${d} mod ${number} = ${x}`,
      number,
      method: 'miller_rabin',
      currentDivisor: a,
      isPrime: null,
      progressStep: 'process',
      pseudocodeLine: 11
    });

    if (x === 1 || x === number - 1) {
      steps.push({
        type: 'continue',
        message: `${a}^${d} mod ${number} = ${x}, which is either 1 or ${number}-1. Continue with the next witness.`,
        number,
        method: 'miller_rabin',
        currentDivisor: a,
        isPrime: null,
        progressStep: 'process',
        pseudocodeLine: 12
      });

      continue;
    }

    let isProbablyPrime = false;

    for (let j = 0; j < r - 1; j++) {
      x = modPow(x, 2, number);

      steps.push({
        type: 'calculation',
        message: `Calculating x^2 mod ${number} = ${x}`,
        number,
        method: 'miller_rabin',
        currentDivisor: a,
        isPrime: null,
        progressStep: 'process',
        pseudocodeLine: 15
      });

      if (x === number - 1) {
        steps.push({
          type: 'continue',
          message: `x = ${x}, which is ${number}-1. Continue with the next witness.`,
          number,
          method: 'miller_rabin',
          currentDivisor: a,
          isPrime: null,
          progressStep: 'process',
          pseudocodeLine: 16
        });

        isProbablyPrime = true;
        break;
      }
    }

    if (!isProbablyPrime) {
      steps.push({
        type: 'composite',
        message: `Witness ${a} shows that ${number} is not prime.`,
        number,
        method: 'miller_rabin',
        currentDivisor: a,
        isPrime: false,
        progressStep: 'process',
        pseudocodeLine: 19
      });

      return false;
    }
  }

  // If all tests pass, the number is probably prime
  steps.push({
    type: 'probably_prime',
    message: `All ${numTests} Miller-Rabin tests passed. ${number} is probably prime.`,
    number,
    method: 'miller_rabin',
    currentDivisor: null,
    isPrime: true,
    progressStep: 'process',
    pseudocodeLine: 22
  });

  return true;
};

/**
 * Calculate (base^exponent) % modulus efficiently
 * @param {Number} base - Base
 * @param {Number} exponent - Exponent
 * @param {Number} modulus - Modulus
 * @returns {Number} - Result of (base^exponent) % modulus
 */
const modPow = (base, exponent, modulus) => {
  if (modulus === 1) return 0;

  let result = 1;
  base = base % modulus;

  while (exponent > 0) {
    if (exponent % 2 === 1) {
      result = (result * base) % modulus;
    }

    exponent = Math.floor(exponent / 2);
    base = (base * base) % modulus;
  }

  return result;
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',

    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const PrimalityTestAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;

  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Determine which pseudocode to show based on the method
  const method = currentAlgorithmStep?.method || 'trial_division';

  // Pseudocode for Trial Division
  const trialDivisionPseudocode = [
    { line: 'function isPrime_trialDivision(n):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Handle special cases', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if n <= 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    if n <= 3:', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '        return true', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    // Check divisibility by small primes', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    if n % 2 == 0 or n % 3 == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '    // Check all numbers of form 6k ± 1 up to sqrt(n)', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '    i = 5', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '    while i * i <= n:', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '        if n % i == 0 or n % (i + 2) == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '            return false', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '        i += 6', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '    return true', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
  ];

  // Pseudocode for Fermat Primality Test
  const fermatPseudocode = [
    { line: 'function isPrime_fermat(n, k):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Handle special cases', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if n <= 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    if n <= 3:', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '        return true', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    // Fermat\'s test: if a^(n-1) ≡ 1 (mod n) for several random a, n is probably prime', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    for i = 1 to k:', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '        a = random(2, n-2)', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '        if modPow(a, n-1, n) != 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '            return false', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '    // If all tests pass, n is probably prime', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '    return true', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
  ];

  // Pseudocode for Miller-Rabin Primality Test
  const millerRabinPseudocode = [
    { line: 'function isPrime_millerRabin(n, k):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Handle special cases', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if n <= 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    if n <= 3:', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '        return true', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    if n % 2 == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    // Write n-1 as 2^r * d where d is odd', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '    r = 0, d = n - 1', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '    while d % 2 == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '        d /= 2', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '        r += 1', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '    // Witness loop', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '    for i = 1 to k:', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '        a = random(2, n-2)', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '        x = modPow(a, d, n)', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
    { line: '        if x == 1 or x == n-1:', highlight: currentAlgorithmStep?.pseudocodeLine === 18 },
    { line: '            continue', highlight: currentAlgorithmStep?.pseudocodeLine === 19 },
    { line: '        for j = 0 to r-1:', highlight: currentAlgorithmStep?.pseudocodeLine === 20 },
    { line: '            x = modPow(x, 2, n)', highlight: currentAlgorithmStep?.pseudocodeLine === 21 },
    { line: '            if x == n-1:', highlight: currentAlgorithmStep?.pseudocodeLine === 22 },
    { line: '                break', highlight: currentAlgorithmStep?.pseudocodeLine === 23 },
    { line: '            if j == r-1:', highlight: currentAlgorithmStep?.pseudocodeLine === 24 },
    { line: '                return false', highlight: currentAlgorithmStep?.pseudocodeLine === 25 },
    { line: '    // If all tests pass, n is probably prime', highlight: currentAlgorithmStep?.pseudocodeLine === 26 },
    { line: '    return true', highlight: currentAlgorithmStep?.pseudocodeLine === 27 },
  ];

  // Choose the appropriate pseudocode
  let pseudocode;
  switch (method) {
    case 'fermat':
      pseudocode = fermatPseudocode;
      break;
    case 'miller_rabin':
      pseudocode = millerRabinPseudocode;
      break;
    default:
      pseudocode = trialDivisionPseudocode;
  }

  return (
    <Box sx={{
      p: 2,
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box
          key={index}
          sx={{
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography
            variant="body2"
            component="span"
            sx={{
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography
            variant="body2"
            component="span"
            sx={{
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default PrimalityTestAlgorithm;
