// StepContext.js
// This context provides state management for algorithm steps and their execution

import React, { createContext, useState, useContext, useCallback, useMemo } from 'react';

// Create the context
const StepContext = createContext();

/**
 * StepProvider - Provides state management for algorithm steps
 * 
 * This context manages:
 * - Step data storage
 * - Step navigation (next, previous)
 * - Step execution state
 * - Step metadata
 */
export const StepProvider = ({ children }) => {
  // Step state
  const [steps, setSteps] = useState([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [stepMetadata, setStepMetadata] = useState({});

  // Current step data
  const currentStep = useMemo(() => 
    steps.length > 0 && currentStepIndex >= 0 && currentStepIndex < steps.length 
      ? steps[currentStepIndex] 
      : null, 
    [steps, currentStepIndex]
  );

  // Total steps
  const totalSteps = useMemo(() => steps.length, [steps]);

  // Step navigation
  const goToStep = useCallback((index) => {
    if (index >= 0 && index < steps.length) {
      setCurrentStepIndex(index);
    }
  }, [steps.length]);

  const goToNextStep = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  }, [currentStepIndex, steps.length]);

  const goToPreviousStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  }, [currentStepIndex]);

  const goToFirstStep = useCallback(() => {
    setCurrentStepIndex(0);
  }, []);

  const goToLastStep = useCallback(() => {
    setCurrentStepIndex(steps.length - 1);
  }, [steps.length]);

  // Step animation
  const startStepAnimation = useCallback(() => {
    setIsAnimating(true);
  }, []);

  const stopStepAnimation = useCallback(() => {
    setIsAnimating(false);
  }, []);

  // Step metadata
  const updateStepMetadata = useCallback((metadata) => {
    setStepMetadata(prevMetadata => ({
      ...prevMetadata,
      ...metadata
    }));
  }, []);

  const getStepMetadata = useCallback((key) => {
    return stepMetadata[key];
  }, [stepMetadata]);

  // Create the value object
  const value = useMemo(() => ({
    // Step data
    steps,
    setSteps,
    currentStep,
    currentStepIndex,
    totalSteps,

    // Step navigation
    goToStep,
    goToNextStep,
    goToPreviousStep,
    goToFirstStep,
    goToLastStep,
    setCurrentStepIndex,

    // Step animation
    isAnimating,
    startStepAnimation,
    stopStepAnimation,

    // Step metadata
    stepMetadata,
    updateStepMetadata,
    getStepMetadata
  }), [
    steps, currentStep, currentStepIndex, totalSteps,
    goToStep, goToNextStep, goToPreviousStep, goToFirstStep, goToLastStep,
    isAnimating, startStepAnimation, stopStepAnimation,
    stepMetadata, updateStepMetadata, getStepMetadata
  ]);

  return (
    <StepContext.Provider value={value}>
      {children}
    </StepContext.Provider>
  );
};

/**
 * useStep - Custom hook to use the step context
 * 
 * @returns {Object} The step context value
 */
export const useStep = () => {
  const context = useContext(StepContext);
  if (context === undefined) {
    throw new Error('useStep must be used within a StepProvider');
  }
  return context;
};
