// LISAlgorithm.js
// Implementation of Longest Increasing Subsequence algorithm using dynamic programming

/**
 * Generate steps for solving the Longest Increasing Subsequence problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateLISSteps = (params) => {
  console.log('generateLISSteps called with params:', params);
  const { sequence = [10, 22, 9, 33, 21, 50, 41, 60, 80] } = params;
  const steps = [];

  // Validate input
  if (!Array.isArray(sequence) || sequence.length === 0) {
    throw new Error('Sequence must be a non-empty array');
  }

  const n = sequence.length;

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Longest Increasing Subsequence algorithm with sequence [${sequence.join(', ')}]`,
    sequence,
    dp: null,
    prev: null,
    currentIndex: null,
    currentJ: null,
    lis: null,
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table
  // dp[i] represents the length of the LIS ending at index i
  const dp = Array(n).fill(1); // Each element is at least a subsequence of length 1

  // Create array to track previous element in LIS
  const prev = Array(n).fill(-1);

  // Add DP table initialization step
  steps.push({
    type: 'initialize_table',
    message: `Initialize the DP table. dp[i] will store the length of the LIS ending at index i.`,
    sequence,
    dp: [...dp],
    prev: [...prev],
    currentIndex: null,
    currentJ: null,
    lis: null,
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  for (let i = 1; i < n; i++) {
    // Add step for current element
    steps.push({
      type: 'consider_element',
      message: `Consider element at index ${i}: ${sequence[i]}`,
      sequence,
      dp: [...dp],
      prev: [...prev],
      currentIndex: i,
      currentJ: null,
      lis: null,
      result: null,
      pseudocodeLine: 6
    });

    for (let j = 0; j < i; j++) {
      // Add step for comparing with previous element
      steps.push({
        type: 'compare_elements',
        message: `Compare with element at index ${j}: ${sequence[j]}`,
        sequence,
        dp: [...dp],
        prev: [...prev],
        currentIndex: i,
        currentJ: j,
        lis: null,
        result: null,
        pseudocodeLine: 7
      });

      // If current element is greater than previous element and we can extend the LIS
      if (sequence[i] > sequence[j]) {
        // Add step for potential update
        steps.push({
          type: 'potential_update',
          message: `Element ${sequence[i]} > ${sequence[j]}, check if we can extend the LIS`,
          sequence,
          dp: [...dp],
          prev: [...prev],
          currentIndex: i,
          currentJ: j,
          potentialLength: dp[j] + 1,
          currentLength: dp[i],
          lis: null,
          result: null,
          pseudocodeLine: 8
        });

        // If extending the LIS ending at j gives a longer LIS ending at i
        if (dp[j] + 1 > dp[i]) {
          dp[i] = dp[j] + 1;
          prev[i] = j;
          
          // Add step for updating LIS length
          steps.push({
            type: 'update_lis',
            message: `Update LIS length at index ${i} to ${dp[i]} by extending from index ${j}`,
            sequence,
            dp: [...dp],
            prev: [...prev],
            currentIndex: i,
            currentJ: j,
            lis: null,
            result: null,
            pseudocodeLine: 10
          });
        } else {
          // Add step for no update
          steps.push({
            type: 'no_update',
            message: `No update needed as current LIS length ${dp[i]} is already optimal`,
            sequence,
            dp: [...dp],
            prev: [...prev],
            currentIndex: i,
            currentJ: j,
            lis: null,
            result: null,
            pseudocodeLine: 12
          });
        }
      } else {
        // Add step for skipping element
        steps.push({
          type: 'skip_element',
          message: `Skip element at index ${j} as ${sequence[i]} <= ${sequence[j]}`,
          sequence,
          dp: [...dp],
          prev: [...prev],
          currentIndex: i,
          currentJ: j,
          lis: null,
          result: null,
          pseudocodeLine: 14
        });
      }
    }
  }

  // Find the maximum LIS length and its ending index
  let maxLength = 0;
  let maxIndex = 0;
  
  for (let i = 0; i < n; i++) {
    if (dp[i] > maxLength) {
      maxLength = dp[i];
      maxIndex = i;
    }
  }
  
  // Add step for finding maximum LIS length
  steps.push({
    type: 'find_max',
    message: `Find the maximum LIS length: ${maxLength} ending at index ${maxIndex}`,
    sequence,
    dp: [...dp],
    prev: [...prev],
    currentIndex: maxIndex,
    currentJ: null,
    maxLength,
    maxIndex,
    lis: null,
    result: maxLength,
    pseudocodeLine: 17
  });

  // Trace back to find the LIS
  const lis = [];
  let currentIndex = maxIndex;
  
  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    message: 'Start tracing back to find the LIS',
    sequence,
    dp: [...dp],
    prev: [...prev],
    currentIndex,
    currentJ: null,
    lis: [...lis],
    result: maxLength,
    pseudocodeLine: 19
  });

  // Trace back to find the LIS
  while (currentIndex !== -1) {
    lis.unshift(sequence[currentIndex]);
    
    // Add step for selecting element
    steps.push({
      type: 'select_element',
      message: `Select element ${sequence[currentIndex]} at index ${currentIndex}`,
      sequence,
      dp: [...dp],
      prev: [...prev],
      currentIndex,
      currentJ: null,
      lis: [...lis],
      result: maxLength,
      pseudocodeLine: 21
    });
    
    currentIndex = prev[currentIndex];
    
    // Add step for updating current index
    if (currentIndex !== -1) {
      steps.push({
        type: 'update_index',
        message: `Update current index to ${currentIndex}`,
        sequence,
        dp: [...dp],
        prev: [...prev],
        currentIndex,
        currentJ: null,
        lis: [...lis],
        result: maxLength,
        pseudocodeLine: 22
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Longest Increasing Subsequence length: ${maxLength}. LIS: [${lis.join(', ')}]`,
    sequence,
    dp: [...dp],
    prev: [...prev],
    currentIndex: null,
    currentJ: null,
    lis: [...lis],
    result: maxLength,
    pseudocodeLine: 25
  });

  return { 
    steps, 
    result: maxLength,
    lis
  };
};

// Create the algorithm object with helper functions
const LISAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateLISSteps
};

export default LISAlgorithm;
