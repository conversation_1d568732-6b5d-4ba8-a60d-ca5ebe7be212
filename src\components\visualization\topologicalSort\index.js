// Topological Sort visualization components index

export { default as TopologicalSortNode } from '../nodes/TopologicalSortNode';
export { default as TopologicalSortScene } from '../scenes/TopologicalSortScene';
export { default as TopologicalSortDemo } from '../demos/TopologicalSortDemo';

// Usage instructions:
/*
This module provides specialized components for Topological Sort visualization:

1. TopologicalSortNode: A specialized node component with state-based styling
   - Handles different states: visited, in stack, sorted, current, neighbor
   - Provides animations for current and neighbor nodes
   - Includes proper labeling

2. TopologicalSortScene: A complete scene for Topological Sort visualization
   - Renders nodes and directed edges
   - Handles proper positioning and state-based styling
   - Includes grid and fog for better depth perception

3. TopologicalSortDemo: A demo component showing how to use the scene
   - Provides a sample graph and simulation steps
   - Auto-advances through the steps
   - Shows how to update node states

Example usage:

import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { TopologicalSortScene } from './components/visualization/topologicalSort';

const MyVisualization = ({ 
  graphData, 
  nodePositions,
  visitedNodes,
  stackNodes,
  sortedNodes,
  currentNode,
  neighborNode,
  isDark
}) => {
  return (
    <Canvas camera={{ position: [0, 0, 15], fov: 50 }}>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={0.8} />
      
      <TopologicalSortScene
        graphData={graphData}
        nodePositions={nodePositions}
        visitedNodes={visitedNodes}
        stackNodes={stackNodes}
        sortedNodes={sortedNodes}
        currentNode={currentNode}
        neighborNode={neighborNode}
        isDark={isDark}
      />
      
      <OrbitControls />
    </Canvas>
  );
};
*/
