// AStarController.js
// This component provides the controls for A* algorithm visualization

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GraphIcon from '@mui/icons-material/AccountTree';
import TuneIcon from '@mui/icons-material/Tune';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'; // Used in customEdges parameter
import PlayArrowIcon from '@mui/icons-material/PlayArrow'; // For start node
import FlagIcon from '@mui/icons-material/Flag'; // For end node

// Import algorithm functions
import AStarAlgorithm from './AStarAlgorithm';

const AStarController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Graph parameters
    const [numNodes, setNumNodes] = useState(params?.nodes || 6);
    const [density, setDensity] = useState(params?.density || 0.5);
    const [startNode, setStartNode] = useState(params?.startNode || 0);
    const [endNode, setEndNode] = useState(params?.endNode || 5);
    const [useCustomGraph, setUseCustomGraph] = useState(params?.useCustomGraph === true);
    const [customEdges, setCustomEdges] = useState('');
    const [customEdgesError, setCustomEdgesError] = useState('');

    // Parse custom edges from string input
    const parseCustomEdges = useCallback((edgesText) => {
        const parsedEdges = [];
        const edgesRegex = /\[\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+(?:\.\d+)?)\s*\]/g;
        let match;

        while ((match = edgesRegex.exec(edgesText)) !== null) {
            const from = parseInt(match[1], 10);
            const to = parseInt(match[2], 10);
            const weight = parseFloat(match[3]);
            parsedEdges.push([from, to, weight]);
        }

        return parsedEdges;
    }, []);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { numNodes, density, startNode, endNode });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { numNodes, density, startNode, endNode, useCustomGraph });

        // Update params first
        onParamChange({
            nodes: numNodes,
            startNode,
            endNode,
            density,
            customEdges: useCustomGraph ? parseCustomEdges(customEdges) : [],
            useCustomGraph
        });

        // Set steps and movements directly
        try {
            const result = AStarAlgorithm.generateAStarSteps({
                nodes: numNodes,
                startNode,
                endNode,
                density,
                customEdges: useCustomGraph ? parseCustomEdges(customEdges) : []
            });
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [numNodes, density, startNode, endNode, useCustomGraph, customEdges, parseCustomEdges, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleNumNodesChange = useCallback((value) => {
        if (value >= 3 && value <= 10) {
            console.log('handleNumNodesChange called with value:', value);
            setNumNodes(value);
            // Ensure startNode and endNode are valid
            if (startNode >= value) {
                setStartNode(value - 1);
            }
            if (endNode >= value) {
                setEndNode(value - 1);
            }
            resetAndGenerateSteps();
        }
    }, [startNode, endNode, resetAndGenerateSteps]);

    const handleDensityChange = useCallback((value) => {
        if (value >= 0.1 && value <= 0.9) {
            console.log('handleDensityChange called with value:', value);
            setDensity(value);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleStartNodeChange = useCallback((value) => {
        if (value >= 0 && value < numNodes && value !== endNode) {
            console.log('handleStartNodeChange called with value:', value);
            setStartNode(value);
            resetAndGenerateSteps();
        }
    }, [numNodes, endNode, resetAndGenerateSteps]);

    const handleEndNodeChange = useCallback((value) => {
        if (value >= 0 && value < numNodes && value !== startNode) {
            console.log('handleEndNodeChange called with value:', value);
            setEndNode(value);
            resetAndGenerateSteps();
        }
    }, [numNodes, startNode, resetAndGenerateSteps]);

    const handleUseCustomGraphChange = useCallback((value) => {
        console.log('handleUseCustomGraphChange called with value:', value);
        setUseCustomGraph(value);
        if (!value) {
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleCustomEdgesChange = useCallback((value) => {
        console.log('handleCustomEdgesChange called with value:', value);
        setCustomEdges(value);
        setCustomEdgesError('');
    }, []);

    // Handle custom edges apply
    const handleApplyCustomEdges = useCallback(() => {
        try {
            const parsedEdges = parseCustomEdges(customEdges);

            // Validate edges
            if (parsedEdges.length === 0) {
                setCustomEdgesError('Please enter at least one edge');
                return;
            }

            // Check for valid node indices
            for (const [from, to] of parsedEdges) {
                if (from < 0 || to < 0 || from >= numNodes || to >= numNodes) {
                    setCustomEdgesError(`Node indices must be between 0 and ${numNodes - 1}`);
                    return;
                }
            }

            // Clear error and update
            setCustomEdgesError('');
            resetAndGenerateSteps();
        } catch (error) {
            setCustomEdgesError('Invalid edge format');
        }
    }, [customEdges, parseCustomEdges, resetAndGenerateSteps, numNodes]);

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for A* algorithm
    const pseudocode = [
        { code: "function aStar(graph, start, goal):", lineNumber: 1, indent: 0 },
        { code: "// Initialize data structures", lineNumber: 2, indent: 1 },
        { code: "openSet = {start}", lineNumber: 3, indent: 1 },
        { code: "closedSet = {}", lineNumber: 4, indent: 1 },
        { code: "gScore = {start: 0, all other nodes: ∞}", lineNumber: 5, indent: 1 },
        { code: "fScore = {start: heuristic(start, goal), all other nodes: ∞}", lineNumber: 6, indent: 1 },
        { code: "previous = {}", lineNumber: 7, indent: 1 },
        { code: "", lineNumber: 8, indent: 1 },
        { code: "// Main algorithm loop", lineNumber: 9, indent: 1 },
        { code: "while openSet is not empty:", lineNumber: 10, indent: 1 },
        { code: "// Find node in openSet with lowest fScore", lineNumber: 11, indent: 2 },
        { code: "current = node in openSet with lowest fScore", lineNumber: 12, indent: 2 },
        { code: "", lineNumber: 13, indent: 2 },
        { code: "// If we've reached the goal, reconstruct and return the path", lineNumber: 14, indent: 2 },
        { code: "if current == goal:", lineNumber: 15, indent: 2 },
        { code: "return reconstructPath(previous, goal)", lineNumber: 16, indent: 3 },
        { code: "", lineNumber: 17, indent: 2 },
        { code: "// Move current from openSet to closedSet", lineNumber: 18, indent: 2 },
        { code: "openSet.remove(current)", lineNumber: 19, indent: 2 },
        { code: "closedSet.add(current)", lineNumber: 20, indent: 2 },
        { code: "", lineNumber: 21, indent: 2 },
        { code: "// Check all neighbors of the current node", lineNumber: 22, indent: 2 },
        { code: "for each neighbor of current:", lineNumber: 23, indent: 2 },
        { code: "// Skip if neighbor is in closed set", lineNumber: 24, indent: 3 },
        { code: "if neighbor in closedSet:", lineNumber: 25, indent: 3 },
        { code: "continue", lineNumber: 26, indent: 4 },
        { code: "", lineNumber: 27, indent: 3 },
        { code: "// Calculate tentative gScore", lineNumber: 28, indent: 3 },
        { code: "tentativeGScore = gScore[current] + distance(current, neighbor)", lineNumber: 29, indent: 3 },
        { code: "", lineNumber: 30, indent: 3 },
        { code: "// If this path is better than the previous one", lineNumber: 31, indent: 3 },
        { code: "if tentativeGScore < gScore[neighbor]:", lineNumber: 32, indent: 3 },
        { code: "// Update path and scores", lineNumber: 33, indent: 4 },
        { code: "previous[neighbor] = current", lineNumber: 34, indent: 4 },
        { code: "gScore[neighbor] = tentativeGScore", lineNumber: 35, indent: 4 },
        { code: "fScore[neighbor] = gScore[neighbor] + heuristic(neighbor, goal)", lineNumber: 36, indent: 4 },
        { code: "", lineNumber: 37, indent: 4 },
        { code: "// Add to open set if not already there", lineNumber: 38, indent: 4 },
        { code: "if neighbor not in openSet:", lineNumber: 39, indent: 4 },
        { code: "openSet.add(neighbor)", lineNumber: 40, indent: 5 },
        { code: "", lineNumber: 41, indent: 1 },
        { code: "// If we get here, no path was found", lineNumber: 42, indent: 1 },
        { code: "return null", lineNumber: 43, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.type === 'initialize' ? 3 : 
        steps[step]?.type === 'select' ? 12 : 
        steps[step]?.type === 'found' ? 16 : 
        steps[step]?.type === 'close' ? 20 : 
        steps[step]?.type === 'skip' ? 26 : 
        steps[step]?.type === 'check' ? 29 : 
        steps[step]?.type === 'update' ? 35 : 
        steps[step]?.type === 'open' ? 40 : 10
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="A* Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        A* (pronounced "A-star") is a pathfinding algorithm that combines the advantages of Dijkstra's algorithm and greedy best-first search.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O((V + E) log V) in the worst case
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(V) for storing the open and closed sets
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Uses a heuristic function to estimate the cost from the current node to the goal
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Guarantees the shortest path if the heuristic is admissible
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'numNodes',
                        type: 'slider',
                        label: 'Number of Nodes',
                        min: 3,
                        max: 10,
                        step: 1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: GraphIcon
                    },
                    {
                        name: 'startNode',
                        type: 'slider',
                        label: 'Start Node',
                        min: 0,
                        max: numNodes - 1,
                        step: 1,
                        marks: true,
                        icon: PlayArrowIcon
                    },
                    {
                        name: 'endNode',
                        type: 'slider',
                        label: 'End Node',
                        min: 0,
                        max: numNodes - 1,
                        step: 1,
                        marks: true,
                        icon: FlagIcon
                    },
                    {
                        name: 'density',
                        type: 'slider',
                        label: 'Edge Density',
                        min: 0.1,
                        max: 0.9,
                        step: 0.1,
                        marks: true,
                        disableWhen: 'useCustomGraph',
                        icon: TuneIcon
                    },
                    {
                        name: 'useCustomGraph',
                        type: 'switch',
                        label: 'Use Custom Graph',
                        icon: ShuffleIcon
                    },
                    {
                        name: 'customEdges',
                        type: 'customArray',
                        label: 'Custom Edges',
                        placeholder: 'Format: [from, to, weight], [from, to, weight], ...',
                        helperText: 'Example: [0, 1, 5], [1, 2, 3], [0, 2, 7]',
                        error: customEdgesError,
                        showOnlyWhen: 'useCustomGraph',
                        onApply: handleApplyCustomEdges,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    numNodes,
                    startNode,
                    endNode,
                    density,
                    useCustomGraph,
                    customEdges
                }}
                onChange={(newValues) => {
                    if (newValues.numNodes !== undefined && newValues.numNodes !== numNodes) {
                        handleNumNodesChange(newValues.numNodes);
                    }
                    if (newValues.startNode !== undefined && newValues.startNode !== startNode) {
                        handleStartNodeChange(newValues.startNode);
                    }
                    if (newValues.endNode !== undefined && newValues.endNode !== endNode) {
                        handleEndNodeChange(newValues.endNode);
                    }
                    if (newValues.density !== undefined && newValues.density !== density) {
                        handleDensityChange(newValues.density);
                    }
                    if (newValues.useCustomGraph !== undefined && newValues.useCustomGraph !== useCustomGraph) {
                        handleUseCustomGraphChange(newValues.useCustomGraph);
                    }
                    if (newValues.customEdges !== undefined) {
                        setCustomEdges(newValues.customEdges);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default AStarController;
