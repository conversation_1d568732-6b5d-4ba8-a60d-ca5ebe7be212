// InsertionSortVisualization.js - Clean InsertionSort visualization following QuickSort pattern
// Uses InsertionSortSimulation component and controller-driven architecture

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import InsertionSortSimulation from '../../../components/visualization/bars/InsertionSortSimulation';
import getAlgorithmColors from '../../../utils/algorithmColors';
import * as THREE from 'three';
import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './InsertionSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

// Extract constants from comprehensive configuration
const BAR_SPACING = CONFIG.mainArray.bars.spacing;

// Main InsertionSort Visualization Component
const InsertionSortVisualization = (props) => {
  const {
    state,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    theme,
    steps,
    array
  } = props;

  // Get speed from context
  const { speed } = useSpeed();

  // Local state to track current step data
  const [currentStep, setCurrentStep] = useState(null);

  // Use array from props as input data
  const inputData = array;

  // Get theme-aware colors with algorithm-specific overrides
  const colors = useMemo(() => getAlgorithmColors(theme, 'insertionSort'), [theme]);

  // Generate color legend items specific to insertion sort
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Default' },
    { color: colors.comparing, label: 'Comparing Elements' },
    { color: colors.swapping, label: 'Shifting Elements' },
    { color: colors.merging, label: 'Current Element' },
    { color: colors.sorted, label: 'Sorted Elements' }
  ], [colors]);

  // Get camera from Three.js context
  const { camera } = useThree();

  // Store speed in a ref for animation frame access
  const speedRef = useRef(speed);
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // State for visualization
  const [arrayData, setArrayData] = useState([]);

  // Simple animation state management
  const [displayStep, setDisplayStep] = useState(null);       // What the visualization currently shows

  // Shift animation state
  const [shiftAnimation, setShiftAnimation] = useState({
    active: false,
    indices: [-1, -1],
    progress: 0,
    startTime: 0
  });

  // Placement animation state (held element being placed)
  const [placementAnimation, setPlacementAnimation] = useState({
    active: false,
    element: null,
    fromPosition: null,
    toPosition: null,
    progress: 0,
    startTime: 0
  });

  // Refs for animation control
  const isAnimatingRef = useRef(false);
  const initialArrayRef = useRef([]);
  const groupRef = useRef(null);
  const animatingRef = useRef(false);



  // Set camera position from config - ALWAYS use config values
  useEffect(() => {
    if (!camera) return;

    console.log('InsertionSort - Setting camera from config:', CONFIG.camera.position);

    // ALWAYS use the config values directly - no custom logic
    camera.position.set(...CONFIG.camera.position);
    camera.lookAt(new THREE.Vector3(...CONFIG.camera.lookAt));
    camera.fov = CONFIG.camera.fov;
    camera.near = CONFIG.camera.near;
    camera.far = CONFIG.camera.far;
    camera.updateProjectionMatrix();

    console.log('InsertionSort - Camera set to:', camera.position.toArray());
  }, [camera, CONFIG.camera]); // Re-run when config changes

  // Initialize array data from inputData
  useEffect(() => {
    if (!inputData || inputData.length === 0 || isAnimatingRef.current) {
      return;
    }

    const newArray = [...inputData];
    console.log('InsertionSortVisualization - Using input data:', newArray);

    setArrayData(newArray);
    initialArrayRef.current = [...newArray];
  }, [inputData]);

  // Simple step handling using previous and current step comparison
  useEffect(() => {
    console.log('InsertionSortVisualization - Step changed to:', step);
    console.log('InsertionSortVisualization - Steps array length:', steps?.length);
    console.log('InsertionSortVisualization - Current state:', state);

    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('InsertionSortVisualization - No steps available or invalid step index');
      return;
    }

    const currentStepData = steps[step];
    const previousStepData = step > 0 ? steps[step - 1] : null;

    // For step 0, create a special "initial" step type
    if (step === 0) {
      const initialStep = {
        ...steps[step],
        type: 'initial',
        initialArray: true,
        message: 'Insertion Sort: Initial Array'
      };
      setDisplayStep(initialStep);
      setCurrentStep(initialStep);
      return;
    }

    // Check if we need shift animation: previous step was 'shift' and current step is 'shifted'
    const needsShiftAnimation = previousStepData?.type === 'shift' &&
                               currentStepData?.type === 'shifted' &&
                               CONFIG.animation?.types?.shift?.enabled;

    // Check if we need placement animation: previous step has held element and current step places it
    const needsPlacementAnimation = previousStepData?.visualizationData?.mainArray?.heldElement &&
                                   !currentStepData?.visualizationData?.mainArray?.heldElement &&
                                   CONFIG.animation?.types?.shift?.enabled; // Use same config for now

    if (needsShiftAnimation) {
      const shiftingIndices = previousStepData.visualizationData?.mainArray?.shiftingIndices;

      if (shiftingIndices && shiftingIndices.length === 2) {
        console.log('=== SHIFT ANIMATION STARTING ===');
        console.log('Previous step (shift announcement):', {
          type: previousStepData.type,
          statement: previousStepData.statement,
          values: previousStepData.visualizationData?.mainArray?.values,
          shiftingIndices: previousStepData.visualizationData?.mainArray?.shiftingIndices
        });
        console.log('Current step (shift result):', {
          type: currentStepData.type,
          statement: currentStepData.statement,
          values: currentStepData.visualizationData?.mainArray?.values,
          shiftingIndices: currentStepData.visualizationData?.mainArray?.shiftingIndices
        });
        console.log('Animation will shift indices:', shiftingIndices);

        // Keep showing the previous step (with original array) during animation
        setDisplayStep(previousStepData);

        // Start animation
        animatingRef.current = true;
        setShiftAnimation({
          active: true,
          indices: shiftingIndices,
          progress: 0,
          startTime: performance.now()
        });

        // End animation after duration and show current step
        const animationDuration = CONFIG.animation?.types?.shift?.duration || 1000;
        setTimeout(() => {
          console.log('=== SHIFT ANIMATION COMPLETED ===');
          const prevValues = previousStepData.visualizationData?.mainArray?.values;
          const currValues = currentStepData.visualizationData?.mainArray?.values;

          console.log('Previous step data (what was shown during animation):');
          console.log('  Type:', previousStepData.type);
          console.log('  Values:', prevValues ? JSON.stringify(prevValues) : 'undefined');
          console.log('  ShiftingIndices:', previousStepData.visualizationData?.mainArray?.shiftingIndices);

          console.log('Current step data (what will be shown after animation):');
          console.log('  Type:', currentStepData.type);
          console.log('  Values:', currValues ? JSON.stringify(currValues) : 'undefined');
          console.log('  ShiftingIndices:', currentStepData.visualizationData?.mainArray?.shiftingIndices);

          console.log('Arrays are equal?', JSON.stringify(prevValues) === JSON.stringify(currValues));

          // Now show the current step (with shifted array)
          setDisplayStep(currentStepData);
          setCurrentStep(currentStepData);

          setShiftAnimation(prev => ({
            ...prev,
            active: false
          }));
          animatingRef.current = false;
          console.log('InsertionSortVisualization - Shift animation completed, showing shifted result');
        }, animationDuration);

        // Exit early - don't update display step until animation completes
        return;
      }
    }

    // Check for placement animation
    if (needsPlacementAnimation) {
      const heldElement = previousStepData.visualizationData?.mainArray?.heldElement;

      if (heldElement) {
        console.log('=== PLACEMENT ANIMATION STARTING ===');
        console.log('Held element:', heldElement);
        console.log('Previous step (with held element):', {
          type: previousStepData.type,
          heldElement: heldElement
        });
        console.log('Current step (element placed):', {
          type: currentStepData.type,
          values: currentStepData.visualizationData?.mainArray?.values
        });

        // Keep showing the previous step (with held element) during animation
        setDisplayStep(previousStepData);

        // Find where the held element is placed in the final array
        const heldValue = heldElement.value;
        const finalArray = currentStepData.visualizationData?.mainArray?.values;
        const targetIndex = finalArray ? finalArray.findIndex(val => val === heldValue) : heldElement.originalIndex;

        console.log('Placement target calculation:', {
          heldValue,
          finalArray,
          targetIndex,
          originalIndex: heldElement.originalIndex
        });

        // Start placement animation
        animatingRef.current = true;

        // Determine starting position based on fixed position mode
        const useFixedPosition = CONFIG.heldElement?.positioning?.useFixedPosition || false;
        const fromPosition = useFixedPosition
          ? 'fixed'  // Special marker for fixed position
          : heldElement.currentPosition;

        setPlacementAnimation({
          active: true,
          element: heldElement,
          fromPosition: fromPosition,
          toPosition: targetIndex >= 0 ? targetIndex : heldElement.originalIndex,
          progress: 0,
          startTime: performance.now()
        });

        // End animation after duration and show current step
        const animationDuration = CONFIG.heldElement?.animation?.placement?.duration || CONFIG.animation?.types?.shift?.duration || 600;
        setTimeout(() => {
          console.log('=== PLACEMENT ANIMATION COMPLETED ===');

          // Now show the current step (with element placed)
          setDisplayStep(currentStepData);
          setCurrentStep(currentStepData);

          setPlacementAnimation(prev => ({
            ...prev,
            active: false
          }));
          animatingRef.current = false;
          console.log('InsertionSortVisualization - Placement animation completed, showing placed result');
        }, animationDuration);

        // Exit early - don't update display step until animation completes
        return;
      }
    }

    // Direct update (no animation needed)
    setDisplayStep(currentStepData);
    setCurrentStep(currentStepData);
  }, [steps, step]);

  // Calculate adaptive dimensions based on array size using config
  const { scaleFactor, adaptiveSpacing } = useMemo(() => {
    const arraySize = arrayData.length;

    // Determine scale factor based on config breakpoints
    let factor;
    if (arraySize <= 10) {
      factor = 1.0;
    } else if (arraySize <= 15) {
      factor = 0.8;
    } else {
      factor = 0.6;
    }

    // Calculate adaptive spacing
    const spacing = Math.max(0.2, BAR_SPACING * factor);

    return {
      scaleFactor: factor,
      adaptiveSpacing: spacing
    };
  }, [arrayData.length]);

  // State to track the actual total width from bars for proper platform sizing
  const [actualTotalWidth, setActualTotalWidth] = useState(0);

  // Calculate stage dimensions using actual bar width from simulation
  const stageDimensions = useMemo(() => {
    // Use the initial array length for fallback calculation
    const arrayLength = arrayData.length || 5;

    // Calculate fallback width if actualTotalWidth is not available yet
    const adaptiveBarWidth = CONFIG.mainArray.bars.width * scaleFactor;
    const fallbackTotalWidth = (arrayLength * (adaptiveBarWidth + adaptiveSpacing)) - adaptiveSpacing;

    // Use actual width from bars if available, otherwise use fallback
    const totalBarsWidth = actualTotalWidth > 0 ? actualTotalWidth : fallbackTotalWidth;

    return {
      width: Math.max(
        totalBarsWidth + CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right,
        CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right + 4 // Minimum width
      ),
      height: CONFIG.basePlatform.dimensions.height,
      depth: CONFIG.basePlatform.dimensions.depth
    };
  }, [arrayData.length, adaptiveSpacing, scaleFactor, actualTotalWidth]);

  // Enhanced auto-advance functionality
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      return;
    }

    // Stop if we've reached the end
    if (step >= totalSteps) {
      return;
    }

    // Calculate delay based on speed using enhanced delay function
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    const timeoutId = setTimeout(() => {
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef, steps]);

  // Animation frame for levitation, shift animation, and placement animation
  useFrame(({ clock }) => {
    // Update shift animation progress
    if (shiftAnimation.active) {
      const elapsed = performance.now() - shiftAnimation.startTime;
      const duration = CONFIG.animation?.types?.shift?.duration || 600;
      const progress = Math.min(elapsed / duration, 1);

      setShiftAnimation(prev => ({
        ...prev,
        progress: progress
      }));
    }

    // Update placement animation progress
    if (placementAnimation.active) {
      const elapsed = performance.now() - placementAnimation.startTime;
      const duration = CONFIG.heldElement?.animation?.placement?.duration || CONFIG.animation?.types?.shift?.duration || 600;
      const progress = Math.min(elapsed / duration, 1);

      setPlacementAnimation(prev => ({
        ...prev,
        progress: progress
      }));
    }
    // Levitation animation - stay at origin, let child components handle their own positioning
    if (CONFIG.visual.levitation.enabled &&
      (!CONFIG.visual.levitation.disableDuringSimulation || state === 'idle') &&
      groupRef.current) {

      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.visual.levitation;

      // Apply levitation relative to base platform position from config
      const basePosition = CONFIG.basePlatform.position;

      if (levitationConfig.movement.y.enabled) {
        groupRef.current.position.y = basePosition[1] + Math.sin(time * levitationConfig.movement.y.frequency) * levitationConfig.movement.y.amplitude;
      } else {
        groupRef.current.position.y = basePosition[1];
      }

      if (levitationConfig.movement.x.enabled) {
        groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.movement.x.frequency) * levitationConfig.movement.x.amplitude;
      } else {
        groupRef.current.position.x = basePosition[0];
      }

      if (levitationConfig.movement.z.enabled) {
        groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.movement.z.frequency) * levitationConfig.movement.z.amplitude;
      } else {
        groupRef.current.position.z = basePosition[2];
      }

      // Apply rotation effects
      if (levitationConfig.rotation.enabled) {
        if (levitationConfig.rotation.x.enabled) {
          groupRef.current.rotation.x = Math.cos(time * levitationConfig.rotation.x.frequency) * levitationConfig.rotation.x.amplitude;
        }

        if (levitationConfig.rotation.y.enabled) {
          groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
        }

        if (levitationConfig.rotation.z.enabled) {
          groupRef.current.rotation.z = Math.sin(time * levitationConfig.rotation.z.frequency) * levitationConfig.rotation.z.amplitude;
        }
      }
    } else if (groupRef.current) {
      // When levitation is disabled, use base platform position from config
      const basePosition = CONFIG.basePlatform.position;
      groupRef.current.position.set(basePosition[0], basePosition[1], basePosition[2]);
      groupRef.current.rotation.set(0, 0, 0);
    }
  });

  return (
    <>
      {/* Step board */}
      {CONFIG.stepBoard.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''}
          totalSteps={totalSteps > 0 ? totalSteps - 1 : 0}
          description={currentStep?.statement || 'Insertion Sort Algorithm'}
          stepData={currentStep}
          showStepNumber={step > 0}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {/* Color legend */}
      {CONFIG.colorLegend.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      {/* Main Array Group - Position controlled by levitation animation in useFrame */}
      <group ref={groupRef}>
        {/* Base platform - positioned at origin since group handles basePlatform.position */}
        <SortingBase
          width={stageDimensions.width}
          height={stageDimensions.height}
          depth={stageDimensions.depth}
          color={colors.base}
          position={[0, -stageDimensions.height / 2, 0]}
        />

        {/* Bars and labels - positioned relative to the base platform using config offset */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <InsertionSortSimulation
            currentStep={displayStep || currentStep}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width * scaleFactor}
            barSpacing={adaptiveSpacing}
            showValues={(() => {
              const shouldShow = CONFIG.visual.labels.values.adaptiveVisibility
                ? (scaleFactor > CONFIG.visual.labels.values.visibilityThreshold)
                : CONFIG.visual.labels.values.enabled;
              return shouldShow;
            })()}
            showIndices={CONFIG.visual.labels.indices.adaptiveVisibility
              ? (scaleFactor > CONFIG.visual.labels.indices.visibilityThreshold)
              : CONFIG.visual.labels.indices.enabled}
            config={CONFIG}
            state={state}
            shiftAnimation={shiftAnimation}
            placementAnimation={placementAnimation}
            originalArray={arrayData}
            onWidthChange={setActualTotalWidth}
          />
        </group>
      </group>
    </>
  );
};

export default InsertionSortVisualization;
