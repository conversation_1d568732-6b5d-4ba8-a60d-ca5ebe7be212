// Dijkstra/index.js
// Export only what's needed from this algorithm

import DijkstraVisualization from './DijkstraVisualization';
import DijkstraController from './DijkstraController';
import DijkstraAlgorithm from './DijkstraAlgorithm';

export const metadata = {
  id: '<PERSON><PERSON><PERSON>',
  name: '<PERSON><PERSON><PERSON>\'s Algorithm',
  description: 'An algorithm for finding the shortest paths between nodes in a graph.',
  timeComplexity: 'O((V + E) log V)',
  spaceComplexity: 'O(V)',
  defaultParams: {
    nodes: 6,
    startNode: 0,
    endNode: 5,
    density: 0.5,
    customEdges: [],
  },
};

export const components = {
  visualization: DijkstraVisualization,
  controller: DijkstraController,
  algorithm: DijkstraAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
