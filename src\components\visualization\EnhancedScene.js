import React, { useRef } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';
import { Stars, Cloud, Environment } from '@react-three/drei';
import { getEnvironmentMap } from '../../utils/environmentMaps';

/**
 * Enhanced 3D scene with interactive elements and real-world background
 *
 * @param {Object} props - Component props
 * @param {string} props.theme - Current theme ('dark' or 'light')
 * @param {Object} props.algorithmInfo - Information about the algorithm to display on the chalkboard
 * @returns {JSX.Element} - The rendered enhanced scene component
 */
const EnhancedScene = ({
  theme = 'dark',
  algorithmInfo = {
    name: 'Algorithm',
    description: 'Description of the algorithm',
    timeComplexity: 'O(n)',
    spaceComplexity: 'O(n)',
    applications: ['Application 1', 'Application 2']
  }
}) => {
  const { scene } = useThree();
  const muiTheme = useTheme();
  const isDark = muiTheme.palette.mode === 'dark';

  // References for animation
  const chalkboardRef = useRef();
  const cloudsRef = useRef();

  // Set scene background based on theme
  React.useEffect(() => {
    if (isDark) {
      scene.background = new THREE.Color('#050505'); // Very dark gray for dark theme
    } else {
      scene.background = new THREE.Color('#f0f0f0'); // Light gray for light theme
    }
  }, [scene, isDark]);

  // Animate the scene elements
  useFrame((state, delta) => {
    // Gently rotate the chalkboard to make it more dynamic
    if (chalkboardRef.current) {
      chalkboardRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;
    }

    // Move the clouds slowly
    if (cloudsRef.current) {
      cloudsRef.current.rotation.y += delta * 0.05;
    }
  });

  return (
    <group>
      {/* Environment lighting and effects */}
      {isDark ? (
        <>
          {/* Dark theme environment */}
          <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade speed={1} />
          <fog attach="fog" args={['#050505', 30, 95]} />
          <Environment files={getEnvironmentMap('night')} />

          {/* Distant light sources */}
          <pointLight position={[-20, 10, -20]} intensity={0.5} color="#4060ff" />
          <pointLight position={[20, 10, 20]} intensity={0.3} color="#ff6040" />
        </>
      ) : (
        <>
          {/* Light theme environment */}
          <fog attach="fog" args={['#f0f0f0', 30, 95]} />
          <Environment files={getEnvironmentMap('sunset')} />

          {/* Clouds for light theme */}
          <group ref={cloudsRef} position={[0, 15, -30]}>
            <Cloud position={[-10, 0, 0]} speed={0.2} opacity={0.4} />
            <Cloud position={[10, 5, 10]} speed={0.2} opacity={0.3} />
            <Cloud position={[0, -5, -10]} speed={0.2} opacity={0.5} />
          </group>
        </>
      )}

      {/* Distant 3D chalkboard with algorithm information */}
      <group ref={chalkboardRef} position={[0, 0, -40]} scale={[3, 3, 3]}>
        {/* Chalkboard frame */}
        <mesh castShadow receiveShadow>
          <boxGeometry args={[10, 6, 0.2]} />
          <meshStandardMaterial
            color={isDark ? '#1a3a1a' : '#2a4a2a'}
            roughness={0.9}
            metalness={0.1}
          />

          {/* Chalkboard frame edges */}
          <lineSegments>
            <edgesGeometry attach="geometry" args={[new THREE.BoxGeometry(10, 6, 0.2)]} />
            <lineBasicMaterial
              attach="material"
              color={isDark ? '#3a5a3a' : '#4a6a4a'}
              linewidth={2}
            />
          </lineSegments>
        </mesh>

        {/* Algorithm information on the chalkboard using simple planes with chalk-like material */}
        {/* Algorithm Title */}
        <mesh position={[0, 2, 0.15]} rotation={[0, 0, 0]}>
          <planeGeometry args={[8, 0.8]} />
          <meshStandardMaterial
            color="#ffffff"
            roughness={0.9}
            metalness={0.1}
            emissive="#ffffff"
            emissiveIntensity={0.2}
            transparent
            opacity={0.9}
          >
            <canvasTexture
              attach="map"
              image={(() => {
                // Create canvas for text
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 64;
                const context = canvas.getContext('2d');
                context.fillStyle = '#ffffff';
                context.font = 'bold 48px Roboto';
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.fillText(algorithmInfo.name, 256, 32);
                return canvas;
              })()}
            />
          </meshStandardMaterial>
        </mesh>

        {/* Algorithm Description */}
        <mesh position={[0, 1, 0.15]} rotation={[0, 0, 0]}>
          <planeGeometry args={[8, 1.5]} />
          <meshStandardMaterial
            color="#ffffff"
            roughness={0.9}
            metalness={0.1}
            transparent
            opacity={0.9}
          >
            <canvasTexture
              attach="map"
              image={(() => {
                // Create canvas for text
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 128;
                const context = canvas.getContext('2d');
                context.fillStyle = '#ffffff';
                context.font = '16px Roboto';
                context.textAlign = 'center';
                context.textBaseline = 'middle';

                // Split description into multiple lines
                const words = algorithmInfo.description.split(' ');
                let line = '';
                let lines = [];
                const maxWidth = 480;

                for (let i = 0; i < words.length; i++) {
                  const testLine = line + words[i] + ' ';
                  const metrics = context.measureText(testLine);
                  if (metrics.width > maxWidth && i > 0) {
                    lines.push(line);
                    line = words[i] + ' ';
                  } else {
                    line = testLine;
                  }
                }
                lines.push(line);

                // Draw lines
                lines.forEach((line, index) => {
                  context.fillText(line, 256, 20 + (index * 20));
                });

                return canvas;
              })()}
            />
          </meshStandardMaterial>
        </mesh>

        {/* Time Complexity */}
        <mesh position={[-1, 0, 0.15]} rotation={[0, 0, 0]}>
          <planeGeometry args={[6, 0.5]} />
          <meshStandardMaterial
            color="#ffffff"
            roughness={0.9}
            metalness={0.1}
            transparent
            opacity={0.9}
          >
            <canvasTexture
              attach="map"
              image={(() => {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 64;
                const context = canvas.getContext('2d');
                context.fillStyle = '#ffffff';
                context.font = '24px Roboto';
                context.textAlign = 'left';
                context.textBaseline = 'middle';
                context.fillText(`Time Complexity: ${algorithmInfo.timeComplexity}`, 20, 32);
                return canvas;
              })()}
            />
          </meshStandardMaterial>
        </mesh>

        {/* Space Complexity */}
        <mesh position={[-1, -0.6, 0.15]} rotation={[0, 0, 0]}>
          <planeGeometry args={[6, 0.5]} />
          <meshStandardMaterial
            color="#ffffff"
            roughness={0.9}
            metalness={0.1}
            transparent
            opacity={0.9}
          >
            <canvasTexture
              attach="map"
              image={(() => {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 64;
                const context = canvas.getContext('2d');
                context.fillStyle = '#ffffff';
                context.font = '24px Roboto';
                context.textAlign = 'left';
                context.textBaseline = 'middle';
                context.fillText(`Space Complexity: ${algorithmInfo.spaceComplexity}`, 20, 32);
                return canvas;
              })()}
            />
          </meshStandardMaterial>
        </mesh>

        {/* Applications Header */}
        <mesh position={[-1, -1.2, 0.15]} rotation={[0, 0, 0]}>
          <planeGeometry args={[6, 0.5]} />
          <meshStandardMaterial
            color="#ffffff"
            roughness={0.9}
            metalness={0.1}
            transparent
            opacity={0.9}
          >
            <canvasTexture
              attach="map"
              image={(() => {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 64;
                const context = canvas.getContext('2d');
                context.fillStyle = '#ffffff';
                context.font = '24px Roboto';
                context.textAlign = 'left';
                context.textBaseline = 'middle';
                context.fillText('Applications:', 20, 32);
                return canvas;
              })()}
            />
          </meshStandardMaterial>
        </mesh>

        {/* Applications List */}
        {algorithmInfo.applications.map((app, index) => (
          <mesh key={index} position={[-1, -1.8 - (index * 0.4), 0.15]} rotation={[0, 0, 0]}>
            <planeGeometry args={[6, 0.3]} />
            <meshStandardMaterial
              color="#ffffff"
              roughness={0.9}
              metalness={0.1}
              transparent
              opacity={0.9}
            >
              <canvasTexture
                attach="map"
                image={(() => {
                  const canvas = document.createElement('canvas');
                  canvas.width = 512;
                  canvas.height = 32;
                  const context = canvas.getContext('2d');
                  context.fillStyle = '#ffffff';
                  context.font = '18px Roboto';
                  context.textAlign = 'left';
                  context.textBaseline = 'middle';
                  context.fillText(`• ${app}`, 40, 16);
                  return canvas;
                })()}
              />
            </meshStandardMaterial>
          </mesh>
        ))}

        {/* Chalk and eraser on the bottom of the board */}
        <mesh position={[-4, -3.2, 0.1]} rotation={[0, 0, Math.PI / 4]}>
          <boxGeometry args={[0.8, 0.2, 0.2]} />
          <meshStandardMaterial color="#ffffff" roughness={0.9} />
        </mesh>

        <mesh position={[4, -3.2, 0.1]}>
          <boxGeometry args={[1, 0.4, 0.3]} />
          <meshStandardMaterial color="#663300" roughness={0.8} />
        </mesh>
      </group>

      {/* Ground plane removed to prevent hiding elements */}
    </group>
  );
};

export default EnhancedScene;
