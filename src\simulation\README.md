# Algorithm Simulation Architecture

A robust, algorithm-agnostic simulation system that can handle complex algorithm visualizations.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
- [Documentation](#documentation)
- [Examples](#examples)
- [Migration](#migration)

## Overview

The Algorithm Simulation Architecture provides a clean, reusable framework for implementing algorithm simulations. It separates algorithm logic from visualization, making it easier to create complex visualizations for any algorithm.

## Features

- **Algorithm-Agnostic**: Works with any type of algorithm, not just sorting algorithms
- **Step-Based Simulation**: Break down algorithms into discrete steps for visualization
- **Customizable Visualizations**: Create custom visualizations for each step type
- **Centralized State Management**: Manage all simulation state in one place
- **Animation Utilities**: Built-in utilities for smooth animations
- **Consistent Interface**: Consistent API for all algorithms
- **Extensible**: Easy to add new algorithms and visualizations

## Architecture

The simulation architecture consists of three main layers:

1. **Context Layer** - Provides state management for simulations
   - `StepContext`: Manages algorithm steps and their execution
   - `SimulationContext`: Manages the overall simulation state
   - `AlgorithmDataContext`: Manages algorithm-specific data structures

2. **Core Components** - Handles step execution and visualization
   - `SimulationEngine`: Handles step execution and transitions
   - `StepProcessor`: Processes individual algorithm steps
   - `StepVisualizer`: Visualizes algorithm steps

3. **Utility Layer** - Provides helper functions for simulations
   - Step utilities: Functions for step generation and processing
   - Animation utilities: Functions for animations and transitions

## Getting Started

To use the simulation architecture in your algorithm:

1. **Define Algorithm Steps**:
   ```javascript
   import { createInitialStep, createCompletionStep } from '../simulation/utils/stepUtils';

   export const generateAlgorithmSteps = (input) => {
     const steps = [];

     // Add initial step
     steps.push(createInitialStep(input, 'Initial state'));

     // Add algorithm-specific steps
     // ...

     // Add completion step
     steps.push(createCompletionStep(result, 'Algorithm complete'));

     return steps;
   };
   ```

2. **Create Step Visualizers**:
   ```javascript
   const ComparisonVisualizer = ({ step }) => {
     // Render comparison visualization
   };

   const SwapVisualizer = ({ step }) => {
     // Render swap visualization
   };

   export const visualizers = {
     comparison: ComparisonVisualizer,
     swap: SwapVisualizer,
     // Map other step types to their visualizers
   };
   ```

3. **Connect to the Simulation Engine**:
   ```javascript
   import { SimulationContextProvider, SimulationEngine, StepProcessor, StepVisualizer } from '../simulation';

   const AlgorithmSimulation = () => {
     return (
       <SimulationContextProvider>
         <AlgorithmController />
         <StepVisualizer visualizers={visualizers} />
         <SimulationEngine />
         <StepProcessor />
       </SimulationContextProvider>
     );
   };
   ```

## Documentation

Detailed documentation is available in the following files:

- [Implementation Guide](./IMPLEMENTATION_GUIDE.md): Step-by-step guide for implementing new algorithms
- [Migration Guide](./MIGRATION_GUIDE.md): Guide for migrating existing algorithms to the new architecture
- [API Reference](./API_REFERENCE.md): Detailed API documentation for all components and utilities

## Examples

Check out the examples to see the simulation architecture in action:

- [MergeSortExample](./examples/MergeSortExample.js): Example implementation of Merge Sort using the new architecture

## Migration

To migrate an existing algorithm to the new architecture, follow these steps:

1. Extract step generation logic to a separate file
2. Create visualizer components for each step type
3. Update controller to use the new context system
4. Update visualization component to use the new step visualizer
5. Connect everything to the simulation engine

For detailed migration instructions, see the [Migration Guide](./MIGRATION_GUIDE.md).
