// Slider.js
// A reusable slider component with consistent styling

import React from 'react';
import { Box, Slider as Mui<PERSON>lider, Typography } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable slider component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string|node} props.label - Slider label (can be a string or a React node)
 * @param {number} props.value - Slider value
 * @param {function} props.onChange - Change handler
 * @param {number} props.min - Minimum value
 * @param {number} props.max - Maximum value
 * @param {number} props.step - Step size
 * @param {boolean} props.showValue - Whether to show the current value
 * @param {function} props.valueLabelFormat - Function to format the value label
 * @param {boolean|Array} props.marks - Whether to show marks or custom marks
 * @param {Object} props.sx - Additional styles
 */
const Slider = ({
  label,
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  showValue = true,
  valueLabelFormat,
  marks,
  sx = {},
  ...rest
}) => {
  // Format the value label
  const formatValue = (value) => {
    if (valueLabelFormat) {
      return valueLabelFormat(value);
    }
    return value;
  };

  const handleChange = (_, newValue) => {
    onChange(newValue);
  };

  return (
    <Box
      sx={{
        width: '100%',
        ...sx
      }}

    >
      {label && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {label}
          </Typography>
          {showValue && (
            <Typography variant="body2" color="text.primary" fontWeight="medium">
              {formatValue(value)}
            </Typography>
          )}
        </Box>
      )}
      <MuiSlider
        value={value}
        onChange={handleChange}
        min={min}
        max={max}
        step={step}
        valueLabelDisplay="auto"
        valueLabelFormat={formatValue}
        marks={marks !== undefined ? marks : step === 1 ? true : undefined} // Use provided marks or show marks when step is 1
        sx={{
          '& .MuiSlider-valueLabel': {
            // Only show value label on hover
            opacity: 0,
            transition: 'opacity 0.2s ease',
          },
          '&:hover .MuiSlider-valueLabel': {
            opacity: 1,
          },
          '& .MuiSlider-mark': {
            width: 4,
            height: 4,
            borderRadius: '50%',
          },
          ...rest?.sx
        }}
        {...rest}
      />
    </Box>
  );
};

Slider.propTypes = {
  label: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node
  ]).isRequired,
  value: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  min: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),
  max: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),
  step: PropTypes.number,
  showValue: PropTypes.bool,
  valueLabelFormat: PropTypes.func,
  marks: PropTypes.oneOfType([PropTypes.bool, PropTypes.array]),
  sx: PropTypes.object
};

export default Slider;
