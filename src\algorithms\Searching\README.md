# Searching Algorithms

This directory contains implementations of various searching algorithms.

## Implemented Algorithms

- B<PERSON> (Breadth-First Search) - An algorithm for traversing or searching tree or graph data structures.
- DFS (Depth-First Search) - An algorithm for traversing or searching tree or graph data structures using a stack.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `BinarySearch`)
2. Implement the required files:
   - `BinarySearchAlgorithm.js` - Core algorithm logic
   - `BinarySearchVisualization.js` - Visualization component
   - `BinarySearchController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
