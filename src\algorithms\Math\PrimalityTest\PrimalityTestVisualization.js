// PrimalityTestVisualization.js
// 3D visualization component for the Primality Test algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const NUMBER_SIZE = 1.2;
const NUMBER_SPACING = 2;
const FACTOR_SIZE = 0.8;
const GRID_SIZE = 10;

const PrimalityTestVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);
  const rotationRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 20, 20);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();

  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);

      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty data to show when no steps are available
      setCurrentStepData({
        number: 17,
        method: 'trial_division',
        currentDivisor: null,
        isPrime: null
      });
    }

    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;

      // Get current time
      const currentTime = clock.getElapsedTime();

      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);

        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }

    // Rotate the visualization slowly
    rotationRef.current += 0.001;
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Number colors
    number: isDark ? '#2d3436' : '#636e72',
    prime: isDark ? '#00b894' : '#55efc4',
    composite: isDark ? '#e84118' : '#ff7675',
    testing: isDark ? '#fdcb6e' : '#ffeaa7',

    // Factor colors
    factor: isDark ? '#0984e3' : '#74b9ff',
    nonFactor: isDark ? '#636e72' : '#b2bec3',

    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',

    // Background colors
    background: isDark ? '#1e272e' : '#f5f6fa',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.prime, label: 'Prime Number' },
    { color: colors.composite, label: 'Composite Number' },
    { color: colors.testing, label: 'Number Being Tested' },
    { color: colors.factor, label: 'Factor/Witness' },
  ], [colors]);

  // Use fixed position for stability
  const position = [0, 0, 0];

  // Create a number display
  const NumberDisplay = ({ position, number, label, color, size = NUMBER_SIZE }) => {
    return (
      <group position={position}>
        {/* Number background */}
        <mesh castShadow>
          <boxGeometry args={[size * 2, size, size]} />
          <meshStandardMaterial color={color} />
        </mesh>

        {/* Number text */}
        <Html
          position={[0, 0, size/2 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: colors.textDark,
            fontSize: `${size * 16}px`,
            fontWeight: 'bold',
            width: '60px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {number}
          </div>
        </Html>

        {/* Label */}
        {label && (
          <Html
            position={[0, -size/2 - 0.5, 0]}
            center
            occlude
          >
            <div style={{
              color: isDark ? 'white' : 'black',
              fontSize: '14px',
              fontWeight: 'bold',
              width: '100px',
              textAlign: 'center',
              userSelect: 'none',
              pointerEvents: 'none'
            }}>
              {label}
            </div>
          </Html>
        )}
      </group>
    );
  };

  // Create a trial division visualization
  const TrialDivisionVisualization = ({ number, currentDivisor, isPrime }) => {
    // Calculate color based on primality
    let numberColor;
    if (isPrime === true) {
      numberColor = colors.prime;
    } else if (isPrime === false) {
      numberColor = colors.composite;
    } else {
      numberColor = colors.testing;
    }

    // Generate potential factors to check
    const factors = [];
    const limit = Math.floor(Math.sqrt(number));

    // Add 2 and 3 as special cases
    if (number > 1) {
      factors.push(2);
      factors.push(3);
    }

    // Add factors of form 6k ± 1 up to sqrt(number)
    for (let i = 5; i <= limit; i += 6) {
      factors.push(i);
      if (i + 2 <= limit) {
        factors.push(i + 2);
      }
    }

    // Calculate positions for factors in a circle
    const factorPositions = [];
    const radius = 5;
    const angleStep = (2 * Math.PI) / factors.length;

    for (let i = 0; i < factors.length; i++) {
      const angle = i * angleStep;
      const x = Math.sin(angle) * radius;
      const z = Math.cos(angle) * radius;
      factorPositions.push([x, 0, z]);
    }

    return (
      <group>
        {/* Main number being tested */}
        <NumberDisplay
          position={[0, 0, 0]}
          number={number}
          label="Number to Test"
          color={numberColor}
        />

        {/* Factors to check */}
        {factors.map((factor, index) => {
          // Determine if this factor is the current one being checked
          const isCurrent = factor === currentDivisor;
          // Determine if this factor divides the number
          const isDivisor = number % factor === 0;
          // Determine color based on status
          let factorColor;
          if (isCurrent) {
            factorColor = colors.factor;
          } else if (isDivisor) {
            factorColor = colors.factor;
          } else {
            factorColor = colors.nonFactor;
          }

          return (
            <group key={`factor-${index}`}>
              <NumberDisplay
                position={factorPositions[index]}
                number={factor}
                label={isCurrent ? "Current Divisor" : null}
                color={factorColor}
                size={FACTOR_SIZE}
              />

              {/* Connection line if this is the current divisor */}
              {isCurrent && (
                <ConnectionLine
                  from={[0, 0, 0]}
                  to={factorPositions[index]}
                  color={isDivisor ? colors.composite : colors.factor}
                />
              )}

              {/* Division result if this is the current divisor */}
              {isCurrent && (
                <Html
                  position={[
                    factorPositions[index][0] / 2,
                    factorPositions[index][1] + 1,
                    factorPositions[index][2] / 2
                  ]}
                  center
                  occlude
                >
                  <div style={{
                    color: isDark ? 'white' : 'black',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    width: '150px',
                    textAlign: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                    padding: '4px 8px',
                    borderRadius: '4px'
                  }}>
                    {number} ÷ {factor} = {Math.floor(number / factor)} remainder {number % factor}
                  </div>
                </Html>
              )}
            </group>
          );
        })}

        {/* Square root limit indicator */}
        <Html
          position={[0, -3, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '14px',
            fontWeight: 'bold',
            width: '200px',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none',
            background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            Only need to check factors up to √{number} ≈ {Math.sqrt(number).toFixed(2)}
          </div>
        </Html>
      </group>
    );
  };

  // Create a Fermat test visualization
  const FermatVisualization = ({ number, currentDivisor, isPrime }) => {
    // Calculate color based on primality
    let numberColor;
    if (isPrime === true) {
      numberColor = colors.prime;
    } else if (isPrime === false) {
      numberColor = colors.composite;
    } else {
      numberColor = colors.testing;
    }

    // Generate witnesses to test
    const witnesses = [];
    const numWitnesses = Math.min(5, number - 2);

    for (let i = 0; i < numWitnesses; i++) {
      witnesses.push(2 + i);
    }

    // Calculate positions for witnesses in a circle
    const witnessPositions = [];
    const radius = 5;
    const angleStep = (2 * Math.PI) / witnesses.length;

    for (let i = 0; i < witnesses.length; i++) {
      const angle = i * angleStep;
      const x = Math.sin(angle) * radius;
      const z = Math.cos(angle) * radius;
      witnessPositions.push([x, 0, z]);
    }

    return (
      <group>
        {/* Main number being tested */}
        <NumberDisplay
          position={[0, 0, 0]}
          number={number}
          label="Number to Test"
          color={numberColor}
        />

        {/* Witnesses to test */}
        {witnesses.map((witness, index) => {
          // Determine if this witness is the current one being checked
          const isCurrent = witness === currentDivisor;
          // Determine if this witness proves the number is composite
          const isCompositeWitness = isCurrent && isPrime === false;
          // Determine color based on status
          let witnessColor;
          if (isCurrent) {
            witnessColor = isCompositeWitness ? colors.composite : colors.factor;
          } else {
            witnessColor = colors.nonFactor;
          }

          return (
            <group key={`witness-${index}`}>
              <NumberDisplay
                position={witnessPositions[index]}
                number={witness}
                label={isCurrent ? "Current Witness" : null}
                color={witnessColor}
                size={FACTOR_SIZE}
              />

              {/* Connection line if this is the current witness */}
              {isCurrent && (
                <ConnectionLine
                  from={[0, 0, 0]}
                  to={witnessPositions[index]}
                  color={isCompositeWitness ? colors.composite : colors.factor}
                />
              )}

              {/* Fermat test result if this is the current witness */}
              {isCurrent && (
                <Html
                  position={[
                    witnessPositions[index][0] / 2,
                    witnessPositions[index][1] + 1,
                    witnessPositions[index][2] / 2
                  ]}
                  center
                  occlude
                >
                  <div style={{
                    color: isDark ? 'white' : 'black',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    width: '200px',
                    textAlign: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                    padding: '4px 8px',
                    borderRadius: '4px'
                  }}>
                    {witness}^({number}-1) mod {number} = {isCompositeWitness ? "not 1" : "1"}
                  </div>
                </Html>
              )}
            </group>
          );
        })}

        {/* Fermat's Little Theorem explanation */}
        <Html
          position={[0, -3, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '14px',
            fontWeight: 'bold',
            width: '300px',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none',
            background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            Fermat's Little Theorem: If n is prime, then a^(n-1) = 1 (mod n) for all a where 1 &lt; a &lt; n
          </div>
        </Html>
      </group>
    );
  };

  // Create a Miller-Rabin test visualization
  const MillerRabinVisualization = ({ number, currentDivisor, isPrime }) => {
    // Calculate color based on primality
    let numberColor;
    if (isPrime === true) {
      numberColor = colors.prime;
    } else if (isPrime === false) {
      numberColor = colors.composite;
    } else {
      numberColor = colors.testing;
    }

    // Generate witnesses to test
    const witnesses = [];
    const numWitnesses = Math.min(4, number - 4);

    for (let i = 0; i < numWitnesses; i++) {
      witnesses.push(2 + i);
    }

    // Calculate positions for witnesses in a circle
    const witnessPositions = [];
    const radius = 5;
    const angleStep = (2 * Math.PI) / witnesses.length;

    for (let i = 0; i < witnesses.length; i++) {
      const angle = i * angleStep;
      const x = Math.sin(angle) * radius;
      const z = Math.cos(angle) * radius;
      witnessPositions.push([x, 0, z]);
    }

    // Calculate r and d where n-1 = 2^r * d
    let r = 0;
    let d = number - 1;

    while (d % 2 === 0) {
      d /= 2;
      r++;
    }

    return (
      <group>
        {/* Main number being tested */}
        <NumberDisplay
          position={[0, 0, 0]}
          number={number}
          label="Number to Test"
          color={numberColor}
        />

        {/* Witnesses to test */}
        {witnesses.map((witness, index) => {
          // Determine if this witness is the current one being checked
          const isCurrent = witness === currentDivisor;
          // Determine if this witness proves the number is composite
          const isCompositeWitness = isCurrent && isPrime === false;
          // Determine color based on status
          let witnessColor;
          if (isCurrent) {
            witnessColor = isCompositeWitness ? colors.composite : colors.factor;
          } else {
            witnessColor = colors.nonFactor;
          }

          return (
            <group key={`witness-${index}`}>
              <NumberDisplay
                position={witnessPositions[index]}
                number={witness}
                label={isCurrent ? "Current Witness" : null}
                color={witnessColor}
                size={FACTOR_SIZE}
              />

              {/* Connection line if this is the current witness */}
              {isCurrent && (
                <ConnectionLine
                  from={[0, 0, 0]}
                  to={witnessPositions[index]}
                  color={isCompositeWitness ? colors.composite : colors.factor}
                />
              )}

              {/* Miller-Rabin test result if this is the current witness */}
              {isCurrent && (
                <Html
                  position={[
                    witnessPositions[index][0] / 2,
                    witnessPositions[index][1] + 1,
                    witnessPositions[index][2] / 2
                  ]}
                  center
                  occlude
                >
                  <div style={{
                    color: isDark ? 'white' : 'black',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    width: '200px',
                    textAlign: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                    padding: '4px 8px',
                    borderRadius: '4px'
                  }}>
                    Testing witness a = {witness}
                  </div>
                </Html>
              )}
            </group>
          );
        })}

        {/* Miller-Rabin decomposition */}
        <Html
          position={[0, -3, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '14px',
            fontWeight: 'bold',
            width: '300px',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none',
            background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            {number}-1 = 2^{r} * {d} (where {d} is odd)
          </div>
        </Html>
      </group>
    );
  };

  // Create a connection line between two points
  const ConnectionLine = ({ from, to, color }) => {
    const ref = useRef();

    useEffect(() => {
      if (ref.current) {
        // Create a direction vector
        const direction = new THREE.Vector3(
          to[0] - from[0],
          to[1] - from[1],
          to[2] - from[2]
        );

        // Calculate length
        const length = direction.length();

        // Normalize direction
        direction.normalize();

        // Position at midpoint
        ref.current.position.set(
          (from[0] + to[0]) / 2,
          (from[1] + to[1]) / 2,
          (from[2] + to[2]) / 2
        );

        // Orient along direction
        ref.current.quaternion.setFromUnitVectors(
          new THREE.Vector3(0, 1, 0),
          direction
        );

        // Scale to length
        ref.current.scale.set(0.1, length, 0.1);
      }
    }, [from, to]);

    return (
      <mesh ref={ref} castShadow>
        <cylinderGeometry args={[1, 1, 1, 8]} />
        <meshStandardMaterial color={color} />
      </mesh>
    );
  };

  // Render the Primality Test visualization
  const renderPrimalityTestVisualization = () => {
    if (!currentStepData) return null;

    const { number, method, currentDivisor, isPrime } = currentStepData;

    // Choose the appropriate visualization based on the method
    switch (method) {
      case 'fermat':
        return (
          <FermatVisualization
            number={number}
            currentDivisor={currentDivisor}
            isPrime={isPrime}
          />
        );
      case 'miller_rabin':
        return (
          <MillerRabinVisualization
            number={number}
            currentDivisor={currentDivisor}
            isPrime={isPrime}
          />
        );
      default:
        return (
          <TrialDivisionVisualization
            number={number}
            currentDivisor={currentDivisor}
            isPrime={isPrime}
          />
        );
    }
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Primality Test Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position}>
          {renderPrimalityTestVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default PrimalityTestVisualization;
