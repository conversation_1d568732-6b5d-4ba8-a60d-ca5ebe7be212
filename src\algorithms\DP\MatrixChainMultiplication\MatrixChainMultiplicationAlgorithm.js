// MatrixChainMultiplicationAlgorithm.js
// Implementation of Matrix Chain Multiplication algorithm using dynamic programming

/**
 * Generate steps for solving the Matrix Chain Multiplication problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateMatrixChainMultiplicationSteps = (params) => {
  console.log('generateMatrixChainMultiplicationSteps called with params:', params);
  const { dimensions = [30, 35, 15, 5, 10, 20, 25] } = params;
  const steps = [];

  // Validate input
  if (!Array.isArray(dimensions) || dimensions.length < 2) {
    throw new Error('Dimensions must be an array with at least 2 elements');
  }

  const n = dimensions.length - 1; // Number of matrices

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Matrix Chain Multiplication algorithm with ${n} matrices`,
    dimensions,
    matrices: dimensions.slice(0, -1).map((rows, i) => ({
      rows,
      cols: dimensions[i + 1],
      index: i
    })),
    dp: null,
    s: null,
    currentI: null,
    currentJ: null,
    currentK: null,
    parenthesization: null,
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table for minimum number of operations
  const dp = Array(n).fill().map(() => Array(n).fill(0));
  
  // Create table to store optimal split positions
  const s = Array(n).fill().map(() => Array(n).fill(0));

  // Add DP table initialization step
  steps.push({
    type: 'initialize_table',
    message: `Initialize the DP table. dp[i][j] will store the minimum number of scalar multiplications needed to compute the matrix product from matrix i to matrix j.`,
    dimensions,
    matrices: dimensions.slice(0, -1).map((rows, i) => ({
      rows,
      cols: dimensions[i + 1],
      index: i
    })),
    dp: JSON.parse(JSON.stringify(dp)),
    s: JSON.parse(JSON.stringify(s)),
    currentI: null,
    currentJ: null,
    currentK: null,
    parenthesization: null,
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  // l is the chain length
  for (let l = 2; l <= n; l++) {
    // Add step for current chain length
    steps.push({
      type: 'chain_length',
      message: `Consider chains of length ${l}`,
      dimensions,
      matrices: dimensions.slice(0, -1).map((rows, i) => ({
        rows,
        cols: dimensions[i + 1],
        index: i
      })),
      dp: JSON.parse(JSON.stringify(dp)),
      s: JSON.parse(JSON.stringify(s)),
      currentI: null,
      currentJ: null,
      currentK: null,
      chainLength: l,
      parenthesization: null,
      result: null,
      pseudocodeLine: 6
    });

    // i is the starting matrix index
    for (let i = 0; i < n - l + 1; i++) {
      const j = i + l - 1; // j is the ending matrix index
      
      // Add step for current subproblem
      steps.push({
        type: 'subproblem',
        message: `Consider subproblem dp[${i}][${j}]: minimum cost to multiply matrices ${i} through ${j}`,
        dimensions,
        matrices: dimensions.slice(0, -1).map((rows, idx) => ({
          rows,
          cols: dimensions[idx + 1],
          index: idx,
          isActive: idx >= i && idx <= j
        })),
        dp: JSON.parse(JSON.stringify(dp)),
        s: JSON.parse(JSON.stringify(s)),
        currentI: i,
        currentJ: j,
        currentK: null,
        chainLength: l,
        parenthesization: null,
        result: null,
        pseudocodeLine: 7
      });

      dp[i][j] = Number.MAX_SAFE_INTEGER;
      
      // Try each possible split position k
      for (let k = i; k < j; k++) {
        // Add step for considering split at k
        steps.push({
          type: 'consider_split',
          message: `Consider splitting between matrices ${k} and ${k+1}`,
          dimensions,
          matrices: dimensions.slice(0, -1).map((rows, idx) => ({
            rows,
            cols: dimensions[idx + 1],
            index: idx,
            isActive: idx >= i && idx <= j,
            isSplit: idx === k
          })),
          dp: JSON.parse(JSON.stringify(dp)),
          s: JSON.parse(JSON.stringify(s)),
          currentI: i,
          currentJ: j,
          currentK: k,
          chainLength: l,
          parenthesization: null,
          result: null,
          pseudocodeLine: 9
        });

        // Calculate cost for this split
        const cost = dp[i][k] + dp[k+1][j] + dimensions[i] * dimensions[k+1] * dimensions[j+1];
        
        // Add step for calculating cost
        steps.push({
          type: 'calculate_cost',
          message: `Calculate cost for split at ${k}: dp[${i}][${k}] + dp[${k+1}][${j}] + dimensions[${i}] * dimensions[${k+1}] * dimensions[${j+1}] = ${dp[i][k]} + ${dp[k+1][j]} + ${dimensions[i]} * ${dimensions[k+1]} * ${dimensions[j+1]} = ${cost}`,
          dimensions,
          matrices: dimensions.slice(0, -1).map((rows, idx) => ({
            rows,
            cols: dimensions[idx + 1],
            index: idx,
            isActive: idx >= i && idx <= j,
            isSplit: idx === k
          })),
          dp: JSON.parse(JSON.stringify(dp)),
          s: JSON.parse(JSON.stringify(s)),
          currentI: i,
          currentJ: j,
          currentK: k,
          cost,
          chainLength: l,
          parenthesization: null,
          result: null,
          pseudocodeLine: 10
        });

        // Update if this split is better
        if (cost < dp[i][j]) {
          dp[i][j] = cost;
          s[i][j] = k;
          
          // Add step for updating minimum cost
          steps.push({
            type: 'update_min',
            message: `Update minimum cost for dp[${i}][${j}] to ${cost} with split at ${k}`,
            dimensions,
            matrices: dimensions.slice(0, -1).map((rows, idx) => ({
              rows,
              cols: dimensions[idx + 1],
              index: idx,
              isActive: idx >= i && idx <= j,
              isSplit: idx === k
            })),
            dp: JSON.parse(JSON.stringify(dp)),
            s: JSON.parse(JSON.stringify(s)),
            currentI: i,
            currentJ: j,
            currentK: k,
            chainLength: l,
            parenthesization: null,
            result: null,
            pseudocodeLine: 12
          });
        }
      }
    }
  }

  // Function to print optimal parenthesization
  const printOptimalParenthesization = (i, j, s, name = 'A') => {
    if (i === j) {
      return `${name}${i}`;
    } else {
      const k = s[i][j];
      const left = printOptimalParenthesization(i, k, s, name);
      const right = printOptimalParenthesization(k + 1, j, s, name);
      return `(${left} × ${right})`;
    }
  };

  // Get the optimal parenthesization
  const parenthesization = printOptimalParenthesization(0, n - 1, s);

  // Add step for traceback
  steps.push({
    type: 'traceback',
    message: `Trace back to find the optimal parenthesization`,
    dimensions,
    matrices: dimensions.slice(0, -1).map((rows, i) => ({
      rows,
      cols: dimensions[i + 1],
      index: i
    })),
    dp: JSON.parse(JSON.stringify(dp)),
    s: JSON.parse(JSON.stringify(s)),
    currentI: null,
    currentJ: null,
    currentK: null,
    parenthesization,
    result: dp[0][n-1],
    pseudocodeLine: 16
  });

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Minimum number of scalar multiplications: ${dp[0][n-1]}. Optimal parenthesization: ${parenthesization}`,
    dimensions,
    matrices: dimensions.slice(0, -1).map((rows, i) => ({
      rows,
      cols: dimensions[i + 1],
      index: i
    })),
    dp: JSON.parse(JSON.stringify(dp)),
    s: JSON.parse(JSON.stringify(s)),
    currentI: null,
    currentJ: null,
    currentK: null,
    parenthesization,
    result: dp[0][n-1],
    pseudocodeLine: 19
  });

  return { 
    steps, 
    result: dp[0][n-1],
    parenthesization
  };
};

// Create the algorithm object with helper functions
const MatrixChainMultiplicationAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateMatrixChainMultiplicationSteps
};

export default MatrixChainMultiplicationAlgorithm;
