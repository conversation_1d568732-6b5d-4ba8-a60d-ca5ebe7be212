// RadixSortDetailedSteps.js
// Detailed step generation for RadixSort algorithm

/**
 * Generates detailed steps for the RadixSort algorithm
 * @param {Array} inputArray - The array to sort
 * @returns {Object} - Object containing steps and metadata
 */
export const generateRadixSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const initialArrForLength = [...inputArray];
  const steps = [];
  let stepCounter = 0;

  // Helper function to create a step
  const createStep = (type, statement, visualizationData, metadata = {}) => {
    return {
      id: stepCounter++,
      type,
      statement,
      visualizationData,
      metadata: {
        algorithm: 'RadixSort',
        ...metadata
      }
    };
  };

  // Helper function to get digit at specific position
  const getDigit = (num, digitPlace) => {
    return Math.floor(Math.abs(num) / Math.pow(10, digitPlace)) % 10;
  };

  // Helper function to get number of digits
  const getDigitCount = (num) => {
    if (num === 0) return 1;
    return Math.floor(Math.log10(Math.abs(num))) + 1;
  };

  // Initialize visualization data
  let currentElement = -1;
  let currentDigitPlace = -1;
  let currentBucket = -1;
  let buckets = Array.from({ length: 10 }, () => []);
  let sortedIndices = [];

  // Step 0: Initial state
  steps.push(createStep(
    'initial',
    'RadixSort: Starting with the initial array',
    {
      mainArray: {
        values: [...arr],
        currentElement,
        currentDigitPlace,
        currentBucket,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
      digitPlace: -1,
      maxDigits: 0,
    },
    { isInitial: true }
  ));

  const maxNumber = Math.max(...arr.map(num => Math.abs(num)));
  const maxDigits = getDigitCount(maxNumber);

  steps.push(createStep(
    'find_max',
    `Found maximum number: ${maxNumber} with ${maxDigits} digit${maxDigits > 1 ? 's' : ''}`,
    {
      mainArray: {
        values: [...arr],
        currentElement: arr.indexOf(maxNumber),
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
      digitPlace: -1,
      maxDigits,
    },
    { maxNumber, maxDigits }
  ));

  for (let digitPlace = 0; digitPlace < maxDigits; digitPlace++) {
    steps.push(createStep(
      'digit_start',
      `Processing digit place ${digitPlace + 1} (10^${digitPlace}) - ${digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}`} place`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
        digitPlace,
        maxDigits,
      },
      { digitPlace, digitName: digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}` }
    ));

    buckets = Array.from({ length: 10 }, () => []);

    for (let i = 0; i < arr.length; i++) {
      const element = arr[i];
      const digit = getDigit(element, digitPlace);

      steps.push(createStep(
        'process_element',
        `Processing element ${element} at index ${i}`,
        // ... (visualizationData for process_element)
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: -1,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit }
      ));

      steps.push(createStep(
        'extract_digit',
        `Digit at position ${digitPlace + 1} of ${element} is ${digit}`,
        // ... (visualizationData for extract_digit)
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, digitPlace }
      ));

      buckets[digit].push({ value: element, originalIndex: i });

      steps.push(createStep(
        'place_in_bucket',
        `Placed ${element} in bucket ${digit}`,
        // ... (visualizationData for place_in_bucket)
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, bucketSize: buckets[digit].length }
      ));
    }

    steps.push(createStep(
      'buckets_filled',
      `All elements distributed into buckets for digit place ${digitPlace + 1}`,
      // ... (visualizationData for buckets_filled)
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
        digitPlace,
        maxDigits,
      },
      { digitPlace, totalElements: arr.length }
    ));

    let arrayIndex = 0;
    for (let bucketIndex = 0; bucketIndex < 10; bucketIndex++) {
      const initialBucketSize = buckets[bucketIndex].length; // Store initial size for the message and condition

      if (initialBucketSize > 0) {
        // Create progressive array for 'collect_bucket_start'
        // Shows items collected from buckets 0 to bucketIndex-1 in this pass
        let vizArrayCollectStart = new Array(arr.length).fill(null);
        for (let m = 0; m < arrayIndex; m++) {
          vizArrayCollectStart[m] = arr[m];
        }

        steps.push(createStep(
          'collect_bucket_start',
          `Collecting ${initialBucketSize} element${initialBucketSize > 1 ? 's' : ''} from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: vizArrayCollectStart, // Use progressive array instead of [...arr]
              currentElement: -1,
              currentDigitPlace: digitPlace,
              currentBucket: bucketIndex,
              sortedIndices: [],
            },
            buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
            digitPlace,
            maxDigits,
          },
          { bucketIndex, bucketSize: initialBucketSize }
        ));

        let lastElementTargetIndexInMainArray = -1; // MODIFICATION: Variable to track the last placed index

        const numElementsInBucket = buckets[bucketIndex].length; // Redundant if using initialBucketSize, but kept for loop logic
        for (let k = 0; k < numElementsInBucket; k++) {
          const elementObject = buckets[bucketIndex].shift();
          arr[arrayIndex] = elementObject.value;
          lastElementTargetIndexInMainArray = arrayIndex; // MODIFICATION: Update the last placed index

          steps.push(createStep(
            'place_in_array',
            `Placing ${elementObject.value} at position ${arrayIndex}`,
            {
              mainArray: {
                values: [...arr],
                currentElement: arrayIndex,
                currentDigitPlace: digitPlace,
                currentBucket: bucketIndex,
                sortedIndices: [],
              },
              buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
              digitPlace,
              maxDigits,
            },
            { arrayIndex, element: elementObject.value, bucketIndex, originalIndex: elementObject.originalIndex }
          ));
          arrayIndex++;
        }

        // Create progressive array for 'collect_bucket_end'
        // Shows items collected from buckets 0 to bucketIndex (inclusive) in this pass.
        // 'arrayIndex' now points to the next free slot, so items are in arr[0]...arr[arrayIndex-1].
        let vizArrayCollectEnd = new Array(initialArrForLength.length).fill(null);
        for (let m = 0; m < arrayIndex; m++) {
          vizArrayCollectEnd[m] = arr[m];
        }
        steps.push(createStep(
          'collect_bucket_end',
          `Finished collecting from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: vizArrayCollectEnd, // MODIFIED
              currentElement: lastElementTargetIndexInMainArray,
              currentDigitPlace: digitPlace,
              currentBucket: bucketIndex,
              sortedIndices: [],
            },
            buckets: buckets.map(b => b.map(item => ({ ...item }))), // Shows bucket[bucketIndex] as empty
            digitPlace,
            maxDigits,
          },
          { bucketIndex }
        ));
      }
    }

    steps.push(createStep(
      'digit_complete',
      `Completed processing digit place ${digitPlace + 1}. Array after this pass: [${arr.join(', ')}]`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: -1,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: Array.from({ length: 10 }, () => []),
        digitPlace: -1,
        maxDigits,
      },
      { digitPlace, completedPass: digitPlace + 1 }
    ));
  }

  sortedIndices = Array.from({ length: arr.length }, (_, i) => i);

  steps.push(createStep(
    'complete',
    `RadixSort complete! Final sorted array: [${arr.join(', ')}]`,
    {
      mainArray: {
        values: [...arr],
        currentElement: -1,
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices,
      },
      buckets: Array.from({ length: 10 }, () => []),
      digitPlace: -1,
      maxDigits,
    },
    { isComplete: true, sortedArray: [...arr] }
  ));

  return {
    steps,
    totalSteps: steps.length,
    sortedArray: arr,
    metadata: {
      algorithm: 'RadixSort',
      inputSize: inputArray.length,
      maxDigits,
      totalPasses: maxDigits,
    }
  };
};

export default generateRadixSortDetailedSteps;