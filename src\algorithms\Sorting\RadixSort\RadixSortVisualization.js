// RadixSortVisualization.js
// Visualization component for RadixSort algorithm following the new architecture

import React, { useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';

// Import configuration and components
import CONFIG from './RadixSortConfig'; // Assuming this path is correct
import { getEnhancedDelay } from '../../../utils/speedUtils'; // Assuming this path is correct

// Import standard visualization components (like other sorting algorithms)
// Ensure these paths are correct for your project structure
import { FixedStepBoard, FixedColorLegend, SortingBase } from '../../../components/visualization';
import RadixSortSimulation from '../../../components/visualization/bars/RadixSortSimulation';

/**
 * Main RadixSort visualization component using new architecture
 */
const RadixSortVisualization = () => {
  const theme = useTheme();
  const { camera } = useThree();
  const { state, setState, step, setStep, steps } = useAlgorithm();
  const { speed } = useSpeed();

  const speedRef = useRef(speed);
  const animatingRef = useRef(false);
  const timeoutIdRef = useRef(null);
  const groupRef = useRef();

  const colors = useMemo(() => {
    const isDark = theme?.palette?.mode === 'dark';
    // Ensure CONFIG.colors and its nested structure exist
    return isDark ? CONFIG.colors?.themes?.dark : CONFIG.colors?.themes?.light;
  }, [theme]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  useFrame(({ clock }) => {
    if (state === 'running' && !animatingRef.current && steps && steps.length > 0) {
      if (step < steps.length - 1) {
        animatingRef.current = true;
        const delay = getEnhancedDelay(speedRef.current);
        timeoutIdRef.current = setTimeout(() => {
          setStep(prev => prev + 1);
          animatingRef.current = false;
        }, delay);
      } else {
        setState('completed');
      }
    }

    // Levitation: Use CONFIG.mainArray.levitation as per your config structure
    // and CONFIG.basePlatform.position for the base
    if (CONFIG.mainArray?.levitation?.enabled && // Check based on your provided CONFIG structure
      // The original code had CONFIG.visual.levitation.disableDuringSimulation,
      // adjust if your levitation config under mainArray has a similar flag
      // (!CONFIG.mainArray.levitation.disableDuringSimulation || state === 'idle') &&
      (state === 'idle' || !CONFIG.mainArray.levitation.disableDuringSimulation) && // A common pattern
      groupRef.current) {
      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.mainArray.levitation;
      const basePosition = CONFIG.basePlatform.position; // Master position for the whole group

      // Y-axis levitation
      groupRef.current.position.y = basePosition[1] + Math.sin(time * (levitationConfig.frequency || 0.5) + (levitationConfig.offset || 0)) * (levitationConfig.amplitude || 0.1);
      
      // X and Z levitation (assuming they are not typically part of this config based on your example, but adding for completeness if they were)
      // You would need similar amplitude/frequency/offset properties in CONFIG.mainArray.levitation.x and .z if you want them
      // groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.x.frequency) * levitationConfig.x.amplitude;
      // groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.z.frequency) * levitationConfig.z.amplitude;
      
      // Keep X and Z static to basePosition if not levitating on those axes
      groupRef.current.position.x = basePosition[0];
      groupRef.current.position.z = basePosition[2];

      // Rotational levitation (if configured, example: CONFIG.mainArray.levitation.rotation)
      // if (levitationConfig.rotation?.enabled) {
      //   groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
      // }
    } else if (groupRef.current && CONFIG.basePlatform?.position) {
        // Reset to base position if levitation is off or not applicable
        groupRef.current.position.set(...CONFIG.basePlatform.position);
    }
  });

  useEffect(() => {
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (camera && CONFIG.camera) {
      const cameraConfig = CONFIG.camera;
      camera.position.set(...cameraConfig.position);
      camera.lookAt(...cameraConfig.lookAt);
      camera.fov = cameraConfig.fov;
      camera.updateProjectionMatrix();
    }
  }, [camera]);

  let currentStepData = null;
  if (steps && steps.length > 0) {
    if (step >= 0 && step < steps.length) {
      currentStepData = steps[step];
    } else if (step >= steps.length) {
      currentStepData = steps[steps.length - 1];
    } else if (step === 0 && steps.length > 0) {
      currentStepData = steps[0];
    }
  }

  const legendItems = useMemo(() => {
    // Ensure CONFIG.colorLegend and CONFIG.colorLegend.legendItems exist
    return CONFIG.colorLegend?.legendItems?.map(item => ({
      ...item,
      color: colors[item.colorKey] || colors.bar
    })) || [];
  }, [colors]);

  // Calculate ONLY the dynamic width for the base platform
  const dynamicPlatformWidth = useMemo(() => {
    // Use currentStepData.visualizationData.mainArray.values.length if that's where your array for length is
    // Or currentStepData.mainArray.values.length as per previous attempts
    const arrayValues = currentStepData?.mainArray?.values || currentStepData?.visualizationData?.mainArray?.values;
    const arrayLength = arrayValues?.length ?? (CONFIG.mainArray.bars.defaultCountForSizing ?? 10);
    
    const barW = CONFIG.mainArray.bars.width;
    const barS = CONFIG.mainArray.bars.spacing;

    let totalWidthOfBarsOnly = 0;
    if (arrayLength > 0) {
      totalWidthOfBarsOnly = (arrayLength * barW) + ((arrayLength - 1) * barS);
      if (arrayLength === 1) totalWidthOfBarsOnly = barW; // Edge case for single bar (no spacing)
    }
     if (arrayLength === 0) totalWidthOfBarsOnly = 0; // No bars, no width


    const paddingL = CONFIG.basePlatform.dimensions.lengthPadding.left;
    const paddingR = CONFIG.basePlatform.dimensions.lengthPadding.right;
    const totalPadding = paddingL + paddingR;

    // Platform width is the sum of bars' width and padding
    let calculatedWidth = totalWidthOfBarsOnly + totalPadding;

    // Ensure a minimum width for the platform, e.g., at least the padding, or padding + one bar width
    // Or use a specific CONFIG.basePlatform.dimensions.minWidth if defined.
    const minPlatformWidth = CONFIG.basePlatform.dimensions.minWidth ?? (totalPadding + (arrayLength > 0 ? barW : 0));
    
    calculatedWidth = Math.max(calculatedWidth, minPlatformWidth);
    
    // If for some reason arrayLength leads to a non-sensical width (e.g. NaN if barW/barS are undefined)
    // provide a fallback.
    if (isNaN(calculatedWidth) || calculatedWidth <=0) {
        return totalPadding + (CONFIG.mainArray.bars.defaultCountForSizing ?? 10) * (barW + barS) - barS; // Fallback based on default
    }

    return calculatedWidth;
  }, [
    currentStepData?.mainArray?.values, // Make sure this is the actual path to your array
    currentStepData?.visualizationData?.mainArray?.values,
    CONFIG.mainArray.bars.defaultCountForSizing,
    CONFIG.mainArray.bars.width,
    CONFIG.mainArray.bars.spacing,
    CONFIG.basePlatform.dimensions.lengthPadding.left,
    CONFIG.basePlatform.dimensions.lengthPadding.right,
    CONFIG.basePlatform.dimensions.minWidth,
  ]);

  // console.log("Current Array Length:", currentStepData?.mainArray?.values?.length, "Calculated Platform Width:", dynamicPlatformWidth);

  return (
    <>
      {CONFIG.stepBoard?.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''}
          totalSteps={steps && steps.length > 0 ? steps.length - 1 : 0}
          description={step === 0 ?
            `Radix Sort: Initial Array [${(currentStepData?.mainArray?.values || currentStepData?.visualizationData?.mainArray?.values)?.join(', ') || ''}]` :
            (currentStepData?.statement || 'Radix Sort Algorithm')}
          stepData={currentStepData}
          showStepNumber={step > 0}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {CONFIG.colorLegend?.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      <group ref={groupRef} position={CONFIG.basePlatform.position}>
        {/* Base platform - THIS IS THE TARGET FOR DYNAMIC WIDTH */}
        <SortingBase
          width={dynamicPlatformWidth} // Apply the calculated dynamic width
          height={CONFIG.basePlatform.dimensions.height}
          depth={CONFIG.basePlatform.dimensions.depth}
          color={colors?.platform} // Safe access to colors
          position={[0, -CONFIG.basePlatform.dimensions.height / 2, 0]}
        />

        {/* RadixSort simulation (bars) - Positioned using STATIC offset from CONFIG */}
        {/* BARS WILL LIKELY NOT BE CENTERED with this change if the platform expands. */}
        {/* This is to isolate the platform expansion issue. */}
        <group position={CONFIG.mainArray.bars.baseOffset}> 
          <RadixSortSimulation
            currentStep={currentStepData}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width}
            barSpacing={CONFIG.mainArray.bars.spacing}
            showValues={CONFIG.mainArray.valueLabels.enabled} // Use config for these
            showIndices={CONFIG.mainArray.indexLabels.enabled} // Use config for these
          />
        </group>
      </group>

      <ambientLight intensity={0.6} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={0.8}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
      />
      <directionalLight
        position={[-10, 5, -5]}
        intensity={0.3}
      />
    </>
  );
};

export default RadixSortVisualization;