// RadixSortAlgorithm.js
// Algorithm wrapper for RadixSort following the standard pattern

import { generateRadixSortDetailedSteps } from './RadixSortDetailedSteps';

/**
 * RadixSort Algorithm wrapper that follows the standard pattern
 * used by other sorting algorithms in the application
 */
const RadixSortAlgorithm = {
  /**
   * Generate steps for the RadixSort algorithm
   * @param {Array} array - The array to sort
   * @returns {Array} - Array of steps (for compatibility with other algorithms)
   */
  generateSteps: (array) => {
    const result = generateRadixSortDetailedSteps(array);
    return result.steps; // Return just the steps array for compatibility
  },

  /**
   * Get algorithm metadata
   * @returns {Object} - Algorithm metadata
   */
  getMetadata: () => {
    return {
      name: 'Radix Sort',
      description: 'A non-comparative sorting algorithm that sorts integers by processing individual digits, starting from the least significant digit to the most significant digit.',
      timeComplexity: {
        best: 'O(n·k)',
        average: 'O(n·k)',
        worst: 'O(n·k)',
        note: 'where n is the number of elements and k is the number of digits'
      },
      spaceComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
      stable: true,
      inPlace: false,
      adaptive: false,
      category: 'Distribution Sort'
    };
  },

  /**
   * Validate input array for RadixSort
   * @param {Array} array - The array to validate
   * @returns {Object} - Validation result
   */
  validateInput: (array) => {
    if (!Array.isArray(array)) {
      return {
        valid: false,
        error: 'Input must be an array'
      };
    }

    if (array.length === 0) {
      return {
        valid: false,
        error: 'Array cannot be empty'
      };
    }

    // Check if all elements are non-negative integers
    for (let i = 0; i < array.length; i++) {
      const element = array[i];
      if (!Number.isInteger(element) || element < 0) {
        return {
          valid: false,
          error: `RadixSort requires non-negative integers. Found: ${element} at index ${i}`
        };
      }
    }

    return {
      valid: true,
      error: null
    };
  },

  /**
   * Get default parameters for RadixSort
   * @returns {Object} - Default parameters
   */
  getDefaultParams: () => {
    return {
      arraySize: 10,
      randomize: true,
      customArray: [],
      maxValue: 999,
      minValue: 1
    };
  },

  /**
   * Generate a random array suitable for RadixSort
   * @param {number} size - Size of the array
   * @param {number} minValue - Minimum value (default: 1)
   * @param {number} maxValue - Maximum value (default: 999)
   * @returns {Array} - Random array of non-negative integers
   */
  generateRandomArray: (size, minValue = 1, maxValue = 999) => {
    return Array.from({ length: size }, () =>
      Math.floor(Math.random() * (maxValue - minValue + 1)) + minValue
    );
  },

  /**
   * Sort array using RadixSort algorithm (for verification)
   * @param {Array} array - The array to sort
   * @returns {Array} - Sorted array
   */
  sort: (array) => {
    const arr = [...array];

    // Find the maximum number to know the number of digits
    const max = Math.max(...arr);

    // Count number of digits in the maximum number
    const maxDigits = Math.floor(Math.log10(max)) + 1;

    // Perform counting sort for each digit
    for (let digitPlace = 0; digitPlace < maxDigits; digitPlace++) {
      // Initialize buckets (0-9)
      const buckets = Array.from({ length: 10 }, () => []);

      // Place numbers in buckets based on the current digit
      for (let i = 0; i < arr.length; i++) {
        const digit = getDigit(arr[i], digitPlace);
        buckets[digit].push(arr[i]);
      }

      // Collect elements back from buckets in order
      let arrayIndex = 0;
      for (let bucketIndex = 0; bucketIndex < 10; bucketIndex++) {
        for (let j = 0; j < buckets[bucketIndex].length; j++) {
          arr[arrayIndex] = buckets[bucketIndex][j];
          arrayIndex++;
        }
      }
    }

    return arr;
  }
};

/**
 * Helper function to get digit at specific position
 * @param {number} num - The number
 * @param {number} digitPlace - The digit position (0 = ones, 1 = tens, etc.)
 * @returns {number} - The digit at the specified position
 */
function getDigit(num, digitPlace) {
  return Math.floor(Math.abs(num) / Math.pow(10, digitPlace)) % 10;
}

export default RadixSortAlgorithm;
