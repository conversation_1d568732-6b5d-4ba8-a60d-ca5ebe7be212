// LinearSearchAlgorithm.js
// Implementation of the Linear Search algorithm with step generation

/**
 * Generates steps for the Linear Search algorithm
 * @param {Array} arr - The array to search in
 * @param {number} target - The value to search for
 * @returns {Object} - Object containing steps and the result
 */
export const generateLinearSearchSteps = (arr, target) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Initialize Linear Search'
  });

  // Perform linear search
  let found = false;
  let result = -1;

  for (let i = 0; i < inputArray.length; i++) {
    // Add step to show the current element being considered
    steps.push({
      type: 'consider',
      array: [...inputArray],
      target,
      currentIndex: i,
      currentValue: inputArray[i],
      found: false,
      movement: `Examine element at index ${i} with value ${inputArray[i]}`
    });

    // Compare current element with target
    steps.push({
      type: 'compare',
      array: [...inputArray],
      target,
      currentIndex: i,
      currentValue: inputArray[i],
      found: false,
      movement: `Compare ${inputArray[i]} with target ${target}`
    });

    // Check if the current element is the target
    if (inputArray[i] === target) {
      found = true;
      result = i;

      // Add step to show the target found
      steps.push({
        type: 'found',
        array: [...inputArray],
        target,
        currentIndex: i,
        currentValue: inputArray[i],
        found: true,
        result: i,
        movement: `Target ${target} found at index ${i}`
      });

      break;
    }

    // Add step to show moving to the next element
    if (i < inputArray.length - 1) {
      steps.push({
        type: 'moveNext',
        array: [...inputArray],
        target,
        currentIndex: i,
        nextIndex: i + 1,
        found: false,
        movement: `Move to the next element at index ${i + 1}`
      });
    }
  }

  // If target not found, add a step to show that
  if (!found) {
    steps.push({
      type: 'notFound',
      array: [...inputArray],
      target,
      currentIndex: inputArray.length - 1,
      found: false,
      movement: `Target ${target} not found in the array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    target,
    currentIndex: found ? result : -1,
    found,
    result: found ? result : -1,
    movement: found ? `Linear Search complete: ${target} found at index ${result}` : `Linear Search complete: ${target} not found`
  });

  return { 
    steps, 
    result: {
      found,
      index: found ? result : -1
    }
  };
};

// Default export
const LinearSearchAlgorithm = {
  generateSteps: generateLinearSearchSteps
};

export default LinearSearchAlgorithm;
