// ThemeHtml.js - A wrapper for Html component that provides theme context
import React from 'react';
import { Html } from '@react-three/drei';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

/**
 * ThemeHtml component that wraps Html with ThemeProvider
 * This ensures MUI components inside Html have access to the theme
 */
const ThemeHtml = ({ children, theme, ...props }) => {
  return (
    <Html {...props}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </Html>
  );
};

export default ThemeHtml;
