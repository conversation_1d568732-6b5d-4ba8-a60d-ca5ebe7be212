// BubbleSortAlgorithm.js
import React, { useEffect } from "react";
import { Paper, Typography, Box, useTheme } from "@mui/material";
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { useAlgorithm } from '../../../context/AlgorithmContext';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    // Syntax highlighting colors
    keyword: isDark ? "#569cd6" : "#0000ff", // Blue for keywords (function, if, else)
    function: isDark ? "#dcdcaa" : "#795e26", // Yellow/brown for function name
    parameter: isDark ? "#9cdcfe" : "#001080", // Blue for parameters/variables
    comment: isDark ? "#6a9955" : "#008000", // Green for comments/actions
    operator: isDark ? "#d4d4d4" : "#000000", // Default/operator color
    number: isDark ? "#b5cea8" : "#098658", // Green for numbers
    string: isDark ? "#ce9178" : "#a31515", // Red for strings

    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const BubbleSortAlgorithm = (props) => {
  // Log props
  console.log('BubbleSortAlgorithm - Props:', props);

  // Destructure props
  const { step = 0 } = props;
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Add debugging information
  console.log('BubbleSortAlgorithm - Current step:', step);
  console.log('BubbleSortAlgorithm - Total steps:', algorithmSteps?.length);
  console.log('BubbleSortAlgorithm - Current algorithm step:', currentAlgorithmStep);

  // Determine which line to highlight based on the current step
  let highlightLine = 0; // Default to function declaration

  if (currentAlgorithmStep) {
    switch (currentAlgorithmStep.type) {
      case 'compare':
        highlightLine = 3; // Compare elements comment
        break;
      case 'swap':
        highlightLine = 5; // Swap elements
        break;
      case 'sorted':
        highlightLine = 6; // Return statement (marking as sorted)
        break;
      default:
        highlightLine = 0;
    }
  }

  // Add debugging information
  console.log('BubbleSortAlgorithm - Highlight line:', highlightLine);

  // Add useEffect to log when highlightLine changes
  useEffect(() => {
    console.log('BubbleSortAlgorithm - Highlight line changed:', highlightLine);
  }, [highlightLine]);

  // Add useEffect to log when component is mounted
  useEffect(() => {
    console.log('BubbleSortAlgorithm - Component mounted');
    return () => {
      console.log('BubbleSortAlgorithm - Component unmounted');
    };
  }, []);

  // Add useEffect to log when component is updated
  useEffect(() => {
    console.log('BubbleSortAlgorithm - Component updated');
  });

  useEffect(() => {
    console.log('BubbleSortAlgorithm - Step changed:', step);
  }, [step]);

  useEffect(() => {
    console.log('BubbleSortAlgorithm - Algorithm steps changed:', algorithmSteps);
  }, [algorithmSteps]);

  useEffect(() => {
    console.log('BubbleSortAlgorithm - Current algorithm step changed:', currentAlgorithmStep);
  }, [currentAlgorithmStep]);

  // Get the step message
  const stepMessage = currentAlgorithmStep
    ? currentAlgorithmStep.message
    : step === 0
      ? "Initial state"
      : "Algorithm complete";

  // Function to render a code line with highlighting and arrow
  const CodeLine = ({ line, content, indent = 0 }) => (
    <Typography
      component="div"
      sx={{
        mb: 0.5,
        bgcolor: highlightLine === line ? colors.highlight : "rgba(0,0,0,0.03)",
        borderRadius: 1,
        p: 1,
        position: 'relative',
        border: `1px solid ${colors.border}`,
        opacity: highlightLine === line ? 1 : 0.7,
        transition: 'all 0.2s ease-in-out',
        pl: 1 + indent * 2, // Increase padding for indentation
      }}
    >
      {highlightLine === line && (
        <ArrowRightIcon
          sx={{
            position: 'absolute',
            left: -32,
            top: '50%',
            transform: 'translateY(-50%)',
            color: theme.palette.primary.main,
            fontSize: '2rem',
            animation: 'pulse 1.5s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
              '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
              '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
            },
          }}
        />
      )}
      {content}
    </Typography>
  );

  return (
    <Paper
      elevation={3}
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
      }}
    >

      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {stepMessage}
        </Typography>
      </Paper>

      {/* Code block */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.7)' : 'rgba(245, 245, 245, 0.9)',
          overflow: 'auto', // Add horizontal scrolling
          maxWidth: '100%', // Ensure it doesn't exceed container width
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        <Box
          sx={{
            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
            whiteSpace: "nowrap", // Prevent line wrapping
            fontSize: "0.9rem",
            lineHeight: 1.7,
            color: colors.text,
            p: 0.5,
            minWidth: 'min-content', // Ensure content doesn't shrink below its minimum width
          }}
        >
          {/* Function Signature */}
          <CodeLine
            line={0}
            content={<>
            <Box component="span" sx={{ color: colors.keyword }}>
              function
            </Box>{" "}
            <Box component="span" sx={{ color: colors.function }}>
              bubbleSort
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              (
            </Box>
            <Box component="span" sx={{ color: colors.parameter }}>
              arr
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              ):
            </Box>
            </>}
          />

          {/* Outer Loop */}
          <Box sx={{ pl: 2 }}>
            {/* Indentation level 1 (increased padding) */}
            <CodeLine
              line={1}
              indent={1}
              content={<>
              <Box component="span" sx={{ color: colors.keyword }}>
                for
              </Box>{" "}
              <Box component="span" sx={{ color: colors.parameter }}>
                i
              </Box>{" "}
              <Box component="span" sx={{ color: colors.keyword }}>
                in
              </Box>{" "}
              <Box component="span" sx={{ color: colors.keyword }}>
                range
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                (
              </Box>
              <Box component="span" sx={{ color: colors.keyword }}>
                len
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                (
              </Box>
              <Box component="span" sx={{ color: colors.parameter }}>
                arr
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                )
              </Box>{" "}
              <Box component="span" sx={{ color: colors.operator }}>
                -
              </Box>{" "}
              <Box component="span" sx={{ color: colors.number }}>
                1
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                ):
              </Box>
              </>}
            />

            {/* Inner Loop */}
            <Box sx={{ pl: 2 }}>
              {/* Indentation level 2 */}
              <CodeLine
                line={2}
                indent={2}
                content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  for
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  j
                </Box>{" "}
                <Box component="span" sx={{ color: colors.keyword }}>
                  in
                </Box>{" "}
                <Box component="span" sx={{ color: colors.keyword }}>
                  range
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.keyword }}>
                  len
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  -
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  i
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  -
                </Box>{" "}
                <Box component="span" sx={{ color: colors.number }}>
                  1
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ):
                </Box>
                </>}
              />

              {/* Compare Elements */}
              <Box sx={{ pl: 2 }}>
                {/* Indentation level 3 */}
                <CodeLine
                  line={3}
                  indent={3}
                  content={
                    <Box component="span" sx={{ color: colors.comment, opacity: 0.5 }}>
                      # Compare adjacent elements
                    </Box>
                  }
                />

                {/* If Condition */}
                <CodeLine
                  line={4}
                  indent={3}
                  content={<>
                  <Box component="span" sx={{ color: colors.keyword }}>
                    if
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.parameter }}>
                    arr
                  </Box>
                  <Box component="span" sx={{ color: colors.operator }}>
                    [
                  </Box>
                  <Box component="span" sx={{ color: colors.parameter }}>
                    j
                  </Box>
                  <Box component="span" sx={{ color: colors.operator }}>
                    ]
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.operator }}>
                    &gt;
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.parameter }}>
                    arr
                  </Box>
                  <Box component="span" sx={{ color: colors.operator }}>
                    [
                  </Box>
                  <Box component="span" sx={{ color: colors.parameter }}>
                    j
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.operator }}>
                    +
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.number }}>
                    1
                  </Box>
                  <Box component="span" sx={{ color: colors.operator }}>
                    ]
                  </Box>
                  <Box component="span" sx={{ color: colors.operator }}>
                    :
                  </Box>
                  </>}
                />

                {/* Swap Elements */}
                <Box sx={{ pl: 2 }}>
                  {/* Indentation level 4 */}
                  <CodeLine
                    line={5}
                    indent={4}
                    content={<>
                    <Box component="span" sx={{ color: colors.parameter }}>
                      arr
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      [
                    </Box>
                    <Box component="span" sx={{ color: colors.parameter }}>
                      j
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ]
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ,
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.parameter }}>
                      arr
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      [
                    </Box>
                    <Box component="span" sx={{ color: colors.parameter }}>
                      j
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.operator }}>
                      +
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.number }}>
                      1
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ]
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.operator }}>
                      =
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.parameter }}>
                      arr
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      [
                    </Box>
                    <Box component="span" sx={{ color: colors.parameter }}>
                      j
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.operator }}>
                      +
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.number }}>
                      1
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ]
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ,
                    </Box>{" "}
                    <Box component="span" sx={{ color: colors.parameter }}>
                      arr
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      [
                    </Box>
                    <Box component="span" sx={{ color: colors.parameter }}>
                      j
                    </Box>
                    <Box component="span" sx={{ color: colors.operator }}>
                      ]
                    </Box>
                    </>}
                  />
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Return Statement */}
          <Box sx={{ pl: 2 }}>
            {/* Indentation level 1 */}
            <CodeLine
              line={6}
              indent={1}
              content={
                <>
                  <Box component="span" sx={{ color: colors.keyword }}>
                    return
                  </Box>{" "}
                  <Box component="span" sx={{ color: colors.parameter }}>
                    arr
                  </Box>
                </>
              }
            />
          </Box>
        </Box>
      </Paper>
    </Paper>
  );
};

export default BubbleSortAlgorithm;
