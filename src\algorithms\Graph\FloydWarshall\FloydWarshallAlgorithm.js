// FloydWarshallAlgorithm.js
// This file contains the core logic for the Floyd-Warshall algorithm

/**
 * Generate a random weighted graph
 * @param {number} vertices - Number of vertices
 * @param {number} density - Edge density (0-1)
 * @param {number} minWeight - Minimum edge weight
 * @param {number} maxWeight - Maximum edge weight
 * @param {boolean} allowNegative - Whether to allow negative weights
 * @returns {Array} - Adjacency matrix representation of the graph
 */
export const generateRandomGraph = (vertices, density, minWeight, maxWeight, allowNegative) => {
  // Initialize adjacency matrix with Infinity
  const graph = Array(vertices).fill().map(() => Array(vertices).fill(Infinity));
  
  // Set diagonal to 0 (distance from vertex to itself)
  for (let i = 0; i < vertices; i++) {
    graph[i][i] = 0;
  }
  
  // Generate random edges
  for (let i = 0; i < vertices; i++) {
    for (let j = 0; j < vertices; j++) {
      if (i !== j) {
        // Determine if edge exists based on density
        if (Math.random() < density) {
          // Generate random weight
          let weight;
          if (allowNegative) {
            weight = Math.floor(Math.random() * (maxWeight - minWeight + 1) + minWeight);
            // 20% chance of negative weight if allowed
            if (Math.random() < 0.2) {
              weight = -weight;
            }
          } else {
            weight = Math.floor(Math.random() * (maxWeight - minWeight + 1) + minWeight);
          }
          graph[i][j] = weight;
        }
      }
    }
  }
  
  return graph;
};

/**
 * Check if the graph has a negative cycle
 * @param {Array} dist - Distance matrix
 * @returns {boolean} - True if negative cycle exists
 */
export const hasNegativeCycle = (dist) => {
  const n = dist.length;
  
  // Check for negative diagonal
  for (let i = 0; i < n; i++) {
    if (dist[i][i] < 0) {
      return true;
    }
  }
  
  return false;
};

/**
 * Generate steps for the Floyd-Warshall algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateFloydWarshallSteps = (params) => {
  const { vertices, density, minWeight, maxWeight, allowNegative } = params;
  const steps = [];
  
  // Generate random graph
  const graph = generateRandomGraph(vertices, density, minWeight, maxWeight, allowNegative);
  
  // Add initial step
  steps.push({
    type: 'initialize',
    dist: JSON.parse(JSON.stringify(graph)),
    k: -1,
    i: -1,
    j: -1,
    movement: 'Initialize the distance matrix with direct edge weights. Infinity means no direct path exists.'
  });
  
  // Create a copy of the graph for the algorithm
  const dist = JSON.parse(JSON.stringify(graph));
  
  // Floyd-Warshall algorithm
  for (let k = 0; k < vertices; k++) {
    // Add step for new intermediate vertex
    steps.push({
      type: 'new_intermediate',
      dist: JSON.parse(JSON.stringify(dist)),
      k,
      i: -1,
      j: -1,
      movement: `Consider vertex ${k} as an intermediate vertex for all paths`
    });
    
    for (let i = 0; i < vertices; i++) {
      for (let j = 0; j < vertices; j++) {
        // Skip if i and j are the same vertex
        if (i === j) continue;
        
        // Current direct distance
        const directDist = dist[i][j];
        
        // Distance through vertex k
        const throughK = dist[i][k] + dist[k][j];
        
        // Add step for comparison
        steps.push({
          type: 'compare',
          dist: JSON.parse(JSON.stringify(dist)),
          k,
          i,
          j,
          directDist,
          throughK,
          movement: `Compare direct path from ${i} to ${j} (${directDist === Infinity ? '∞' : directDist}) with path through vertex ${k} (${dist[i][k] === Infinity || dist[k][j] === Infinity ? '∞' : throughK})`
        });
        
        // Update distance if path through k is shorter
        if (dist[i][k] !== Infinity && dist[k][j] !== Infinity && throughK < directDist) {
          dist[i][j] = throughK;
          
          // Add step for update
          steps.push({
            type: 'update',
            dist: JSON.parse(JSON.stringify(dist)),
            k,
            i,
            j,
            oldDist: directDist,
            newDist: throughK,
            movement: `Update distance from ${i} to ${j} to ${throughK} using vertex ${k} as intermediate`
          });
        } else {
          // Add step for no update
          steps.push({
            type: 'no_update',
            dist: JSON.parse(JSON.stringify(dist)),
            k,
            i,
            j,
            movement: `Keep current distance from ${i} to ${j} as ${directDist === Infinity ? '∞' : directDist}`
          });
        }
      }
    }
  }
  
  // Check for negative cycles
  const hasNegative = hasNegativeCycle(dist);
  
  // Add final step
  steps.push({
    type: 'complete',
    dist: JSON.parse(JSON.stringify(dist)),
    k: -1,
    i: -1,
    j: -1,
    hasNegativeCycle: hasNegative,
    movement: hasNegative 
      ? 'Algorithm complete. The graph contains a negative cycle!' 
      : 'Algorithm complete. The distance matrix now contains the shortest path distances between all pairs of vertices.'
  });
  
  return {
    steps,
    graph,
    dist,
    hasNegativeCycle: hasNegative
  };
};

// Export the algorithm functions
export default {
  generateFloydWarshallSteps,
  generateRandomGraph,
  hasNegativeCycle
};
