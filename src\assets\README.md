# Logo Assets for Algorithm Simulator

This directory contains the logo assets for the Algorithm Simulator application.

## Available Logo Variants

- `logo.svg` - Full logo for dark backgrounds
- `logo-light.svg` - Full logo for light backgrounds
- `logo-icon.svg` - Icon-only version (works on both light and dark backgrounds)
- `/public/favicon.svg` - Favicon version

## Usage

### Direct Import

You can import the SVG files directly as React components:

```jsx
import { ReactComponent as Logo } from './assets/logo.svg';
import { ReactComponent as LogoLight } from './assets/logo-light.svg';
import { ReactComponent as LogoIcon } from './assets/logo-icon.svg';

// Usage
<Logo width="100" height="100" />
```

### Theme-Aware Logo Component

For automatic theme detection, use the `ThemeAwareLogo` component:

```jsx
import ThemeAwareLogo from './components/Logo/ThemeAwareLogo';

// Usage
<ThemeAwareLogo variant="full" sx={{ width: '100px', height: '100px' }} />
<ThemeAwareLogo variant="icon" sx={{ width: '50px', height: '50px' }} />
```

## Design Elements

The logo represents algorithm visualization concepts:

1. **Sorting Bars** - Representing sorting algorithms like Bubble Sort, Quick Sort, etc.
2. **Graph Nodes** - Representing graph algorithms like Dijkstra's, BFS, etc.
3. **Binary Tree Elements** - Representing tree-based algorithms
4. **Central Node** - Representing the focal point of algorithm execution

## Colors

The logo uses the application's theme colors:
- Primary blues (`#4fc3f7`, `#1976d2`) for main elements
- Secondary pinks (`#f06292`, `#dc004e`) for highlighted elements
- Success greens (`#81c784`, `#2e7d32`) for completion indicators

These colors automatically adapt to the current theme when using the `ThemeAwareLogo` component.
