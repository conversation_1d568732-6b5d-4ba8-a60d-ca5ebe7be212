import React from 'react';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable ground plane component for algorithm visualizations
 * 
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the ground plane
 * @param {number} props.width - Width of the ground plane
 * @param {number} props.depth - Depth of the ground plane
 * @param {string} props.color - Color of the ground plane (overrides theme-based color)
 * @param {boolean} props.receiveShadow - Whether the ground plane should receive shadows
 * @returns {JSX.Element} - The rendered ground plane component
 */
const GroundPlane = ({ 
    position = [0, -0.01, 0], 
    width = 50, 
    depth = 50,
    color = null,
    receiveShadow = true
}) => {
    const theme = useTheme();
    
    // Use theme-based color if no specific color is provided
    const planeColor = color || (theme.palette.mode === 'dark' ? '#111111' : '#f5f5f5');
    
    return (
        <mesh position={position} rotation={[-Math.PI / 2, 0, 0]} receiveShadow={receiveShadow}>
            <planeGeometry args={[width, depth]} />
            <meshStandardMaterial 
                color={planeColor}
                roughness={0.8}
                metalness={0.2}
            />
        </mesh>
    );
};

export default GroundPlane;
