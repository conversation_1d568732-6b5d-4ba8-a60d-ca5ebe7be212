// ExponentialSearchController.js
// This component provides the controls for the Exponential Search algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import SearchIcon from '@mui/icons-material/Search';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const ExponentialSearchController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];
    const target = params?.target !== undefined ? params.target : 42;

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // State for target input
    const [targetInput, setTargetInput] = useState(target.toString());
    const [targetError, setTargetError] = useState('');

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Initialize target input when params change
    useEffect(() => {
        setTargetInput(target.toString());
    }, [target]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: []
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle target value change
    const handleTargetChange = (newTarget) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                target: newTarget
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle target input change
    const handleTargetInputChange = (value) => {
        setTargetInput(value);
    };

    // Handle target apply button
    const handleTargetApply = () => {
        try {
            // Check if input is empty
            if (!targetInput || targetInput.trim() === '') {
                setTargetInput('42');
                handleTargetChange(42);
                setTargetError('');
                return;
            }
            
            // Parse the input string into a number
            const parsedTarget = parseInt(targetInput.trim(), 10);
            if (isNaN(parsedTarget)) {
                throw new Error('Invalid number');
            }
            
            // Update the target value
            handleTargetChange(parsedTarget);
            setTargetError('');
        } catch (error) {
            setTargetError(error.message);
        }
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default sorted array if input is empty
                const defaultArray = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default sorted array if input is empty
                const defaultArray = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Check if the array is sorted
            for (let i = 1; i < parsedArray.length; i++) {
                if (parsedArray[i] < parsedArray[i - 1]) {
                    setCustomArrayError('Exponential Search requires a sorted array. Please provide a sorted array.');
                    return;
                }
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box sx={{ p: 1, height: '100%', overflowY: 'auto' }}>
            {/* Algorithm Title */}
            <Typography variant="h5" gutterBottom>
                Exponential Search
            </Typography>

            {/* Information Section */}
            <InformationSection defaultExpanded={false}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Exponential Search:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Exponential Search is a searching algorithm that works on sorted arrays. It involves two stages: first, it finds a range where the target might be by repeatedly doubling an index, then it performs a binary search within that range.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(1) - Target is found at the first position
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(log n) - Where n is the size of the array
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(log n) - Target is found at the last position or not found
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(1) - Exponential Search uses a constant amount of extra space
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, mt: 1, fontWeight: 500 }}>
                        Important Note:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Exponential Search requires a sorted array to work correctly. It's particularly useful for unbounded or infinite arrays, and when the target is likely to be near the beginning of the array.
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'targetInput',
                        type: 'numberInput',
                        label: 'Target Value',
                        error: targetError,
                        helperText: "Enter a number to search for in the array.",
                        placeholder: "e.g., 42",
                        onApply: handleTargetApply,
                        icon: SearchIcon
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated numbers in sorted order (e.g., 5, 10, 15). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 10, 15, 20, 25 (min 3, max 20 numbers, sorted)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    targetInput,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.targetInput !== undefined && newValues.targetInput !== targetInput) {
                        handleTargetInputChange(newValues.targetInput);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.movement || ''
                }))}
                currentStep={step}
                defaultExpanded
                renderStep={(_, index) => {
                    const currentStep = steps && steps[index];
                    const isCurrentStep = index === step - 1;

                    if (!currentStep) return null;

                    // Determine step color based on step type
                    let stepColor = 'primary.main';
                    if (currentStep?.type === 'checkBound' || currentStep?.type === 'updateBound') {
                        stepColor = 'secondary.main';
                    } else if (currentStep?.type === 'compareMid') {
                        stepColor = 'warning.main';
                    } else if (currentStep?.type === 'found') {
                        stepColor = 'success.main';
                    } else if (currentStep?.type === 'notFound') {
                        stepColor = 'error.main';
                    } else if (currentStep?.type === 'updateLeft' || currentStep?.type === 'updateRight') {
                        stepColor = 'info.main';
                    }

                    return (
                        <Typography
                            variant="body2"
                            component="div"
                            sx={{
                                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                                fontSize: '0.85rem',
                                fontWeight: isCurrentStep ? 'bold' : 'normal',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                mb: 0.75,
                                pb: 0.75,
                                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                    bgcolor: 'action.hover',
                                    borderRadius: '4px',
                                }
                            }}
                        >
                            <Box
                                component="span"
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: '24px',
                                    height: '24px',
                                    borderRadius: '12px',
                                    bgcolor: isCurrentStep ? stepColor : 'rgba(0, 0, 0, 0.1)',
                                    color: isCurrentStep ? '#ffffff' : 'text.secondary',
                                    mr: 1.5,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold',
                                    flexShrink: 0,
                                    boxShadow: isCurrentStep ? `0 0 0 2px ${stepColor}40` : 'none',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {index + 1}
                            </Box>
                            {currentStep.movement}
                        </Typography>
                    );
                }}
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Exponential Search Algorithm"
                defaultExpanded
                currentStep={step > 0 && steps && steps.length > 0 ?
                    // Map the current step to the corresponding line number
                    steps[step - 1]?.type === 'init' ? 0 :
                        steps[step - 1]?.type === 'checkSorted' ? 1 :
                            steps[step - 1]?.type === 'checkFirst' ? 2 :
                                steps[step - 1]?.type === 'startExponential' ? 3 :
                                    steps[step - 1]?.type === 'checkBound' || steps[step - 1]?.type === 'updateBound' ? 4 :
                                        steps[step - 1]?.type === 'finalBound' ? 5 :
                                            steps[step - 1]?.type === 'startBinary' ? 6 :
                                                steps[step - 1]?.type === 'calculateMid' ? 7 :
                                                    steps[step - 1]?.type === 'compareMid' ? 8 :
                                                        steps[step - 1]?.type === 'found' ? 9 :
                                                            steps[step - 1]?.type === 'updateLeft' || steps[step - 1]?.type === 'updateRight' ? 10 :
                                                                steps[step - 1]?.type === 'notFound' ? 11 :
                                                                    steps[step - 1]?.type === 'complete' ? 12 : 0
                    : 0
                }
                algorithm={[
                    { code: "function exponentialSearch(arr, target):", lineNumber: 0, indent: 0 },
                    { code: "// Exponential Search requires a sorted array", lineNumber: 1, indent: 1 },
                    { code: "if (arr[0] === target) return 0  // Check first element", lineNumber: 2, indent: 1 },
                    { code: "// Find range for binary search by repeated doubling", lineNumber: 3, indent: 1 },
                    { code: "let bound = 1", lineNumber: 4, indent: 1 },
                    { code: "while (bound < arr.length && arr[bound] < target) {", lineNumber: 5, indent: 1 },
                    { code: "    bound *= 2  // Double the bound", lineNumber: 6, indent: 2 },
                    { code: "}", lineNumber: 7, indent: 1 },
                    { code: "// Perform binary search in the range [bound/2, min(bound, n-1)]", lineNumber: 8, indent: 1 },
                    { code: "let left = bound / 2", lineNumber: 9, indent: 1 },
                    { code: "let right = Math.min(bound, arr.length - 1)", lineNumber: 10, indent: 1 },
                    { code: "return binarySearch(arr, target, left, right)", lineNumber: 11, indent: 1 },
                    { code: "", lineNumber: 12, indent: 0 },
                    { code: "function binarySearch(arr, target, left, right):", lineNumber: 13, indent: 0 },
                    { code: "while (left <= right) {", lineNumber: 14, indent: 1 },
                    { code: "    let mid = Math.floor((left + right) / 2)", lineNumber: 15, indent: 2 },
                    { code: "    if (arr[mid] === target) return mid  // Found", lineNumber: 16, indent: 2 },
                    { code: "    if (arr[mid] > target) right = mid - 1  // Search left", lineNumber: 17, indent: 2 },
                    { code: "    else left = mid + 1  // Search right", lineNumber: 18, indent: 2 },
                    { code: "}", lineNumber: 19, indent: 1 },
                    { code: "return -1  // Not found", lineNumber: 20, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default ExponentialSearchController;
