// MergeSortVisualization.js
// This component provides the visualization for the Merge Sort algorithm.

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import EnhancedMergeSortBars3D from '../../../components/visualization/bars/EnhancedMergeSortBars3D';
import getAlgorithmColors from '../../../utils/algorithmColors';
import * as THREE from 'three';
import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './MergeSortConfig';
import { getEnhancedDelay, getEnhancedAnimationDuration } from '../../../utils/speedUtils';

// Extract constants for backward compatibility
const BAR_WIDTH = CONFIG.bars.width;
const BAR_SPACING = CONFIG.bars.spacing;
const BASE_HEIGHT = CONFIG.stage.height;
const MAX_BAR_HEIGHT = CONFIG.bars.maxHeight;

// Use enhanced animation duration from shared utility
const getAnimationDuration = getEnhancedAnimationDuration;

// Main MergeSort Visualization Component
const MergeSortVisualization = (props) => {
  const {
    state,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    theme,
    steps,
    array
  } = props;

  // Get speed from context
  const { speed } = useSpeed();

  // Local state to track current step data
  const [currentStep, setCurrentStep] = useState(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Use array from props as input data
  const inputData = array;

  // Get theme-aware colors with algorithm-specific overrides
  const colors = useMemo(() => getAlgorithmColors(theme, 'mergeSort'), [theme]);

  // Generate color legend items specific to merge sort
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Default' },
    { color: colors.comparing, label: 'Comparing Elements' },
    { color: colors.merging, label: 'Splitting/Merging' },
    { color: colors.sorted, label: 'Sorted Subarray' }
  ], [colors]);

  // Get camera from Three.js context
  const { camera } = useThree();

  // Store speed in a ref for animation frame access
  const speedRef = useRef(speed);
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // State for visualization
  const [arrayData, setArrayData] = useState([]);
  const [comparing, setComparing] = useState([-1, -1]);
  const [merging, setMerging] = useState([-1, -1]);
  const [sorted, setSorted] = useState([]);
  const [showArrows, setShowArrows] = useState([]);

  // Refs for animation control
  const timeoutIdRef = useRef(null);
  const isAnimatingRef = useRef(false);
  const initialArrayRef = useRef([]);

  // Store the camera initialization state
  const cameraInitializedRef = useRef(false);

  // Set camera position dynamically based on array size and total width
  // But only do this once at the beginning, then preserve user's camera position
  useEffect(() => {
    if (!camera || !arrayData.length) return;

    // If we've already initialized the camera, don't reset it
    if (cameraInitializedRef.current) {
      return;
    }

    // Calculate the total width of the visualization based on current array size
    const arraySize = arrayData.length;

    // Calculate the adaptive width and spacing based on array size
    const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.025));
    const currentBarWidth = BAR_WIDTH * scaleFactor;
    const currentBarSpacing = BAR_SPACING * scaleFactor;

    // Calculate the total width of all bars
    const totalWidth = (arraySize * (currentBarWidth + currentBarSpacing)) - currentBarSpacing;

    // Calculate the camera distance based on the total width
    const fov = camera.fov * (Math.PI / 180); // Convert FOV to radians

    // Calculate the minimum distance needed to see the entire width
    const padding = 2; // Extra padding factor
    const minDistance = (totalWidth / 2) / Math.tan(fov / 2) * padding;

    // Set the camera position only if we haven't initialized it yet
    if (!cameraInitializedRef.current) {
      camera.position.set(CONFIG.camera.position[0], CONFIG.camera.position[1], Math.max(CONFIG.camera.position[2], minDistance));
      camera.lookAt(new THREE.Vector3(...CONFIG.camera.lookAt));
      camera.updateProjectionMatrix();
      cameraInitializedRef.current = true;
    }

    // console.log('MergeSortVisualization - Updated camera position:', {
    //   arraySize,
    //   totalWidth,
    //   minDistance,
    //   cameraPosition: camera.position.toArray()
    // });
  }, [camera, arrayData]);

  // Add an effect to handle camera controls
  useEffect(() => {
    if (!camera) return;

    // Create a function to handle camera changes
    const handleCameraChange = () => {
      // Mark the camera as initialized once the user interacts with it
      cameraInitializedRef.current = true;
    };

    // Add event listeners to the camera controls
    const controls = camera.userData.controls;
    if (controls) {
      controls.addEventListener('change', handleCameraChange);
    }

    // Cleanup
    return () => {
      if (controls) {
        controls.removeEventListener('change', handleCameraChange);
      }
    };
  }, [camera]);

  // We already have getAnimationDuration defined at the top level

  // Initialize array data from inputData
  useEffect(() => {
    if (!inputData || inputData.length === 0 || isAnimatingRef.current) {
      return;
    }

    // Create a deep copy to avoid reference issues
    const newArray = [...inputData];
    console.log('MergeSortVisualization - Using input data:', newArray);

    // Reset visualization state
    setArrayData(newArray);
    setComparing([-1, -1]);
    setMerging([-1, -1]);
    setSorted([]);
    setShowArrows([]);
    setPlaceAnimation({
      active: false,
      indices: [-1, -1],
      progress: 0,
      startTime: 0
    });

    // Store the initial array for resetting
    initialArrayRef.current = [...newArray];
  }, [inputData]);

  // We no longer generate steps here as we use the steps from props
  // The step generation is handled in the controller

  // Animation state for placing elements
  const [placeAnimation, setPlaceAnimation] = useState({
    active: false,
    indices: [-1, -1],
    progress: 0,
    startTime: 0
  });

  // State for tracking replaced values
  const [trackedValue, setTrackedValue] = useState({
    value: null,
    targetIndex: -1,
    visible: false
  });

  // State for animating bars in place (when source and destination are the same)
  const [barAnimations, setBarAnimations] = useState({});

  // Track if we're currently processing a step
  const [isProcessingStep, setIsProcessingStep] = useState(false);
  const isProcessingStepRef = useRef(false);

  // Update the ref whenever the state changes
  useEffect(() => {
    isProcessingStepRef.current = isProcessingStep;
  }, [isProcessingStep]);



  // Update current step based on step prop
  useEffect(() => {
    console.log('MergeSortVisualization - Step changed to:', step);
    console.log('MergeSortVisualization - Steps array length:', steps?.length);
    console.log('MergeSortVisualization - Current state:', state);

    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('MergeSortVisualization - No steps available or invalid step index');
      return;
    }

    // For step 0, create a special "initial" step type
    if (step === 0) {
      setCurrentStep({
        ...steps[step],
        type: 'initial',
        initialArray: true,
        message: 'Merge Sort: Initial Array'
      });
    } else {
      setCurrentStep(steps[step]);
    }

    setCurrentStepIndex(step);
  }, [steps, step]);

  // Function to safely clear any existing timeout
  const clearExistingTimeout = useCallback(() => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }
  }, []);

  // Function to process a step with proper state management
  const processStep = useCallback((step) => {
    if (!step || !inputData || inputData.length === 0) {
      return;
    }

    console.log('MergeSortVisualization - Processing step:', {
      index: step === currentStep ? currentStepIndex : '?',
      type: step.type,
      indices: step.indices,
      message: step.message || step.movement
    });

    // Clear any existing timeout
    clearExistingTimeout();

    // For initial step, reset visualization
    if (step.type === 'initial' || step.initialArray) {
      console.log('Processing initial step');
      setArrayData([...inputData]);
      setComparing([-1, -1]);
      setMerging([-1, -1]);
      setSorted([]);
      setShowArrows([]);
      setPlaceAnimation({
        active: false,
        indices: [-1, -1],
        progress: 0,
        startTime: 0
      });

      // Mark step as complete after a speed-based delay
      timeoutIdRef.current = setTimeout(() => {
        isProcessingStepRef.current = false;
        setIsProcessingStep(false);
        console.log('Initial step completed');
      }, getAnimationDuration(speedRef.current) * 0.5); // Shorter delay for initial step
      return;
    }

    // Set the processing flag
    isProcessingStepRef.current = true;
    setIsProcessingStep(true);

    // Handle different step types
    switch (step.type) {
      case 'split':
        setComparing([-1, -1]);
        // Create an array of all indices in the range to highlight all bars
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
          setMerging(allIndices);
        } else {
          setMerging([-1, -1]);
        }
        setShowArrows([]);

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Split step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'compare':
        // Set comparing indices for the elements being compared
        if (step.indices && step.indices.length === 2) {
          const [index1, index2] = step.indices;
          // Set comparing indices and show arrows for the elements being compared
          const compareIndices = [index1, index2];
          console.log('Setting compare indices:', compareIndices);
          setComparing(compareIndices);
          setShowArrows(compareIndices);
        } else {
          setComparing([-1, -1]);
          setShowArrows([]);
        }
        // Clear any merging highlights during comparison
        setMerging([]);

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Schedule the end of the comparison after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          // Mark that we're done processing this step
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Comparison step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'merge':
        setComparing([-1, -1]);
        // Create an array of all indices in the range to highlight all bars
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
          setMerging(allIndices);
        } else {
          setMerging([-1, -1]);
        }
        setShowArrows([]);

        // If this is a sorted merge, update sorted indices
        if (step.sorted || step.type === 'sorted') {
          const [low, high] = step.indices || [0, 0];
          // Create an array of all indices in the range
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);

          setSorted(prev => {
            const newSorted = [...prev];
            // Add all indices in the range
            for (const index of allIndices) {
              if (!newSorted.includes(index)) {
                newSorted.push(index);
              }
            }
            return newSorted;
          });
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Merge step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'create_temp_arrays':
        // Clear any previous highlights
        setComparing([-1, -1]);
        setShowArrows([]);

        // Highlight the subarrays being split
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const mid = step.tempArrays?.mid || Math.floor(low + (high - low) / 2);

          // Highlight left subarray
          const leftIndices = Array.from({ length: mid - low + 1 }, (_, i) => low + i);
          // Highlight right subarray
          const rightIndices = Array.from({ length: high - mid }, (_, i) => mid + 1 + i);

          // Set merging to highlight both subarrays
          setMerging([...leftIndices, ...rightIndices]);
        } else {
          setMerging([-1, -1]);
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Create temp arrays step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'create_result_array':
        // Clear any previous highlights
        setComparing([-1, -1]);
        setShowArrows([]);

        // Highlight the range where the result array will be created
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
          setMerging(allIndices);
        } else {
          setMerging([-1, -1]);
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Create result array step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'result_array_complete':
        // Clear any previous highlights
        setComparing([-1, -1]);
        setShowArrows([]);

        // Highlight the range of the completed result array
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
          setMerging(allIndices);
        } else {
          setMerging([-1, -1]);
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Result array complete step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'copy_back':
        // Clear any previous highlights
        setComparing([-1, -1]);
        setShowArrows([]);

        // Highlight the range being copied back
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);
          setMerging(allIndices);
        } else {
          setMerging([-1, -1]);
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Copy back step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'place_from_left_subarray':
        // Set the animating flag
        isAnimatingRef.current = true;

        // Get indices for the placement
        const [targetIndexLeft, sourceIndexLeft] = step.indices || [0, 0];

        // Get the result array index if available
        const resultIndexLeft = step.placeInfo?.resultIndex;

        // Clear tracked value for left subarray operations
        setTrackedValue({
          value: null,
          targetIndex: -1,
          visible: false
        });

        // For place_from_left_subarray, always use the animation
        console.log(`Setting up place animation for place_from_left_subarray from ${sourceIndexLeft} to ${targetIndexLeft}`);

        // Set up the place animation for left subarray
        setPlaceAnimation({
          active: true,
          indices: [targetIndexLeft, sourceIndexLeft],
          progress: 0,
          startTime: Date.now(),
          sourceValue: step.sourceValue,
          // Add additional information for merge sort visualization
          placeInfo: step.placeInfo,
          isFromLeftSubarray: true,
          isFromRightSubarray: false,
          resultIndex: resultIndexLeft
        });

        // Schedule the end of the step processing (not the animation)
        // The animation itself is handled by useFrame
        timeoutIdRef.current = setTimeout(() => {
          console.log('Place step timeout triggered - clearing processing flags');

          // Clear the processing flag
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);

          // Only update the main array data for copy_back steps, not for intermediate operations
          if (step.array && step.type === 'copy_back') {
            setArrayData([...step.array]);
          }

          console.log('Place step completed by timeout');
        }, getAnimationDuration(speedRef.current) * 1.5); // Use standard timing for step completion
        break;

      case 'place_from_right_subarray':
        // Set the animating flag
        isAnimatingRef.current = true;

        // Get indices for the placement
        const [targetIndexRight, sourceIndexRight] = step.indices || [0, 0];

        // Get the result array index if available
        const resultIndexRight = step.placeInfo?.resultIndex;

        // Only track replaced values for place operations from right subarray
        if (step.sourceArray && step.sourceValue !== undefined) {
          // Get the value that's currently at the target position (the one being replaced)
          const replacedValue = step.sourceArray[targetIndexRight];

          // If there's a value being replaced and it's different from the source value
          if (replacedValue !== undefined && replacedValue !== step.sourceValue) {
            // Track the replaced value
            setTrackedValue({
              value: replacedValue,
              targetIndex: targetIndexRight,
              visible: true
            });

            console.log(`Tracking replaced value: ${replacedValue} from position ${targetIndexRight}`);
          } else {
            // Clear tracked value if nothing is being replaced
            setTrackedValue({
              value: null,
              targetIndex: -1,
              visible: false
            });
          }
        } else {
          // Clear tracked value if no source array or value
          setTrackedValue({
            value: null,
            targetIndex: -1,
            visible: false
          });
        }

        // For place_from_right_subarray, always use the animation
        console.log(`Setting up place animation for place_from_right_subarray from ${sourceIndexRight} to ${targetIndexRight}`);

        // Set up the place animation for right subarray
        setPlaceAnimation({
          active: true,
          indices: [targetIndexRight, sourceIndexRight],
          progress: 0,
          startTime: Date.now(),
          sourceValue: step.sourceValue,
          // Add additional information for merge sort visualization
          placeInfo: step.placeInfo,
          isFromLeftSubarray: false,
          isFromRightSubarray: true,
          resultIndex: resultIndexRight
        });

        // Schedule the end of the step processing (not the animation)
        // The animation itself is handled by useFrame
        timeoutIdRef.current = setTimeout(() => {
          console.log('Place step timeout triggered - clearing processing flags');

          // Clear the processing flag
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);

          // Only update the main array data for copy_back steps, not for intermediate operations
          if (step.array && step.type === 'copy_back') {
            setArrayData([...step.array]);
          }

          console.log('Place step completed by timeout');
        }, getAnimationDuration(speedRef.current) * 1.5); // Use standard timing for step completion
        break;

      case 'sorted':
        setComparing([-1, -1]);
        setMerging([-1, -1]);
        setShowArrows([]);

        // Mark the subarray as sorted
        if (step.indices && step.indices.length === 2) {
          const [low, high] = step.indices;
          const allIndices = Array.from({ length: high - low + 1 }, (_, i) => low + i);

          setSorted(prev => {
            const newSorted = [...prev];
            // Add all indices in the range
            for (const index of allIndices) {
              if (!newSorted.includes(index)) {
                newSorted.push(index);
              }
            }
            return newSorted;
          });
        }

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Sorted step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      case 'complete':
        setComparing([-1, -1]);
        setMerging([-1, -1]);
        setShowArrows([]);

        // Mark all indices as sorted
        setSorted(Array.from({ length: inputData.length }, (_, i) => i));

        // Update array data if available
        if (step.array) {
          setArrayData([...step.array]);
        }

        // Mark step as complete after a speed-based delay
        timeoutIdRef.current = setTimeout(() => {
          isProcessingStepRef.current = false;
          setIsProcessingStep(false);
          console.log('Complete step completed');
        }, getAnimationDuration(speedRef.current));
        break;

      default:
        // For unknown step types, mark as complete immediately
        isProcessingStepRef.current = false;
        setIsProcessingStep(false);
        break;
    }
  }, [clearExistingTimeout, inputData, speedRef]);

  // Apply current step to visualization
  useEffect(() => {
    // If there's no current step, return
    if (!currentStep) {
      return;
    }

    console.log('Current step changed to:', {
      type: currentStep.type,
      index: currentStepIndex,
      message: currentStep.message || currentStep.movement
    });

    // Skip if an animation is already in progress
    if (isAnimatingRef.current || isProcessingStepRef.current) {
      console.log('Skipping step application because a step is already being processed');

      // Set up a polling interval to check when processing is complete
      const pollingId = setInterval(() => {
        if (!isProcessingStepRef.current && !isAnimatingRef.current) {
          console.log('Processing completed, now processing the new step');
          clearInterval(pollingId);
          processStep(currentStep);
        }
      }, 100); // Check every 100ms

      // Clean up the polling interval if the component unmounts
      return () => clearInterval(pollingId);
    }

    // Process the current step
    processStep(currentStep);
  }, [currentStep, currentStepIndex, processStep]);

  // Update place animation progress
  useFrame(() => {
    if (placeAnimation.active) {
      const elapsed = Date.now() - placeAnimation.startTime;
      const duration = getAnimationDuration(speedRef.current); // Use speed-based animation duration
      const progress = Math.min(elapsed / duration, 1);

      // Update progress
      setPlaceAnimation(prev => ({
        ...prev,
        progress
      }));

      // If the animation is complete, end it after a very small delay
      // This ensures the final frame is rendered before ending the animation
      if (progress >= 1) {
        console.log('Animation completed, ending after small delay');

        // Use a minimal timeout to ensure the final frame is rendered
        setTimeout(() => {
          console.log('Animation timeout fired, ending animation');

          // Clear the animating flag
          isAnimatingRef.current = false;

          // End the animation
          setPlaceAnimation({
            active: false,
            indices: [-1, -1],
            progress: 0,
            startTime: 0
          });

          // Clear any bar animations
          setBarAnimations({});
        }, 50); // Small delay to ensure the final frame is rendered
      }
    }
  });

  // Handle state changes
  useEffect(() => {
    console.log('MergeSortVisualization - State changed to:', state);

    // If state is completed, mark all bars as sorted and show the sorted array
    if (state === 'completed') {
      // Mark all indices as sorted
      const allSorted = Array.from({ length: arrayData.length }, (_, i) => i);
      console.log('MergeSortVisualization - Algorithm completed, marking all bars as sorted:', allSorted);

      // Make sure we're showing the correctly sorted array
      const sortedArray = [...initialArrayRef.current].sort((a, b) => a - b);

      // Update the visualization with the sorted array
      setArrayData(sortedArray);
      setSorted(allSorted);
      setComparing([-1, -1]);
      setMerging([-1, -1]);
    }
  }, [state, arrayData.length]);

  // Handle auto-advance when state is 'running'
  useEffect(() => {
    console.log('Auto-advance effect triggered with state:', state, 'step:', step, 'totalSteps:', totalSteps);

    // Only proceed if state is 'running'
    if (state !== 'running') {
      console.log('Not auto-advancing because state is not running');
      return;
    }

    // Stop if we've reached the end
    if (step >= totalSteps) {
      console.log('Not auto-advancing because we reached the end');
      return;
    }

    console.log('Auto-advance conditions met - should advance to next step soon');

    // Calculate delay based on speed
    // Use the enhanced delay function which properly accounts for speed
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    console.log(`Scheduling next step with speed-based delay: ${speedBasedDelay}ms (speed: ${speedRef.current})`);

    const timeoutId = setTimeout(() => {
      console.log('Auto-advancing to next step with speed-based timing');
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef]);

  // Flag to enable/disable levitation effect - now using CONFIG

  // No need for levitation time state as we use useFrame directly

  // Create a reference to the group that will be animated
  const groupRef = useRef();

  // Use useFrame for continuous animation without React state updates - match BubbleSort
  useFrame(({ clock }) => {
    if (CONFIG.levitation.enabled && state === "idle" && groupRef.current) {
      // Get the current time for smooth animation
      const time = clock.getElapsedTime();

      // Apply position changes directly to the group
      groupRef.current.position.x = Math.sin(time * CONFIG.levitation.xFrequency) * CONFIG.levitation.xAmplitude;
      groupRef.current.position.y = CONFIG.levitation.baseY + Math.sin(time * CONFIG.levitation.yFrequency) * CONFIG.levitation.amplitude;
      groupRef.current.position.z = Math.sin(time * CONFIG.levitation.zFrequency) * CONFIG.levitation.zAmplitude;

      // Apply rotation changes directly to the group
      groupRef.current.rotation.x = Math.sin(time * 0.2) * 0.02;
      groupRef.current.rotation.y = Math.cos(time * 0.15) * 0.02;
      groupRef.current.rotation.z = Math.sin(time * 0.1) * 0.01;
    }
  });

  // Calculate positions for bars with adaptive spacing for larger arrays
  // Using a continuous function instead of discrete thresholds
  const arraySize = arrayData.length;

  // Calculate scale factor based on array size - smoothly scales down as array size increases
  // This matches the calculation in the camera positioning effect
  const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.01));

  // Apply the scale factor to both width and spacing
  const adaptiveWidth = BAR_WIDTH * scaleFactor;
  const adaptiveSpacing = BAR_SPACING * scaleFactor;

  // State to track the actual total width including gaps
  const [actualTotalWidth, setActualTotalWidth] = useState(0);

  // Calculate initial total width for the first render
  const initialTotalWidth = (arrayData.length * (adaptiveWidth + adaptiveSpacing)) - adaptiveSpacing;

  // Use the actual width from the bars component if available, otherwise use the initial calculation
  const totalWidth = actualTotalWidth > 0 ? actualTotalWidth : initialTotalWidth;

  // Log the adaptive scaling for debugging
  // console.log('MergeSort - Dynamic bar scaling:', {
  //   arraySize,
  //   scaleFactor,
  //   adaptiveWidth,
  //   adaptiveSpacing,
  //   totalWidth
  // });

  // We'll use the colors object directly in the SortingBars3D component

  // Log colors for debugging
  // console.log('MergeSort - Colors:', {
  //   colors,
  //   legendItems
  // });

  // No need for formatted step data, we use the currentStep directly

  return (
    <>
      {/* Fixed Step board - stays at the top of the screen */}
      <FixedStepBoard
        description={currentStep?.message || currentStep?.movement || 'Merge Sort Algorithm'}
        currentStep={step > 0 ? step : ''} // Only show step number for steps > 0
        totalSteps={totalSteps > 0 ? totalSteps - 1 : 0} // Subtract 1 to exclude initial state from total
        stepData={currentStep || {}}
        showStepNumber={step > 0} // Don't show step number for initial state
        config={{
          stepNumberFormat: 'Step {current}: ' // Only show current step number, not total
        }}
      />

      {/* Fixed Color Legend - stays at the bottom of the screen */}
      <FixedColorLegend items={legendItems} theme={theme} />

      {/* Levitating visualization group with fixed center positioning - match BubbleSort */}
      <group
        ref={groupRef}
        position={CONFIG.group.position} // Position from centralized config
        rotation={[0, 0, 0]} // Initial rotation
      >
        {/* This group contains both the base and the bars to keep them aligned */}
        {/* Base - positioned at the bottom of the visualization */}
        <group position={[0, -0.1, 0]}>
          <SortingBase
            width={CONFIG.stage.useFixedLength
              ? CONFIG.stage.length
              : Math.max(totalWidth * CONFIG.stage.lengthMultiplier + CONFIG.stage.lengthPadding.left + CONFIG.stage.lengthPadding.right, CONFIG.stage.length)}
            color={CONFIG.stage.color || colors}
            height={BASE_HEIGHT}
            depth={CONFIG.stage.width} /* Use fixed width from config */
            useThemeColors={CONFIG.stage.color === null}
            metalness={0.3}
            roughness={0.4}
            highlightHeight={0.04}
          />
        </group>

        {/* Bars - Using our enhanced MergeSortBars3D component */}
        <EnhancedMergeSortBars3D
          arrayData={arrayData}
          maxBarHeight={MAX_BAR_HEIGHT}
          barWidth={adaptiveWidth}
          barSpacing={adaptiveSpacing}
          colors={colors}
          comparing={comparing}
          merging={merging}
          sorted={sorted}
          showArrows={CONFIG.visual.showArrows && showArrows}
          showValues={CONFIG.visual.adaptiveLabels ? (scaleFactor > CONFIG.visual.valueVisibilityThreshold) : CONFIG.visual.showValues}
          showIndices={CONFIG.visual.adaptiveLabels ? (scaleFactor > CONFIG.visual.indexVisibilityThreshold) : CONFIG.visual.showIndices}
          placeAnimation={placeAnimation.active ? placeAnimation : { active: false, indices: [-1, -1], progress: 0, startTime: 0 }}
          currentStep={currentStep}
          onWidthChange={setActualTotalWidth}
          trackedValue={trackedValue} // Pass the tracked value from parent state
          barAnimations={barAnimations} // Pass the bar animations for in-place animation
          stagePadding={CONFIG.stage.lengthPadding} // Pass stage padding for alignment
          explanatoryConfig={CONFIG.explanatoryElements} // Pass the unified explanatory elements config
        />
      </group>
    </>
  );
};

export default MergeSortVisualization;