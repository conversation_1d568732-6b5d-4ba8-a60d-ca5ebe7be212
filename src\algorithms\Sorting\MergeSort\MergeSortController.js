// MergeSortController.js
// This component provides the controls for the Merge Sort algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';
import { generateRandomArray } from '../../../utils/algorithmUtils';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const MergeSortController = (props) => {
  // Log props
  console.log('MergeSortController - Props:', props);

  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;
  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    steps,
    setSteps,
    algorithmArray,
    setAlgorithmArray
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Extract parameters with safety checks
  const arraySize = params?.arraySize || 10;
  const randomize = params?.randomize !== undefined ? params.randomize : true;
  const customArray = params?.customArray || [];

  // State for custom array input
  const [customArrayInput, setCustomArrayInput] = useState('');
  const [customArrayError, setCustomArrayError] = useState('');
  const [useCustomArray, setUseCustomArray] = useState(false); // Initialize to false, independent of randomize

  // Debounce mechanism for array size changes
  const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

  // Alias setSteps to setAlgorithmSteps for clarity
  const setAlgorithmSteps = setSteps;

  // Function to generate steps for the merge sort algorithm
  const generateMergeSortSteps = (array) => {
    const steps = [];

    // IMPORTANT: Verify we have a valid array
    if (!Array.isArray(array) || array.length === 0) {
      return steps;
    }

    // Create a deep copy of the original array
    const originalArray = [...array];

    // Create an auxiliary array for the merge sort algorithm
    // This is used to show the correct visualization of merge sort
    const auxArray = new Array(originalArray.length).fill(null);

    // Add initial state step at index 0
    steps.push({
      type: 'initial',
      array: [...originalArray],
      message: `Merge Sort: Initial Array [${originalArray.join(', ')}]`,
      movement: `Merge Sort: Initial Array [${originalArray.join(', ')}]`,
      initialArray: true
    });

    // Track which subarrays have been marked as sorted to avoid duplicates
    const sortedSubarrays = new Set();

    // Helper function to check if a subarray has been marked as sorted
    const isSubarraySorted = (low, high) => {
      return sortedSubarrays.has(`${low}-${high}`);
    };

    // Helper function to mark a subarray as sorted
    const markSubarraySorted = (low, high) => {
      sortedSubarrays.add(`${low}-${high}`);
    };

    // Helper function to calculate the split level (depth in the recursion tree)
    const getSplitLevel = (low, high, arrayLength) => {
      // Calculate the level based on the size of the subarray relative to the original array
      const subarraySize = high - low + 1;
      const ratio = subarraySize / arrayLength;

      if (ratio === 1) return 0; // Full array
      if (ratio >= 0.5) return 1; // First level split
      if (ratio >= 0.25) return 2; // Second level split
      if (ratio >= 0.125) return 3; // Third level split
      return 4; // Deeper levels
    };

    // Main merge sort function
    const mergeSort = (mainArray, aux, low, high) => {
      // Base case: if the subarray has 1 or 0 elements, it's already sorted
      if (high <= low) {
        // For single-element subarrays, mark as sorted immediately
        if (high === low && !isSubarraySorted(low, high)) {
          steps.push({
            type: 'sorted',
            indices: [low, high],
            movement: `Single element at index ${low} is already sorted`,
            array: [...mainArray]
          });
          markSubarraySorted(low, high);
        }
        return;
      }

      // Find the middle point
      const mid = Math.floor(low + (high - low) / 2);

      // Add step for splitting the array with enhanced information
      steps.push({
        type: 'split',
        indices: [low, high],
        movement: `Split array from index ${low} to ${high} at middle index ${mid}`,
        message: `Split array [${mainArray.slice(low, high + 1).join(', ')}] into left [${mainArray.slice(low, mid + 1).join(', ')}] and right [${mainArray.slice(mid + 1, high + 1).join(', ')}]`,
        array: [...mainArray],
        splitInfo: {
          low,
          high,
          mid,
          leftArray: mainArray.slice(low, mid + 1),
          rightArray: mainArray.slice(mid + 1, high + 1),
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // Sort the left half
      mergeSort(mainArray, aux, low, mid);

      // Sort the right half
      mergeSort(mainArray, aux, mid + 1, high);

      // Merge the sorted halves
      merge(mainArray, aux, low, mid, high);
    };

    // Merge function to combine two sorted subarrays
    const merge = (mainArray, aux, low, mid, high) => {
      // Add step for starting the merge with enhanced information
      steps.push({
        type: 'merge',
        indices: [low, high],
        movement: `Merging subarrays from index ${low} to ${mid} and from ${mid + 1} to ${high}`,
        message: `Merge left [${mainArray.slice(low, mid + 1).join(', ')}] and right [${mainArray.slice(mid + 1, high + 1).join(', ')}] subarrays`,
        array: [...mainArray],
        mergeInfo: {
          low,
          mid,
          high,
          leftArray: mainArray.slice(low, mid + 1),
          rightArray: mainArray.slice(mid + 1, high + 1),
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // Copy elements to auxiliary array for the original implementation
      for (let k = low; k <= high; k++) {
        aux[k] = mainArray[k];
      }

      // Create a copy of the main array for visualization
      const visualArray = [...mainArray];

      // Create a copy of the final sorted subarray in advance
      const finalSortedSubarray = [...mainArray];

      // We'll use the same approach for all merges, regardless of size
      // No special case handling for simple merges

      // Merge back to the final sorted array first (without showing steps)
      let i_final = low;      // Index for the left subarray
      let j_final = mid + 1;  // Index for the right subarray

      for (let k = low; k <= high; k++) {
        if (i_final > mid) {
          finalSortedSubarray[k] = aux[j_final++];
        } else if (j_final > high) {
          finalSortedSubarray[k] = aux[i_final++];
        } else if (aux[j_final] < aux[i_final]) {
          finalSortedSubarray[k] = aux[j_final++];
        } else {
          finalSortedSubarray[k] = aux[i_final++];
        }
      }

      // Now do the merge again for visualization with steps
      let i = low;      // Index for the left subarray
      let j = mid + 1;  // Index for the right subarray

      // Create temporary arrays for left and right subarrays
      const leftArray = aux.slice(low, mid + 1);
      const rightArray = aux.slice(mid + 1, high + 1);

      // Store these arrays for use in all place operations

      // Add step to show the creation of temporary arrays
      steps.push({
        type: 'create_temp_arrays',
        indices: [low, high],
        movement: `Create temporary arrays for left [${leftArray.join(', ')}] and right [${rightArray.join(', ')}] subarrays`,
        message: `Create temporary arrays for merging: Left [${leftArray.join(', ')}] and Right [${rightArray.join(', ')}]`,
        array: [...visualArray],
        tempArrays: {
          low,
          mid,
          high,
          leftArray,
          rightArray,
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // Add step to show the creation of result array
      steps.push({
        type: 'create_result_array',
        indices: [low, high],
        movement: `Create empty result array of size ${high - low + 1}`,
        message: `Create empty result array to store merged elements`,
        array: [...visualArray],
        resultArray: {
          low,
          high,
          size: high - low + 1,
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // First, check if one of the subarrays is already exhausted at the beginning
      let leftExhausted = i > mid;
      let rightExhausted = j > high;

      // If left subarray is exhausted at the beginning, add a single compare step
      if (leftExhausted) {
        steps.push({
          type: 'compare',
          indices: [-1, j],
          movement: `Left subarray exhausted, taking all elements from right subarray`,
          array: [...visualArray],
          compareInfo: {
            leftIndex: -1,
            rightIndex: 0,
            leftExhausted: true,
            rightExhausted: false,
            low,
            mid,
            high,
            level: getSplitLevel(low, high, originalArray.length)
          }
        });
      }
      // If right subarray is exhausted at the beginning, add a single compare step
      else if (rightExhausted) {
        steps.push({
          type: 'compare',
          indices: [i, -1],
          movement: `Right subarray exhausted, taking all elements from left subarray`,
          array: [...visualArray],
          compareInfo: {
            leftIndex: 0,
            rightIndex: -1,
            leftExhausted: false,
            rightExhausted: true,
            low,
            mid,
            high,
            level: getSplitLevel(low, high, originalArray.length)
          }
        });
      }

      for (let k = low; k <= high; k++) {
        // If left subarray is exhausted, take from right
        if (i > mid) {
          // Create a snapshot of the current visual array
          const beforeArray = [...visualArray];

          // Create a new array for this step that shows only the current element being placed
          const stepArray = [...beforeArray];
          stepArray[k] = aux[j];

          // Create a unique final array for this specific step
          const finalArrayForStep = [...finalSortedSubarray];
          // Only update positions up to the current position k
          for (let pos = low; pos <= k; pos++) {
            finalArrayForStep[pos] = finalSortedSubarray[pos];
          }

          // Create an array of previous values for the result array
          const previousValues = [];
          for (let i = 0; i < k - low; i++) {
            previousValues[i] = visualArray[low + i];
          }

          // Add step for placing the element from right subarray with enhanced information
          steps.push({
            type: 'place_from_right_subarray',
            indices: [k, j],
            movement: `Place element ${aux[j]} from right subarray to position ${k}`,
            array: stepArray,
            sourceValue: aux[j],
            sourceArray: beforeArray,
            finalArray: finalArrayForStep, // Include the step-specific final array
            placeInfo: {
              targetIndex: k,
              sourceIndex: j,
              sourceValue: aux[j],
              fromRightSubarray: true,
              resultIndex: k - low, // Index in the result array
              rightArrayIndex: j - (mid + 1), // Index in the right subarray
              previousValues: previousValues, // Add the previous values
              low,
              mid,
              high,
              leftArray, // Add the left array for visualization
              rightArray, // Add the right array for visualization
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Update the visual array for the next step
          visualArray[k] = aux[j++];
        }
        // If right subarray is exhausted, take from left
        else if (j > high) {
          // Create a snapshot of the current visual array
          const beforeArray = [...visualArray];

          // Create a new array for this step that shows only the current element being placed
          const stepArray = [...beforeArray];
          stepArray[k] = aux[i];

          // Create a unique final array for this specific step
          const finalArrayForStep = [...finalSortedSubarray];
          // Only update positions up to the current position k
          for (let pos = low; pos <= k; pos++) {
            finalArrayForStep[pos] = finalSortedSubarray[pos];
          }

          // Create an array of previous values for the result array
          const previousValues = [];
          for (let i = 0; i < k - low; i++) {
            previousValues[i] = visualArray[low + i];
          }

          // Add step for placing the element from left subarray with enhanced information
          steps.push({
            type: 'place_from_left_subarray',
            indices: [k, i],
            movement: `Place element ${aux[i]} from left subarray to position ${k}`,
            array: stepArray,
            sourceValue: aux[i],
            sourceArray: beforeArray,
            finalArray: finalArrayForStep, // Include the step-specific final array
            placeInfo: {
              targetIndex: k,
              sourceIndex: i,
              sourceValue: aux[i],
              fromLeftSubarray: true,
              resultIndex: k - low, // Index in the result array
              leftArrayIndex: i - low, // Index in the left subarray
              previousValues: previousValues, // Add the previous values
              low,
              mid,
              high,
              leftArray, // Add the left array for visualization
              rightArray, // Add the right array for visualization
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Update the visual array for the next step
          visualArray[k] = aux[i++];
        }
        // If right element is less than left element, take from right
        else if (aux[j] < aux[i]) {
          // Add step for comparing elements
          steps.push({
            type: 'compare',
            indices: [i, j],
            movement: `Compare elements ${aux[i]} and ${aux[j]}, ${aux[j]} is smaller`,
            array: [...visualArray],
            compareInfo: {
              leftIndex: i - low, // Convert to index in left subarray
              rightIndex: j - (mid + 1), // Convert to index in right subarray
              leftValue: aux[i],
              rightValue: aux[j],
              rightIsSmaller: true,
              low,
              mid,
              high,
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Create a snapshot of the current visual array
          const beforeArray = [...visualArray];

          // Create a new array for this step that shows only the current element being placed
          const stepArray = [...beforeArray];
          stepArray[k] = aux[j];

          // Create a unique final array for this specific step
          const finalArrayForStep = [...finalSortedSubarray];
          // Only update positions up to the current position k
          for (let pos = low; pos <= k; pos++) {
            finalArrayForStep[pos] = finalSortedSubarray[pos];
          }

          // Create an array of previous values for the result array
          const previousValues = [];
          for (let i = 0; i < k - low; i++) {
            previousValues[i] = visualArray[low + i];
          }

          // Add step for placing the element from right subarray with enhanced information
          steps.push({
            type: 'place_from_right_subarray',
            indices: [k, j],
            movement: `Place element ${aux[j]} from right subarray to position ${k}`,
            array: stepArray,
            sourceValue: aux[j],
            sourceArray: beforeArray,
            finalArray: finalArrayForStep, // Include the step-specific final array
            placeInfo: {
              targetIndex: k,
              sourceIndex: j,
              sourceValue: aux[j],
              fromRightSubarray: true,
              resultIndex: k - low, // Index in the result array
              rightArrayIndex: j - (mid + 1), // Index in the right subarray
              previousValues: previousValues, // Add the previous values
              low,
              mid,
              high,
              leftArray, // Add the left array for visualization
              rightArray, // Add the right array for visualization
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Update the visual array for the next step
          visualArray[k] = aux[j++];
        }
        // Otherwise, take from left
        else {
          // Add step for comparing elements
          steps.push({
            type: 'compare',
            indices: [i, j],
            movement: `Compare elements ${aux[i]} and ${aux[j]}, ${aux[i]} is smaller or equal`,
            array: [...visualArray],
            compareInfo: {
              leftIndex: i - low, // Convert to index in left subarray
              rightIndex: j - (mid + 1), // Convert to index in right subarray
              leftValue: aux[i],
              rightValue: aux[j],
              leftIsSmallerOrEqual: true,
              low,
              mid,
              high,
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Create a snapshot of the current visual array
          const beforeArray = [...visualArray];

          // Create a new array for this step that shows only the current element being placed
          const stepArray = [...beforeArray];
          stepArray[k] = aux[i];

          // Create a unique final array for this specific step
          const finalArrayForStep = [...finalSortedSubarray];
          // Only update positions up to the current position k
          for (let pos = low; pos <= k; pos++) {
            finalArrayForStep[pos] = finalSortedSubarray[pos];
          }

          // Create an array of previous values for the result array
          const previousValues = [];
          for (let i = 0; i < k - low; i++) {
            previousValues[i] = visualArray[low + i];
          }

          // Add step for placing the element from left subarray with enhanced information
          steps.push({
            type: 'place_from_left_subarray',
            indices: [k, i],
            movement: `Place element ${aux[i]} from left subarray to position ${k}`,
            array: stepArray,
            sourceValue: aux[i],
            sourceArray: beforeArray,
            finalArray: finalArrayForStep, // Include the step-specific final array
            placeInfo: {
              targetIndex: k,
              sourceIndex: i,
              sourceValue: aux[i],
              fromLeftSubarray: true,
              resultIndex: k - low, // Index in the result array
              leftArrayIndex: i - low, // Index in the left subarray
              previousValues: previousValues, // Add the previous values
              low,
              mid,
              high,
              leftArray, // Add the left array for visualization
              rightArray, // Add the right array for visualization
              level: getSplitLevel(low, high, originalArray.length)
            }
          });

          // Update the visual array for the next step
          visualArray[k] = aux[i++];
        }
      }

      // Create a copy of the result array for visualization
      const finalResultArray = new Array(high - low + 1);

      // Copy the final sorted subarray to the result array
      for (let i = 0; i < high - low + 1; i++) {
        finalResultArray[i] = finalSortedSubarray[low + i];
      }

      // Add step to show the completed result array
      steps.push({
        type: 'result_array_complete',
        indices: [low, high],
        movement: `Result array complete: [${finalResultArray.join(', ')}]`,
        message: `Result array after merging: [${finalResultArray.join(', ')}]`,
        array: [...mainArray],
        resultArray: {
          low,
          high,
          result: finalResultArray,
          size: finalResultArray.length, // Add size property for visualization
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // Update the main array with the final sorted subarray
      for (let k = low; k <= high; k++) {
        mainArray[k] = finalSortedSubarray[k];
      }

      // Add step for copying result back to the original array
      steps.push({
        type: 'copy_back',
        indices: [low, high],
        movement: `Copy result array back to original array positions ${low} to ${high}`,
        message: `Copy result [${finalResultArray.join(', ')}] back to original array`,
        array: [...mainArray],
        copyInfo: {
          low,
          high,
          result: finalResultArray,
          size: finalResultArray.length, // Add size property for visualization
          level: getSplitLevel(low, high, originalArray.length)
        }
      });

      // Add step for completed merge if not already marked as sorted
      if (!isSubarraySorted(low, high)) {
        steps.push({
          type: 'sorted',
          indices: [low, high],
          movement: `Merged subarray from index ${low} to ${high}`,
          array: [...mainArray],
          mergeInfo: {
            low,
            high,
            isSorted: true,
            level: getSplitLevel(low, high, originalArray.length)
          }
        });
        markSubarraySorted(low, high);
      }
    };

    // Start the merge sort
    mergeSort(originalArray, auxArray, 0, originalArray.length - 1);

    // Add final step to mark the entire array as sorted
    steps.push({
      type: 'complete',
      array: [...originalArray],
      movement: `Merge Sort complete: [${originalArray.join(', ')}]`,
      data: {
        data: [...originalArray],
        isSorted: true
      }
    });

    return steps;
  };

  // Update custom array input when customArray changes
  useEffect(() => {
    if (customArray && customArray.length > 0) {
      setCustomArrayInput(customArray.join(', '));
      setUseCustomArray(true);
    } else {
      // Reset custom array input when customArray is empty
      setCustomArrayInput('');
      setUseCustomArray(false);
    }
  }, [customArray]);

  // Update useCustomArray state when params.useCustomArray changes
  useEffect(() => {
    if (params.useCustomArray !== undefined) {
      setUseCustomArray(params.useCustomArray);
    }
  }, [params.useCustomArray]);

  // Calculate total steps when array size changes
  useEffect(() => {
    // Generate array data
    let array = [];

    // Use custom array if provided
    if (customArray.length > 0) {
      array = [...customArray];
      console.log('MergeSortController - Using custom array:', array);
    } else if (randomize) {
      // Generate random array using utility function with configured range
      array = generateRandomArray(arraySize);
      console.log('MergeSortController - Generated random array:', array);
    } else {
      // Generate sorted array in reverse order (worst case for merge sort)
      array = Array.from({ length: arraySize }, (_, i) => arraySize - i);
      console.log('MergeSortController - Generated reverse sorted array:', array);
    }

    // IMPORTANT: Set the array in the context so visualization can use it
    console.log('MergeSortController - Setting algorithm array in context:', array);
    setAlgorithmArray(array);
    console.log('MergeSortController - Current algorithm array in context:', algorithmArray);
    console.log('MergeSortController - After setting array, algorithmArray should be updated');

    // Generate steps
    const generatedSteps = generateMergeSortSteps(array);

    // Set total steps
    setTotalSteps(generatedSteps.length);

    // Set steps for visualization
    setAlgorithmSteps(generatedSteps);

    // Log completion
    console.log(`MergeSortController - Total steps: ${generatedSteps.length}`);
  }, [arraySize, randomize, customArray, setAlgorithmArray, setAlgorithmSteps, setTotalSteps]);

  // Log state changes
  useEffect(() => {
    console.log('MergeSortController - Total steps:', totalSteps);
    console.log('MergeSortController - Current state:', state);

    // Only update if we have valid steps and we're not in idle state
    if (totalSteps > 0 && state !== 'idle') {
      // If we've reached the last step, mark as completed
      if (step >= totalSteps) {
        console.log('MergeSortController - Setting state to completed');
        setState('completed');
      }
      // If we were in completed state but stepped back, go to paused
      else if (state === 'completed' && step < totalSteps) {
        console.log('MergeSortController - Setting state to paused');
        setState('paused');
      }
    }
  }, [step, totalSteps, setState, state]);

  // Log algorithm array changes
  useEffect(() => {
    console.log('MergeSortController - Algorithm array changed:', algorithmArray);
  }, [algorithmArray]);

  // Handle array size change with debounce
  const handleArraySizeChange = (value) => {
    // Validate the input
    if (value >= 3 && value <= 20) {
      // Clear any existing timeout
      if (arraySizeTimeoutId) {
        clearTimeout(arraySizeTimeoutId);
      }

      // Set a new timeout to debounce the change
      const timeoutId = setTimeout(() => {
        // Always reset when changing array size
        if (typeof onParamChange === 'function') {
          onParamChange({ ...params, arraySize: value });
        }
        setState('idle');
        setStep(0);
      }, 300); // 300ms debounce

      setArraySizeTimeoutId(timeoutId);
    }
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (arraySizeTimeoutId) {
        clearTimeout(arraySizeTimeoutId);
      }
    };
  }, [arraySizeTimeoutId]);

  // Handle randomize toggle
  const handleRandomizeChange = (value) => {
    // When enabling randomize, clear any custom array but don't affect useCustomArray toggle
    if (value) {
      onParamChange({ ...params, randomize: value, customArray: [] });
      // Don't update useCustomArray state to keep toggles independent
    } else {
      onParamChange({ ...params, randomize: value });
    }
    setState('idle');
    setStep(0);
  };

  // Custom array toggle is handled directly in the onChange handler of ParametersSection

  // Handle custom array input change
  const handleCustomArrayInputChange = (value) => {
    setCustomArrayInput(value);
    setCustomArrayError(''); // Clear any previous errors
  };

  // This function is used in the onChange handler of ParametersSection

  // Apply custom array
  const applyCustomArray = (parsedArray) => {
    console.log('MergeSortController - applyCustomArray called with:', parsedArray);

    // If validation failed in the CustomArrayInput component
    if (parsedArray === false) {
      return false;
    }

    // Clear any previous error
    setCustomArrayError('');

    // Update the params with the new array
    onParamChange({
      ...params,
      customArray: parsedArray,
      // Set arraySize to match the custom array length
      arraySize: parsedArray.length,
      // Turn on useCustomArray
      useCustomArray: true
    });

    // IMPORTANT: Set the array in the context directly
    // This ensures the visualization uses the same array
    console.log('MergeSortController - Setting custom array in context:', parsedArray);
    setAlgorithmArray(parsedArray);

    // Generate steps for the custom array
    const generatedSteps = generateMergeSortSteps(parsedArray);
    console.log('MergeSortController - Generated steps for custom array:', generatedSteps.length);

    // Set the steps in the context
    setAlgorithmSteps(generatedSteps);
    setTotalSteps(generatedSteps.length);

    // Reset step to 0
    setState('idle');
    setStep(0);
    return true;
  };
  return (
    <Box>
      {/* Information Section */}
      <InformationSection title={"Merge Sort"}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Merge Sort:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Merge Sort is a divide-and-conquer algorithm that divides the input array into two halves, recursively sorts them, and then merges the sorted halves.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Best Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Average Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            - Worst Case: O(n log n)
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Space Complexity:
          </Typography>
          <Typography variant="body2">
            O(n) - Merge Sort requires additional space for the temporary arrays used during merging
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'arraySize',
            type: 'slider',
            label: 'Array Size',
            min: 3,
            max: 20,
            step: 1,
            defaultValue: arraySize,
            icon: ViewArrayIcon,
            disabled: useCustomArray || state !== 'idle' // Disable when custom array is enabled or not in idle state
          },
          {
            name: 'randomize',
            type: 'switch',
            label: 'Randomize Array',
            defaultValue: randomize,
            icon: ShuffleIcon,
            disabled: useCustomArray || state !== 'idle' // Disable when custom array is enabled or not in idle state
          },
          {
            name: 'useCustomArray',
            type: 'switch',
            label: 'Use Custom Array',
            defaultValue: useCustomArray, // Use the state value, independent of randomize
            // Allow toggling between randomize and custom array
            icon: FormatListNumberedIcon,
            disabled: state !== 'idle' // Disable when not in idle state
          },
          {
            name: 'customArrayInput',
            type: 'customArray',
            label: 'Custom Array',
            // Don't use defaultValue here as we're using value in the values prop
            showOnlyWhen: 'useCustomArray',
            disabled: state !== 'idle', // Disable when not in idle state
            error: customArrayError,
            helperText: "Enter comma-separated numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
            placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
            onApply: applyCustomArray,
            icon: FormatListNumberedIcon
          }
        ]}
        values={{
          arraySize,
          randomize,
          useCustomArray,
          customArrayInput
        }}
        onChange={(newValues) => {
          if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize && !useCustomArray) {
            // Only allow changing array size when custom array is not enabled
            handleArraySizeChange(newValues.arraySize);
          }
          if (newValues.randomize !== undefined && newValues.randomize !== randomize && !useCustomArray) {
            // Only allow changing randomize when custom array is not enabled
            handleRandomizeChange(newValues.randomize);
          }
          if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
            setUseCustomArray(newValues.useCustomArray);
            // Update the params with the useCustomArray flag
            onParamChange({
              ...params,
              useCustomArray: newValues.useCustomArray
            });
            setState('idle');
            setStep(0);
          }
          if (newValues.customArrayInput !== undefined) {
            handleCustomArrayInputChange(newValues.customArrayInput);
          }
        }}
        disabled={state !== 'idle'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setState('idle');
          setStep(0);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            console.log('MergeSortController - Stepping forward from', step, 'to', step + 1);
            setStep(step + 1);
            // If this will be the last step, mark as completed
            if (step + 1 >= totalSteps) {
              console.log('MergeSortController - Last step reached, setting state to completed');
              setState('completed');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              console.log('MergeSortController - Was in idle state, setting state to paused');
              setState('paused');
            }
          } else {
            console.log('MergeSortController - Cannot step forward, already at last step');
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            // If we were in completed state, go back to paused
            if (state === 'completed') {
              setState('paused');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.movement || step.message || ''
        }))}
        currentStep={step} // Use the actual step index directly
        defaultExpanded
        renderStep={(_, index) => {
          const currentStep = steps && steps[index];
          const isCurrentStep = index === step;

          if (!currentStep) return null;

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '20px',
                  height: '20px',
                  borderRadius: '10px',
                  bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                  mr: 1,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                }}
              >
                {index + 1}
              </Box>
              {currentStep.type === 'compare' ? (
                <>
                  Compare elements at indices{' '}
                  <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>{' '}
                  and{' '}
                  <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>
                </>
              ) : currentStep.type === 'split' ? (
                <>
                  Split array at indices{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>{' '}
                  to{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>
                </>
              ) : currentStep.type === 'merge' ? (
                <>
                  Merge arrays from indices{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>{' '}
                  to{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>
                </>
              ) : currentStep.type === 'place' ? (
                <>
                  Place element from index{' '}
                  <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>{' '}
                  to index{' '}
                  <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>
                </>
              ) : (
                <>
                  {currentStep.movement || 'Processing...'}
                </>
              )}
            </Typography>
          );
        }}
        emptyMessage="No steps yet. Start the algorithm to see the sequence."
      />
      {/* Algorithm Section */}
      <AlgorithmSection
        title="Merge Sort Algorithm"
        defaultExpanded
        currentStep={step >= 0 && steps && steps.length > 0 ?
          // Map the current step to the corresponding line number
          steps[step]?.type === 'initial' ? 0 :
          steps[step]?.type === 'split' ? 3 :
          steps[step]?.type === 'merge' ? 10 :
          steps[step]?.type === 'create_temp_arrays' ? 14 :
          steps[step]?.type === 'create_result_array' ? 14 :
          steps[step]?.type === 'compare' ? 15 :
          steps[step]?.type === 'place_from_left_subarray' ? 16 :
          steps[step]?.type === 'place_from_right_subarray' ? 18 :
          steps[step]?.type === 'result_array_complete' ? 20 :
          steps[step]?.type === 'copy_back' ? 21 :
          steps[step]?.type === 'sorted' ? 22 :
          steps[step]?.type === 'complete' ? 24 : 0
          : 0
        }
        algorithm={[
          // More detailed MergeSort algorithm steps
          { code: "function mergeSort(array, low, high):", lineNumber: 0, indent: 0 },
          { code: "// Base case: if subarray has 1 or 0 elements", lineNumber: 1, indent: 1 },
          { code: "if (low >= high) return;", lineNumber: 2, indent: 1 },
          { code: "// Find the middle point", lineNumber: 3, indent: 1 },
          { code: "mid = Math.floor(low + (high - low) / 2);", lineNumber: 4, indent: 1 },
          { code: "// Split array into left and right subarrays", lineNumber: 5, indent: 1 },
          { code: "// Recursively sort left half", lineNumber: 6, indent: 1 },
          { code: "mergeSort(array, low, mid);", lineNumber: 7, indent: 1 },
          { code: "// Recursively sort right half", lineNumber: 8, indent: 1 },
          { code: "mergeSort(array, mid + 1, high);", lineNumber: 9, indent: 1 },
          { code: "// Merge the sorted halves", lineNumber: 10, indent: 1 },
          { code: "merge(array, low, mid, high);", lineNumber: 11, indent: 1 },
          { code: "", lineNumber: 12, indent: 0 },
          { code: "function merge(array, low, mid, high):", lineNumber: 13, indent: 0 },
          { code: "// Create auxiliary arrays for left and right subarrays", lineNumber: 14, indent: 1 },
          { code: "// Compare elements from left and right subarrays", lineNumber: 15, indent: 1 },
          { code: "// If left element is smaller, place it in the result", lineNumber: 16, indent: 1 },
          { code: "result[k++] = leftArray[i++];", lineNumber: 17, indent: 1 },
          { code: "// If right element is smaller, place it in the result", lineNumber: 18, indent: 1 },
          { code: "result[k++] = rightArray[j++];", lineNumber: 19, indent: 1 },
          { code: "// Copy any remaining elements from left or right", lineNumber: 20, indent: 1 },
          { code: "// Copy result back to the original array", lineNumber: 21, indent: 1 },
          { code: "// Subarray is now sorted", lineNumber: 22, indent: 1 },
          { code: "return array;", lineNumber: 23, indent: 1 },
          { code: "// Merge Sort complete", lineNumber: 24, indent: 0 },
        ]}
      />
    </Box>
  );
};

export default MergeSortController;
