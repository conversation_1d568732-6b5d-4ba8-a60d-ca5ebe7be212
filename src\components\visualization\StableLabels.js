// StableLabels.js
// A component to render stable labels in 3D space

import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { createPortal } from 'react-dom';
import './StableLabels.css';

// Create a context for the labels
const LabelsContext = createContext([]);

// Provider component for labels
export const LabelsProvider = ({ children }) => {
  const [labels, setLabels] = useState([]);

  const registerLabel = (id, type, position, content) => {
    setLabels(prev => {
      // Check if label already exists
      const existingIndex = prev.findIndex(l => l.id === id);
      if (existingIndex >= 0) {
        // Update existing label
        const updated = [...prev];
        updated[existingIndex] = { id, type, position, content };
        return updated;
      } else {
        // Add new label
        return [...prev, { id, type, position, content }];
      }
    });
  };

  const unregisterLabel = (id) => {
    setLabels(prev => prev.filter(l => l.id !== id));
  };

  return (
    <LabelsContext.Provider value={{ labels, registerLabel, unregisterLabel }}>
      {children}
    </LabelsContext.Provider>
  );
};

// Hook to use the labels context
export const useLabels = () => useContext(LabelsContext);

// Hook to register a label
export const useRegisterLabel = (id, type, position, content) => {
  const { registerLabel, unregisterLabel } = useLabels();

  useEffect(() => {
    registerLabel(id, type, position, content);
    return () => unregisterLabel(id);
  }, [id, type, position, content, registerLabel, unregisterLabel]);
};

// Components to register different types of labels
export const StableNodeLabel = ({ id, position, label }) => {
  useRegisterLabel(id, 'node', position, label);
  return null; // This component doesn't render anything
};

export const StableDistanceLabel = ({ id, position, distance }) => {
  useRegisterLabel(id, 'distance', position, distance === Infinity ? '∞' : distance.toString());
  return null; // This component doesn't render anything
};

export const StableEdgeLabel = ({ id, position, weight }) => {
  useRegisterLabel(id, 'edge', position, weight);
  return null; // This component doesn't render anything
};

// Component to render all labels
export const StableLabelsContainer = () => {
  const { labels } = useLabels();
  const { camera, size, gl } = useThree();
  const [screenLabels, setScreenLabels] = useState([]);

  // Update screen positions on each frame
  useFrame(() => {
    if (!labels.length) return;

    const updatedLabels = labels.map(label => {
      if (!label.position) return { ...label, visible: false };

      // Create a vector from the 3D position
      const vector = label.position.clone();

      // Project the 3D position to screen space
      vector.project(camera);

      // Convert to screen coordinates
      const x = (vector.x * 0.5 + 0.5) * size.width;
      const y = (-(vector.y * 0.5) + 0.5) * size.height;

      // Check if the point is in front of the camera
      const visible = vector.z < 1;

      return { ...label, x, y, visible };
    });

    setScreenLabels(updatedLabels);
  });

  // Create a portal to render labels outside the canvas
  const canvasParent = gl?.domElement?.parentNode;

  if (!canvasParent) return null;

  return createPortal(
    <div className="stable-labels-container">
      {screenLabels.map(label => {
        if (!label.visible) return null;

        let className = 'stable-label';
        if (label.type === 'node') className += ' node-label';
        if (label.type === 'distance') className += ' distance-label';
        if (label.type === 'edge') className += ' edge-label';

        return (
          <div
            key={label.id}
            className={className}
            style={{
              left: `${label.x}px`,
              top: `${label.y}px`,
            }}
          >
            {label.content}
          </div>
        );
      })}
    </div>,
    canvasParent
  );
};

// Export the components
export default {
  LabelsProvider,
  StableNodeLabel,
  StableDistanceLabel,
  StableEdgeLabel,
  StableLabelsContainer
};
