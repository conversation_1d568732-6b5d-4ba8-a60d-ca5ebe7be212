// ProgressSection.js
// A reusable progress section component with consistent styling

import React, { useState } from 'react';
import { Box, Typography, Stack, CircularProgress, Paper, Collapse, LinearProgress, useTheme } from '@mui/material';
import TimelineIcon from '@mui/icons-material/Timeline';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PendingIcon from '@mui/icons-material/Pending';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PropTypes from 'prop-types';

/**
 * A reusable progress section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string} props.state - Current state of the algorithm (idle, running, paused, completed)
 * @param {number} props.step - Current step
 * @param {number} props.totalSteps - Total number of steps
 */
const ProgressSection = ({
  state = 'idle',
  step = 0,
  totalSteps = 0,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(true);

  // Calculate progress percentage
  const progressPercentage = Math.round((step / totalSteps) * 100) || 0;

  // Determine color based on state
  const getStateColor = () => {
    switch (state) {
      case 'idle': return '#0288D1'; // Blue
      case 'running': return '#2E7D32'; // Green
      case 'paused': return theme.palette.warning.main; // Warning color (matches pause button)
      case 'completed': return '#2E7D32'; // Green
      default: return theme.palette.text.primary;
    }
  };

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <TimelineIcon fontSize="small" sx={{ mr: 0.5 }} /> Progress
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Stack spacing={2}>
          {/* Progress Bar */}
          <Box sx={{
            p: 1.5,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
            borderRadius: 1,
            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
          }}>
            <Stack spacing={1.5}>
              {/* Step Counter with Circular Progress */}
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                  <CircularProgress
                    variant="determinate"
                    value={progressPercentage}
                    size={60}
                    thickness={4} // Thinner circle
                    sx={{
                      color: state === 'completed' || progressPercentage === 100
                        ? '#2E7D32' // Green when completed
                        : theme.palette.mode === 'dark'
                          ? theme.palette.primary.light
                          : theme.palette.primary.main,
                      '& .MuiCircularProgress-circle': {
                        strokeLinecap: 'round',
                      },
                    }}
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" component="div" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                      {`${progressPercentage}%`}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                    Step: {step} / {totalSteps}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={progressPercentage}
                    sx={{
                      height: 6, // Thinner bar
                      borderRadius: 3,
                      backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 3,
                        backgroundColor: state === 'completed' || progressPercentage === 100
                          ? '#2E7D32' // Green when completed
                          : theme.palette.mode === 'dark'
                            ? theme.palette.primary.light
                            : theme.palette.primary.main,
                      },
                    }}
                  />
                </Box>
              </Stack>

              {/* Status Indicator */}
              <Box sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: state === 'idle' ? 'rgba(2, 136, 209, 0.15)' : // Light blue for idle
                  state === 'running' ? 'rgba(46, 125, 50, 0.15)' : // Light green for running
                    state === 'paused' ? 'rgba(237, 108, 2, 0.15)' : // Light orange for paused
                      state === 'completed' ? 'rgba(46, 125, 50, 0.15)' : // Light green for completed
                        theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
                color: getStateColor(),
              }}>
                <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
                  {state === 'idle' &&
                    <HourglassEmptyIcon
                      fontSize="small"
                      sx={{
                        animation: 'hourglass-flip 4s infinite',
                        '@keyframes hourglass-flip': {
                          '0%': { transform: 'rotate(0deg)', opacity: 0.8 },
                          '20%': { transform: 'rotate(180deg)', opacity: 1 },
                          '40%': { transform: 'rotate(180deg)', opacity: 1 }, /* Pause at 180 degrees */
                          '60%': { transform: 'rotate(360deg)', opacity: 1 },
                          '80%': { transform: 'rotate(360deg)', opacity: 1 }, /* Pause at 360 degrees */
                          '100%': { transform: 'rotate(360deg)', opacity: 0.8 },
                        },
                      }}
                    />}
                  {state === 'running' && <CircularProgress size={16} thickness={4} sx={{ color: 'inherit' }} />}
                  {state === 'paused' && <PendingIcon fontSize="small" />}
                  {state === 'completed' && <CheckCircleIcon fontSize="small" />}
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {state === 'idle' ? 'Ready to Start' :
                      state === 'running' ? 'Running...' :
                        state === 'paused' ? 'Paused' :
                          state === 'completed' ? 'Completed' : 'Unknown'}
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Box>
        </Stack>
      </Collapse>
    </Paper>
  );
};

ProgressSection.propTypes = {
  state: PropTypes.oneOf(['idle', 'running', 'paused', 'completed']),
  step: PropTypes.number,
  totalSteps: PropTypes.number,
};

export default ProgressSection;
