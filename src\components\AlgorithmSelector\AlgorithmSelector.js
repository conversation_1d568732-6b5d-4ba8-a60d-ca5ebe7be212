import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemButton,
  Chip,
  Tooltip,
  useTheme,
} from "@mui/material";
// Removed DonationSection import
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SortIcon from "@mui/icons-material/Sort";
import SearchIcon from "@mui/icons-material/Search";
import AccountTreeIcon from "@mui/icons-material/AccountTree";
import MemoryIcon from "@mui/icons-material/Memory";
import ExtensionIcon from "@mui/icons-material/Extension";
import FunctionsIcon from "@mui/icons-material/Functions";
import CodeIcon from "@mui/icons-material/Code";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

// Import algorithm registry
import { getAlgorithmList } from "../../algorithms/AlgorithmRegistry";

// Define algorithm categories with their algorithms
const algorithmCategories = [
  {
    id: "sorting",
    name: "Sorting Algorithms",
    icon: <SortIcon />,
    algorithms: [
      { id: "BubbleSort", name: "Bubble Sort", implemented: true },
      { id: "MergeSort", name: "Merge Sort", implemented: true },
      { id: "QuickSort", name: "Quick Sort", implemented: true },
      { id: "HeapSort", name: "Heap Sort", implemented: true },
      { id: "InsertionSort", name: "Insertion Sort", implemented: true },
      { id: "SelectionSort", name: "Selection Sort", implemented: true },
      { id: "RadixSort", name: "Radix Sort", implemented: true },
      { id: "CountingSort", name: "Counting Sort", implemented: true },
      { id: "BucketSort", name: "Bucket Sort", implemented: true },
      { id: "ShellSort", name: "Shell Sort", implemented: true },
      { id: "TimSort", name: "Tim Sort", implemented: true },
    ],
  },
  {
    id: "searching",
    name: "Searching Algorithms",
    icon: <SearchIcon />,
    algorithms: [
      { id: "BFS", name: "Breadth-First Search", implemented: true },
      { id: "DFS", name: "Depth-First Search", implemented: true },
      { id: "BinarySearch", name: "Binary Search", implemented: true },
      { id: "LinearSearch", name: "Linear Search", implemented: true },
      { id: "JumpSearch", name: "Jump Search", implemented: true },
      { id: "InterpolationSearch", name: "Interpolation Search", implemented: true },
      { id: "ExponentialSearch", name: "Exponential Search", implemented: true },
    ],
  },
  {
    id: "graph",
    name: "Graph Algorithms",
    icon: <AccountTreeIcon />,
    algorithms: [
      { id: "Dijkstra", name: "Dijkstra's Algorithm", implemented: true },
      { id: "BellmanFord", name: "Bellman-Ford Algorithm", implemented: true },
      { id: "FloydWarshall", name: "Floyd-Warshall Algorithm", implemented: true },
      { id: "Kruskals", name: "Kruskal's Algorithm", implemented: true },
      { id: "Prims", name: "Prim's Algorithm", implemented: true },
      { id: "TopologicalSort", name: "Topological Sort", implemented: true },
      { id: "AStar", name: "A* Search Algorithm", implemented: true },
      { id: "StronglyConnected", name: "Strongly Connected Components", implemented: true },
      { id: "ArticulationPoints", name: "Articulation Points", implemented: true },
      { id: "Bridges", name: "Bridges in Graph", implemented: true },
    ],
  },
  {
    id: "dp",
    name: "Dynamic Programming",
    icon: <MemoryIcon />,
    algorithms: [
      { id: "Fibonacci", name: "Fibonacci Sequence", implemented: true },
      { id: "Knapsack", name: "0/1 Knapsack Problem", implemented: true },
      { id: "LCS", name: "Longest Common Subsequence", implemented: true },
      { id: "EditDistance", name: "Edit Distance", implemented: true },
      { id: "MatrixChainMultiplication", name: "Matrix Chain Multiplication", implemented: true },
      { id: "CoinChange", name: "Coin Change Problem", implemented: true },
      { id: "LIS", name: "Longest Increasing Subsequence", implemented: true },
      { id: "RodCutting", name: "Rod Cutting Problem", implemented: true },
    ],
  },
  {
    id: "recursion",
    name: "Recursion & Backtracking",
    icon: <ExtensionIcon />,
    algorithms: [
      { id: "TowersOfHanoi", name: "Towers of Hanoi", implemented: true },
      { id: "NQueens", name: "N-Queens Problem", implemented: true },
      { id: "Sudoku", name: "Sudoku Solver", implemented: true },
      { id: "RatInMaze", name: "Rat in a Maze", implemented: true },
      { id: "Permutations", name: "Generate Permutations", implemented: true },
      { id: "Combinations", name: "Generate Combinations", implemented: true },
      { id: "SubsetSum", name: "Subset Sum Problem", implemented: true },
    ],
  },
  {
    id: "math",
    name: "Mathematical Algorithms",
    icon: <FunctionsIcon />,
    algorithms: [
      { id: "Sieve", name: "Sieve of Eratosthenes", implemented: true },
      { id: "GCD", name: "Euclidean GCD", implemented: true },
      { id: "PrimalityTest", name: "Primality Test", implemented: true },
      { id: "FastPower", name: "Fast Power (Exponentiation)", implemented: false },
      { id: "MatrixExponentiation", name: "Matrix Exponentiation", implemented: false },
      { id: "FFT", name: "Fast Fourier Transform", implemented: false },
      { id: "Factorial", name: "Factorial Calculation", implemented: false },
    ],
  },
  {
    id: "other",
    name: "Other Algorithms",
    icon: <CodeIcon />,
    algorithms: [
      { id: "Huffman", name: "Huffman Coding", implemented: false },
      { id: "KMP", name: "KMP String Matching", implemented: false },
      { id: "RabinKarp", name: "Rabin-Karp Algorithm", implemented: false },
      { id: "LRUCache", name: "LRU Cache Implementation", implemented: false },
      { id: "ConvexHull", name: "Convex Hull Algorithm", implemented: false },
      { id: "SegmentTree", name: "Segment Tree", implemented: false },
      { id: "FenwickTree", name: "Fenwick Tree (BIT)", implemented: false },
    ],
  },
];

const AlgorithmSelector = ({ selectedAlgorithm, onAlgorithmChange }) => {
  // Get the list of implemented algorithms from the registry
  const implementedAlgorithms = getAlgorithmList();
  const implementedIds = implementedAlgorithms.map(algo => algo.id);

  // State for expanded categories
  const [expandedCategory, setExpandedCategory] = useState(null);

  // Get theme for consistent styling
  const theme = useTheme();

  // Handle category expansion
  const handleCategoryChange = (categoryId) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId);
  };

  // Find the category of the selected algorithm
  const findCategoryForAlgorithm = (algorithmId) => {
    for (const category of algorithmCategories) {
      if (category.algorithms.some(algo => algo.id === algorithmId)) {
        return category.id;
      }
    }
    return null;
  };

  // Ensure the category of the selected algorithm is expanded
  React.useEffect(() => {
    if (selectedAlgorithm) {
      const categoryId = findCategoryForAlgorithm(selectedAlgorithm);
      if (categoryId && expandedCategory !== categoryId) {
        setExpandedCategory(categoryId);
      }
    }
  }, [selectedAlgorithm]);

  return (
    <Paper
      elevation={3}
      sx={{
        overflow: "hidden",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        boxShadow: theme.shadows[3],
        mx: 1, // Add margin on the sides
        // my: 1, // Add margin on top and bottom
      }}
    >
      <Box sx={{
        p: 2,
        borderBottom: 1,
        borderColor: "divider",
        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
      }}>
        <Typography
          variant="h6"
          gutterBottom
          sx={{
            color: theme.palette.primary.main,
            fontWeight: 500,
          }}
        >
          Explore Algorithms
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Visualize and learn how algorithms work
        </Typography>
      </Box>

      {/* Algorithm Selection Section - Takes available space */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden', // Prevent overflow from affecting other sections
      }}>
        <Box sx={{
          overflowY: "auto",
          flex: 1,
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}>
          {algorithmCategories.map((category) => (
            <Accordion
              key={category.id}
              expanded={expandedCategory === category.id}
              onChange={() => handleCategoryChange(category.id)}
              disableGutters
              sx={{
                '&:before': { display: 'none' }, // Remove the default divider
                boxShadow: 'none',
                borderBottom: 1,
                borderColor: 'divider',
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  minHeight: 56,
                  '& .MuiAccordionSummary-content': {
                    alignItems: 'center',
                  },
                  bgcolor: expandedCategory === category.id ?
                    (theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)') :
                    'transparent',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ color: 'primary.main', mr: 1.5 }}>
                    {category.icon}
                  </Box>
                  <Typography variant="subtitle1">{category.name}</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 0 }}>
                <List disablePadding>
                  {category.algorithms.map((algorithm) => {
                    const isImplemented = implementedIds.includes(algorithm.id);
                    return (
                      <ListItem
                        key={algorithm.id}
                        disablePadding
                        secondaryAction={
                          !isImplemented && (
                            <Tooltip title="Coming soon" arrow>
                              <InfoOutlinedIcon
                                fontSize="small"
                                color="action"
                                sx={{ opacity: 0.6, mr: 1 }}
                              />
                            </Tooltip>
                          )
                        }
                      >
                        <ListItemButton
                          selected={selectedAlgorithm === algorithm.id}
                          onClick={() => isImplemented && onAlgorithmChange(algorithm.id)}
                          disabled={!isImplemented}
                          sx={{
                            pl: 4,
                            py: 1,
                            opacity: isImplemented ? 1 : 0.6,
                            '&.Mui-selected': {
                              bgcolor: theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.16)' : 'rgba(33, 150, 243, 0.08)',
                            },
                            '&.Mui-selected:hover': {
                              bgcolor: theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.24)' : 'rgba(33, 150, 243, 0.12)',
                            },
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: selectedAlgorithm === algorithm.id ? 500 : 400,
                                color: 'text.primary'
                              }}
                            >
                              {algorithm.name}
                            </Typography>

                            {isImplemented && selectedAlgorithm === algorithm.id && (
                              <Chip
                                label="Active"
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ height: 20, fontSize: '0.7rem' }}
                              />
                            )}
                          </Box>
                        </ListItemButton>
                      </ListItem>
                    );
                  })}
                </List>
              </AccordionDetails>
            </Accordion>
          ))}

        </Box>
      </Box>

      {/* No divider or donation section here */}
    </Paper>
  );
};

export default AlgorithmSelector;
