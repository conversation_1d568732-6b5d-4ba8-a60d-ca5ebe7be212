# Dynamic Programming Algorithms

This directory contains implementations of various dynamic programming algorithms.

## Implemented Algorithms

- LCS (Longest Common Subsequence) - A dynamic programming algorithm that finds the longest subsequence common to two sequences.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `Knapsack`)
2. Implement the required files:
   - `KnapsackAlgorithm.js` - Core algorithm logic
   - `KnapsackVisualization.js` - Visualization component
   - `KnapsackController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
