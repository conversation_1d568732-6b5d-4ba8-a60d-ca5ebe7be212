import React from 'react';
import Bar from './Bar';

/**
 * Reusable sorted array component for algorithm visualizations
 * 
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the sorted array
 * @param {Array} props.array - Array of values to display
 * @param {number} props.maxValue - Maximum value in the array (for scaling)
 * @param {string} props.color - Color of the sorted bars
 * @param {boolean} props.showValues - Whether to show values on bars
 * @param {boolean} props.showIndices - Whether to show indices on bars
 * @param {number} props.maxBarHeight - Maximum height of bars
 * @param {number} props.barWidth - Width of bars
 * @param {number} props.spacing - Spacing between bars
 * @returns {JSX.Element} - The rendered sorted array component
 */
const SortedArray = ({ 
    position = [0, 0, 0], 
    array = [], 
    maxValue = 100,
    color = '#4caf50',
    showValues = true,
    showIndices = true,
    maxBarHeight = 3,
    barWidth = 0.6,
    spacing = 0.2
}) => {
    if (!array || array.length === 0) return null;
    
    // Calculate dimensions
    const totalWidth = (barWidth + spacing) * array.length;
    const startX = -(totalWidth / 2) + (barWidth / 2);
    
    return (
        <group position={position}>
            {array.map((value, index) => {
                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * maxBarHeight;
                const x = startX + index * (barWidth + spacing);
                
                return (
                    <Bar
                        key={`sorted-${index}`}
                        position={[x, 0, 0]}
                        height={normalizedHeight}
                        width={barWidth}
                        color={color}
                        value={value}
                        index={index}
                        showValue={showValues}
                        showIndex={showIndices}
                        showArrow={false}
                    />
                );
            })}
        </group>
    );
};

export default SortedArray;
