// TopologicalSort/index.js
// Export only what's needed from this algorithm

// Import the visualization
import TopologicalSortVisualization from './TopologicalSortVisualization';
import TopologicalSortController from './TopologicalSortController';
import TopologicalSortAlgorithm from './TopologicalSortAlgorithm';

export const metadata = {
  id: 'TopologicalSort',
  name: 'Topological Sort',
  description: 'An algorithm for ordering the vertices of a directed acyclic graph (DAG) such that for every directed edge (u, v), vertex u comes before vertex v in the ordering.',
  timeComplexity: 'O(V + E)',
  spaceComplexity: 'O(V)',
  defaultParams: {
    nodes: 6,
    density: 0.5,
    customEdges: [],
  },
};

export const components = {
  visualization: TopologicalSortVisualization,
  controller: TopologicalSortController,
  algorithm: TopologicalSortAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
