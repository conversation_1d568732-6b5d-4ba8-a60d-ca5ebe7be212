// Prims/index.js
// Export only what's needed from this algorithm

// Import the visualization
import PrimsVisualization from './PrimsVisualization';
import PrimsController from './PrimsController';
import PrimsAlgorithm from './PrimsAlgorithm';

export const metadata = {
  id: 'Prims',
  name: 'Prim\'s Algorithm',
  description: 'A greedy algorithm that finds a minimum spanning tree for a weighted undirected graph.',
  timeComplexity: 'O(E log V)',
  spaceComplexity: 'O(V + E)',
  defaultParams: {
    nodes: 6,
    density: 0.7,
    minWeight: 1,
    maxWeight: 10,
    startNode: 0,
    customEdges: [],
  },
};

export const components = {
  visualization: PrimsVisualization, // Use the standard visualization
  controller: PrimsController,
  algorithm: PrimsAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
