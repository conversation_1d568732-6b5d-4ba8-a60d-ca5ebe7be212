// LIS/index.js
// Export only what's needed from this algorithm

import LISVisualization from './LISVisualization';
import LISController from './LISController';
import LISAlgorithm from './LISAlgorithm';

export const metadata = {
  id: 'LIS',
  name: 'Longest Increasing Subsequence',
  description: 'A dynamic programming algorithm that finds the length of the longest subsequence of a given sequence such that all elements of the subsequence are sorted in increasing order.',
  timeComplexity: 'O(n²)',
  spaceComplexity: 'O(n)',
  defaultParams: {
    sequence: [10, 22, 9, 33, 21, 50, 41, 60, 80],
  },
};

export const components = {
  visualization: LISVisualization,
  controller: LISController,
  algorithm: LISAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
