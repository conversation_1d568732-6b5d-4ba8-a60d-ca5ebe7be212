// SelectionSortVisualization.js - Clean SelectionSort visualization following InsertionSort pattern
// Uses SelectionSortSimulation component and controller-driven architecture

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import SelectionSortSimulation from '../../../components/visualization/bars/SelectionSortSimulation';

import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './SelectionSortConfig';
import { getEnhancedDelay, getEnhancedAnimationDuration } from '../../../utils/speedUtils';
import * as THREE from 'three';

// Extract constants from comprehensive configuration
const BAR_SPACING = CONFIG.mainArray.bars.spacing;

// Main SelectionSort Visualization Component
const SelectionSortVisualization = (props) => {
  const {
    state,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    theme,
    steps,
    array
  } = props;

  // Get array data from props
  const arrayData = array || [];

  // Speed context
  const { speed } = useSpeed();

  // Store speed in a ref for animation frame access (like HeapSort/InsertionSort)
  const speedRef = useRef(speed);
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Three.js scene reference
  const { camera } = useThree();

  // Group reference for levitation animation
  const groupRef = useRef();

  // Set camera position from config - ALWAYS use config values (like InsertionSort)
  useEffect(() => {
    if (!camera) return;

    console.log('SelectionSort - Setting camera from config:', CONFIG.camera.position);

    // ALWAYS use the config values directly - no custom logic
    camera.position.set(...CONFIG.camera.position);
    camera.lookAt(new THREE.Vector3(...CONFIG.camera.lookAt));
    camera.fov = CONFIG.camera.fov;
    camera.near = CONFIG.camera.near;
    camera.far = CONFIG.camera.far;
    camera.updateProjectionMatrix();

    console.log('SelectionSort - Camera set to:', camera.position.toArray());
  }, [camera, CONFIG.camera]); // Re-run when config changes

  // State for current step data
  const [currentStep, setCurrentStep] = useState(null);
  const [displayStep, setDisplayStep] = useState(null);

  // Animation states
  const [swapAnimation, setSwapAnimation] = useState({
    active: false,
    indices: [],
    positions: {},
    progress: 0,
    startTime: 0
  });

  // Animation control
  const animatingRef = useRef(false);

  // Get colors from configuration based on theme
  const colors = useMemo(() => {
    const isDark = theme?.palette?.mode === 'dark';
    return isDark ? CONFIG.colors.themes.dark : CONFIG.colors.themes.light;
  }, [theme]);

  // Create legend items with current colors
  const legendItems = useMemo(() => {
    return CONFIG.colorLegend.legendItems.map(item => ({
      ...item,
      color: colors[item.colorKey] || colors.bar
    }));
  }, [colors]);

  // Update current step when step changes
  useEffect(() => {
    if (!steps || steps.length === 0) return;

    const currentStepData = steps[step] || null;
    // Validate step bounds
    if (step >= steps.length) {
      return;
    }

    // Check for swap animation (like other sorting algorithms)
    if (step > 0 && currentStepData && !animatingRef.current) {
      const previousStepData = steps[step - 1];

      // Detect swap animation
      if (previousStepData?.type === 'prepare_swap' && currentStepData?.type === 'swapped') {
        const swappingIndices = previousStepData.visualizationData?.mainArray?.swappingIndices || [];

        if (swappingIndices.length === 2) {
          // Start swap animation (exactly like HeapSort)
          animatingRef.current = true;

          // Show preparation state during animation
          setDisplayStep(previousStepData);

          setSwapAnimation({
            active: true,
            indices: swappingIndices,
            progress: 0,
            startTime: performance.now()
          });

          // End animation after duration and show current step
          // Use speed-based animation duration like other algorithms
          const speedBasedAnimationDuration = getEnhancedAnimationDuration(speedRef.current);
          setTimeout(() => {
            // Now show the current step (with elements swapped)
            setDisplayStep(currentStepData);
            setCurrentStep(currentStepData);

            setSwapAnimation(prev => ({
              ...prev,
              active: false
            }));
            animatingRef.current = false;
          }, speedBasedAnimationDuration);

          // Exit early - don't update display step until animation completes
          return;
        }
      }
    }

    // Direct update (no animation needed)
    setDisplayStep(currentStepData);
    setCurrentStep(currentStepData);
  }, [steps, step]);

  // Calculate adaptive dimensions based on array size using config
  const { scaleFactor, adaptiveSpacing } = useMemo(() => {
    const arraySize = arrayData.length;

    // Determine scale factor based on config breakpoints
    let factor;
    if (arraySize <= 10) {
      factor = 1.0;
    } else if (arraySize <= 15) {
      factor = 0.8;
    } else {
      factor = 0.6;
    }

    // Calculate adaptive spacing
    const spacing = Math.max(0.2, BAR_SPACING * factor);

    return {
      scaleFactor: factor,
      adaptiveSpacing: spacing
    };
  }, [arrayData.length]);

  // State to track the actual total width from bars for proper platform sizing
  const [actualTotalWidth, setActualTotalWidth] = useState(0);

  // Calculate stage dimensions using actual bar width from simulation
  const stageDimensions = useMemo(() => {
    // Use the initial array length for fallback calculation
    const arrayLength = arrayData.length || 5;

    // Calculate fallback width if actualTotalWidth is not available yet
    const adaptiveBarWidth = CONFIG.mainArray.bars.width * scaleFactor;
    const fallbackTotalWidth = (arrayLength * (adaptiveBarWidth + adaptiveSpacing)) - adaptiveSpacing;

    // Use actual width from bars if available, otherwise use fallback
    const totalBarsWidth = actualTotalWidth > 0 ? actualTotalWidth : fallbackTotalWidth;

    return {
      width: Math.max(
        totalBarsWidth + CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right,
        CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right + 4 // Minimum width
      ),
      height: CONFIG.basePlatform.dimensions.height,
      depth: CONFIG.basePlatform.dimensions.depth
    };
  }, [arrayData.length, adaptiveSpacing, scaleFactor, actualTotalWidth]);

  // Enhanced auto-advance functionality
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      return;
    }

    // Stop if we've reached the end
    if (step >= totalSteps) {
      return;
    }

    // Calculate delay based on speed using enhanced delay function
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    const timeoutId = setTimeout(() => {
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef, steps]);

  // Animation frame for levitation and swap animation
  useFrame(({ clock }) => {
    // Update swap animation progress
    if (swapAnimation.active) {
      const now = performance.now();
      const elapsed = now - swapAnimation.startTime;
      const duration = CONFIG.animation.types.swap.duration;
      const progress = Math.min(elapsed / duration, 1);

      // Only update if there's a meaningful change to reduce unnecessary renders
      if (Math.abs(progress - swapAnimation.progress) > 0.01) {
        setSwapAnimation(prev => ({
          ...prev,
          progress
        }));
      }
    }

    // Levitation animation - stay at origin, let child components handle their own positioning
    if (CONFIG.visual.levitation.enabled &&
      (!CONFIG.visual.levitation.disableDuringSimulation || state === 'idle') &&
      groupRef.current) {

      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.visual.levitation;

      // Apply levitation relative to base platform position from config
      const basePosition = CONFIG.basePlatform.position;

      if (levitationConfig.movement.y.enabled) {
        groupRef.current.position.y = basePosition[1] + Math.sin(time * levitationConfig.movement.y.frequency) * levitationConfig.movement.y.amplitude;
      } else {
        groupRef.current.position.y = basePosition[1];
      }

      if (levitationConfig.movement.x.enabled) {
        groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.movement.x.frequency) * levitationConfig.movement.x.amplitude;
      } else {
        groupRef.current.position.x = basePosition[0];
      }

      if (levitationConfig.movement.z.enabled) {
        groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.movement.z.frequency) * levitationConfig.movement.z.amplitude;
      } else {
        groupRef.current.position.z = basePosition[2];
      }

      // Apply rotation effects
      if (levitationConfig.rotation.enabled) {
        if (levitationConfig.rotation.x.enabled) {
          groupRef.current.rotation.x = Math.cos(time * levitationConfig.rotation.x.frequency) * levitationConfig.rotation.x.amplitude;
        }

        if (levitationConfig.rotation.y.enabled) {
          groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
        }

        if (levitationConfig.rotation.z.enabled) {
          groupRef.current.rotation.z = Math.sin(time * levitationConfig.rotation.z.frequency) * levitationConfig.rotation.z.amplitude;
        }
      }
    } else if (groupRef.current) {
      // When levitation is disabled, use base platform position from config
      const basePosition = CONFIG.basePlatform.position;
      groupRef.current.position.set(basePosition[0], basePosition[1], basePosition[2]);
      groupRef.current.rotation.set(0, 0, 0);
    }
  });

  return (
    <>
      {/* Step board */}
      {CONFIG.stepBoard.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''}
          totalSteps={totalSteps > 0 ? totalSteps - 1 : 0}
          description={currentStep?.statement || 'Selection Sort Algorithm'}
          stepData={currentStep}
          showStepNumber={step > 0}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {/* Color legend */}
      {CONFIG.colorLegend.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      {/* Main Array Group - Position controlled by levitation animation in useFrame */}
      <group ref={groupRef}>
        {/* Base platform - positioned at origin since group handles basePlatform.position */}
        <SortingBase
          width={stageDimensions.width}
          height={stageDimensions.height}
          depth={stageDimensions.depth}
          color={colors.base}
          position={[0, -stageDimensions.height / 2, 0]}
        />

        {/* Bars and labels - positioned relative to the base platform using config offset */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <SelectionSortSimulation
            currentStep={displayStep || currentStep}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width * scaleFactor}
            barSpacing={adaptiveSpacing}
            showValues={(() => {
              const shouldShow = CONFIG.visual.labels.values.adaptiveVisibility
                ? (scaleFactor > CONFIG.visual.labels.values.visibilityThreshold)
                : CONFIG.visual.labels.values.enabled;

              // Value visibility calculation for adaptive display

              return shouldShow;
            })()}
            showIndices={CONFIG.visual.labels.indices.adaptiveVisibility
              ? (scaleFactor > CONFIG.visual.labels.indices.visibilityThreshold)
              : CONFIG.visual.labels.indices.enabled}
            config={CONFIG}
            state={state}
            swapAnimation={swapAnimation}
            originalArray={arrayData}
            onWidthChange={setActualTotalWidth}
          />
        </group>
      </group>
    </>
  );
};

export default SelectionSortVisualization;
