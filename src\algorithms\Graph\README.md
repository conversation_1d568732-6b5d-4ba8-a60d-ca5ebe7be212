# Graph Algorithms

This directory contains implementations of various graph algorithms.

## Implemented Algorithms

- Dijkstra - An algorithm for finding the shortest paths between nodes in a graph.
- FloydWarshall - An algorithm for finding shortest paths in a weighted graph with positive or negative edge weights.
- BellmanFord - An algorithm for finding the shortest paths from a source vertex to all other vertices in a weighted graph, which can handle negative edge weights and detect negative cycles.
- Kruskals - A minimum spanning tree algorithm that finds an edge of the least possible weight that connects any two trees in the forest.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `BellmanFord`)
2. Implement the required files:
   - `BellmanFordAlgorithm.js` - Core algorithm logic
   - `BellmanFordVisualization.js` - Visualization component
   - `BellmanFordController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
