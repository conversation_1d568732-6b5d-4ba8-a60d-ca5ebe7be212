// TimSortAlgorithm.js
// Implementation of the Tim Sort algorithm with step generation

/**
 * Generates steps for the Tim Sort algorithm
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateTimSortSteps = (arr) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    movement: 'Initialize Tim Sort'
  });

  // Tim Sort constants
  const RUN = 32; // Size of the run for insertion sort
  const n = inputArray.length;

  // Add step to explain the run size
  steps.push({
    type: 'setRunSize',
    array: [...inputArray],
    runSize: RUN,
    movement: `Set run size to ${RUN} for insertion sort`
  });

  // Step 1: Sort individual subarrays of size RUN using insertion sort
  for (let i = 0; i < n; i += RUN) {
    // Calculate the end index for this run
    const end = Math.min(i + RUN - 1, n - 1);
    
    // Add step to show the current run
    steps.push({
      type: 'startRun',
      array: [...inputArray],
      runStart: i,
      runEnd: end,
      movement: `Start sorting run from index ${i} to ${end} using insertion sort`
    });

    // Perform insertion sort on this run
    insertionSort(inputArray, i, end, steps);
  }

  // Add step to show all runs sorted
  steps.push({
    type: 'allRunsSorted',
    array: [...inputArray],
    movement: 'All runs sorted using insertion sort'
  });

  // Step 2: Merge the sorted runs
  // Start with size RUN, double the size on each iteration
  for (let size = RUN; size < n; size = 2 * size) {
    // Add step to show the current merge size
    steps.push({
      type: 'setMergeSize',
      array: [...inputArray],
      mergeSize: size,
      movement: `Set merge size to ${size}`
    });

    // Merge subarrays in pairs
    for (let left = 0; left < n; left += 2 * size) {
      // Calculate mid and right indices
      const mid = Math.min(n - 1, left + size - 1);
      const right = Math.min(n - 1, left + 2 * size - 1);

      // Add step to show the current merge operation
      steps.push({
        type: 'startMerge',
        array: [...inputArray],
        left,
        mid,
        right,
        movement: `Start merging subarrays from index ${left} to ${mid} and from ${mid + 1} to ${right}`
      });

      // Merge the two subarrays
      merge(inputArray, left, mid, right, steps);
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    sorted: Array.from({ length: inputArray.length }, (_, i) => i),
    movement: 'Tim Sort complete'
  });

  return { steps, sortedArray: inputArray };
};

/**
 * Performs insertion sort on a subarray and generates steps
 * @param {Array} arr - The array to sort
 * @param {number} left - The left index of the subarray
 * @param {number} right - The right index of the subarray
 * @param {Array} steps - The steps array to append to
 */
const insertionSort = (arr, left, right, steps) => {
  for (let i = left + 1; i <= right; i++) {
    // Add step to show the current element being considered
    steps.push({
      type: 'insertionConsider',
      array: [...arr],
      currentIndex: i,
      currentValue: arr[i],
      runStart: left,
      runEnd: right,
      movement: `Consider element ${arr[i]} at index ${i} for insertion`
    });

    // Save the current element
    const temp = arr[i];
    let j = i - 1;

    // Add step to start the comparison process
    steps.push({
      type: 'insertionStartCompare',
      array: [...arr],
      currentIndex: i,
      compareIndex: j,
      currentValue: temp,
      runStart: left,
      runEnd: right,
      movement: `Start comparing ${temp} with previous elements`
    });

    // Shift elements that are greater than the current element
    while (j >= left && arr[j] > temp) {
      // Add step to show the comparison
      steps.push({
        type: 'insertionCompare',
        array: [...arr],
        currentIndex: i,
        compareIndex: j,
        compareValue: arr[j],
        currentValue: temp,
        runStart: left,
        runEnd: right,
        movement: `Compare ${arr[j]} > ${temp}`
      });

      // Shift the element
      arr[j + 1] = arr[j];

      // Add step to show the shift
      steps.push({
        type: 'insertionShift',
        array: [...arr],
        currentIndex: i,
        shiftFromIndex: j,
        shiftToIndex: j + 1,
        shiftValue: arr[j + 1],
        runStart: left,
        runEnd: right,
        movement: `Shift ${arr[j + 1]} from index ${j} to index ${j + 1}`
      });

      j--;
    }

    // Insert the saved element
    arr[j + 1] = temp;

    // Add step to show the insertion
    steps.push({
      type: 'insertionInsert',
      array: [...arr],
      currentIndex: i,
      insertIndex: j + 1,
      insertValue: temp,
      runStart: left,
      runEnd: right,
      movement: `Insert ${temp} at index ${j + 1}`
    });
  }

  // Add step to show the sorted run
  steps.push({
    type: 'runSorted',
    array: [...arr],
    runStart: left,
    runEnd: right,
    movement: `Run from index ${left} to ${right} sorted`
  });
};

/**
 * Merges two subarrays and generates steps
 * @param {Array} arr - The array to merge
 * @param {number} left - The left index of the first subarray
 * @param {number} mid - The right index of the first subarray
 * @param {number} right - The right index of the second subarray
 * @param {Array} steps - The steps array to append to
 */
const merge = (arr, left, mid, right, steps) => {
  // Calculate lengths of the two subarrays
  const len1 = mid - left + 1;
  const len2 = right - mid;

  // Create temporary arrays
  const leftArr = new Array(len1);
  const rightArr = new Array(len2);

  // Copy data to temporary arrays
  for (let i = 0; i < len1; i++) {
    leftArr[i] = arr[left + i];
  }
  for (let i = 0; i < len2; i++) {
    rightArr[i] = arr[mid + 1 + i];
  }

  // Add step to show the subarrays to be merged
  steps.push({
    type: 'mergeSubarrays',
    array: [...arr],
    left,
    mid,
    right,
    leftArray: [...leftArr],
    rightArray: [...rightArr],
    movement: `Prepare to merge subarrays [${leftArr.join(', ')}] and [${rightArr.join(', ')}]`
  });

  // Merge the temporary arrays back into arr[left...right]
  let i = 0; // Initial index of first subarray
  let j = 0; // Initial index of second subarray
  let k = left; // Initial index of merged subarray

  while (i < len1 && j < len2) {
    // Add step to show the comparison
    steps.push({
      type: 'mergeCompare',
      array: [...arr],
      left,
      mid,
      right,
      leftIndex: i,
      rightIndex: j,
      leftValue: leftArr[i],
      rightValue: rightArr[j],
      mergeIndex: k,
      movement: `Compare ${leftArr[i]} and ${rightArr[j]}`
    });

    if (leftArr[i] <= rightArr[j]) {
      // Add step to show the selection from left array
      steps.push({
        type: 'mergeSelectLeft',
        array: [...arr],
        left,
        mid,
        right,
        leftIndex: i,
        rightIndex: j,
        leftValue: leftArr[i],
        rightValue: rightArr[j],
        mergeIndex: k,
        movement: `Select ${leftArr[i]} from left array`
      });

      arr[k] = leftArr[i];
      i++;
    } else {
      // Add step to show the selection from right array
      steps.push({
        type: 'mergeSelectRight',
        array: [...arr],
        left,
        mid,
        right,
        leftIndex: i,
        rightIndex: j,
        leftValue: leftArr[i],
        rightValue: rightArr[j],
        mergeIndex: k,
        movement: `Select ${rightArr[j]} from right array`
      });

      arr[k] = rightArr[j];
      j++;
    }

    // Add step to show the placement
    steps.push({
      type: 'mergePlacement',
      array: [...arr],
      left,
      mid,
      right,
      mergeIndex: k,
      mergeValue: arr[k],
      movement: `Place ${arr[k]} at index ${k}`
    });

    k++;
  }

  // Copy the remaining elements of leftArr[], if any
  while (i < len1) {
    // Add step to show the remaining elements from left array
    steps.push({
      type: 'mergeRemainingLeft',
      array: [...arr],
      left,
      mid,
      right,
      leftIndex: i,
      leftValue: leftArr[i],
      mergeIndex: k,
      movement: `Copy remaining element ${leftArr[i]} from left array`
    });

    arr[k] = leftArr[i];

    // Add step to show the placement
    steps.push({
      type: 'mergePlacement',
      array: [...arr],
      left,
      mid,
      right,
      mergeIndex: k,
      mergeValue: arr[k],
      movement: `Place ${arr[k]} at index ${k}`
    });

    i++;
    k++;
  }

  // Copy the remaining elements of rightArr[], if any
  while (j < len2) {
    // Add step to show the remaining elements from right array
    steps.push({
      type: 'mergeRemainingRight',
      array: [...arr],
      left,
      mid,
      right,
      rightIndex: j,
      rightValue: rightArr[j],
      mergeIndex: k,
      movement: `Copy remaining element ${rightArr[j]} from right array`
    });

    arr[k] = rightArr[j];

    // Add step to show the placement
    steps.push({
      type: 'mergePlacement',
      array: [...arr],
      left,
      mid,
      right,
      mergeIndex: k,
      mergeValue: arr[k],
      movement: `Place ${arr[k]} at index ${k}`
    });

    j++;
    k++;
  }

  // Add step to show the merged subarray
  steps.push({
    type: 'mergeComplete',
    array: [...arr],
    left,
    mid,
    right,
    movement: `Subarrays merged from index ${left} to ${right}`
  });
};

// Default export
const TimSortAlgorithm = {
  generateSteps: generateTimSortSteps
};

export default TimSortAlgorithm;
