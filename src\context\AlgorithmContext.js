// AlgorithmContext.js
// This context provides state management for algorithm visualization

import React, { createContext, useState, useContext } from 'react';

// Create the context
const AlgorithmContext = createContext();

// Create a provider component
export const AlgorithmProvider = ({ children }) => {
  // State for algorithm visualization
  const [state, setState] = useState("idle");
  const [step, setStep] = useState(0);
  const [totalSteps, setTotalSteps] = useState(0);
  const [steps, setSteps] = useState([]);
  const [movements, setMovements] = useState([]);
  const [algorithmArray, setAlgorithmArray] = useState([]);
  const [sortedArray, setSortedArray] = useState([]);

  // Create the value object
  const value = {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    steps,
    setSteps,
    movements,
    setMovements,
    algorithmArray,
    setAlgorithmArray
  };

  return (
    <AlgorithmContext.Provider value={value}>
      {children}
    </AlgorithmContext.Provider>
  );
};

// Create a custom hook to use the context
export const useAlgorithm = () => {
  const context = useContext(AlgorithmContext);
  if (context === undefined) {
    throw new Error('useAlgorithm must be used within an AlgorithmProvider');
  }
  return context;
};
