import React, { useMemo } from 'react';
import { RadixBar } from './index';
import * as THREE from 'three';

/**
 * Reusable component for rendering sorting algorithm bars using RadixSort-style bars
 * 
 * @param {Object} props - Component props
 * @param {Array} props.arrayData - Array of values to visualize
 * @param {Object} props.colors - Object containing color definitions
 * @param {number} props.maxBarHeight - Maximum height for bars
 * @param {number} props.barWidth - Width of each bar
 * @param {number} props.barSpacing - Spacing between bars
 * @param {Array} props.comparing - Indices of bars being compared
 * @param {Array} props.swapping - Indices of bars being swapped
 * @param {Array} props.sorted - Indices of sorted bars
 * @param {Object} props.swapAnimation - Animation state for swapping
 * @param {boolean} props.swapAnimation.active - Whether swap animation is active
 * @param {Array} props.swapAnimation.indices - Indices of bars being swapped
 * @param {number} props.swapAnimation.progress - Animation progress (0-1)
 * @param {boolean} props.showValues - Whether to show values on bars
 * @param {boolean} props.showIndices - Whether to show indices on bars
 * @param {Function} props.getBarColor - Function to determine bar color (optional)
 * @returns {JSX.Element} - The rendered bars
 */
const RadixStyleSortingBars = ({
  arrayData = [],
  colors,
  maxBarHeight = 3.8,
  barWidth = 0.6,
  barSpacing = 0.3,
  comparing = [],
  swapping = [],
  sorted = [],
  swapAnimation = { active: false, indices: [], progress: 0 },
  showValues = true,
  showIndices = true,
  getBarColor,
}) => {
  // Calculate adaptive width and spacing based on array size
  const adaptiveWidth = useMemo(() => {
    if (arrayData.length > 25) {
      return barWidth * 0.5;
    } else if (arrayData.length > 15) {
      return barWidth * 0.7;
    }
    return barWidth;
  }, [arrayData.length, barWidth]);

  // Calculate adaptive spacing based on array size
  const adaptiveSpacing = useMemo(() => {
    if (arrayData.length > 25) {
      return barSpacing * 0.5;
    } else if (arrayData.length > 15) {
      return barSpacing * 0.7;
    }
    return barSpacing;
  }, [arrayData.length, barSpacing]);

  // Calculate total width of all bars
  const totalWidth = useMemo(() => {
    return (arrayData.length * (adaptiveWidth + adaptiveSpacing)) - adaptiveSpacing;
  }, [arrayData.length, adaptiveWidth, adaptiveSpacing]);

  // Calculate starting X position to center the array
  const startX = useMemo(() => {
    return -totalWidth / 2 + adaptiveWidth / 2;
  }, [totalWidth, adaptiveWidth]);

  // Calculate the maximum value for normalization
  const maxValue = useMemo(() => {
    return arrayData.length > 0 ? Math.max(...arrayData) : 1;
  }, [arrayData]);

  // Default function to determine bar color if not provided
  const defaultGetBarColor = (index) => {
    if (comparing.includes(index)) {
      return colors.comparing;
    } else if (swapping.includes(index)) {
      return colors.swapping;
    } else if (sorted.includes(index)) {
      return colors.sorted;
    }
    return colors.bar;
  };

  // Use provided getBarColor function or default
  const getBarColorFn = getBarColor || defaultGetBarColor;

  return (
    <>
      {arrayData.map((value, index) => {
        // Calculate bar properties
        const normalizedHeight = (value / maxValue) * maxBarHeight;

        // Calculate position with animation using adaptive width and spacing
        let x = startX + index * (adaptiveWidth + adaptiveSpacing);
        let y = 0;
        let z = 0;

        // Apply animation for bars being swapped
        if (swapAnimation.active && swapAnimation.indices.includes(index)) {
          const [i, j] = swapAnimation.indices;
          const { progress } = swapAnimation;

          // Determine if this is a forward or reverse animation
          if (index === i) {
            // First bar animation
            const targetX = startX + j * (adaptiveWidth + adaptiveSpacing);
            const distance = targetX - x;
            const radius = Math.abs(distance) / 2;
            const centerX = x + distance / 2;

            // Calculate position along circular path
            const angle = progress * Math.PI;
            x = centerX + Math.cos(Math.PI - angle) * radius;
            y = Math.sin(Math.PI - angle) * radius * 0.6; // Scale height for better visibility

            // Add slight z-axis movement for 3D effect
            z = Math.sin(angle) * 1.0;
          } else if (index === j) {
            // Second bar animation
            const targetX = startX + i * (adaptiveWidth + adaptiveSpacing);
            const distance = targetX - x;
            const radius = Math.abs(distance) / 2;
            const centerX = x + distance / 2;

            // Calculate position along circular path
            const angle = progress * Math.PI;
            x = centerX + Math.cos(angle) * radius;
            y = Math.sin(angle) * radius * 0.6; // Scale height for better visibility

            // Add slight z-axis movement for 3D effect (opposite direction)
            z = -Math.sin(angle) * 1.0;
          }
        }

        // Determine bar color
        const barColor = getBarColorFn(index);

        // Determine if we should show an arrow for this bar
        const showArrow = comparing.includes(index) || swapping.includes(index);

        return (
          <RadixBar
            key={`bar-${index}`}
            position={[x, y, z]}
            height={normalizedHeight}
            width={adaptiveWidth}
            color={barColor}
            value={value}
            index={index}
            showValue={showValues && arrayData.length <= 15}
            showIndex={showIndices && arrayData.length <= 25}
            showArrow={showArrow}
          />
        );
      })}
    </>
  );
};

export default RadixStyleSortingBars;
