// TimSortVisualization.js
// This component provides the visualization for the Tim Sort algorithm.

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateTimSortSteps } from './TimSortAlgorithm';
import * as THREE from 'three';

// Import reusable visualization components
import { Bar, ColorLegend, StepBoard, GroundPlane, Arrow } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.5;
const BAR_SPACING = 0.5;
const MAX_BAR_HEIGHT = 3.8;
const CAMERA_POSITION = [0, 5, 8];
const CAMERA_LOOKAT = [0, 0, -2];

// Main visualization component
const TimSortVisualization = ({ params = {} }) => {
    const theme = useTheme();
    const { camera } = useThree();
    const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
    const { speed } = useSpeed();

    // Refs for animation control
    const speedRef = useRef(speed);
    const stepsRef = useRef([]);
    const initialArrayRef = useRef([]);
    const lastAppliedStepRef = useRef(-1);
    const animatingRef = useRef(false);
    const timeoutIdRef = useRef(null);
    const stateRef = useRef(state);
    const currentStepRef = useRef(step);

    // State for array data
    const [arrayData, setArrayData] = useState([]);

    // State for visualization
    const [currentIndex, setCurrentIndex] = useState(-1);
    const [currentValue, setCurrentValue] = useState(null);
    const [compareIndex, setCompareIndex] = useState(-1);
    const [insertIndex, setInsertIndex] = useState(-1);
    const [runStart, setRunStart] = useState(-1);
    const [runEnd, setRunEnd] = useState(-1);
    const [mergeLeft, setMergeLeft] = useState(-1);
    const [mergeMid, setMergeMid] = useState(-1);
    const [mergeRight, setMergeRight] = useState(-1);
    const [leftArray, setLeftArray] = useState([]);
    const [rightArray, setRightArray] = useState([]);
    const [leftIndex, setLeftIndex] = useState(-1);
    const [rightIndex, setRightIndex] = useState(-1);
    const [mergeIndex, setMergeIndex] = useState(-1);
    const [runSize, setRunSize] = useState(0);
    const [mergeSize, setMergeSize] = useState(0);
    const [sorted, setSorted] = useState([]);
    
    // Animation state
    const [moveAnimation, setMoveAnimation] = useState({
        active: false,
        fromIndex: -1,
        toIndex: -1,
        progress: 0,
        startTime: 0,
        value: 0
    });

    // Colors based on theme
    const colors = useMemo(() => {
        const isDark = theme.palette.mode === 'dark';
        return {
            bar: isDark ? '#64b5f6' : '#2196f3',      // Default bar color
            current: isDark ? '#ce93d8' : '#9c27b0',  // Current element being considered
            compare: isDark ? '#ffb74d' : '#ff9800',  // Element being compared
            sorted: isDark ? '#81c784' : '#4caf50',   // Sorted element
            run: isDark ? '#4fc3f7' : '#03a9f4',      // Run color
            merge: isDark ? '#ff8a65' : '#ff5722',    // Merge color
            left: isDark ? '#9fa8da' : '#3f51b5',     // Left array color
            right: isDark ? '#f48fb1' : '#e91e63',    // Right array color
            base: isDark ? '#1a1a1a' : '#f5f5f5',     // Base color
            ground: isDark ? '#121212' : '#eeeeee',   // Ground color
        };
    }, [theme.palette.mode]);

    // Update refs when state changes
    useEffect(() => {
        stateRef.current = state;
    }, [state]);

    useEffect(() => {
        currentStepRef.current = step;
    }, [step]);

    useEffect(() => {
        speedRef.current = speed;
    }, [speed]);

    // Initialize array when params change
    useEffect(() => {
        // Clear any existing animation timeouts
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state
        animatingRef.current = false;
        setMoveAnimation({
            active: false,
            fromIndex: -1,
            toIndex: -1,
            progress: 0,
            startTime: 0,
            value: 0
        });

        // Extract parameters with safety checks
        const { arraySize = 10, randomize = true, customArray = [] } = params;

        // Generate array data
        let newArray = [];

        // Use custom array if provided
        if (customArray && customArray.length > 0) {
            newArray = [...customArray];
        } else if (randomize) {
            // Generate random array
            newArray = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 99) + 1
            );
        } else {
            // Generate reverse sorted array
            newArray = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        // Store the initial array
        initialArrayRef.current = [...newArray];
        setArrayData(newArray);

        // Generate steps
        const { steps } = generateTimSortSteps(newArray);
        stepsRef.current = steps;
        setSteps(steps);
        setTotalSteps(steps.length);
        setAlgorithmSteps(steps.map(step => step.movement));

        // Reset visualization state
        setCurrentIndex(-1);
        setCurrentValue(null);
        setCompareIndex(-1);
        setInsertIndex(-1);
        setRunStart(-1);
        setRunEnd(-1);
        setMergeLeft(-1);
        setMergeMid(-1);
        setMergeRight(-1);
        setLeftArray([]);
        setRightArray([]);
        setLeftIndex(-1);
        setRightIndex(-1);
        setMergeIndex(-1);
        setRunSize(0);
        setMergeSize(0);
        setSorted([]);

        // Reset step if needed
        setStep(0);
        lastAppliedStepRef.current = -1;

        // Position camera
        if (camera) {
            camera.position.set(...CAMERA_POSITION);
            camera.lookAt(...CAMERA_LOOKAT);
        }

        // Force a small delay to ensure everything is reset
        setTimeout(() => {
            // Apply the initial step
            if (steps.length > 0) {
                applyStep(0);
            }
        }, 50);
    }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

    // Apply a single step of the Tim Sort algorithm
    const applyStep = (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
            return;
        }

        const currentStep = stepsRef.current[stepIndex];
        lastAppliedStepRef.current = stepIndex;

        // Update array data if needed
        if (currentStep.array) {
            setArrayData(currentStep.array);
        }

        // Update visualization state based on step type
        switch (currentStep.type) {
            case 'init':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                setRunStart(-1);
                setRunEnd(-1);
                setMergeLeft(-1);
                setMergeMid(-1);
                setMergeRight(-1);
                setLeftArray([]);
                setRightArray([]);
                setLeftIndex(-1);
                setRightIndex(-1);
                setMergeIndex(-1);
                setRunSize(0);
                setMergeSize(0);
                setSorted([]);
                break;

            case 'setRunSize':
                setRunSize(currentStep.runSize);
                break;

            case 'startRun':
                setRunStart(currentStep.runStart);
                setRunEnd(currentStep.runEnd);
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'insertionConsider':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'insertionStartCompare':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(currentStep.compareIndex);
                setInsertIndex(-1);
                break;

            case 'insertionCompare':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(currentStep.compareIndex);
                setInsertIndex(-1);
                break;

            case 'insertionShift':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(currentStep.shiftFromIndex);
                setInsertIndex(currentStep.shiftToIndex);

                // Start animation to move item
                if (!animatingRef.current) {
                    animatingRef.current = true;
                    setMoveAnimation({
                        active: true,
                        fromIndex: currentStep.shiftFromIndex,
                        toIndex: currentStep.shiftToIndex,
                        progress: 0,
                        startTime: performance.now(),
                        value: currentStep.shiftValue
                    });
                }
                break;

            case 'insertionInsert':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndex(-1);
                setInsertIndex(currentStep.insertIndex);
                break;

            case 'runSorted':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'allRunsSorted':
                setRunStart(-1);
                setRunEnd(-1);
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                break;

            case 'setMergeSize':
                setMergeSize(currentStep.mergeSize);
                break;

            case 'startMerge':
                setMergeLeft(currentStep.left);
                setMergeMid(currentStep.mid);
                setMergeRight(currentStep.right);
                setLeftArray([]);
                setRightArray([]);
                setLeftIndex(-1);
                setRightIndex(-1);
                setMergeIndex(-1);
                break;

            case 'mergeSubarrays':
                setLeftArray(currentStep.leftArray);
                setRightArray(currentStep.rightArray);
                break;

            case 'mergeCompare':
                setLeftIndex(currentStep.leftIndex);
                setRightIndex(currentStep.rightIndex);
                setMergeIndex(currentStep.mergeIndex);
                break;

            case 'mergeSelectLeft':
            case 'mergeSelectRight':
                // These steps are handled by the mergeCompare step
                break;

            case 'mergePlacement':
                // Start animation to move item
                if (!animatingRef.current) {
                    animatingRef.current = true;
                    setMoveAnimation({
                        active: true,
                        fromIndex: -1, // Special case for merge
                        toIndex: currentStep.mergeIndex,
                        progress: 0,
                        startTime: performance.now(),
                        value: currentStep.mergeValue
                    });
                }
                break;

            case 'mergeRemainingLeft':
                setLeftIndex(currentStep.leftIndex);
                setRightIndex(-1);
                setMergeIndex(currentStep.mergeIndex);
                break;

            case 'mergeRemainingRight':
                setLeftIndex(-1);
                setRightIndex(currentStep.rightIndex);
                setMergeIndex(currentStep.mergeIndex);
                break;

            case 'mergeComplete':
                setMergeLeft(-1);
                setMergeMid(-1);
                setMergeRight(-1);
                setLeftArray([]);
                setRightArray([]);
                setLeftIndex(-1);
                setRightIndex(-1);
                setMergeIndex(-1);
                break;

            case 'complete':
                setCurrentIndex(-1);
                setCurrentValue(null);
                setCompareIndex(-1);
                setInsertIndex(-1);
                setRunStart(-1);
                setRunEnd(-1);
                setMergeLeft(-1);
                setMergeMid(-1);
                setMergeRight(-1);
                setLeftArray([]);
                setRightArray([]);
                setLeftIndex(-1);
                setRightIndex(-1);
                setMergeIndex(-1);
                setRunSize(0);
                setMergeSize(0);
                setSorted(currentStep.sorted || []);
                break;

            default:
                break;
        }
    };

    // Handle step changes
    useEffect(() => {
        // Clear any existing timeout
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state if step changes manually
        if (animatingRef.current && lastAppliedStepRef.current !== step - 1) {
            animatingRef.current = false;
            setMoveAnimation(prev => ({
                ...prev,
                active: false
            }));
        }

        // Don't apply steps if we're still animating and the step is sequential
        if (animatingRef.current && lastAppliedStepRef.current === step - 1) {
            return;
        }

        // Apply the current step
        applyStep(step);
    }, [step]);

    // Handle automatic stepping when state is 'running'
    useEffect(() => {
        // Reset animation state when state changes to idle
        if (state === 'idle') {
            // Clear any existing timeouts
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }

            // Reset animation state
            animatingRef.current = false;
            setMoveAnimation({
                active: false,
                fromIndex: -1,
                toIndex: -1,
                progress: 0,
                startTime: 0,
                value: 0
            });

            // Apply the initial step
            if (stepsRef.current && stepsRef.current.length > 0) {
                applyStep(0);
            }

            return;
        }

        // Only proceed if state is 'running'
        if (state !== 'running') {
            return;
        }

        const steps = stepsRef.current || [];

        // Stop if we've reached the end
        if (step >= steps.length) {
            setState('completed');
            return;
        }

        // Don't schedule next step if we're animating
        if (animatingRef.current) {
            return;
        }

        // Schedule the next step with a delay
        const delay = Math.max(200, 800 - (speedRef.current * 80));
        const timeoutId = setTimeout(() => {
            // Only increment if still in running state
            if (stateRef.current === 'running' && !animatingRef.current) {
                setStep(prevStep => prevStep + 1);
            }
        }, delay);

        // Store the timeout ID for cleanup
        timeoutIdRef.current = timeoutId;

        // Cleanup function
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }
        };
    }, [state, step, setStep, setState]);

    // Animation frame for move animation
    useFrame(() => {
        if (moveAnimation.active) {
            // Use performance.now() for more accurate timing
            const now = performance.now();
            const elapsed = now - moveAnimation.startTime;
            
            // Adjust duration based on speed - slower for better visibility
            const baseDuration = 1000; // Shorter base duration for faster animation
            const duration = Math.max(500, baseDuration - (speedRef.current * 100));
            
            const progress = Math.min(elapsed / duration, 1);

            // Only update if there's a meaningful change to reduce unnecessary renders
            if (Math.abs(progress - moveAnimation.progress) > 0.01) {
                setMoveAnimation(prev => ({
                    ...prev,
                    progress
                }));
            }

            // If animation is complete, mark it as inactive
            if (progress >= 1) {
                // Small delay to ensure the animation completes visually
                setTimeout(() => {
                    setMoveAnimation(prev => ({
                        ...prev,
                        active: false
                    }));
                    animatingRef.current = false;

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50); // Shorter delay before next step
                    }
                }, 50); // Shorter delay for faster transitions
            }
        }
    });

    // Calculate maximum value for scaling
    const maxValue = useMemo(() => {
        if (!arrayData || arrayData.length === 0) return 1;
        return Math.max(...arrayData);
    }, [arrayData]);

    // Calculate adaptive width and spacing based on array size
    const adaptiveWidth = Math.max(0.2, BAR_WIDTH - (arrayData.length * 0.01));
    const adaptiveSpacing = Math.max(0.1, BAR_SPACING - (arrayData.length * 0.01));

    // Calculate total width of all bars
    const totalWidth = (adaptiveWidth + adaptiveSpacing) * arrayData.length;

    // Calculate starting X position to center the array
    const startX = -(totalWidth / 2) + (adaptiveWidth / 2);

    // Get the current step data for the step board
    const currentStepData = useMemo(() => {
        if (step >= 0 && step < stepsRef.current?.length) {
            return stepsRef.current[step];
        }
        return null;
    }, [step]);

    // Helper function to get X position for an index
    const getXPosition = (index) => {
        return startX + index * (adaptiveWidth + adaptiveSpacing);
    };

    // Render the visualization
    return (
        <group position={[0, -2, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <directionalLight position={[-10, 10, -10]} intensity={0.4} />
            <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

            {/* Ground plane */}
            <GroundPlane 
                position={[0, -0.3, 0]} 
                width={100} 
                depth={100} 
                color={colors.ground} 
                receiveShadow={true} 
            />

            {/* Step Board */}
            <StepBoard 
                position={[0, 6, 0.5]} 
                description={currentStepData?.movement || ''} 
                stepData={currentStepData || {}} 
                currentStep={step + 1} 
                totalSteps={stepsRef.current.length} 
                showBackground={false}
            />

            {/* Color Legend */}
            <ColorLegend 
                position={[0, -3, 0.5]} 
                colors={colors} 
                items={[
                    { label: 'Default', color: colors.bar },
                    { label: 'Current', color: colors.current },
                    { label: 'Compare', color: colors.compare },
                    { label: 'Run', color: colors.run },
                    { label: 'Merge', color: colors.merge },
                    { label: 'Sorted', color: colors.sorted }
                ]} 
            />

            {/* Run Visualization */}
            {runStart >= 0 && runEnd >= 0 && (
                <group>
                    {/* Run indicator */}
                    <mesh 
                        position={[
                            (getXPosition(runStart) + getXPosition(runEnd)) / 2,
                            -0.5,
                            0
                        ]}
                        rotation={[Math.PI / 2, 0, 0]}
                    >
                        <planeGeometry 
                            args={[
                                (runEnd - runStart + 1) * (adaptiveWidth + adaptiveSpacing),
                                adaptiveWidth * 2
                            ]} 
                        />
                        <meshBasicMaterial 
                            color={colors.run} 
                            transparent 
                            opacity={0.3} 
                        />
                    </mesh>
                    
                    {/* Run label */}
                    <Arrow 
                        start={[getXPosition(runStart), 4, 0]} 
                        end={[getXPosition(runEnd), 4, 0]} 
                        color={colors.run}
                        thickness={0.05}
                        headSize={0.2}
                        label={`Run: ${runStart} to ${runEnd}`}
                        showLabel={true}
                    />
                </group>
            )}

            {/* Merge Visualization */}
            {mergeLeft >= 0 && mergeRight >= 0 && (
                <group>
                    {/* Merge indicator */}
                    <mesh 
                        position={[
                            (getXPosition(mergeLeft) + getXPosition(mergeRight)) / 2,
                            -0.6,
                            0
                        ]}
                        rotation={[Math.PI / 2, 0, 0]}
                    >
                        <planeGeometry 
                            args={[
                                (mergeRight - mergeLeft + 1) * (adaptiveWidth + adaptiveSpacing),
                                adaptiveWidth * 2
                            ]} 
                        />
                        <meshBasicMaterial 
                            color={colors.merge} 
                            transparent 
                            opacity={0.3} 
                        />
                    </mesh>
                    
                    {/* Merge label */}
                    <Arrow 
                        start={[getXPosition(mergeLeft), 4.5, 0]} 
                        end={[getXPosition(mergeRight), 4.5, 0]} 
                        color={colors.merge}
                        thickness={0.05}
                        headSize={0.2}
                        label={`Merge: ${mergeLeft} to ${mergeRight}`}
                        showLabel={true}
                    />
                    
                    {/* Mid point indicator */}
                    {mergeMid >= 0 && (
                        <mesh position={[getXPosition(mergeMid), -0.6, 0]}>
                            <sphereGeometry args={[0.1]} />
                            <meshBasicMaterial color={colors.merge} />
                        </mesh>
                    )}
                </group>
            )}

            {/* Array Bars */}
            {arrayData.map((value, index) => {
                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

                // Calculate position with animation
                let x = getXPosition(index);
                let y = 0;
                let z = 0;

                // Apply animation for items being moved
                if (moveAnimation.active) {
                    if (moveAnimation.fromIndex === index) {
                        // Hide the source bar during animation
                        return null;
                    }
                    
                    if (moveAnimation.toIndex === index && moveAnimation.fromIndex !== -1) {
                        // This is where the animated bar will end up
                        // We'll render the animated bar separately
                    }
                }

                // Determine bar color based on current state
                let barColor = colors.bar; // Default color
                let showArrow = false; // Flag to show arrow indicator

                // Use a priority system for coloring and arrow indicators
                if (sorted.includes(index)) {
                    barColor = colors.sorted;
                } else if (index === currentIndex) {
                    barColor = colors.current;
                    showArrow = true;
                } else if (index === compareIndex) {
                    barColor = colors.compare;
                    showArrow = true;
                } else if (index === insertIndex) {
                    barColor = colors.current;
                    showArrow = true;
                } else if (index === mergeIndex) {
                    barColor = colors.merge;
                    showArrow = true;
                } else if (runStart <= index && index <= runEnd) {
                    barColor = colors.run;
                } else if (mergeLeft <= index && index <= mergeRight) {
                    if (index <= mergeMid) {
                        barColor = colors.left;
                    } else {
                        barColor = colors.right;
                    }
                }

                // Return the bar component
                return (
                    <Bar
                        key={`bar-${index}`}
                        position={[x, y, z]}
                        height={normalizedHeight}
                        width={adaptiveWidth}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={arrayData.length <= 20}
                        showIndex={true}
                        showArrow={showArrow}
                        depth={adaptiveWidth}
                    />
                );
            })}

            {/* Animated Bar */}
            {moveAnimation.active && (
                (() => {
                    const fromIndex = moveAnimation.fromIndex;
                    const toIndex = moveAnimation.toIndex;
                    const value = moveAnimation.value;
                    
                    const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;
                    
                    // Calculate source and target positions
                    let sourceX, targetX;
                    
                    if (fromIndex === -1) {
                        // Special case for merge - animate from above
                        sourceX = getXPosition(toIndex);
                        targetX = getXPosition(toIndex);
                    } else {
                        sourceX = getXPosition(fromIndex);
                        targetX = getXPosition(toIndex);
                    }
                    
                    // Use a smoother easing function for more natural movement
                    const { progress } = moveAnimation;
                    const easedProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);
                    
                    // Calculate current position
                    let x, y;
                    
                    if (fromIndex === -1) {
                        // Special case for merge - animate from above
                        x = sourceX;
                        y = 3 * (1 - easedProgress);
                    } else {
                        // Normal case - animate horizontally with arc
                        x = sourceX + (targetX - sourceX) * easedProgress;
                        
                        // Add a slight arc for better visual effect
                        const arcHeight = 0.5;
                        const midPoint = Math.sin(easedProgress * Math.PI);
                        y = arcHeight * midPoint;
                    }
                    
                    return (
                        <Bar
                            key="animated-bar"
                            position={[x, y, 0]}
                            height={normalizedHeight}
                            width={adaptiveWidth}
                            color={colors.current}
                            value={value}
                            index={toIndex}
                            showValue={arrayData.length <= 20}
                            showIndex={false}
                            showArrow={true}
                            depth={adaptiveWidth}
                        />
                    );
                })()
            )}
        </group>
    );
};

export default TimSortVisualization;
