// HeapSortController.js - Controller-driven HeapSort following modern pattern
// Generates visualizationData for each step to drive the visualization

import React, { useEffect, useState, useMemo } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// Import HeapSort algorithm
import { generateDetailedSteps } from './HeapSortDetailedSteps';

const HeapSortController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => { } } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    steps,
    setSteps
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Import configuration
  const CONFIG = require('./HeapSortConfig').default;

  // Extract parameters with safety checks using config defaults
  const arraySize = params?.arraySize || CONFIG.performance.arraySize.default;
  const randomize = params?.randomize !== undefined ? params.randomize : true;
  const customArray = params?.customArray || [];
  const useCustomArray = params?.useCustomArray || false;

  // State for custom array input
  const [customArrayInput, setCustomArrayInput] = useState('');
  const [customArrayError, setCustomArrayError] = useState('');

  // Generate the input array based on parameters using config
  const inputArray = useMemo(() => {
    if (useCustomArray && customArray.length > 0) {
      return [...customArray];
    } else if (randomize) {
      // Generate random array using config values
      const minValue = CONFIG.algorithm.arrayGeneration.randomValues.min;
      const maxValue = CONFIG.algorithm.arrayGeneration.randomValues.max;
      const range = maxValue - minValue + 1;

      return Array.from({ length: arraySize }, () =>
        Math.floor(Math.random() * range) + minValue
      );
    } else {
      // Generate sequential array using config
      const startValue = CONFIG.algorithm.arrayGeneration.sequentialArray.startValue;
      const increment = CONFIG.algorithm.arrayGeneration.sequentialArray.increment;
      return Array.from({ length: arraySize }, (_, i) => startValue + (i * increment));
    }
  }, [arraySize, randomize, customArray, useCustomArray]);

  // Generate steps with visualization data when input array changes
  useEffect(() => {
    if (inputArray.length === 0) return;

    console.log('HeapSortController - Generating steps for array:', inputArray);

    try {
      // Generate detailed algorithm steps
      const algorithmSteps = generateDetailedSteps(inputArray);

      console.log('HeapSortController - Generated steps:', algorithmSteps);

      setSteps(algorithmSteps);
      setTotalSteps(algorithmSteps.length);

      setStep(0); // Reset to initial step
      setState('idle');
    } catch (error) {
      console.error('HeapSortController - Error generating steps:', error);
      setSteps([]);
      setTotalSteps(0);
    }
  }, [inputArray, setSteps, setTotalSteps, setStep, setState]);

  // Set state to completed when step reaches totalSteps
  useEffect(() => {
    // Only update if we have valid steps and we're not in idle state
    if (totalSteps > 0 && state !== 'idle') {
      // If we've reached the last step, mark as completed
      if (step >= totalSteps && state !== 'completed') {
        console.log('HeapSortController - Reached final step, setting state to completed');
        setState('completed');
      }
      // If we were in completed state but stepped back, go to paused
      else if (state === 'completed' && step < totalSteps) {
        console.log('HeapSortController - Stepped back from completion, setting state to paused');
        setState('paused');
      }
    }
  }, [step, totalSteps, setState, state]);

  // Handle array size change using config limits
  const handleArraySizeChange = (value) => {
    if (value >= CONFIG.performance.arraySize.min && value <= CONFIG.performance.arraySize.max) {
      onParamChange({ ...params, arraySize: value });
      setState('idle');
      setStep(0);
    }
  };

  // Handle randomize toggle
  const handleRandomizeChange = (value) => {
    onParamChange({ ...params, randomize: value });
    setState('idle');
    setStep(0);
  };

  // Handle custom array toggle
  const handleCustomArrayChange = (value) => {
    onParamChange({ ...params, useCustomArray: value });
    setState('idle');
    setStep(0);
  };

  // Handle custom array input change
  const handleCustomArrayInputChange = (value) => {
    setCustomArrayInput(value);
    setCustomArrayError('');
  };

  // Apply custom array
  const applyCustomArray = () => {
    try {
      const parsedArray = customArrayInput
        .split(',')
        .map(item => item.trim())
        .filter(item => item !== '')
        .map(item => {
          const num = Number(item);
          if (isNaN(num)) throw new Error('Invalid number');
          return num;
        });

      if (parsedArray.length < CONFIG.performance.arraySize.min) {
        setCustomArrayError(`Please enter at least ${CONFIG.performance.arraySize.min} numbers`);
        return false;
      } else if (parsedArray.length > CONFIG.performance.arraySize.max) {
        setCustomArrayError(`Maximum ${CONFIG.performance.arraySize.max} numbers allowed`);
        return false;
      } else {
        setCustomArrayError('');
        onParamChange({
          ...params,
          customArray: parsedArray,
          arraySize: parsedArray.length,
          useCustomArray: true
        });
        setState('idle');
        setStep(0);
        return true;
      }
    } catch (error) {
      setCustomArrayError('Please enter valid numbers separated by commas');
      return false;
    }
  };

  // Update custom array input when customArray changes
  useEffect(() => {
    if (customArray && customArray.length > 0) {
      setCustomArrayInput(customArray.join(', '));
    }
  }, [customArray]);

  return (
    <Box>
      {/* Information Section */}
      <InformationSection title={"Heap Sort"}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Heap Sort:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Heap Sort is a comparison-based sorting algorithm that uses a binary heap data structure. It first builds a max heap, then repeatedly extracts the maximum element. Random arrays contain values from {CONFIG.algorithm.arrayGeneration.randomValues.min} to {CONFIG.algorithm.arrayGeneration.randomValues.max}.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Best Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Average Case: O(n log n)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            - Worst Case: O(n log n) - consistent performance
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Space Complexity:
          </Typography>
          <Typography variant="body2">
            O(1) - Heap Sort is an in-place sorting algorithm
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'arraySize',
            type: 'slider',
            label: 'Array Size',
            min: CONFIG.performance.arraySize.min,
            max: CONFIG.performance.arraySize.max,
            step: 1,
            defaultValue: arraySize,
            icon: ViewArrayIcon,
            disabled: useCustomArray
          },
          {
            name: 'randomize',
            type: 'switch',
            label: 'Randomize Array',
            defaultValue: randomize,
            icon: ShuffleIcon,
            disabled: useCustomArray
          },
          {
            name: 'useCustomArray',
            type: 'switch',
            label: 'Use Custom Array',
            defaultValue: useCustomArray,
            icon: FormatListNumberedIcon
          },
          {
            name: 'customArrayInput',
            type: 'customArray',
            label: 'Custom Array',
            showOnlyWhen: 'useCustomArray',
            error: customArrayError,
            helperText: `Enter comma-separated numbers (e.g., 5, 3, 8, 1). Values can range from ${CONFIG.algorithm.arrayGeneration.randomValues.min} to ${CONFIG.algorithm.arrayGeneration.randomValues.max}. Maximum ${CONFIG.performance.arraySize.max} numbers allowed.`,
            placeholder: `e.g., 5, 3, 8, 4, 2 (min ${CONFIG.performance.arraySize.min}, max ${CONFIG.performance.arraySize.max} numbers)`,
            onApply: applyCustomArray,
            icon: FormatListNumberedIcon
          }
        ]}
        values={{
          arraySize,
          randomize,
          useCustomArray,
          customArrayInput
        }}
        onChange={(newValues) => {
          if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize && !useCustomArray) {
            handleArraySizeChange(newValues.arraySize);
          }
          if (newValues.randomize !== undefined && newValues.randomize !== randomize && !useCustomArray) {
            handleRandomizeChange(newValues.randomize);
          }
          if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
            handleCustomArrayChange(newValues.useCustomArray);
          }
          if (newValues.customArrayInput !== undefined) {
            handleCustomArrayInputChange(newValues.customArrayInput);
          }
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setStep(0);
          setTimeout(() => {
            setState('idle');
          }, 50);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            setStep(step + 1);
            if (step + 1 >= totalSteps) {
              setState('completed');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            if (state === 'completed') {
              setState('paused');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={step > 0 ? (steps || []).slice(1).map(step => ({
          description: step.statement || ''
        })) : []}
        currentStep={step > 0 ? step - 1 : 0}
        defaultExpanded
        renderStep={(_, index) => {
          const currentStep = steps && steps[index + 1]; // Add 1 to skip initial step
          const isCurrentStep = index === step - 1;

          if (!currentStep) return null;

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
                mb: 0.75,
                pb: 0.75,
                borderBottom: index < steps.length - 2 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  bgcolor: 'action.hover',
                  borderRadius: '4px',
                }
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '24px',
                  height: '24px',
                  borderRadius: '12px',
                  bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                  mr: 1.5,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                  boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {index + 1}
              </Box>
              {currentStep.statement || 'Processing...'}
            </Typography>
          );
        }}
        emptyMessage="No steps yet. Start the algorithm to see the sequence."
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Heap Sort Algorithm"
        defaultExpanded
        currentStep={step >= 0 && steps && steps.length > 0 ?
          // Map the detailed step types to corresponding line numbers based on context
          steps[step]?.type === 'initial' ? 0 :
            steps[step]?.type === 'info' ? (
              // Check the statement to determine which phase we're in
              steps[step]?.statement?.includes('Phase 1') || steps[step]?.statement?.includes('Build max heap') ? 1 :
              steps[step]?.statement?.includes('Heapifying subtree') ? 3 :
              steps[step]?.statement?.includes('Max heap built') ? 7 :
              steps[step]?.statement?.includes('Phase 2') || steps[step]?.statement?.includes('Extract elements') ? 8 :
              steps[step]?.statement?.includes('Heapifying reduced heap') ? 11 : 1
            ) :
              steps[step]?.type === 'compare' ? 4 :
                steps[step]?.type === 'swap' ? (
                  // Check if it's phase 1 (heapify) or phase 2 (extract) swap
                  steps[step]?.statement?.includes('root') || steps[step]?.statement?.includes('max element') ? 10 : 5
                ) :
                  steps[step]?.type === 'swapped' ? (
                    // Check if it's phase 1 or phase 2 swapped
                    steps[step]?.statement?.includes('correct sorted position') ? 10 : 6
                  ) :
                    steps[step]?.type === 'heapified' ? 7 :
                      steps[step]?.type === 'complete' ? 12 : 0
          : 0
        }
        algorithm={[
          { code: "function heapSort(arr):", lineNumber: 0, indent: 0 },
          { code: "# Phase 1: Build max heap from bottom up", lineNumber: 1, indent: 1 },
          { code: "for i = floor(n/2) - 1 down to 0:", lineNumber: 2, indent: 1 },
          { code: "heapify(arr, n, i)  # Heapify subtree rooted at i", lineNumber: 3, indent: 2 },
          { code: "# Compare parent with children to find largest", lineNumber: 4, indent: 2 },
          { code: "if largest != parent: swap(arr[parent], arr[largest])", lineNumber: 5, indent: 2 },
          { code: "# Element moved to correct heap position", lineNumber: 6, indent: 2 },
          { code: "# Subtree now satisfies max heap property", lineNumber: 7, indent: 2 },
          { code: "# Phase 2: Extract elements from heap one by one", lineNumber: 8, indent: 1 },
          { code: "for i = n-1 down to 1:", lineNumber: 9, indent: 1 },
          { code: "swap(arr[0], arr[i])  # Move max to sorted position", lineNumber: 10, indent: 2 },
          { code: "heapify(arr, i, 0)  # Restore heap property", lineNumber: 11, indent: 2 },
          { code: "# Array is completely sorted", lineNumber: 12, indent: 1 }
        ]}
      />
    </Box>
  );
};

export default HeapSortController;
