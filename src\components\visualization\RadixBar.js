import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable RadixSort-style bar component for algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the bar
 * @param {number} props.height - Height of the bar
 * @param {number} props.width - Width of the bar
 * @param {string} props.color - Color of the bar
 * @param {number} props.value - Value represented by the bar
 * @param {number} props.index - Index of the bar in the array
 * @param {boolean} props.showValue - Whether to show the value label
 * @param {boolean} props.showIndex - Whether to show the index label
 * @param {boolean} props.showArrow - Whether to show an arrow above the bar
 * @returns {JSX.Element} - The rendered bar component
 */
const RadixBar = ({
    position,
    height,
    width,
    color,
    value,
    index,
    showValue = true,
    showIndex = true,
    showArrow = false
}) => {
    const theme = useTheme();
    const barHeight = Math.max(0.1, height);

    // Calculate a slightly darker color for the bar edges
    const getEdgeColor = (baseColor) => {
        // Convert hex to RGB
        const r = parseInt(baseColor.slice(1, 3), 16);
        const g = parseInt(baseColor.slice(3, 5), 16);
        const b = parseInt(baseColor.slice(5, 7), 16);

        // Darken the color
        const darkenFactor = 0.7;
        const newR = Math.floor(r * darkenFactor);
        const newG = Math.floor(g * darkenFactor);
        const newB = Math.floor(b * darkenFactor);

        // Convert back to hex
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    };

    const edgeColor = getEdgeColor(color);

    return (
        <group position={position}>
            {/* Bar base */}
            <mesh position={[0, 0.05, 0]} castShadow receiveShadow>
                <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
                <meshStandardMaterial color={edgeColor} />
            </mesh>

            {/* Main bar */}
            <mesh position={[0, barHeight / 2 + 0.05, 0]} castShadow receiveShadow>
                <boxGeometry args={[width, barHeight, width]} />
                <meshStandardMaterial
                    color={color}
                    metalness={0.3}
                    roughness={0.7}
                    emissive={color}
                    emissiveIntensity={0.2}
                />
            </mesh>

            {/* Bar top */}
            <mesh position={[0, barHeight + 0.05, 0]} castShadow receiveShadow>
                <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
                <meshStandardMaterial color={edgeColor} />
            </mesh>

            {/* Value label */}
            {showValue && (
                <Html position={[0, barHeight + 0.3, 0]} center sprite occlude>
                    <div style={{
                        color: theme.palette.text.primary,
                        background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.6)' : 'rgba(255,255,255,0.8)',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '0.6rem',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        minWidth: '16px',
                        boxShadow: `0 1px 2px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)'}`,
                        border: `1px solid ${theme.palette.divider}`,
                        userSelect: 'none',
                        pointerEvents: 'none'
                    }}>
                        {value}
                    </div>
                </Html>
            )}

            {/* Index label */}
            {showIndex && (
                <Html position={[0, 0.1, 0.4]} center sprite occlude>
                    <div style={{
                        color: theme.palette.text.secondary,
                        background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.7)',
                        padding: '2px 6px',
                        borderRadius: '50%',
                        fontSize: '10px',
                        fontWeight: 'normal',
                        textAlign: 'center',
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: `0 1px 2px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.2)'}`,
                        userSelect: 'none',
                        pointerEvents: 'none'
                    }}>
                        {index}
                    </div>
                </Html>
            )}

            {/* Current element arrow */}
            {showArrow && (
                <Html position={[0, barHeight + 1.2, 0]} center>
                    <div style={{
                        color: theme.palette.secondary.main,
                        background: 'transparent',
                        fontSize: '1.5rem',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        textShadow: `0 0 8px ${theme.palette.secondary.main}`
                    }}>
                        ↓
                    </div>
                </Html>
            )}
        </group>
    );
};

export default RadixBar;
