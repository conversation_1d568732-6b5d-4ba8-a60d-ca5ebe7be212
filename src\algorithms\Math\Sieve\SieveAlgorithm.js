// SieveAlgorithm.js
// Implementation of the Sieve of Eratosthenes algorithm

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for the Sieve of Eratosthenes algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateSieveSteps = (params) => {
  console.log('generateSieveSteps called with params:', params);
  const { limit = 30 } = params;
  
  // Ensure limit is within a reasonable range
  const actualLimit = Math.min(Math.max(2, limit), 1000);
  
  const steps = [];
  const primes = [];
  
  // Create an array of boolean values, initially all set to true
  // isPrime[i] will be true if i is prime, false otherwise
  const isPrime = Array(actualLimit + 1).fill(true);
  
  // 0 and 1 are not prime
  isPrime[0] = isPrime[1] = false;
  
  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize Sieve of Eratosthenes for finding all primes up to ${actualLimit}.`,
    isPrime: [...isPrime],
    currentNumber: -1,
    currentMultiple: -1,
    primes: [],
    progressStep: 'init',
    pseudocodeLine: 1
  });
  
  // Main Sieve algorithm
  for (let i = 2; i * i <= actualLimit; i++) {
    // If i is prime (not marked as composite yet)
    if (isPrime[i]) {
      // Add step for identifying a prime number
      steps.push({
        type: 'prime',
        message: `${i} is prime. Mark all its multiples as non-prime.`,
        isPrime: [...isPrime],
        currentNumber: i,
        currentMultiple: -1,
        primes: [...primes],
        progressStep: 'process',
        pseudocodeLine: 3
      });
      
      // Mark all multiples of i as non-prime
      for (let j = i * i; j <= actualLimit; j += i) {
        isPrime[j] = false;
        
        // Add step for marking a multiple as non-prime
        steps.push({
          type: 'mark',
          message: `Mark ${j} as non-prime (multiple of ${i}).`,
          isPrime: [...isPrime],
          currentNumber: i,
          currentMultiple: j,
          primes: [...primes],
          progressStep: 'process',
          pseudocodeLine: 5
        });
      }
    } else {
      // Add step for skipping a non-prime number
      steps.push({
        type: 'skip',
        message: `${i} is already marked as non-prime. Skip it.`,
        isPrime: [...isPrime],
        currentNumber: i,
        currentMultiple: -1,
        primes: [...primes],
        progressStep: 'process',
        pseudocodeLine: 2
      });
    }
  }
  
  // Collect all prime numbers
  for (let i = 2; i <= actualLimit; i++) {
    if (isPrime[i]) {
      primes.push(i);
    }
  }
  
  // Add final step
  steps.push({
    type: 'complete',
    message: `Found all ${primes.length} prime numbers up to ${actualLimit}: [${primes.join(', ')}].`,
    isPrime: [...isPrime],
    currentNumber: -1,
    currentMultiple: -1,
    primes: [...primes],
    progressStep: 'complete',
    pseudocodeLine: 0
  });
  
  return { steps, primes };
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const SieveAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Pseudocode for Sieve of Eratosthenes
  const pseudocode = [
    { line: 'function sieveOfEratosthenes(limit):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Create a boolean array "isPrime[0..limit]" and initialize all entries as true', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    isPrime = new boolean[limit+1]', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '    for i = 0 to limit:', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '        isPrime[i] = true', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '', highlight: false },
    { line: '    // 0 and 1 are not prime', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    isPrime[0] = isPrime[1] = false', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '', highlight: false },
    { line: '    // Main Sieve algorithm', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    for i = 2 to sqrt(limit):', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '        // If i is prime (not marked as composite yet)', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '        if isPrime[i] is true:', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            // Mark all multiples of i as non-prime', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '            for j = i*i to limit step i:', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '                isPrime[j] = false', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '', highlight: false },
    { line: '    // Collect all prime numbers', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '    primes = []', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '    for i = 2 to limit:', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
    { line: '        if isPrime[i] is true:', highlight: currentAlgorithmStep?.pseudocodeLine === 18 },
    { line: '            primes.add(i)', highlight: currentAlgorithmStep?.pseudocodeLine === 19 },
    { line: '', highlight: false },
    { line: '    return primes', highlight: currentAlgorithmStep?.pseudocodeLine === 20 },
  ];

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default SieveAlgorithm;
