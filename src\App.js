import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
  Box,
  Typography,
  useTheme,
  AppBar,
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>,

} from "@mui/material";
import { ThemeProvider, useThemeContext } from "./theme/ThemeProvider"; // Keep your custom provider imports
import { SpeedProvider } from "./context/SpeedContext"; // Import the SpeedProvider
import { AlgorithmProvider, useAlgorithm } from "./context/AlgorithmContext"; // Import the AlgorithmProvider and hook
import AlgorithmSelector from "./components/AlgorithmSelector/AlgorithmSelector";
import AlgorithmVisualizer from "./components/AlgorithmVisualizer/AlgorithmVisualizer";
import AlgorithmController from "./components/AlgorithmController/AlgorithmController";
import DonationSection from "./components/DonationSection/DonationSection";
// Controls component has been removed and replaced with AlgorithmController
import Brightness4Icon from "@mui/icons-material/Brightness4";
import Brightness7Icon from "@mui/icons-material/Brightness7";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
// Import custom logo component
import ThemeAwareLogo from "./components/Logo/ThemeAwareLogo";

// Import custom components
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";



// Panel width storage key
const PANEL_WIDTH_KEY = 'algorithm_simulator_panel_width';

// Sidebar state storage key
const SIDEBAR_STATE_KEY = 'algorithm_simulator_sidebar_state';

function AppContent() {
  const [selectedAlgorithm, setSelectedAlgorithm] = React.useState("CountingSort");
  const [algorithmParams, setAlgorithmParams] = React.useState({});

  // Get the algorithm context to reset state when changing algorithms
  const { setState, setStep, setTotalSteps, setSteps, setMovements, setAlgorithmArray } = useAlgorithm();

  // Handle algorithm selection with complete state reset
  const handleAlgorithmChange = (algorithmId) => {
    // Reset all algorithm state
    setState('idle');
    setStep(0);
    setTotalSteps(0);
    setSteps([]);
    setMovements([]);
    setAlgorithmArray([]);
    setAlgorithmParams({});

    // Set the new algorithm
    setSelectedAlgorithm(algorithmId);
  };


  // Initialize panel width from localStorage
  const [panelWidth, setPanelWidth] = React.useState(() => {
    try {
      const savedWidth = localStorage.getItem(PANEL_WIDTH_KEY);
      if (savedWidth) {
        const parsedWidth = parseInt(savedWidth, 10);
        if (!isNaN(parsedWidth) && parsedWidth >= 300 && parsedWidth <= 500) {
          // console.log(`Loaded panel width from localStorage: ${parsedWidth}px`);
          return parsedWidth;
        }
      }
    } catch (error) {
      console.error('Error loading panel width from localStorage:', error);
    }
    return 400; // Default width
  });

  // Initialize sidebar state from localStorage
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(() => {
    try {
      const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
      if (savedState === '1') {
        // console.log('Loaded sidebar state from localStorage: collapsed');
        return true;
      }
    } catch (error) {
      console.error('Error loading sidebar state from localStorage:', error);
    }
    return false; // Default to expanded
  });

  // Save panel width to localStorage when it changes
  React.useEffect(() => {
    try {
      localStorage.setItem(PANEL_WIDTH_KEY, panelWidth.toString());
      // console.log(`Saved panel width to localStorage: ${panelWidth}px`);
    } catch (error) {
      console.error('Error saving panel width to localStorage:', error);
    }
  }, [panelWidth]);

  // Save sidebar state to localStorage when it changes
  React.useEffect(() => {
    try {
      localStorage.setItem(SIDEBAR_STATE_KEY, sidebarCollapsed ? '1' : '0');
      // console.log(`Saved sidebar state to localStorage: ${sidebarCollapsed ? 'collapsed' : 'expanded'}`);
    } catch (error) {
      console.error('Error saving sidebar state to localStorage:', error);
    }
  }, [sidebarCollapsed]);

  // Get the context methods/state
  const { themeMode, toggleTheme } = useThemeContext();
  // Get the actual theme object provided by the MuiThemeProvider inside your custom ThemeProvider
  const theme = useTheme(); // This theme object should now correctly be light or dark

  // NO MuiThemeProvider HERE! It's provided by the parent ThemeProvider in App.js
  return (
    <>
      <CssBaseline /> {/* Apply baseline styles based on the inherited theme */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100vh",
          bgcolor: "background.default", // This will now use the theme provided by the parent
          color: "text.primary", // Explicitly set text color based on theme
          transition: "background-color 0.3s ease, color 0.3s ease", // Added color transition
        }}
      >
        {/* Top Header */}
        <AppBar
          position="static"
          elevation={0}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.paper', // Use theme's background color
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between' }}>
            {/* Brand Logo */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ThemeAwareLogo
                variant="icon"
                sx={{ mr: 1.5 }}
              />
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main', // Use theme's primary color
                }}
              >
                Algorithm Simulator
              </Typography>

            </Box>

            {/* Right Side Icons */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Theme Toggle */}
              <Tooltip title={`Switch to ${themeMode === 'light' ? 'dark' : 'light'} mode`}>
                <IconButton
                  onClick={toggleTheme}
                  size="medium"
                  sx={{
                    color: "text.primary", // Ensure icon color matches theme
                    bgcolor: "action.hover",
                    "&:hover": {
                      bgcolor: "action.selected",
                    },
                  }}
                >
                  {themeMode === "light" ? <Brightness4Icon /> : <Brightness7Icon />}
                </IconButton>
              </Tooltip>

              {/* User Account */}
              <Tooltip title="Account">
                <IconButton
                  size="medium"
                  sx={{
                    color: "text.secondary", // Use theme's text color
                  }}
                >
                  <AccountCircleIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Box sx={{
          display: "flex",
          flexDirection: "row",
          flex: 1,
          overflow: "hidden",
          width: '100%',
          height: 'calc(100vh - 64px)', // Subtract the AppBar height
          position: 'relative', // For proper positioning
        }}>

          {/* Left Sidebar - Collapsible */}
          <Box
            sx={{
              width: sidebarCollapsed ? 50 : 300,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.8)',
              display: "flex",
              flexDirection: "column",
              position: "relative",
              overflow: "hidden",
              boxShadow: theme.palette.mode === 'dark' ? '0px 0px 10px rgba(0, 0, 0, 0.5)' : '0px 0px 10px rgba(0, 0, 0, 0.1)',
              transition: 'width 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease',
              flexShrink: 0,
              flexGrow: 0,
              zIndex: 10,
              borderRight: 1,
              borderColor: "divider",
              mt: 1,
            }}
          >
            {/* Toggle Button */}
            <IconButton
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              size="small"
              sx={{
                position: 'absolute',
                top: 10,
                right: -15,
                zIndex: 1200,
                bgcolor: theme.palette.background.paper,
                boxShadow: theme.palette.mode === 'dark' ? '0px 0px 5px rgba(255, 255, 255, 0.2)' : '0px 0px 5px rgba(0, 0, 0, 0.2)',
                '&:hover': {
                  bgcolor: theme.palette.action.hover,
                },
                transition: 'all 0.3s ease',
              }}
            >
              {sidebarCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
            {/* Algorithm Selector - Takes full height */}
            <Box
              sx={{
                flex: 1,
                overflow: "hidden",
                display: "flex",
                flexDirection: "column",
                gap: 1,
                opacity: sidebarCollapsed ? 0 : 1,
                visibility: sidebarCollapsed ? 'hidden' : 'visible',
                transition: 'opacity 0.3s ease',
              }}>
              {/* Algorithm Selector */}
              <Box sx={{ flex: 1, overflow: "hidden" }}>
                <AlgorithmSelector
                  selectedAlgorithm={selectedAlgorithm}
                  onAlgorithmChange={handleAlgorithmChange}
                />
              </Box>

              {/* Donation Section - Separate component */}
              <Box sx={{ p: 1 }}>
                <DonationSection />
              </Box>
            </Box>
          </Box>

          {/* Main Content Area */}
          <Box
            sx={{
              flexGrow: 1, // Grow to fill available space
              flexShrink: 1, // Allow shrinking when needed
              flexBasis: 'auto', // Auto basis
              display: "flex",
              bgcolor: "background.paper",
              borderRadius: 1,
              overflow: "hidden", // Prevent content overflow
              minWidth: 0, // Critical for flex items
              width: '100%', // Take full width
            }}
          >
            <AlgorithmVisualizer
              key={`algorithm-${selectedAlgorithm}`} // Force complete remount on algorithm change
              algorithm={selectedAlgorithm}
              params={algorithmParams}
              theme={theme} // Pass the correctly inherited theme if the component needs it directly
            />
          </Box>

          {/* Right Sidebar - Resizable */}
          <Box
            sx={{
              width: `${panelWidth}px`,
              bgcolor: theme.palette.background.paper,
              display: "flex",
              flexDirection: "column",
              position: "relative",
              overflow: "hidden",
              flexShrink: 0,
              flexGrow: 0,
              zIndex: 10,
              borderLeft: 1,
              borderColor: "divider",
              mt: 1,
              height: "100%",
              // overflow: "auto",
            }}
          >
            {/* Resizer */}
            <Box
              onMouseDown={(e) => {
                // Prevent default behavior
                e.preventDefault();

                // Get initial mouse position
                const startX = e.clientX;
                const startWidth = panelWidth;

                // Handle mouse move
                function handleMouseMove(e) {
                  // Calculate new width
                  const newWidth = startWidth - (e.clientX - startX);

                  // Clamp width between min and max
                  const clampedWidth = Math.max(300, Math.min(500, newWidth));

                  // Update width
                  setPanelWidth(clampedWidth);
                }

                // Handle mouse up
                function handleMouseUp() {
                  // Remove event listeners
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);

                  // Reset cursor
                  document.body.style.cursor = '';
                }

                // Add event listeners
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);

                // Set cursor
                document.body.style.cursor = 'col-resize';
              }}
              sx={{
                position: 'absolute',
                left: -5,
                top: 0,
                bottom: 0,
                width: 10,
                cursor: 'col-resize',
                zIndex: 1000,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                '&:hover': {
                  '&::after': {
                    backgroundColor: theme.palette.primary.main,
                    width: 4,
                    opacity: 1,
                  },
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: '15%',
                  bottom: '15%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 2,
                  backgroundColor: theme.palette.divider,
                  opacity: 0.7,
                  borderRadius: 4,
                }
              }}
            >
              <DragIndicatorIcon
                sx={{
                  transform: 'rotate(90deg)',
                  fontSize: '1.2rem',
                  opacity: 0.5,
                  color: theme.palette.text.secondary,
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%) rotate(90deg)',
                }}
              />
            </Box>
            {/* Algorithm Controller */}
            <Box sx={{ flex: 1, overflow: "auto", backgroundColor: "background.default", paddingLeft: 1, paddingRight: 0.75 }}>
              <AlgorithmController
                algorithmId={selectedAlgorithm}
                params={algorithmParams}
                onParamsChange={setAlgorithmParams}
                theme={theme}
              // Removed key prop to prevent remounting when algorithm changes
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}

// App component with AlgorithmProvider added
function App() {
  return (
    <ThemeProvider>
      {/* ThemeProvider wraps with MuiThemeProvider */}
      <SpeedProvider>
        {/* SpeedProvider for animation speed control */}
        <AlgorithmProvider>
          {/* AlgorithmProvider for algorithm state management */}
          <AppContent />
        </AlgorithmProvider>
      </SpeedProvider>
    </ThemeProvider>
  );
}

export default App;
