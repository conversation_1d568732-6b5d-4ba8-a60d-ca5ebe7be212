// Section.js
// A reusable section component with consistent styling

import React, { useState } from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {React.ReactNode} props.children - Section content
 * @param {React.ReactNode} props.icon - Icon to display next to the title
 * @param {boolean} props.collapsible - Whether the section can be collapsed
 * @param {boolean} props.defaultExpanded - Whether the section is expanded by default
 * @param {Object} props.sx - Additional styles
 */
const Section = ({
  title,
  children,
  icon,
  collapsible = false,
  defaultExpanded = true,
  sx = {},
  ...rest
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    if (collapsible) {
      setExpanded(!expanded);
    }
  };

  return (
    <Paper
      elevation={1}
      sx={{
        mb: 2,
        overflow: 'hidden',
        ...sx
      }}
      {...rest}
    >
      {/* Section Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          px: 2,
          py: 1,
          mb: 1,
          cursor: collapsible ? 'pointer' : 'default',
        }}
        onClick={toggleExpanded}
      >
        {icon && (
          <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
            {icon}
          </Box>
        )}
        <Typography variant="subtitle1" fontWeight="medium">
          {title}
        </Typography>
        {collapsible && (
          <Box
            sx={{
              ml: 'auto',
              fontSize: '0.8rem',
              color: theme.palette.text.secondary
            }}
          >
            {expanded ? '▼' : '▶'}
          </Box>
        )}
      </Box>

      {/* Section Content */}
      {(!collapsible || expanded) && (
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      )}
    </Paper>
  );
};

Section.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  icon: PropTypes.node,
  collapsible: PropTypes.bool,
  defaultExpanded: PropTypes.bool,
  sx: PropTypes.object
};

export default Section;
