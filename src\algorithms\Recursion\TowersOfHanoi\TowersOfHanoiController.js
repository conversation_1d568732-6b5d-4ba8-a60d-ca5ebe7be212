// TowersOfHanoiController.js
// This component provides the controls for the Towers of Hanoi algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';



import CircleIcon from '@mui/icons-material/Circle';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const TowersOfHanoiController = ({ params = {}, onParamChange = () => {} }) => {
  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    setTotalSteps
    // We don't need movements and setMovements for this component
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Extract parameters with safety checks
  const numDiscs = params?.numDiscs || 3;

  // CRITICAL FIX: Add debounce mechanism to prevent rapid changes
  const [discsChangeTimeoutId, setDiscsChangeTimeoutId] = useState(null);

  // Generate all possible movements for the Tower of Hanoi
  const generateMovements = (n) => {
    const movements = [];
    const generateMovementsRecursive = (n, source, destination, auxiliary) => {
      if (n === 1) {
        movements.push({ disc: 1, source, destination });
        return;
      }
      generateMovementsRecursive(n - 1, source, auxiliary, destination);
      movements.push({ disc: n, source, destination });
      generateMovementsRecursive(n - 1, auxiliary, destination, source);
    };
    generateMovementsRecursive(n, 'source', 'destination', 'auxiliary');
    return movements;
  };

  // Calculate total steps when numDiscs changes
  useEffect(() => {
    // Generate all movements to count them
    const movements = generateMovements(numDiscs);

    // Set the total steps to the number of movements
    // For Towers of Hanoi with n discs, there should be 2^n - 1 steps
    // This is verified by checking the length of the movements array
    setTotalSteps(movements.length);

    // Log for debugging
    console.log(`Total steps for ${numDiscs} discs: ${movements.length}`);
  }, [numDiscs, setTotalSteps]);

  // Set state to completed when step reaches totalSteps
  useEffect(() => {
    // Only update if we have valid steps and we're not in idle state
    if (totalSteps > 0 && state !== 'idle') {
      // If we've reached the last step, mark as completed
      if (step >= totalSteps) {
        setState('completed');
      }
      // If we were in completed state but stepped back, go to paused
      else if (state === 'completed' && step < totalSteps) {
        setState('paused');
      }
    }
  }, [step, totalSteps, setState, state]);

  // Handle number of discs change with debounce
  const handleDiscsChange = (value) => {
    // Validate the input
    if (value >= 1 && value <= 8) {
      // CRITICAL FIX: Clear any existing timeout
      if (discsChangeTimeoutId) {
        clearTimeout(discsChangeTimeoutId);
      }

      // CRITICAL FIX: Set a new timeout to debounce the change
      const timeoutId = setTimeout(() => {
        // Always reset when changing disc count
        if (typeof onParamChange === 'function') {
          onParamChange({ numDiscs: value });
        }
        setState('idle');
        setStep(0);
      }, 300); // 300ms debounce

      setDiscsChangeTimeoutId(timeoutId);
    }
  };

  // CRITICAL FIX: Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (discsChangeTimeoutId) {
        clearTimeout(discsChangeTimeoutId);
      }
    };
  }, [discsChangeTimeoutId]);







  return (
    <Box sx={{ p: 1, height: '100%', overflowY: 'auto' }}>
      {/* Algorithm Title */}
      <Typography variant="h5" gutterBottom>
        Towers of Hanoi
      </Typography>

      {/* Information Section */}
      <InformationSection defaultExpanded={false}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Towers of Hanoi:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            The Towers of Hanoi is a classic puzzle where the objective is to move a stack of discs from one rod to another, following specific rules.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Rules:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Move all discs from the source pole to the destination pole
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Only one disc can be moved at a time
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            - No disc may be placed on top of a smaller disc
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Time Complexity: O(2<sup>n</sup>)
          </Typography>
          <Typography variant="body2">
            - Space Complexity: O(n)
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'numDiscs',
            type: 'slider',
            label: 'Number of Discs',
            min: 1,
            max: 8,
            step: 1,
            defaultValue: numDiscs,
            icon: CircleIcon
          }
        ]}
        values={{ numDiscs }}
        onChange={(newValues) => handleDiscsChange(newValues.numDiscs)}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setState('idle');
          setStep(0);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            setStep(step + 1);
            // If this will be the last step, mark as completed
            if (step + 1 >= totalSteps) {
              setState('completed');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            // If we were in completed state, go back to paused
            if (state === 'completed') {
              setState('paused');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={generateMovements(numDiscs).map(movement => ({
          description: `Move disc ${movement.disc} from ${movement.source} to ${movement.destination}`
        }))}
        currentStep={step}
        defaultExpanded
        renderStep={(_, index) => {
          const movement = generateMovements(numDiscs)[index];
          const isCurrentStep = index === step - 1;

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '20px',
                  height: '20px',
                  borderRadius: '10px',
                  bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                  mr: 1,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                }}
              >
                {index + 1}
              </Box>
              Move disc{' '}
              <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                {movement.disc}
              </Box>
              from{' '}
              <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                {movement.source}
              </Box>
              to{' '}
              <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                {movement.destination}
              </Box>
            </Typography>
          );
        }}
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Towers of Hanoi: Recursive Algorithm"
        defaultExpanded
        currentStep={step > 0 ? Math.min(Math.ceil(step / 2), 8) : 0}
        algorithm={[
          { code: "function hanoi(n, source, target, auxiliary) {", lineNumber: 0, indent: 0 },
          { code: "if (n === 1) {", lineNumber: 1, indent: 1 },
          { code: 'print("Move disc 1 from " + source + " to " + target);', lineNumber: 2, indent: 2 },
          { code: "return;", lineNumber: 3, indent: 2 },
          { code: "}", lineNumber: 4, indent: 1 },
          { code: "hanoi(n-1, source, auxiliary, target);", lineNumber: 5, indent: 1 },
          { code: 'print("Move disc " + n + " from " + source + " to " + target);', lineNumber: 6, indent: 1 },
          { code: "hanoi(n-1, auxiliary, target, source);", lineNumber: 7, indent: 1 },
          { code: "}", lineNumber: 8, indent: 0 },
        ]}
      />
    </Box>
  );
};

export default TowersOfHanoiController;
