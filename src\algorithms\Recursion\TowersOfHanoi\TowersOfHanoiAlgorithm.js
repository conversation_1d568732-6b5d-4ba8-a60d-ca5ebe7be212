// TowersOfHanoiAlgorithm.js
import React from "react";
import { Paper, Typography, Box, useTheme } from "@mui/material";
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    // Syntax highlighting colors
    keyword: isDark ? "#569cd6" : "#0000ff", // Blue for keywords (function, if, else)
    function: isDark ? "#dcdcaa" : "#795e26", // Yellow/brown for function name
    parameter: isDark ? "#9cdcfe" : "#001080", // Blue for parameters/variables
    comment: isDark ? "#6a9955" : "#008000", // Green for comments/actions
    operator: isDark ? "#d4d4d4" : "#000000", // Default/operator color

    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const TowersOfHanoiAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Define the steps of the algorithm
  const steps = [
    { line: 0, description: "Initial state", movement: null },
    { line: 1, description: "Check if n == 1 (base case)", movement: null },
    { line: 2, description: "Move disc 1 from source to destination", movement: "Move disc 1 from source to destination" },
    { line: 3, description: "Recursive case (else)", movement: null },
    { line: 4, description: "Move n-1 discs from source to auxiliary", movement: "Move disc 1 from source to auxiliary" },
    { line: 5, description: "Move disc n from source to destination", movement: "Move disc 2 from source to destination" },
    { line: 6, description: "Move n-1 discs from auxiliary to destination", movement: "Move disc 1 from auxiliary to destination" },
  ];

  // Get the current step
  const currentStep = steps[Math.min(step, steps.length - 1)];

  // No need to generate movement sequence here as it's handled in the controller

  return (
    <Box
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
      }}
    >

      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {currentStep?.description || "Algorithm complete"}
        </Typography>
      </Paper>



      {/* Code block */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.7)' : 'rgba(245, 245, 245, 0.9)',
          overflow: 'auto', // Add horizontal scrolling
          maxWidth: '100%', // Ensure it doesn't exceed container width
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        <Box
          sx={{
            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
            whiteSpace: "nowrap", // Prevent line wrapping
            fontSize: "0.9rem",
            lineHeight: 1.7,
            color: colors.text,
            p: 0.5,
            minWidth: 'min-content', // Ensure content doesn't shrink below its minimum width
          }}
        >
          {/* Function Signature */}
          <Typography
            component="div"
            sx={{
              mb: 0.5,
              bgcolor: currentStep?.line === 0 ? colors.highlight : "rgba(0,0,0,0.03)",
              borderRadius: 1,
              p: 1,
              position: 'relative',
              border: `1px solid ${colors.border}`,
              opacity: currentStep?.line === 0 ? 1 : 0.7,
              transition: 'all 0.2s ease-in-out',
            }}
          >
            {currentStep?.line === 0 && (
              <ArrowRightIcon
                sx={{
                  position: 'absolute',
                  left: -32,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: theme.palette.primary.main,
                  fontSize: '2rem',
                  animation: 'pulse 1.5s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                    '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                    '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                  },
                }}
              />
            )}
            <Box component="span" sx={{ color: colors.keyword }}>
              function
            </Box>{" "}
            <Box component="span" sx={{ color: colors.function }}>
              solveHanoi
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              (
            </Box>
            <Box component="span" sx={{ color: colors.parameter }}>
              n
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              ,
            </Box>{" "}
            <Box component="span" sx={{ color: colors.parameter }}>
              source
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              ,
            </Box>{" "}
            <Box component="span" sx={{ color: colors.parameter }}>
              destination
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              ,
            </Box>{" "}
            <Box component="span" sx={{ color: colors.parameter }}>
              auxiliary
            </Box>
            <Box component="span" sx={{ color: colors.operator }}>
              ):
            </Box>
          </Typography>

          {/* Base Case */}
          <Box sx={{ pl: 2 }}>
            {/* Indentation level 1 (increased padding) */}
            <Typography
              component="div"
              sx={{
                mb: 0.5,
                bgcolor: currentStep?.line === 1 ? colors.highlight : "rgba(0,0,0,0.03)",
                borderRadius: 1,
                p: 1,
                position: 'relative',
                border: `1px solid ${colors.border}`,
                opacity: currentStep?.line === 1 ? 1 : 0.7,
                transition: 'all 0.2s ease-in-out',
              }}
            >
              {currentStep?.line === 1 && (
                <ArrowRightIcon
                  sx={{
                    position: 'absolute',
                    left: -32,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: theme.palette.primary.main,
                    fontSize: '2rem',
                    animation: 'pulse 1.5s infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                      '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                    },
                  }}
                />
              )}
              <Box component="span" sx={{ color: colors.keyword }}>
                if
              </Box>{" "}
              <Box component="span" sx={{ color: colors.parameter }}>
                n
              </Box>{" "}
              <Box component="span" sx={{ color: colors.operator }}>
                ==
              </Box>{" "}
              <Box component="span" sx={{ color: colors.operator }}>
                1
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                :
              </Box>
            </Typography>

            <Box sx={{ pl: 2 }}>
              {/* Indentation level 2 */}
              <Typography
                component="div"
                sx={{
                  color: colors.comment,
                  mb: 0.5,
                  bgcolor: currentStep?.line === 2 ? colors.highlight : "rgba(0,0,0,0.03)",
                  borderRadius: 1,
                  p: 1,
                  position: 'relative',
                  border: `1px solid ${colors.border}`,
                  opacity: currentStep?.line === 2 ? 1 : 0.7,
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {currentStep?.line === 2 && (
                  <ArrowRightIcon
                    sx={{
                      position: 'absolute',
                      left: -32,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: theme.palette.primary.main,
                      fontSize: '2rem',
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                        '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                        '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      },
                    }}
                  />
                )}
                # Move the single disc from source to destination
              </Typography>
              <Typography component="div" sx={{ opacity: 0.5 }}>
                <Box component="span" sx={{ color: colors.keyword }}>
                  print
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  "
                </Box>
                <Box component="span">
                  Move disc 1 from
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  {"{source}"}
                </Box>{" "}
                <Box component="span">
                  to
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  {"{destination}"}
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  "
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
              </Typography>
            </Box>

            {/* Recursive Case */}
            <Typography
              component="div"
              sx={{
                mb: 0.5,
                bgcolor: currentStep?.line === 3 ? colors.highlight : "rgba(0,0,0,0.03)",
                borderRadius: 1,
                p: 1,
                position: 'relative',
                border: `1px solid ${colors.border}`,
                opacity: currentStep?.line === 3 ? 1 : 0.7,
                transition: 'all 0.2s ease-in-out',
              }}
            >
              {currentStep?.line === 3 && (
                <ArrowRightIcon
                  sx={{
                    position: 'absolute',
                    left: -32,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: theme.palette.primary.main,
                    fontSize: '2rem',
                    animation: 'pulse 1.5s infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                      '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                    },
                  }}
                />
              )}
              <Box component="span" sx={{ color: colors.keyword }}>
                else
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                :
              </Box>
            </Typography>

            <Box sx={{ pl: 2 }}>
              {/* Step 1 */}
              <Typography
                component="div"
                sx={{
                  mb: 0.5,
                  bgcolor: currentStep?.line === 4 ? colors.highlight : "rgba(0,0,0,0.03)",
                  borderRadius: 1,
                  p: 1,
                  position: 'relative',
                  border: `1px solid ${colors.border}`,
                  opacity: currentStep?.line === 4 ? 1 : 0.7,
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {currentStep?.line === 4 && (
                  <ArrowRightIcon
                    sx={{
                      position: 'absolute',
                      left: -32,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: theme.palette.primary.main,
                      fontSize: '2rem',
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                        '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                        '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      },
                    }}
                  />
                )}
                {/* Move n-1 discs from source to auxiliary */}
                <Box component="span" sx={{ color: colors.function }}>
                  solveHanoi
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  n
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  -1
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  source
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  auxiliary
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  destination
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
              </Typography>

              {/* Step 2 */}
              <Typography
                component="div"
                sx={{
                  color: colors.comment,
                  mb: 0.5,
                  bgcolor: currentStep?.line === 5 ? colors.highlight : "rgba(0,0,0,0.03)",
                  borderRadius: 1,
                  p: 1,
                  position: 'relative',
                  border: `1px solid ${colors.border}`,
                  opacity: currentStep?.line === 5 ? 1 : 0.7,
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {currentStep?.line === 5 && (
                  <ArrowRightIcon
                    sx={{
                      position: 'absolute',
                      left: -32,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: theme.palette.primary.main,
                      fontSize: '2rem',
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                        '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                        '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      },
                    }}
                  />
                )}
                # Move the largest disc from source to destination
              </Typography>
              <Typography component="div" sx={{ opacity: 0.5 }}>
                <Box component="span" sx={{ color: colors.keyword }}>
                  print
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  "
                </Box>
                <Box component="span">
                  Move disc
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  {"{n}"}
                </Box>{" "}
                <Box component="span">
                  from
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  {"{source}"}
                </Box>{" "}
                <Box component="span">
                  to
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  {"{destination}"}
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  "
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
              </Typography>

              {/* Step 3 */}
              <Typography
                component="div"
                sx={{
                  mb: 0.5,
                  bgcolor: currentStep?.line === 6 ? colors.highlight : "rgba(0,0,0,0.03)",
                  borderRadius: 1,
                  p: 1,
                  position: 'relative',
                  border: `1px solid ${colors.border}`,
                  opacity: currentStep?.line === 6 ? 1 : 0.7,
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {currentStep?.line === 6 && (
                  <ArrowRightIcon
                    sx={{
                      position: 'absolute',
                      left: -32,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: theme.palette.primary.main,
                      fontSize: '2rem',
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                        '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
                        '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
                      },
                    }}
                  />
                )}
                <Box component="span" sx={{ color: colors.function }}>
                  solveHanoi
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  n
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  -1
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  auxiliary
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  destination
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  source
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
              </Typography>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default TowersOfHanoiAlgorithm;
