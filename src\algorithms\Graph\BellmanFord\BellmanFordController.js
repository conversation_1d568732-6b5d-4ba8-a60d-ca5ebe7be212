// BellmanFordController.js
// This component provides the controls for the Bellman-Ford algorithm.

import React, { useEffect, useState, useCallback } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography, TextField, MenuItem, Button, Divider } from '@mui/material';
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// Import icons
import GraphIcon from '@mui/icons-material/AccountTree';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import SettingsIcon from '@mui/icons-material/Settings';

// Sample graph presets
const GRAPH_PRESETS = {
  simple: {
    name: 'Simple Graph',
    nodes: [
      { id: 'A', label: 'A', x: 0, y: 0 },
      { id: 'B', label: 'B', x: 2, y: 0 },
      { id: 'C', label: 'C', x: 4, y: 0 },
      { id: 'D', label: 'D', x: 1, y: 2 },
      { id: 'E', label: 'E', x: 3, y: 2 }
    ],
    edges: [
      { id: 'AB', source: 'A', target: 'B', weight: 4 },
      { id: 'AC', source: 'A', target: 'C', weight: 2 },
      { id: 'BC', source: 'B', target: 'C', weight: 1 },
      { id: 'BD', source: 'B', target: 'D', weight: 5 },
      { id: 'CD', source: 'C', target: 'D', weight: 8 },
      { id: 'CE', source: 'C', target: 'E', weight: 10 },
      { id: 'DE', source: 'D', target: 'E', weight: 2 },
      { id: 'DA', source: 'D', target: 'A', weight: 7 }
    ]
  },
  negativeEdges: {
    name: 'Graph with Negative Edges',
    nodes: [
      { id: 'A', label: 'A', x: 0, y: 0 },
      { id: 'B', label: 'B', x: 2, y: 0 },
      { id: 'C', label: 'C', x: 4, y: 0 },
      { id: 'D', label: 'D', x: 1, y: 2 },
      { id: 'E', label: 'E', x: 3, y: 2 }
    ],
    edges: [
      { id: 'AB', source: 'A', target: 'B', weight: 4 },
      { id: 'AC', source: 'A', target: 'C', weight: 2 },
      { id: 'BC', source: 'B', target: 'C', weight: -1 },
      { id: 'BD', source: 'B', target: 'D', weight: 5 },
      { id: 'CD', source: 'C', target: 'D', weight: 8 },
      { id: 'CE', source: 'C', target: 'E', weight: -3 },
      { id: 'DE', source: 'D', target: 'E', weight: 2 },
      { id: 'DA', source: 'D', target: 'A', weight: -2 }
    ]
  },
  negativeCycle: {
    name: 'Graph with Negative Cycle',
    nodes: [
      { id: 'A', label: 'A', x: 0, y: 0 },
      { id: 'B', label: 'B', x: 2, y: 0 },
      { id: 'C', label: 'C', x: 4, y: 0 },
      { id: 'D', label: 'D', x: 1, y: 2 },
      { id: 'E', label: 'E', x: 3, y: 2 }
    ],
    edges: [
      { id: 'AB', source: 'A', target: 'B', weight: 4 },
      { id: 'BC', source: 'B', target: 'C', weight: -3 },
      { id: 'CA', source: 'C', target: 'A', weight: -2 },
      { id: 'AD', source: 'A', target: 'D', weight: 5 },
      { id: 'DE', source: 'D', target: 'E', weight: 3 },
      { id: 'EC', source: 'E', target: 'C', weight: 2 }
    ]
  },
  disconnected: {
    name: 'Disconnected Graph',
    nodes: [
      { id: 'A', label: 'A', x: 0, y: 0 },
      { id: 'B', label: 'B', x: 2, y: 0 },
      { id: 'C', label: 'C', x: 4, y: 0 },
      { id: 'D', label: 'D', x: 0, y: 3 },
      { id: 'E', label: 'E', x: 2, y: 3 },
      { id: 'F', label: 'F', x: 4, y: 3 }
    ],
    edges: [
      { id: 'AB', source: 'A', target: 'B', weight: 4 },
      { id: 'BC', source: 'B', target: 'C', weight: 3 },
      { id: 'CA', source: 'C', target: 'A', weight: 2 },
      { id: 'DE', source: 'D', target: 'E', weight: 5 },
      { id: 'EF', source: 'E', target: 'F', weight: 1 },
      { id: 'FD', source: 'F', target: 'D', weight: 6 }
    ]
  },
  large: {
    name: 'Large Graph',
    nodes: [
      { id: 'A', label: 'A', x: 0, y: 0 },
      { id: 'B', label: 'B', x: 2, y: 0 },
      { id: 'C', label: 'C', x: 4, y: 0 },
      { id: 'D', label: 'D', x: 6, y: 0 },
      { id: 'E', label: 'E', x: 0, y: 2 },
      { id: 'F', label: 'F', x: 2, y: 2 },
      { id: 'G', label: 'G', x: 4, y: 2 },
      { id: 'H', label: 'H', x: 6, y: 2 },
      { id: 'I', label: 'I', x: 0, y: 4 },
      { id: 'J', label: 'J', x: 2, y: 4 },
      { id: 'K', label: 'K', x: 4, y: 4 },
      { id: 'L', label: 'L', x: 6, y: 4 }
    ],
    edges: [
      { id: 'AB', source: 'A', target: 'B', weight: 4 },
      { id: 'AE', source: 'A', target: 'E', weight: 3 },
      { id: 'BC', source: 'B', target: 'C', weight: 5 },
      { id: 'BF', source: 'B', target: 'F', weight: 2 },
      { id: 'CD', source: 'C', target: 'D', weight: 1 },
      { id: 'CG', source: 'C', target: 'G', weight: 6 },
      { id: 'DH', source: 'D', target: 'H', weight: 4 },
      { id: 'EF', source: 'E', target: 'F', weight: 7 },
      { id: 'EI', source: 'E', target: 'I', weight: 2 },
      { id: 'FG', source: 'F', target: 'G', weight: 3 },
      { id: 'FJ', source: 'F', target: 'J', weight: 5 },
      { id: 'GH', source: 'G', target: 'H', weight: 8 },
      { id: 'GK', source: 'G', target: 'K', weight: 1 },
      { id: 'HL', source: 'H', target: 'L', weight: 3 },
      { id: 'IJ', source: 'I', target: 'J', weight: 6 },
      { id: 'JK', source: 'J', target: 'K', weight: 4 },
      { id: 'KL', source: 'K', target: 'L', weight: 2 },
      { id: 'KG', source: 'K', target: 'G', weight: -1 },
      { id: 'JF', source: 'J', target: 'F', weight: -2 }
    ]
  }
};

const BellmanFordController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setAlgorithmSteps
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Extract parameters with safety checks
  const graphPreset = params?.graphPreset || 'simple';
  const startNode = params?.startNode || 'A';
  const customGraph = params?.customGraph || null;
  const useCustomGraph = params?.useCustomGraph || false;

  // State for graph editor
  const [graphEditorOpen, setGraphEditorOpen] = useState(false);
  const [nodesText, setNodesText] = useState('');
  const [edgesText, setEdgesText] = useState('');
  const [graphError, setGraphError] = useState('');

  // State for available start nodes
  const [availableNodes, setAvailableNodes] = useState(['A']);

  // Get the current graph
  const getCurrentGraph = useCallback(() => {
    if (useCustomGraph && customGraph) {
      return customGraph;
    }
    return GRAPH_PRESETS[graphPreset] || GRAPH_PRESETS.simple;
  }, [useCustomGraph, customGraph, graphPreset]);

  // Pass the current graph to the visualization
  useEffect(() => {
    const currentGraph = getCurrentGraph();
    if (typeof onParamChange === 'function' && currentGraph) {
      onParamChange({
        ...params,
        graph: currentGraph
      });
    }
  }, [graphPreset, useCustomGraph, customGraph, onParamChange, params, getCurrentGraph]);

  // Update available start nodes when graph changes
  useEffect(() => {
    const graph = getCurrentGraph();
    const nodes = graph.nodes.map(node => node.id);
    setAvailableNodes(nodes);

    // If current start node is not in the graph, update it
    if (!nodes.includes(startNode) && nodes.length > 0) {
      handleStartNodeChange(nodes[0]);
    }
  }, [graphPreset, useCustomGraph, customGraph, startNode, getCurrentGraph]);

  // Initialize graph editor text when opening
  useEffect(() => {
    if (graphEditorOpen) {
      const graph = getCurrentGraph();
      setNodesText(JSON.stringify(graph.nodes, null, 2));
      setEdgesText(JSON.stringify(graph.edges, null, 2));
    }
  }, [graphEditorOpen, getCurrentGraph]);

  // Set state to completed when step reaches totalSteps
  useEffect(() => {
    if (totalSteps > 0 && state !== 'idle') {
      if (step >= totalSteps) {
        setState('completed');
      } else if (state === 'completed' && step < totalSteps) {
        setState('paused');
      }
    }
  }, [step, totalSteps, setState, state]);

  // Handle graph preset change
  const handleGraphPresetChange = (newPreset) => {
    if (typeof onParamChange === 'function') {
      onParamChange({
        ...params,
        graphPreset: newPreset,
        useCustomGraph: false
      });
    }

    // Reset state
    setState('idle');
    setStep(0);
  };

  // Handle start node change
  const handleStartNodeChange = (newStartNode) => {
    if (typeof onParamChange === 'function') {
      onParamChange({
        ...params,
        startNode: newStartNode
      });
    }

    // Reset state
    setState('idle');
    setStep(0);
  };

  // Handle custom graph toggle
  const handleUseCustomGraphChange = (checked) => {
    if (typeof onParamChange === 'function') {
      onParamChange({
        ...params,
        useCustomGraph: checked
      });
    }

    // Reset state
    setState('idle');
    setStep(0);
  };

  // Handle graph editor save
  const handleSaveCustomGraph = () => {
    try {
      // Parse nodes and edges
      const nodes = JSON.parse(nodesText);
      const edges = JSON.parse(edgesText);

      // Validate nodes
      if (!Array.isArray(nodes)) {
        throw new Error('Nodes must be an array');
      }

      nodes.forEach(node => {
        if (!node.id) {
          throw new Error('Each node must have an id');
        }
      });

      // Validate edges
      if (!Array.isArray(edges)) {
        throw new Error('Edges must be an array');
      }

      edges.forEach(edge => {
        if (!edge.source || !edge.target) {
          throw new Error('Each edge must have source and target');
        }
        if (typeof edge.weight !== 'number') {
          throw new Error('Each edge must have a numeric weight');
        }
      });

      // Create custom graph
      const customGraph = {
        name: 'Custom Graph',
        nodes,
        edges
      };

      // Update params
      if (typeof onParamChange === 'function') {
        onParamChange({
          ...params,
          customGraph,
          useCustomGraph: true
        });
      }

      // Close editor
      setGraphEditorOpen(false);
      setGraphError('');

      // Reset state
      setState('idle');
      setStep(0);
    } catch (error) {
      setGraphError(error.message);
    }
  };

  return (
    <Box sx={{ p: 1, height: '100%', overflowY: 'auto' }}>
      {/* Algorithm Title */}
      <Typography variant="h5" gutterBottom>
        Bellman-Ford Algorithm
      </Typography>

      {/* Information Section */}
      <InformationSection defaultExpanded={false}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Bellman-Ford Algorithm:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            The Bellman-Ford algorithm is a single-source shortest path algorithm that can handle graphs with negative edge weights. It finds the shortest paths from a source vertex to all other vertices in a weighted graph.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            O(V × E) where V is the number of vertices and E is the number of edges.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Space Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            O(V) for storing distances and predecessors.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Key Features:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            • Can handle graphs with negative edge weights
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            • Can detect negative cycles in the graph
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            • Guarantees the shortest path if no negative cycles exist
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • Works on both directed and undirected graphs
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Algorithm Steps:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            1. Initialize distances from source to all vertices as infinite and distance to source as 0
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            2. Relax all edges |V| - 1 times, where |V| is the number of vertices
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            3. Check for negative-weight cycles by trying to relax edges one more time
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            4. If any distance can still be reduced, there is a negative-weight cycle
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'graphPreset',
            type: 'select',
            label: 'Graph Preset',
            options: Object.keys(GRAPH_PRESETS).map(key => ({
              value: key,
              label: GRAPH_PRESETS[key].name
            })),
            defaultValue: graphPreset,
            icon: GraphIcon,
            disabled: useCustomGraph
          },
          {
            name: 'startNode',
            type: 'select',
            label: 'Start Node',
            options: availableNodes.map(node => ({
              value: node,
              label: node
            })),
            defaultValue: startNode,
            icon: PlayArrowIcon
          },
          {
            name: 'useCustomGraph',
            type: 'switch',
            label: 'Use Custom Graph',
            defaultValue: useCustomGraph,
            icon: ShuffleIcon
          }
        ]}
        values={{
          graphPreset,
          startNode,
          useCustomGraph
        }}
        onChange={(newValues) => {
          // Handle parameter changes
          if (newValues.graphPreset !== undefined && newValues.graphPreset !== graphPreset) {
            handleGraphPresetChange(newValues.graphPreset);
          }

          if (newValues.startNode !== undefined && newValues.startNode !== startNode) {
            handleStartNodeChange(newValues.startNode);
          }

          if (newValues.useCustomGraph !== undefined && newValues.useCustomGraph !== useCustomGraph) {
            handleUseCustomGraphChange(newValues.useCustomGraph);
          }
        }}
        disabled={state === 'running'}
      />

      {/* Edit Custom Graph Button */}
      {useCustomGraph && (
        <Box sx={{ mt: 2, mb: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            startIcon={<SettingsIcon />}
            onClick={() => setGraphEditorOpen(true)}
            disabled={state === 'running'}
          >
            Edit Custom Graph
          </Button>
        </Box>
      )}

      {/* Graph Editor Dialog */}
      {graphEditorOpen && (
        <Box sx={{ mt: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          <Typography variant="h6" gutterBottom>
            Edit Custom Graph
          </Typography>

          <Typography variant="subtitle2" gutterBottom>
            Nodes (JSON format)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={6}
            value={nodesText}
            onChange={(e) => setNodesText(e.target.value)}
            placeholder='[{"id": "A", "label": "A", "x": 0, "y": 0}, ...]'
            variant="outlined"
            size="small"
            sx={{ mb: 2, fontFamily: 'monospace' }}
          />

          <Typography variant="subtitle2" gutterBottom>
            Edges (JSON format)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={6}
            value={edgesText}
            onChange={(e) => setEdgesText(e.target.value)}
            placeholder='[{"id": "AB", "source": "A", "target": "B", "weight": 4}, ...]'
            variant="outlined"
            size="small"
            sx={{ mb: 2, fontFamily: 'monospace' }}
          />

          {graphError && (
            <Typography color="error" variant="body2" sx={{ mb: 2 }}>
              {graphError}
            </Typography>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="outlined"
              onClick={() => {
                setGraphEditorOpen(false);
                setGraphError('');
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSaveCustomGraph}
            >
              Save
            </Button>
          </Box>
        </Box>
      )}

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setStep(0);
          setTimeout(() => {
            setState('idle');
          }, 50);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            setStep(step + 1);
            if (step + 1 >= totalSteps) {
              setState('completed');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            if (state === 'completed') {
              setState('paused');
            } else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        defaultExpanded
        renderStep={(_, index) => {
          const currentStep = steps && steps[index];
          const isCurrentStep = index === step - 1;

          if (!currentStep) return null;

          // Determine step color based on step type
          let stepColor = 'primary.main';
          if (currentStep?.type === 'relaxEdge') {
            stepColor = 'success.main';
          } else if (currentStep?.type === 'skipEdge') {
            stepColor = 'info.main';
          } else if (currentStep?.type === 'negCycleFound') {
            stepColor = 'error.main';
          } else if (currentStep?.type === 'iterationStart' || currentStep?.type === 'iterationEnd') {
            stepColor = 'secondary.main';
          }

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
                mb: 0.75,
                pb: 0.75,
                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  bgcolor: 'action.hover',
                  borderRadius: '4px',
                }
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '24px',
                  height: '24px',
                  borderRadius: '12px',
                  bgcolor: isCurrentStep ? stepColor : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? '#ffffff' : 'text.secondary',
                  mr: 1.5,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                  boxShadow: isCurrentStep ? `0 0 0 2px ${stepColor}40` : 'none',
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                {index + 1}
              </Box>
              {currentStep.message}
            </Typography>
          );
        }}
        emptyMessage="No steps yet. Start the algorithm to see the sequence."
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Bellman-Ford Algorithm"
        defaultExpanded
        algorithm={[
          { code: "function bellmanFord(graph, source):", lineNumber: 1, indent: 0 },
          { code: "  // Initialize distances and predecessors", lineNumber: 2, indent: 0 },
          { code: "  let distances = {}, predecessors = {}", lineNumber: 3, indent: 0 },
          { code: "  for each vertex v in graph:", lineNumber: 4, indent: 0 },
          { code: "    distances[v] = Infinity", lineNumber: 5, indent: 1 },
          { code: "    predecessors[v] = null", lineNumber: 6, indent: 1 },
          { code: "  distances[source] = 0", lineNumber: 7, indent: 0 },
          { code: "", lineNumber: 8, indent: 0 },
          { code: "  // Relax edges |V| - 1 times", lineNumber: 9, indent: 0 },
          { code: "  for i from 1 to |V| - 1:", lineNumber: 10, indent: 0 },
          { code: "    for each edge (u, v) with weight w in graph:", lineNumber: 11, indent: 1 },
          { code: "      if distances[u] + w < distances[v]:", lineNumber: 12, indent: 2 },
          { code: "        distances[v] = distances[u] + w", lineNumber: 13, indent: 3 },
          { code: "        predecessors[v] = u", lineNumber: 14, indent: 3 },
          { code: "", lineNumber: 15, indent: 0 },
          { code: "  // Check for negative-weight cycles", lineNumber: 16, indent: 0 },
          { code: "  for each edge (u, v) with weight w in graph:", lineNumber: 17, indent: 0 },
          { code: "    if distances[u] + w < distances[v]:", lineNumber: 18, indent: 1 },
          { code: "      // Negative-weight cycle found", lineNumber: 19, indent: 2 },
          { code: "      return { hasNegativeCycle: true }", lineNumber: 20, indent: 2 },
          { code: "", lineNumber: 21, indent: 0 },
          { code: "  return { distances, predecessors, hasNegativeCycle: false }", lineNumber: 22, indent: 0 },
        ]}
        currentStep={
          step > 0 && steps && steps.length > 0 ?
            // Map the current step to the corresponding line number
            steps[step - 1]?.type === 'init' || steps[step - 1]?.type === 'initialize' ? 4 :
              steps[step - 1]?.type === 'iterationStart' ? 10 :
                steps[step - 1]?.type === 'considerEdge' ? 11 :
                  steps[step - 1]?.type === 'relaxEdge' ? 13 :
                    steps[step - 1]?.type === 'skipEdge' ? 12 :
                      steps[step - 1]?.type === 'iterationEnd' || steps[step - 1]?.type === 'earlyTermination' ? 15 :
                        steps[step - 1]?.type === 'startNegativeCycleCheck' || steps[step - 1]?.type === 'checkNegativeCycle' ? 17 :
                          steps[step - 1]?.type === 'negCycleFound' ? 19 :
                            steps[step - 1]?.type === 'complete' ? 22 : null
            : null
        }
      />
    </Box>
  );
};

export default BellmanFordController;
