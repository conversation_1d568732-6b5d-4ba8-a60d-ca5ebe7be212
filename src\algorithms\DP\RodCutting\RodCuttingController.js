// RodCuttingController.js
// This component provides the controls for Rod Cutting algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, TextField, IconButton, Slider } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import StraightenIcon from '@mui/icons-material/Straighten';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// Import algorithm functions
import RodCuttingAlgorithm from './RodCuttingAlgorithm';

const RodCuttingController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [prices, setPrices] = useState(params?.prices || [0, 1, 5, 8, 9, 10, 17, 17, 20, 24, 30]);
    const [rodLength, setRodLength] = useState(params?.rodLength || 8);
    const [customPrices, setCustomPrices] = useState(prices.join(', '));
    const [useCustomPrices, setUseCustomPrices] = useState(false);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { prices, rodLength });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { prices, rodLength });

        // Update params first
        onParamChange({
            prices,
            rodLength
        });

        // Set steps and movements directly
        try {
            const result = RodCuttingAlgorithm.generateRodCuttingSteps({
                prices,
                rodLength
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [prices, rodLength, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle rod length change
    const handleRodLengthChange = useCallback((value) => {
        const newValue = parseInt(value, 10);
        if (!isNaN(newValue) && newValue >= 1 && newValue < prices.length) {
            console.log('handleRodLengthChange called with value:', newValue);
            setRodLength(newValue);
            resetAndGenerateSteps();
        }
    }, [prices.length, resetAndGenerateSteps]);

    // Handle price change
    const handlePriceChange = useCallback((index, value) => {
        const newValue = parseInt(value, 10);
        if (isNaN(newValue) || newValue < 0) return;

        const newPrices = [...prices];
        newPrices[index] = newValue;
        setPrices(newPrices);
        setCustomPrices(newPrices.join(', '));
        resetAndGenerateSteps();
    }, [prices, resetAndGenerateSteps]);

    // Generate random prices
    const generateRandomPrices = useCallback(() => {
        const length = prices.length;
        const newPrices = [0]; // Price for length 0 is always 0
        for (let i = 1; i < length; i++) {
            // Generate random prices with some correlation to length
            // This makes the problem more realistic
            const basePrice = i * 3;
            const randomFactor = Math.random() * 5 - 2; // Random factor between -2 and 3
            newPrices.push(Math.max(1, Math.floor(basePrice + randomFactor)));
        }
        setPrices(newPrices);
        setCustomPrices(newPrices.join(', '));
        resetAndGenerateSteps();
    }, [prices.length, resetAndGenerateSteps]);

    // Handle custom prices input
    const handleCustomPricesChange = useCallback((value) => {
        setCustomPrices(value);
    }, []);

    // Apply custom prices
    const applyCustomPrices = useCallback(() => {
        try {
            const newPrices = customPrices.split(',').map(item => {
                const num = parseInt(item.trim(), 10);
                if (isNaN(num) || num < 0) {
                    throw new Error('Invalid prices');
                }
                return num;
            });
            
            if (newPrices.length < 2) {
                throw new Error('Prices array must have at least 2 elements');
            }
            
            if (newPrices[0] !== 0) {
                throw new Error('First price (for length 0) must be 0');
            }
            
            setPrices(newPrices);
            
            // Adjust rod length if necessary
            if (rodLength >= newPrices.length) {
                setRodLength(newPrices.length - 1);
            }
            
            resetAndGenerateSteps();
        } catch (error) {
            console.error('Error applying custom prices:', error);
            // Reset custom prices to current prices
            setCustomPrices(prices.join(', '));
        }
    }, [customPrices, prices, rodLength, resetAndGenerateSteps]);

    // Custom component for prices input
    const PricesInput = ({ disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>Price Table</Typography>
                    <Button 
                        startIcon={<ShuffleIcon />} 
                        onClick={generateRandomPrices}
                        disabled={disabled}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                    >
                        Random Prices
                    </Button>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <TextField
                        fullWidth
                        label="Custom Prices (comma-separated)"
                        value={customPrices}
                        onChange={(e) => handleCustomPricesChange(e.target.value)}
                        disabled={disabled}
                        sx={{ mb: 1 }}
                    />
                    <Button
                        variant="contained"
                        onClick={applyCustomPrices}
                        disabled={disabled}
                        size="small"
                        sx={{ mb: 2 }}
                    >
                        Apply Custom Prices
                    </Button>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Price Table:</Typography>
                    <Box sx={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: 1,
                        p: 1,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        maxHeight: '200px',
                        overflowY: 'auto'
                    }}>
                        {prices.map((price, i) => (
                            <Box key={i} sx={{ 
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: 1,
                                p: 1,
                                minWidth: 60,
                                bgcolor: 'background.paper'
                            }}>
                                <Typography variant="caption">
                                    Length {i}
                                </Typography>
                                <TextField
                                    type="number"
                                    size="small"
                                    value={price}
                                    onChange={(e) => handlePriceChange(i, e.target.value)}
                                    slotProps={{ input: { min: i === 0 ? 0 : 1, max: 100 } }}
                                    sx={{ width: 60, mt: 0.5 }}
                                    disabled={disabled || i === 0} // Length 0 price is always 0
                                />
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Box>
        );
    };

    // Custom component for rod length input
    const RodLengthInput = ({ value, onChange, disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                    Rod Length: {value}
                </Typography>
                <Slider
                    value={value}
                    onChange={(_, newValue) => onChange(newValue)}
                    min={1}
                    max={prices.length - 1}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    disabled={disabled}
                    sx={{ mb: 2 }}
                />
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Rod Cutting algorithm
    const pseudocode = [
        { code: "function RodCutting(prices, rodLength):", lineNumber: 1, indent: 0 },
        { code: "  // Create DP table", lineNumber: 2, indent: 1 },
        { code: "  // dp[i] = maximum value obtainable from a rod of length i", lineNumber: 3, indent: 1 },
        { code: "  dp = [0, 0, ..., 0] // Length rodLength+1", lineNumber: 4, indent: 1 },
        { code: "  cuts = [0, 0, ..., 0] // Length rodLength+1, to track optimal cuts", lineNumber: 5, indent: 1 },
        { code: "  for i = 1 to rodLength:", lineNumber: 6, indent: 1 },
        { code: "    maxValue = -∞", lineNumber: 7, indent: 2 },
        { code: "    for j = 1 to i:", lineNumber: 8, indent: 2 },
        { code: "      // Calculate value for this cut", lineNumber: 9, indent: 3 },
        { code: "      currentValue = prices[j] + dp[i-j]", lineNumber: 10, indent: 3 },
        { code: "      if currentValue > maxValue:", lineNumber: 11, indent: 3 },
        { code: "        maxValue = currentValue", lineNumber: 12, indent: 4 },
        { code: "        bestCut = j", lineNumber: 13, indent: 4 },
        { code: "      // else: keep current maximum value", lineNumber: 14, indent: 3 },
        { code: "    dp[i] = maxValue", lineNumber: 15, indent: 2 },
        { code: "    cuts[i] = bestCut", lineNumber: 16, indent: 2 },
        { code: "", lineNumber: 17, indent: 0 },
        { code: "  // Trace back to find the optimal cuts", lineNumber: 18, indent: 1 },
        { code: "  remainingLength = rodLength", lineNumber: 19, indent: 1 },
        { code: "  while remainingLength > 0:", lineNumber: 20, indent: 1 },
        { code: "    cut = cuts[remainingLength]", lineNumber: 21, indent: 2 },
        { code: "    optimalCuts.add(cut)", lineNumber: 22, indent: 2 },
        { code: "    remainingLength = remainingLength - cut", lineNumber: 23, indent: 2 },
        { code: "  return dp[rodLength], optimalCuts", lineNumber: 24, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Rod Cutting Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Rod Cutting algorithm determines the maximum value obtainable by cutting a rod of a given length into smaller pieces, where each piece has a specific price.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(n²) where n is the length of the rod
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(n) for the dynamic programming table
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to build a table of maximum values for each rod length
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • It can also track which cuts were made to achieve the optimal solution
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'prices',
                        type: 'component',
                        label: 'Price Table',
                        component: PricesInput,
                        icon: AttachMoneyIcon
                    },
                    {
                        name: 'rodLength',
                        type: 'component',
                        label: 'Rod Length',
                        component: RodLengthInput,
                        componentProps: {
                            value: rodLength,
                            onChange: handleRodLengthChange
                        },
                        icon: StraightenIcon
                    }
                ]}
                values={{
                    prices,
                    rodLength
                }}
                onChange={(newValues) => {
                    if (newValues.rodLength !== undefined && newValues.rodLength !== rodLength) {
                        handleRodLengthChange(newValues.rodLength);
                    }
                    // Prices are handled by the PricesInput component
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default RodCuttingController;
