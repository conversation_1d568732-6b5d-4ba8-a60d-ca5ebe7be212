// InsertionSortDetailedSteps.js
// This file contains the detailed step generation logic for Insertion Sort visualization

/**
 * Generate detailed steps for Insertion Sort algorithm visualization
 * @param {Array} inputArray - The array to sort
 * @returns {Array} - Array of detailed steps for visualization
 */
export const generateInsertionSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const steps = [];
  const sortedIndices = [];

  // Add initial step
  steps.push({
    type: 'initial',
    statement: `Insertion Sort: Initial array [${arr.join(', ')}] - Ready to sort by inserting elements`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [],
        comparingIndices: [],
        shiftingIndices: [],
        currentIndex: null,
      }
    }
  });

  // First element is considered sorted
  sortedIndices.push(0);
  steps.push({
    type: 'info',
    statement: `First element ${arr[0]} at index 0 is considered sorted (base case)`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        comparingIndices: [],
        shiftingIndices: [],
        currentIndex: null,
      }
    }
  });

  // Insertion Sort main loop - start from second element
  for (let i = 1; i < arr.length; i++) {
    const currentElement = arr[i];

    // Remove the current element from the array immediately to prevent corruption during shifts
    arr[i] = null;

    // Show current element selection and "hold" it for insertion
    const arrayWithHeldElement = [...arr];
    steps.push({
      type: 'select',
      statement: `Select element ${currentElement} at index ${i} to insert into sorted portion [0..${i-1}]`,
      visualizationData: {
        mainArray: {
          values: arrayWithHeldElement,
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          shiftingIndices: [],
          currentIndex: i,
          heldElement: {
            value: currentElement,
            originalIndex: i,
            currentPosition: i
          }
        }
      }
    });

    // Find the correct position for insertion
    let j = i - 1;
    let insertionPosition = i;

    // Compare with elements in sorted portion (from right to left)
    while (j >= 0) {
      // Skip null positions (positions that were cleared during shifting)
      if (arr[j] === null) {
        j--;
        continue;
      }

      // Show comparison with held element
      const comparisonArray = [...arr];
      steps.push({
        type: 'compare',
        statement: `Compare current element ${currentElement} with ${arr[j]} at index ${j}`,
        visualizationData: {
          mainArray: {
            values: comparisonArray,
            sortedIndices: [...sortedIndices],
            comparingIndices: [j],
            shiftingIndices: [],
            currentIndex: i,
            heldElement: {
              value: currentElement,
              originalIndex: i,
              currentPosition: i // Floating above for comparison
            }
          }
        }
      });

      if (arr[j] > currentElement) {
        // Need to shift element to the right - show original state with held element
        const originalArray = [...arr];
        steps.push({
          type: 'shift',
          statement: `${arr[j]} > ${currentElement}, shift ${arr[j]} from index ${j} to index ${j + 1}`,
          visualizationData: {
            mainArray: {
              values: originalArray,
              sortedIndices: [...sortedIndices],
              comparingIndices: [],
              shiftingIndices: [j, j + 1],
              currentIndex: i,
              heldElement: {
                value: currentElement,
                originalIndex: i,
                currentPosition: i // Still floating above original position
              }
            }
          }
        });

        // Perform the shift
        arr[j + 1] = arr[j];
        // Clear the source position to prevent duplication in subsequent steps
        arr[j] = null;
        insertionPosition = j;
        j--;

        // Show array after shift - use the actual algorithm state
        const shiftedArray = [...arr];
        steps.push({
          type: 'shifted',
          statement: `Element shifted: continue searching for insertion position for ${currentElement}`,
          visualizationData: {
            mainArray: {
              values: shiftedArray,
              sortedIndices: [...sortedIndices],
              comparingIndices: [],
              shiftingIndices: [],
              currentIndex: i,
              heldElement: {
                value: currentElement,
                originalIndex: i,
                currentPosition: i // Still floating above original position
              }
            }
          }
        });
      } else {
        // Found the correct position
        insertionPosition = j + 1;
        const foundArray = [...arr];
        steps.push({
          type: 'found',
          statement: `${arr[j]} ≤ ${currentElement}, found insertion position at index ${insertionPosition}`,
          visualizationData: {
            mainArray: {
              values: foundArray,
              sortedIndices: [...sortedIndices],
              comparingIndices: [],
              shiftingIndices: [],
              currentIndex: i,
              heldElement: {
                value: currentElement,
                originalIndex: i,
                currentPosition: i // Keep at fixed original position
              }
            }
          }
        });
        break;
      }
    }

    // Insert the current element at the correct position
    arr[insertionPosition] = currentElement;
    sortedIndices.push(i); // Add current index to sorted portion

    steps.push({
      type: 'insert',
      statement: `Insert ${currentElement} at index ${insertionPosition}. Sorted portion is now [0..${i}]`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          shiftingIndices: [],
          currentIndex: null,
          heldElement: null // Element is now placed, no longer held
        }
      }
    });

    // Show progress after insertion
    const sortedPortion = arr.slice(0, i + 1);
    steps.push({
      type: 'progress',
      statement: `Progress: Sorted portion [${sortedPortion.join(', ')}], remaining elements: ${arr.length - i - 1}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          shiftingIndices: [],
          currentIndex: null,
        }
      }
    });
  }

  // Final completion step
  steps.push({
    type: 'complete',
    statement: `Insertion Sort completed! Array is now sorted: [${arr.join(', ')}]`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: Array.from({ length: arr.length }, (_, idx) => idx),
        comparingIndices: [],
        shiftingIndices: [],
        currentIndex: null,
      }
    }
  });

  return steps;
};

// Helper function to get step description for display
export const getStepDescription = (step) => {
  if (!step) return '';
  return step.statement || '';
};

// Helper function to get step type for styling
export const getStepType = (step) => {
  if (!step) return 'default';
  return step.type || 'default';
};

// Export default
export default {
  generateInsertionSortDetailedSteps,
  getStepDescription,
  getStepType,
};
