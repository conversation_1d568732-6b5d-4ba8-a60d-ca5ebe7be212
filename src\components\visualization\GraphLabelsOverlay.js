// GraphLabelsOverlay.js
// A 2D overlay component for graph labels that's completely separate from the 3D scene

import React, { useState, useEffect, useRef, useMemo } from 'react';
import * as THREE from 'three';
import './GraphLabelsOverlay.css';

const GraphLabelsOverlay = ({
  graphData,
  nodePositions,
  camera,
  canvasRef,
  isDark = false
}) => {
  const [labels, setLabels] = useState([]);
  const requestRef = useRef();
  const overlayRef = useRef();

  // Function to convert 3D position to 2D screen coordinates with depth and zoom information
  const getScreenPosition = (position) => {
    if (!camera || !canvasRef.current) return null;

    const canvas = canvasRef.current;
    const vector = new THREE.Vector3(...position);

    // Calculate distance from camera to point (before projection)
    const cameraPosition = camera.position.clone();
    const distanceToCamera = cameraPosition.distanceTo(vector);

    // Project the 3D position to screen space
    vector.project(camera);

    // Convert to screen coordinates
    const x = (vector.x * 0.5 + 0.5) * canvas.clientWidth;
    const y = (-(vector.y * 0.5) + 0.5) * canvas.clientHeight;

    // Check if the point is in front of the camera
    if (vector.z > 1) return null;

    // Calculate depth factor (0 = closest to camera, 1 = furthest visible)
    // Normalize z from [-1, 1] to [0, 1]
    const depth = (vector.z + 1) / 2;

    // Calculate zoom factor based on camera position and field of view
    // This helps scale labels appropriately when zooming in/out
    const fov = camera.fov * (Math.PI / 180); // Convert FOV to radians
    const fovFactor = Math.tan(fov / 2) * distanceToCamera;
    const zoomFactor = 1 / (fovFactor * 0.1 + 0.1); // Normalize to a reasonable range

    return { x, y, depth, zoomFactor };
  };

  // Create a raycaster for occlusion detection
  const raycaster = useMemo(() => new THREE.Raycaster(), []);

  // Function to check if a point is occluded by nodes
  const isOccluded = (position, nodePositions, nodeRadius = 0.8) => {
    if (!camera) return false;

    // Direction from camera to the point
    const cameraPosition = camera.position.clone();
    const pointVector = new THREE.Vector3(...position);
    const direction = pointVector.clone().sub(cameraPosition).normalize();

    // Set up raycaster from camera to point
    raycaster.set(cameraPosition, direction);

    // Create spheres representing nodes for intersection testing
    const nodeSpheres = [];
    Object.entries(nodePositions).forEach(([nodeId, pos]) => {
      // Skip if this is the position we're checking (for labels near their own nodes)
      const nodePos = new THREE.Vector3(...pos);
      if (nodePos.distanceTo(pointVector) < nodeRadius * 0.6) return;

      // Create a sphere representing the node
      const sphere = new THREE.Sphere(nodePos, nodeRadius);
      nodeSpheres.push({ nodeId, sphere });
    });

    // Check for intersections between camera and point
    const pointDistance = cameraPosition.distanceTo(pointVector);

    for (const { sphere } of nodeSpheres) {
      // Check if ray intersects this sphere
      const intersection = raycaster.ray.intersectSphere(sphere, new THREE.Vector3());

      // If there's an intersection and it's closer than our point, the point is occluded
      if (intersection && cameraPosition.distanceTo(intersection) < pointDistance * 0.98) {
        return true; // Occluded
      }
    }

    return false; // Not occluded
  };

  // Update label positions on animation frame
  const updateLabelPositions = () => {
    if (!graphData || !nodePositions || !camera || !canvasRef.current) return;

    const newLabels = [];

    // Add node labels
    graphData.nodes.forEach(node => {
      const position = nodePositions[node.id];
      if (!position) return;

      const screenPos = getScreenPosition(position);
      if (!screenPos) return;

      // Check if this node label is occluded by other nodes in front
      // We need to create a slightly offset position for the label (in front of the node)
      // to ensure the label's occlusion is checked properly
      const labelPosition = [
        position[0],
        position[1],
        position[2] + 0.1 // Slightly in front of the node
      ];
      const occluded = isOccluded(labelPosition, nodePositions);

      newLabels.push({
        id: `node-${node.id}`,
        type: 'node',
        x: screenPos.x,
        y: screenPos.y,
        depth: screenPos.depth,
        zoomFactor: screenPos.zoomFactor,
        content: node.id.toString(),
        occluded: occluded
      });
    });

    // Add edge labels
    graphData.edges.forEach(edge => {
      const sourcePos = nodePositions[edge.source];
      const targetPos = nodePositions[edge.target];
      if (!sourcePos || !targetPos) return;

      // Calculate midpoint for label position
      const midpoint = [
        (sourcePos[0] + targetPos[0]) / 2,
        (sourcePos[1] + targetPos[1]) / 2,
        (sourcePos[2] + targetPos[2]) / 2
      ];

      const screenPos = getScreenPosition(midpoint);
      if (!screenPos) return;

      // Check if this edge label is occluded by any node
      const occluded = isOccluded(midpoint, nodePositions);

      // Only add edge label if it has a weight property
      if (edge.weight !== undefined) {
        newLabels.push({
          id: `edge-${edge.id}`,
          type: 'edge',
          x: screenPos.x,
          y: screenPos.y,
          depth: screenPos.depth,
          zoomFactor: screenPos.zoomFactor,
          content: edge.weight.toString(),
          occluded: occluded
        });
      }
    });

    setLabels(newLabels);

    // Request next animation frame
    requestRef.current = requestAnimationFrame(updateLabelPositions);
  };

  // Set up animation frame loop
  useEffect(() => {
    requestRef.current = requestAnimationFrame(updateLabelPositions);
    return () => cancelAnimationFrame(requestRef.current);
  }, [graphData, nodePositions, camera, canvasRef.current]);

  // Create overlay element
  useEffect(() => {
    if (!canvasRef.current) return;

    // Get canvas parent
    const canvasParent = canvasRef.current.parentElement;
    if (!canvasParent) return;

    // Create overlay if it doesn't exist
    if (!overlayRef.current) {
      const overlay = document.createElement('div');
      overlay.className = 'graph-labels-overlay';
      overlay.style.position = 'absolute';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.pointerEvents = 'none';
      overlay.style.zIndex = '1000';
      overlay.style.overflow = 'hidden';

      canvasParent.appendChild(overlay);
      overlayRef.current = overlay;
    }

    // Clean up on unmount
    return () => {
      if (overlayRef.current && overlayRef.current.parentElement) {
        overlayRef.current.parentElement.removeChild(overlayRef.current);
      }
    };
  }, [canvasRef.current]);

  // Render labels into overlay
  useEffect(() => {
    if (!overlayRef.current) return;

    // Clear existing labels
    overlayRef.current.innerHTML = '';

    // Sort labels by depth (furthest first, closest last)
    // This ensures closer labels are rendered on top
    const sortedLabels = [...labels].sort((a, b) => b.depth - a.depth);

    // Add new labels
    sortedLabels.forEach(label => {
      // Skip occluded labels (both node and edge)
      if (label.occluded) return;

      const labelElement = document.createElement('div');
      labelElement.className = `graph-label ${label.type}-label`;
      labelElement.style.left = `${label.x}px`;
      labelElement.style.top = `${label.y}px`;

      // Calculate opacity based on depth (1.0 for closest, 0.6 for furthest)
      // This creates a depth effect where further labels are more transparent
      // but still visible enough
      const opacityFactor = 1 - (label.depth * 0.4); // Reduced depth effect
      const opacity = Math.max(0.6, opacityFactor); // Increased minimum opacity

      if (label.type === 'node') {
        // Bright white color with strong text shadow for better visibility
        labelElement.style.color = '#ffffff';
        labelElement.style.textShadow = '0 0 4px black, 0 0 6px black, 0 0 8px black, 0 0 10px rgba(0,0,0,0.8)';
        labelElement.style.opacity = Math.min(1, opacity + 0.3).toString(); // Increase opacity for better visibility

        // Calculate scale based on both depth and zoom factor
        // This ensures labels scale appropriately when zooming in/out
        const depthScale = Math.max(0.85, 1 - (label.depth * 0.15));
        const zoomScale = Math.min(2, Math.max(0.5, label.zoomFactor));
        const finalScale = depthScale * zoomScale;
        labelElement.style.transform = `translate(-50%, -50%) scale(${finalScale})`;

        // Add a stronger text stroke effect for better visibility
        labelElement.style.WebkitTextStroke = '1px rgba(0,0,0,0.8)';

        // Add a transition for smooth fade-in/out when occlusion changes
        labelElement.style.transition = 'opacity 0.2s ease-in-out';
      } else if (label.type === 'edge') {
        // Increase contrast for edge labels
        labelElement.style.color = isDark ? '#ffffff' : '#000000';
        labelElement.style.backgroundColor = isDark ?
          `rgba(0,0,0,${Math.min(1, opacity * 0.9)})` :
          `rgba(255,255,255,${Math.min(1, opacity * 0.9)})`;
        labelElement.style.border = `1px solid ${isDark ?
          `rgba(255,255,255,${Math.min(1, opacity * 0.5)})` :
          `rgba(0,0,0,${Math.min(1, opacity * 0.5)})`}`;

        // Calculate scale based on both depth and zoom factor
        // This ensures labels scale appropriately when zooming in/out
        const depthScale = Math.max(0.8, 1 - (label.depth * 0.2));
        const zoomScale = Math.min(1.8, Math.max(0.6, label.zoomFactor));
        const finalScale = depthScale * zoomScale;
        labelElement.style.transform = `translate(-50%, -50%) scale(${finalScale})`;

        // Add a subtle glow effect
        labelElement.style.boxShadow = isDark ?
          `0 0 5px rgba(255, 255, 255, ${opacity * 0.3}), 0 0 3px rgba(0, 0, 0, 0.7)` :
          `0 0 5px rgba(0, 0, 0, ${opacity * 0.3}), 0 0 3px rgba(255, 255, 255, 0.7)`;

        // Add a transition for smooth fade-in/out when occlusion changes
        labelElement.style.transition = 'opacity 0.2s ease-in-out';
      }

      // Set z-index based on depth (higher z-index = closer to camera)
      // This ensures proper stacking of overlapping labels
      const zIndex = Math.floor(1000 - (label.depth * 900));
      labelElement.style.zIndex = zIndex.toString();

      labelElement.textContent = label.content;
      overlayRef.current.appendChild(labelElement);
    });
  }, [labels, isDark]);

  // This component doesn't render anything directly
  return null;
};

export default GraphLabelsOverlay;
