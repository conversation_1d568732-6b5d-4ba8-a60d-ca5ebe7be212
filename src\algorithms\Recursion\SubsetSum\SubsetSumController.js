// SubsetSumController.js
// This component provides the controls for the Subset Sum algorithm

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup, TextField } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import SettingsIcon from '@mui/icons-material/Settings';
import FunctionsIcon from '@mui/icons-material/Functions';

// Import algorithm functions
import { generateSubsetSumSteps } from './SubsetSumAlgorithm';

const SubsetSumController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Algorithm parameters
  const [numbers, setNumbers] = useState(params?.numbers || [3, 34, 4, 12, 5, 2]);
  const [target, setTarget] = useState(params?.target || 9);
  const [approach, setApproach] = useState(params?.approach || 'dp');
  const [inputValue, setInputValue] = useState(numbers.join(', '));
  const [targetValue, setTargetValue] = useState(target.toString());

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    // Reset state and step
    setState('idle');
    setStep(0);

    // Parse input numbers
    const parsedNumbers = inputValue.split(',')
      .map(item => parseInt(item.trim(), 10))
      .filter(item => !isNaN(item));

    // Parse target value
    const parsedTarget = parseInt(targetValue, 10);

    // Limit to 15 numbers to prevent performance issues
    const limitedNumbers = parsedNumbers.slice(0, 15);

    // Update numbers and target state
    setNumbers(limitedNumbers);
    setTarget(parsedTarget);

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { numbers: limitedNumbers, target: parsedTarget, approach });

    // Update params first
    onParamChange({
      numbers: limitedNumbers,
      target: parsedTarget,
      approach
    });

    // Set steps and movements directly
    try {
      const result = generateSubsetSumSteps({
        numbers: limitedNumbers,
        target: parsedTarget,
        approach
      });

      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      if (setMovements && typeof setMovements === 'function' && result.steps.length > 0) {
        setMovements([result.steps[0].message]);
      }
    } catch (error) {
      console.error('Error setting steps:', error);
    }
  }, [inputValue, targetValue, approach, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts
  useEffect(() => {
    resetAndGenerateSteps();
    // Start with step 1 to show the initial state
    setTimeout(() => {
      setStep(1);
    }, 500);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle input value change
  const handleInputValueChange = useCallback((event) => {
    setInputValue(event.target.value);
  }, []);

  // Handle target value change
  const handleTargetValueChange = useCallback((event) => {
    setTargetValue(event.target.value);
  }, []);

  // Handle approach change
  const handleApproachChange = useCallback((value) => {
    setApproach(value);
  }, []);

  // Handle apply button click
  const handleApplyClick = useCallback(() => {
    resetAndGenerateSteps();
  }, [resetAndGenerateSteps]);

  // Handle control button clicks
  const handleStart = useCallback(() => {
    console.log('Starting algorithm...');
    // If we're at step 0, move to step 1 first
    if (step === 0 && totalSteps > 0) {
      setStep(1);
    }
    // Set state to running
    setState('running');
  }, [setState, step, totalSteps, setStep]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Subset Sum algorithm (Dynamic Programming approach)
  const dpPseudocode = [
    { code: "function subsetSumDP(numbers, target):", lineNumber: 1, indent: 0 },
    { code: "    // Create a 2D array dp[n+1][target+1]", lineNumber: 2, indent: 0 },
    { code: "    // dp[i][j] will be true if there is a subset of numbers[0..i-1] with sum equal to j", lineNumber: 3, indent: 0 },
    { code: "    dp = new boolean[n+1][target+1]", lineNumber: 4, indent: 1 },
    { code: "", lineNumber: 5, indent: 0 },
    { code: "    // If sum is 0, then answer is true (empty subset)", lineNumber: 6, indent: 0 },
    { code: "    for i = 0 to n:", lineNumber: 7, indent: 1 },
    { code: "        dp[i][0] = true", lineNumber: 8, indent: 2 },
    { code: "", lineNumber: 9, indent: 0 },
    { code: "    // Fill the dp table", lineNumber: 10, indent: 0 },
    { code: "    for i = 1 to n:", lineNumber: 11, indent: 1 },
    { code: "        for j = 1 to target:", lineNumber: 12, indent: 2 },
    { code: "            // If current number is greater than sum j, exclude it", lineNumber: 13, indent: 0 },
    { code: "            if numbers[i-1] > j:", lineNumber: 14, indent: 3 },
    { code: "                dp[i][j] = dp[i-1][j]", lineNumber: 15, indent: 4 },
    { code: "            else:", lineNumber: 16, indent: 3 },
    { code: "                // We can either include or exclude the current number", lineNumber: 17, indent: 0 },
    { code: "                dp[i][j] = dp[i-1][j-numbers[i-1]] || dp[i-1][j]", lineNumber: 18, indent: 4 },
    { code: "", lineNumber: 19, indent: 0 },
    { code: "    // Return the final result", lineNumber: 20, indent: 0 },
    { code: "    return dp[n][target]", lineNumber: 21, indent: 1 },
  ];

  // Pseudocode for Subset Sum algorithm (Recursive approach)
  const recursivePseudocode = [
    { code: "function subsetSumRecursive(numbers, target, index, currentSum, currentSubset):", lineNumber: 1, indent: 0 },
    { code: "    // Base case: if current sum equals target", lineNumber: 2, indent: 0 },
    { code: "    if currentSum == target:", lineNumber: 3, indent: 1 },
    { code: "        return true", lineNumber: 4, indent: 2 },
    { code: "", lineNumber: 5, indent: 0 },
    { code: "    // Base case: if we've gone through all numbers", lineNumber: 6, indent: 0 },
    { code: "    if index >= numbers.length || currentSum > target:", lineNumber: 7, indent: 1 },
    { code: "        return false", lineNumber: 8, indent: 2 },
    { code: "", lineNumber: 9, indent: 0 },
    { code: "    // Include the current number", lineNumber: 10, indent: 0 },
    { code: "    currentSubset.push(numbers[index])", lineNumber: 11, indent: 1 },
    { code: "    includeSolution = subsetSumRecursive(numbers, target, index+1, currentSum+numbers[index], currentSubset)", lineNumber: 12, indent: 1 },
    { code: "", lineNumber: 13, indent: 0 },
    { code: "    // Exclude the current number (backtrack)", lineNumber: 14, indent: 0 },
    { code: "    currentSubset.pop()", lineNumber: 15, indent: 1 },
    { code: "    excludeSolution = subsetSumRecursive(numbers, target, index+1, currentSum, currentSubset)", lineNumber: 16, indent: 1 },
    { code: "", lineNumber: 17, indent: 0 },
    { code: "    // Return true if either inclusion or exclusion leads to a solution", lineNumber: 18, indent: 0 },
    { code: "    return includeSolution || excludeSolution", lineNumber: 19, indent: 1 },
  ];

  // Calculate current line based on step
  const currentLine = step > 0 && steps && steps.length > 0 ?
    steps[step - 1]?.pseudocodeLine || 0 : 0;

  // Choose the appropriate pseudocode
  const pseudocode = approach === 'dp' ? dpPseudocode : recursivePseudocode;

  // Custom component for approach selection
  const ApproachSelector = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Approach</Typography>
        <RadioGroup
          row
          value={value}
          onChange={(e) => onChange(e.target.value)}
          sx={{ ml: 1 }}
          disabled={disabled}
        >
          <FormControlLabel
            value="dp"
            control={<Radio size="small" />}
            label="Dynamic Programming"
            disabled={disabled}
          />
          <FormControlLabel
            value="recursive"
            control={<Radio size="small" />}
            label="Recursive (Backtracking)"
            disabled={disabled}
          />
        </RadioGroup>
      </Box>
    );
  };

  // Custom component for numbers input
  const NumbersInput = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Numbers (comma-separated)</Typography>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          disabled={disabled}
          placeholder="3, 34, 4, 12, 5, 2"
          helperText="Maximum 15 numbers to prevent performance issues"
        />
      </Box>
    );
  };

  // Custom component for target input
  const TargetInput = ({ value, onChange, disabled }) => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Target Sum</Typography>
        <TextField
          fullWidth
          size="small"
          value={value}
          onChange={onChange}
          disabled={disabled}
          placeholder="9"
          type="number"
        />
      </Box>
    );
  };

  // No custom results components - visualization will handle this

  return (
    <Box sx={{ p: 2 }}>
      {/* Information Section */}
      <InformationSection
        title="Subset Sum Problem"
        defaultExpanded={false}
      >
        <Box>
          <Typography variant="body2" paragraph>
            The Subset Sum problem is a classic computer science problem: given a set of integers and a target sum,
            determine if there exists a subset of the integers that adds up exactly to the target sum.
          </Typography>
          <Typography variant="body2" paragraph>
            This problem can be solved using two main approaches:
          </Typography>
          <ol>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Dynamic Programming Approach:</Typography>
              <Typography variant="body2" paragraph>
                Uses a 2D table to store intermediate results. For each number and each possible sum,
                we determine if the sum can be achieved by either including or excluding the current number.
                This approach has O(n*target) time complexity and O(n*target) space complexity.
              </Typography>
            </li>
            <li>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>Recursive Approach with Backtracking:</Typography>
              <Typography variant="body2" paragraph>
                For each number, we try both including and excluding it from our subset, and recursively check if
                either choice leads to a valid solution. This approach has O(2^n) time complexity in the worst case,
                but can be faster for certain inputs and can find all possible solutions.
              </Typography>
            </li>
          </ol>
          <Typography variant="body2" paragraph>
            The Subset Sum problem is NP-complete, which means there's no known polynomial-time algorithm that works for all inputs.
            It's a special case of the Knapsack problem where all items have the same value.
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        title="Parameters"
        defaultExpanded={true}
        parameters={[
          {
            name: 'approach',
            type: 'component',
            label: 'Approach',
            component: ApproachSelector,
            componentProps: {
              value: approach,
              onChange: handleApproachChange,
              disabled: state !== 'idle'
            },
            icon: SettingsIcon
          },
          {
            name: 'numbers',
            type: 'component',
            label: 'Numbers',
            component: NumbersInput,
            componentProps: {
              value: inputValue,
              onChange: handleInputValueChange,
              disabled: state !== 'idle'
            },
            icon: FormatListNumberedIcon
          },
          {
            name: 'target',
            type: 'component',
            label: 'Target Sum',
            component: TargetInput,
            componentProps: {
              value: targetValue,
              onChange: handleTargetValueChange,
              disabled: state !== 'idle'
            },
            icon: FunctionsIcon
          }
        ]}
        values={{
          approach,
          numbers: inputValue,
          target: targetValue
        }}
        disabled={state === 'running'}
        onApply={handleApplyClick}
        showApplyButton={true}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        state={state}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        title="Steps Sequence"
        defaultExpanded={true}
      />

      {/* No Results Display Section - visualization will handle this */}

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Subset Sum Algorithm"
        defaultExpanded={true}
        algorithm={pseudocode}
        currentStep={currentLine}
      />
    </Box>
  );
};

export default SubsetSumController;
