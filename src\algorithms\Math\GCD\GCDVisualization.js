// GCDVisualization.js
// 3D visualization component for the Euclidean GCD algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const NUMBER_SIZE = 1.2;
const NUMBER_SPACING = 3;
const BLOCK_SIZE = 0.5;
const GRID_SIZE = 20;

const GCDVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);
  const rotationRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 20, 20);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();
  
  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);
      
      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty data to show when no steps are available
      setCurrentStepData({
        a: 48,
        b: 18,
        remainder: null,
        quotient: null,
        method: 'division'
      });
    }
    
    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;
      
      // Get current time
      const currentTime = clock.getElapsedTime();
      
      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);
        
        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }
    
    // Rotate the visualization slowly
    rotationRef.current += 0.001;
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Number colors
    numberA: isDark ? '#00b894' : '#55efc4',
    numberB: isDark ? '#0984e3' : '#74b9ff',
    remainder: isDark ? '#e84118' : '#ff7675',
    quotient: isDark ? '#fdcb6e' : '#ffeaa7',
    
    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',
    
    // Block colors
    blockA: isDark ? '#00b894' : '#55efc4',
    blockB: isDark ? '#0984e3' : '#74b9ff',
    blockHighlight: isDark ? '#e84118' : '#ff7675',
    
    // Background colors
    background: isDark ? '#1e272e' : '#f5f6fa',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.numberA, label: 'First Number (a)' },
    { color: colors.numberB, label: 'Second Number (b)' },
    { color: colors.remainder, label: 'Remainder' },
    { color: colors.quotient, label: 'Quotient' },
  ], [colors]);

  // Use fixed position for stability
  const position = [0, 0, 0];

  // Create a number display
  const NumberDisplay = ({ position, number, label, color }) => {
    return (
      <group position={position}>
        {/* Number background */}
        <mesh castShadow>
          <boxGeometry args={[NUMBER_SIZE * 2, NUMBER_SIZE, NUMBER_SIZE]} />
          <meshStandardMaterial color={color} />
        </mesh>
        
        {/* Number text */}
        <Html
          position={[0, 0, NUMBER_SIZE/2 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: colors.textDark,
            fontSize: `${NUMBER_SIZE * 16}px`,
            fontWeight: 'bold',
            width: '60px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {number}
          </div>
        </Html>
        
        {/* Label */}
        <Html
          position={[0, -NUMBER_SIZE/2 - 0.5, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '14px',
            fontWeight: 'bold',
            width: '100px',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {label}
          </div>
        </Html>
      </group>
    );
  };

  // Create a division visualization
  const DivisionVisualization = ({ a, b, quotient, remainder }) => {
    // Calculate positions
    const aPosition = [-NUMBER_SPACING, 0, 0];
    const bPosition = [0, 0, 0];
    const quotientPosition = [0, NUMBER_SIZE * 2, 0];
    const remainderPosition = [NUMBER_SPACING, 0, 0];
    
    // Create division equation
    return (
      <group>
        {/* Numbers */}
        <NumberDisplay
          position={aPosition}
          number={a}
          label="a"
          color={colors.numberA}
        />
        <NumberDisplay
          position={bPosition}
          number={b}
          label="b"
          color={colors.numberB}
        />
        
        {/* Quotient (if available) */}
        {quotient !== null && (
          <NumberDisplay
            position={quotientPosition}
            number={quotient}
            label="Quotient"
            color={colors.quotient}
          />
        )}
        
        {/* Remainder (if available) */}
        {remainder !== null && (
          <NumberDisplay
            position={remainderPosition}
            number={remainder}
            label="Remainder"
            color={colors.remainder}
          />
        )}
        
        {/* Division symbols */}
        <Html
          position={[-NUMBER_SPACING/2, 0, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '24px',
            fontWeight: 'bold',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            ÷
          </div>
        </Html>
        
        {remainder !== null && (
          <Html
            position={[NUMBER_SPACING/2, 0, 0]}
            center
            occlude
          >
            <div style={{
              color: isDark ? 'white' : 'black',
              fontSize: '24px',
              fontWeight: 'bold',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              userSelect: 'none',
              pointerEvents: 'none'
            }}>
              =
            </div>
          </Html>
        )}
        
        {/* Division equation */}
        {quotient !== null && remainder !== null && (
          <Html
            position={[0, -NUMBER_SIZE * 2, 0]}
            center
            occlude
          >
            <div style={{
              color: isDark ? 'white' : 'black',
              fontSize: '18px',
              fontWeight: 'bold',
              width: '300px',
              textAlign: 'center',
              userSelect: 'none',
              pointerEvents: 'none'
            }}>
              {a} = {b} × {quotient} + {remainder}
            </div>
          </Html>
        )}
        
        {/* Visual representation of division */}
        {a > 0 && b > 0 && (
          <BlockVisualization
            a={a}
            b={b}
            quotient={quotient}
            remainder={remainder}
            position={[0, -NUMBER_SIZE * 4, 0]}
          />
        )}
      </group>
    );
  };

  // Create a subtraction visualization
  const SubtractionVisualization = ({ a, b }) => {
    // Calculate positions
    const aPosition = [-NUMBER_SPACING, 0, 0];
    const bPosition = [NUMBER_SPACING, 0, 0];
    
    // Create subtraction equation
    return (
      <group>
        {/* Numbers */}
        <NumberDisplay
          position={aPosition}
          number={a}
          label="a"
          color={colors.numberA}
        />
        <NumberDisplay
          position={bPosition}
          number={b}
          label="b"
          color={colors.numberB}
        />
        
        {/* Subtraction symbols */}
        <Html
          position={[0, 0, 0]}
          center
          occlude
        >
          <div style={{
            color: isDark ? 'white' : 'black',
            fontSize: '24px',
            fontWeight: 'bold',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            -
          </div>
        </Html>
        
        {/* Visual representation of subtraction */}
        {a > 0 && b > 0 && (
          <BlockVisualization
            a={a}
            b={b}
            position={[0, -NUMBER_SIZE * 4, 0]}
          />
        )}
      </group>
    );
  };

  // Create a block visualization for division or subtraction
  const BlockVisualization = ({ a, b, quotient, remainder, position }) => {
    // Limit the number of blocks to display
    const maxBlocks = 100;
    const scaleFactor = Math.max(1, Math.ceil(Math.max(a, b) / maxBlocks));
    
    // Scale down the numbers if they're too large
    const scaledA = Math.min(maxBlocks, Math.ceil(a / scaleFactor));
    const scaledB = Math.min(maxBlocks, Math.ceil(b / scaleFactor));
    
    // Calculate grid dimensions
    const gridWidth = Math.ceil(Math.sqrt(scaledA));
    
    // Create blocks for a
    const aBlocks = [];
    for (let i = 0; i < scaledA; i++) {
      const row = Math.floor(i / gridWidth);
      const col = i % gridWidth;
      
      // Position the block in a grid
      const x = (col - gridWidth / 2) * BLOCK_SIZE * 1.2;
      const z = (row - Math.ceil(scaledA / gridWidth) / 2) * BLOCK_SIZE * 1.2;
      
      aBlocks.push(
        <mesh
          key={`a-block-${i}`}
          position={[x, 0, z]}
          castShadow
        >
          <boxGeometry args={[BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE]} />
          <meshStandardMaterial color={colors.blockA} />
        </mesh>
      );
    }
    
    // Create blocks for b
    const bBlocks = [];
    for (let i = 0; i < scaledB; i++) {
      const row = Math.floor(i / gridWidth);
      const col = i % gridWidth;
      
      // Position the block in a grid
      const x = (col - gridWidth / 2) * BLOCK_SIZE * 1.2;
      const z = (row - Math.ceil(scaledB / gridWidth) / 2) * BLOCK_SIZE * 1.2;
      
      bBlocks.push(
        <mesh
          key={`b-block-${i}`}
          position={[x, BLOCK_SIZE * 1.5, z]}
          castShadow
        >
          <boxGeometry args={[BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE]} />
          <meshStandardMaterial color={colors.blockB} />
        </mesh>
      );
    }
    
    // Create scale factor label if scaling was applied
    const scaleLabel = scaleFactor > 1 ? (
      <Html
        position={[0, BLOCK_SIZE * 3, 0]}
        center
        occlude
      >
        <div style={{
          color: isDark ? 'white' : 'black',
          fontSize: '14px',
          fontWeight: 'bold',
          width: '200px',
          textAlign: 'center',
          userSelect: 'none',
          pointerEvents: 'none'
        }}>
          Scale: 1 block = {scaleFactor} units
        </div>
      </Html>
    ) : null;
    
    return (
      <group position={position}>
        {/* A blocks */}
        <group position={[-gridWidth * BLOCK_SIZE * 1.5, 0, 0]}>
          {aBlocks}
          <Html
            position={[0, -BLOCK_SIZE * 1.5, 0]}
            center
            occlude
          >
            <div style={{
              color: isDark ? 'white' : 'black',
              fontSize: '14px',
              fontWeight: 'bold',
              width: '100px',
              textAlign: 'center',
              userSelect: 'none',
              pointerEvents: 'none'
            }}>
              a = {a}
            </div>
          </Html>
        </group>
        
        {/* B blocks */}
        <group position={[gridWidth * BLOCK_SIZE * 1.5, 0, 0]}>
          {bBlocks}
          <Html
            position={[0, -BLOCK_SIZE * 1.5, 0]}
            center
            occlude
          >
            <div style={{
              color: isDark ? 'white' : 'black',
              fontSize: '14px',
              fontWeight: 'bold',
              width: '100px',
              textAlign: 'center',
              userSelect: 'none',
              pointerEvents: 'none'
            }}>
              b = {b}
            </div>
          </Html>
        </group>
        
        {/* Scale label */}
        {scaleLabel}
      </group>
    );
  };

  // Render the GCD visualization
  const renderGCDVisualization = () => {
    if (!currentStepData) return null;

    const { a, b, remainder, quotient, method } = currentStepData;
    
    // Choose the appropriate visualization based on the method
    if (method === 'division') {
      return (
        <DivisionVisualization
          a={a}
          b={b}
          quotient={quotient}
          remainder={remainder}
        />
      );
    } else {
      return (
        <SubtractionVisualization
          a={a}
          b={b}
        />
      );
    }
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Euclidean GCD Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position}>
          {renderGCDVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default GCDVisualization;
