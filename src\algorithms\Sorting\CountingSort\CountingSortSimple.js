// CountingSortSimple.js
// A simplified implementation of Counting Sort without step generation

/**
 * Performs a simple counting sort on an array of positive integers
 * @param {Array} arr - The array to sort
 * @returns {Array} - The sorted array
 */
export const countingSort = (arr) => {
  console.log('Starting simple Counting Sort with array:', arr);
  
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  
  // Find the maximum element to determine the size of count array
  const max = Math.max(...inputArray);
  console.log('Max value:', max);
  
  // Initialize count array with zeros
  const count = new Array(max + 1).fill(0);
  
  // Store the count of each element
  for (let i = 0; i < inputArray.length; i++) {
    count[inputArray[i]]++;
  }
  
  // Modify the count array to store the position of each element
  for (let i = 1; i <= max; i++) {
    count[i] += count[i - 1];
  }
  
  // Build the output array
  const output = new Array(inputArray.length);
  
  // Place the elements in their correct positions
  for (let i = inputArray.length - 1; i >= 0; i--) {
    const element = inputArray[i];
    output[count[element] - 1] = element;
    count[element]--;
  }
  
  console.log('Counting Sort complete, returning sorted array:', output);
  return output;
};

/**
 * Generates steps for the Counting Sort algorithm with a simplified approach
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateSimpleCountingSortSteps = (arr) => {
  console.log('Generating simple steps for Counting Sort with array:', arr);
  
  try {
    // Create a copy of the array to avoid modifying the original
    const inputArray = [...arr];
    
    // Sort the array using the simple counting sort
    const sortedArray = countingSort(inputArray);
    
    // Generate minimal steps for visualization
    const steps = [
      {
        type: 'init',
        array: inputArray,
        movement: 'Initialize Counting Sort'
      },
      {
        type: 'complete',
        array: sortedArray,
        sorted: Array.from({ length: sortedArray.length }, (_, i) => i),
        movement: 'Counting Sort complete'
      }
    ];
    
    console.log('Simple steps generated:', steps.length);
    
    return { steps, sortedArray };
  } catch (error) {
    console.error('Error in simple Counting Sort algorithm:', error);
    return { 
      steps: [
        {
          type: 'error',
          array: [...arr],
          movement: `Error: ${error.message}`
        }
      ], 
      sortedArray: [...arr] 
    };
  }
};

// Default export
export default {
  countingSort,
  generateSteps: generateSimpleCountingSortSteps
};
