// QuickSortVisualization.js - Clean QuickSort visualization following MergeSort pattern
// Uses QuickSortSimulation component and controller-driven architecture

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import QuickSortSimulation from '../../../components/visualization/bars/QuickSortSimulation';
import getAlgorithmColors from '../../../utils/algorithmColors';
import * as THREE from 'three';
import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './QuickSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

// Extract constants from comprehensive configuration
const BAR_WIDTH = CONFIG.mainArray.bars.width;
const BAR_SPACING = CONFIG.mainArray.bars.spacing;





// Main QuickSort Visualization Component
const QuickSortVisualization = (props) => {
  const {
    state,
    step,
    setStep,
    totalSteps,
    setTotalSteps,
    theme,
    steps,
    array
  } = props;



  // Get speed from context
  const { speed } = useSpeed();

  // Local state to track current step data
  const [currentStep, setCurrentStep] = useState(null);

  // Use array from props as input data
  const inputData = array;

  // Get theme-aware colors with algorithm-specific overrides
  const colors = useMemo(() => getAlgorithmColors(theme, 'quickSort'), [theme]);

  // Generate color legend items specific to quick sort
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Default' },
    { color: colors.comparing, label: 'Comparing Elements' },
    { color: colors.swapping, label: 'Swapping Elements' },
    { color: colors.pivot, label: 'Pivot Element' },
    { color: colors.merging, label: 'Partition Range' },
    { color: colors.sorted, label: 'Sorted Elements' }
  ], [colors]);

  // Get camera from Three.js context
  const { camera } = useThree();

  // Store speed in a ref for animation frame access
  const speedRef = useRef(speed);
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // State for visualization
  const [arrayData, setArrayData] = useState([]);

  // Refs for animation control
  const timeoutIdRef = useRef(null);
  const isAnimatingRef = useRef(false);
  const animatingRef = useRef(false); // For swap animations
  const initialArrayRef = useRef([]);
  const groupRef = useRef(null);

  // Store the camera initialization state
  const cameraInitializedRef = useRef(false);

  // No swap animation - just show the swapped state directly

  // Set camera position dynamically based on array size and total width
  useEffect(() => {
    if (!camera || !arrayData.length) return;

    if (cameraInitializedRef.current) {
      return;
    }

    const arraySize = arrayData.length;
    const scaleFactor = Math.max(0.15, 1 - (arraySize * 0.025));
    const currentBarWidth = BAR_WIDTH * scaleFactor;
    const currentBarSpacing = BAR_SPACING * scaleFactor;
    const totalWidth = (arraySize * (currentBarWidth + currentBarSpacing)) - currentBarSpacing;

    const fov = camera.fov * (Math.PI / 180);
    const padding = 2;
    const minDistance = (totalWidth / 2) / Math.tan(fov / 2) * padding;

    if (!cameraInitializedRef.current) {
      const cameraDistance = CONFIG.camera.dynamicPositioning.enabled
        ? Math.max(CONFIG.camera.dynamicPositioning.minDistance, minDistance)
        : CONFIG.camera.position[2];

      camera.position.set(
        CONFIG.camera.position[0],
        CONFIG.camera.position[1] + CONFIG.camera.dynamicPositioning.heightOffset,
        cameraDistance
      );
      camera.lookAt(new THREE.Vector3(...CONFIG.camera.lookAt));
      camera.updateProjectionMatrix();
      cameraInitializedRef.current = true;
    }
  }, [camera, arrayData]);

  // Add an effect to handle camera controls
  useEffect(() => {
    if (!camera) return;

    const handleCameraChange = () => {
      cameraInitializedRef.current = true;
    };

    const controls = camera.userData.controls;
    if (controls) {
      controls.addEventListener('change', handleCameraChange);
    }

    return () => {
      if (controls) {
        controls.removeEventListener('change', handleCameraChange);
      }
    };
  }, [camera]);

  // Initialize array data from inputData
  useEffect(() => {
    if (!inputData || inputData.length === 0 || isAnimatingRef.current) {
      return;
    }

    const newArray = [...inputData];
    console.log('QuickSortVisualization - Using input data:', newArray);

    setArrayData(newArray);
    initialArrayRef.current = [...newArray];
  }, [inputData]);

  // Update current step based on step prop
  useEffect(() => {
    console.log('QuickSortVisualization - Step changed to:', step);
    console.log('QuickSortVisualization - Steps array length:', steps?.length);
    console.log('QuickSortVisualization - Current state:', state);

    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('QuickSortVisualization - No steps available or invalid step index');
      return;
    }

    // For step 0, create a special "initial" step type
    if (step === 0) {
      setCurrentStep({
        ...steps[step],
        type: 'initial',
        initialArray: true,
        message: 'Quick Sort: Initial Array'
      });
    } else {
      setCurrentStep(steps[step]);
    }
  }, [steps, step]);



  // Function to safely clear any existing timeout
  const clearExistingTimeout = useCallback(() => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }
  }, []);

  // Calculate adaptive dimensions based on array size using config
  const { scaleFactor, adaptiveSpacing } = useMemo(() => {
    const arraySize = arrayData.length;

    // Determine scale factor based on config breakpoints
    let factor;
    if (arraySize <= CONFIG.responsive.breakpoints.arraySize.small) {
      factor = CONFIG.responsive.scaleFactors.small;
    } else if (arraySize <= CONFIG.responsive.breakpoints.arraySize.medium) {
      factor = CONFIG.responsive.scaleFactors.medium;
    } else {
      factor = CONFIG.responsive.scaleFactors.large;
    }

    // Calculate adaptive spacing for stage dimensions
    const spacing = CONFIG.responsive.adaptive.bars.enabled
      ? Math.max(CONFIG.responsive.adaptive.bars.minSpacing, BAR_SPACING * factor)
      : BAR_SPACING * factor;

    return {
      scaleFactor: factor,
      adaptiveSpacing: spacing
    };
  }, [arrayData.length]);

  // State to track the actual total width from bars for proper platform sizing
  const [actualTotalWidth, setActualTotalWidth] = useState(0);

  // Calculate stage dimensions using actual bar width from simulation
  const stageDimensions = useMemo(() => {
    // Use the initial array length for fallback calculation
    const arrayLength = arrayData.length || 5;

    // Calculate fallback width if actualTotalWidth is not available yet
    const adaptiveBarWidth = CONFIG.mainArray.bars.width * scaleFactor;
    const fallbackTotalWidth = (arrayLength * (adaptiveBarWidth + adaptiveSpacing)) - adaptiveSpacing;

    // Use actual width from bars if available, otherwise use fallback
    const totalBarsWidth = actualTotalWidth > 0 ? actualTotalWidth : fallbackTotalWidth;

    return {
      width: Math.max(
        totalBarsWidth + CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right,
        CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right + 4 // Minimum width
      ),
      height: CONFIG.basePlatform.dimensions.height,
      depth: CONFIG.basePlatform.dimensions.depth
    };
  }, [arrayData.length, adaptiveSpacing, scaleFactor, actualTotalWidth]);

  // Enhanced auto-advance functionality with swap animation support
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      console.log('Auto-advance stopped - state is not running:', state);
      return;
    }

    // Stop if we've reached the end
    if (step >= totalSteps) {
      console.log('Auto-advance stopped - reached end of steps:', step, '>=', totalSteps);
      return;
    }

    // Don't advance if we're currently animating
    if (animatingRef.current) {
      console.log('Auto-advance stopped - currently animating');
      return;
    }

    // No swap animation handling needed

    console.log('Auto-advance conditions met - should advance to next step soon');

    // Calculate delay based on speed using enhanced delay function
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    console.log(`Scheduling next step with enhanced speed-based delay: ${speedBasedDelay}ms (speed: ${speedRef.current})`);

    const timeoutId = setTimeout(() => {
      console.log('Auto-advancing to next step with enhanced speed-based timing');
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef, steps]);

  // Animation frame for levitation only
  useFrame(({ clock }) => {

    // Levitation animation - stay at origin, let child components handle their own positioning
    if (CONFIG.visual.levitation.enabled &&
      (!CONFIG.visual.levitation.disableDuringSimulation || state === 'idle') &&
      groupRef.current) {

      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.visual.levitation;

      // Apply levitation relative to base platform position from config
      const basePosition = CONFIG.basePlatform.position;

      if (levitationConfig.movement.y.enabled) {
        groupRef.current.position.y = basePosition[1] + Math.sin(time * levitationConfig.movement.y.frequency) * levitationConfig.movement.y.amplitude;
      } else {
        groupRef.current.position.y = basePosition[1];
      }

      if (levitationConfig.movement.x.enabled) {
        groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.movement.x.frequency) * levitationConfig.movement.x.amplitude;
      } else {
        groupRef.current.position.x = basePosition[0];
      }

      if (levitationConfig.movement.z.enabled) {
        groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.movement.z.frequency) * levitationConfig.movement.z.amplitude;
      } else {
        groupRef.current.position.z = basePosition[2];
      }

      // Apply rotation effects
      if (levitationConfig.rotation.enabled) {
        if (levitationConfig.rotation.x.enabled) {
          groupRef.current.rotation.x = Math.cos(time * levitationConfig.rotation.x.frequency) * levitationConfig.rotation.x.amplitude;
        }

        if (levitationConfig.rotation.y.enabled) {
          groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
        }

        if (levitationConfig.rotation.z.enabled) {
          groupRef.current.rotation.z = Math.sin(time * levitationConfig.rotation.z.frequency) * levitationConfig.rotation.z.amplitude;
        }
      }
    } else if (groupRef.current) {
      // When levitation is disabled, use base platform position from config
      const basePosition = CONFIG.basePlatform.position;
      groupRef.current.position.set(basePosition[0], basePosition[1], basePosition[2]);
      groupRef.current.rotation.set(0, 0, 0);
    }
  });

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      clearExistingTimeout();
    };
  }, [clearExistingTimeout]);

  // Note: Position is now handled by the levitation animation in useFrame
  // This ensures the base platform position works correctly with levitation

  return (
    <>
      {/* Step board */}
      {CONFIG.stepBoard.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''} // Only show step number for steps > 0 (like MergeSort)
          totalSteps={totalSteps > 0 ? totalSteps - 1 : 0} // Subtract 1 to exclude initial state from total
          description={currentStep?.movement || currentStep?.message || 'Quick Sort Algorithm'}
          stepData={currentStep}
          showStepNumber={step > 0} // Don't show step number for initial state (like MergeSort)
          config={{
            stepNumberFormat: 'Step {current}: ' // Only show current step number, not total
          }}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {/* Color legend */}
      {CONFIG.colorLegend.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      {/* Main Array Group - Position controlled by levitation animation in useFrame */}
      <group ref={groupRef}>
        {/* Base platform - positioned at origin since group handles basePlatform.position */}
        <SortingBase
          width={stageDimensions.width}
          height={stageDimensions.height}
          depth={stageDimensions.depth}
          color={colors.base}
          position={[0, -stageDimensions.height / 2, 0]}
        />

        {/* Bars and labels - positioned relative to the base platform using config offset */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <QuickSortSimulation
            currentStep={currentStep}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width * scaleFactor}
            barSpacing={adaptiveSpacing}
            showValues={(() => {
              const shouldShow = CONFIG.visual.labels.values.adaptiveVisibility
                ? (scaleFactor > CONFIG.visual.labels.values.visibilityThreshold)
                : CONFIG.visual.labels.values.enabled;
              console.log('QuickSort showValues calculation:', {
                adaptiveVisibility: CONFIG.visual.labels.values.adaptiveVisibility,
                scaleFactor,
                visibilityThreshold: CONFIG.visual.labels.values.visibilityThreshold,
                enabled: CONFIG.visual.labels.values.enabled,
                shouldShow
              });
              return shouldShow;
            })()}
            showIndices={CONFIG.visual.labels.indices.adaptiveVisibility
              ? (scaleFactor > CONFIG.visual.labels.indices.visibilityThreshold)
              : CONFIG.visual.labels.indices.enabled}
            config={CONFIG}
            state={state}
            onWidthChange={setActualTotalWidth}
          />
        </group>
      </group>
    </>
  );
};

export default QuickSortVisualization;
