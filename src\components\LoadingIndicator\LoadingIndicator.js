// LoadingIndicator.js
// A component to display a loading indicator while the scene is rendering

import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const LoadingIndicator = ({ loading }) => {
  if (!loading) return null;

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: (theme) => theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
        zIndex: 1000,
        backdropFilter: 'blur(8px)',
        borderRadius: 1,
      }}
    >
      <CircularProgress size={60} thickness={4} sx={{ color: 'white' }} />
      <Typography variant="subtitle1" sx={{ mt: 2, color: 'white', fontWeight: 'bold' }}>
        Loading Visualization...
      </Typography>
    </Box>
  );
};

export default LoadingIndicator;
