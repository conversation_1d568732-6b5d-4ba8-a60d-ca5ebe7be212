// FibonacciController.js
// This component provides the controls for <PERSON><PERSON><PERSON><PERSON> algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import NumbersIcon from '@mui/icons-material/Numbers';
import SettingsIcon from '@mui/icons-material/Settings';

// Import algorithm functions
import FibonacciAlgorithm from './FibonacciAlgorithm';

const FibonacciController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [n, setN] = useState(params?.n || 10);
    const [approach, setApproach] = useState(params?.approach || 'tabulation');

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { n, approach });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { n, approach });

        // Update params first
        onParamChange({
            n,
            approach
        });

        // Set steps and movements directly
        try {
            const result = FibonacciAlgorithm.generateFibonacciSteps({
                n,
                approach
            });
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [n, approach, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleNChange = useCallback((value) => {
        if (value >= 0 && value <= 20) {
            console.log('handleNChange called with value:', value);
            setN(value);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    const handleApproachChange = useCallback((value) => {
        console.log('handleApproachChange called with value:', value);
        setApproach(value);
        resetAndGenerateSteps();
    }, [resetAndGenerateSteps]);

    // Custom component for approach selection
    const ApproachSelector = ({ value, onChange, disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>Approach</Typography>
                <RadioGroup
                    row
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    sx={{ ml: 1 }}
                >
                    <FormControlLabel
                        value="tabulation"
                        control={<Radio size="small" />}
                        label="Bottom-up (Tabulation)"
                        disabled={disabled}
                    />
                    <FormControlLabel
                        value="memoization"
                        control={<Radio size="small" />}
                        label="Top-down (Memoization)"
                        disabled={disabled}
                    />
                </RadioGroup>
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Fibonacci algorithm
    const tabulationPseudocode = [
        { code: "function fibonacci_tabulation(n):", lineNumber: 1, indent: 0 },
        { code: "  // Initialize array for bottom-up approach", lineNumber: 2, indent: 1 },
        { code: "  dp = [0, 1]", lineNumber: 3, indent: 1 },
        { code: "  // Fill the dp array", lineNumber: 4, indent: 1 },
        { code: "  for i from 2 to n:", lineNumber: 5, indent: 1 },
        { code: "    dp[i] = dp[i-1] + dp[i-2]", lineNumber: 6, indent: 2 },
        { code: "  return dp[n]", lineNumber: 7, indent: 1 },
    ];

    const memoizationPseudocode = [
        { code: "function fibonacci_memoization(n, memo = {}):", lineNumber: 8, indent: 0 },
        { code: "  // Base cases", lineNumber: 9, indent: 1 },
        { code: "  if n <= 1: return n", lineNumber: 10, indent: 1 },
        { code: "  // Check if already memoized", lineNumber: 11, indent: 1 },
        { code: "  if n in memo: return memo[n]", lineNumber: 12, indent: 1 },
        { code: "  // Recursive calculation with memoization", lineNumber: 13, indent: 1 },
        { code: "  memo[n] = fibonacci_memoization(n-1, memo) + fibonacci_memoization(n-2, memo)", lineNumber: 14, indent: 1 },
        { code: "  return memo[n]", lineNumber: 15, indent: 1 },
        { code: "// Call with fibonacci_memoization(n)", lineNumber: 16, indent: 0 },
    ];

    // Combine pseudocode based on approach
    const pseudocode = approach === 'tabulation' ? tabulationPseudocode : memoizationPseudocode;

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Fibonacci Sequence Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Fibonacci sequence is a series of numbers where each number is the sum of the two preceding ones, usually starting with 0 and 1.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(n) for tabulation, O(n) for memoization
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(n) for both approaches
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Bottom-up (Tabulation): Iterative approach that builds the solution from the base cases
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Top-down (Memoization): Recursive approach that stores previously computed results
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'n',
                        type: 'slider',
                        label: 'n',
                        min: 1,
                        max: 20,
                        step: 1,
                        marks: true,
                        icon: NumbersIcon
                    },
                    {
                        name: 'approach',
                        type: 'component',
                        label: 'Approach',
                        component: ApproachSelector,
                        icon: SettingsIcon
                    }
                ]}
                values={{
                    n,
                    approach
                }}
                onChange={(newValues) => {
                    if (newValues.n !== undefined && newValues.n !== n) {
                        handleNChange(newValues.n);
                    }
                    if (newValues.approach !== undefined && newValues.approach !== approach) {
                        handleApproachChange(newValues.approach);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default FibonacciController;
