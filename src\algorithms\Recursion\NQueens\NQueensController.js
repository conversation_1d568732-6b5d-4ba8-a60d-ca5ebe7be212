// NQueensController.js
// This component provides the controls for N-Queens algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Slider, FormControlLabel, Switch } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GridOnIcon from '@mui/icons-material/GridOn';
import FindInPageIcon from '@mui/icons-material/FindInPage';

// Import algorithm functions
import NQueensAlgorithm from './NQueensAlgorithm';

const NQueensController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [boardSize, setBoardSize] = useState(params?.boardSize || 4);
    const [findAllSolutions, setFindAllSolutions] = useState(params?.findAllSolutions || false);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { boardSize, findAllSolutions });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { boardSize, findAllSolutions });

        // Update params first
        onParamChange({
            boardSize,
            findAllSolutions
        });

        // Set steps and movements directly
        try {
            const result = NQueensAlgorithm.generateNQueensSteps({
                boardSize,
                findAllSolutions
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [boardSize, findAllSolutions, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle board size change
    const handleBoardSizeChange = useCallback((_, newValue) => {
        setBoardSize(newValue);
        resetAndGenerateSteps();
    }, [resetAndGenerateSteps]);

    // Handle find all solutions change
    const handleFindAllSolutionsChange = useCallback((event) => {
        setFindAllSolutions(event.target.checked);
        resetAndGenerateSteps();
    }, [resetAndGenerateSteps]);

    // Custom component for board size input
    const BoardSizeInput = ({ value, onChange, disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                    Board Size: {value}x{value}
                </Typography>
                <Slider
                    value={value}
                    onChange={onChange}
                    min={4}
                    max={8}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    disabled={disabled}
                    sx={{ mb: 2 }}
                />
            </Box>
        );
    };

    // Custom component for find all solutions toggle
    const FindAllSolutionsInput = ({ value, onChange, disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <FormControlLabel
                    control={
                        <Switch
                            checked={value}
                            onChange={onChange}
                            disabled={disabled}
                        />
                    }
                    label="Find All Solutions"
                />
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for N-Queens algorithm
    const pseudocode = [
        { code: "function solveNQueens(n):", lineNumber: 1, indent: 0 },
        { code: "  board = new Array(n).fill(0).map(() => new Array(n).fill(0))", lineNumber: 2, indent: 1 },
        { code: "  solutions = []", lineNumber: 3, indent: 1 },
        { code: "  solveNQueensUtil(board, 0, solutions)", lineNumber: 4, indent: 1 },
        { code: "  return solutions", lineNumber: 5, indent: 1 },
        { code: "", lineNumber: 6, indent: 0 },
        { code: "function solveNQueensUtil(board, col, solutions):", lineNumber: 7, indent: 0 },
        { code: "  if (col >= board.length):", lineNumber: 8, indent: 1 },
        { code: "    solutions.push(copy(board))", lineNumber: 9, indent: 2 },
        { code: "    return true", lineNumber: 10, indent: 2 },
        { code: "", lineNumber: 11, indent: 0 },
        { code: "  for (row = 0; row < board.length; row++):", lineNumber: 12, indent: 1 },
        { code: "    if (isSafe(board, row, col)):", lineNumber: 13, indent: 2 },
        { code: "      board[row][col] = 1  // Place queen", lineNumber: 14, indent: 3 },
        { code: "      solveNQueensUtil(board, col + 1, solutions)  // Recur", lineNumber: 15, indent: 3 },
        { code: "      board[row][col] = 0  // Backtrack", lineNumber: 16, indent: 3 },
        { code: "", lineNumber: 17, indent: 0 },
        { code: "  return false", lineNumber: 18, indent: 1 },
        { code: "", lineNumber: 19, indent: 0 },
        { code: "function isSafe(board, row, col):", lineNumber: 20, indent: 0 },
        { code: "  // Check row on left side", lineNumber: 21, indent: 1 },
        { code: "  // Check upper diagonal on left side", lineNumber: 22, indent: 1 },
        { code: "  // Check lower diagonal on left side", lineNumber: 23, indent: 1 },
        { code: "  return true if safe, false otherwise", lineNumber: 24, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="N-Queens Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The N-Queens problem is the challenge of placing N chess queens on an N×N chessboard so that no two queens threaten each other.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(N!) where N is the board size
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(N²) for the board representation
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses backtracking to find all possible solutions
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • A queen can attack any piece in the same row, column, or diagonal
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'boardSize',
                        type: 'component',
                        label: 'Board Size',
                        component: BoardSizeInput,
                        componentProps: {
                            value: boardSize,
                            onChange: handleBoardSizeChange
                        },
                        icon: GridOnIcon
                    },
                    {
                        name: 'findAllSolutions',
                        type: 'component',
                        label: 'Find All Solutions',
                        component: FindAllSolutionsInput,
                        componentProps: {
                            value: findAllSolutions,
                            onChange: handleFindAllSolutionsChange
                        },
                        icon: FindInPageIcon
                    }
                ]}
                values={{
                    boardSize,
                    findAllSolutions
                }}
                onChange={(newValues) => {
                    if (newValues.boardSize !== undefined && newValues.boardSize !== boardSize) {
                        setBoardSize(newValues.boardSize);
                        resetAndGenerateSteps();
                    }
                    if (newValues.findAllSolutions !== undefined && newValues.findAllSolutions !== findAllSolutions) {
                        setFindAllSolutions(newValues.findAllSolutions);
                        resetAndGenerateSteps();
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default NQueensController;
