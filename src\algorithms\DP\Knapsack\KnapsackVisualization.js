// KnapsackVisualization.js
// 3D visualization component for 0/1 Knapsack algorithm

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html, shaderMaterial } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { extend } from '@react-three/fiber';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const ITEM_SIZE = 1.2;
const ITEM_SPACING = 2.0;
const GRID_CELL_SIZE = 1.5;
const GRID_SPACING = 0.2;

// Create SDF shader material for smooth 3D items
const SDFItemMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0x2196f3),
    emissive: new THREE.Color(0x2196f3),
    emissiveIntensity: 0.3,
    selected: 0.0, // 0.0 = not selected, 1.0 = selected
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for crystal-clear items
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform float selected;

    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    // SDF for rounded box with sharper edges
    float sdRoundBox(vec3 p, vec3 b, float r) {
      vec3 q = abs(p) - b + r;
      return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }

    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdRoundBox(p, vec3(0.45, 0.45, 0.45), 0.05); // Sharper corners

      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);

      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);

      // Specular highlight for crystal-like appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 32.0);

      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);

      // Selection effect - make it glow more when selected
      float selectionGlow = selected * 0.5 * (1.0 + sin(time * 3.0));

      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = color * (0.2 + 0.8 * diffuse) +
                        emissive * (emissiveIntensity * (1.0 + pulse + selectionGlow)) +
                        vec3(1.0) * specular * 0.5;

      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom material
extend({ SDFItemMaterial });

// Item component representing a knapsack item
const KnapsackItem = ({ position, weight, value, isSelected, isActive, color }) => {
  // Reference to update shader uniforms
  const materialRef = useRef();

  // Update shader uniforms
  useEffect(() => {
    if (materialRef.current) {
      // Convert color string to THREE.Color
      const threeColor = new THREE.Color(color);

      // Enhance the emissive color for more vibrant appearance
      const emissiveColor = new THREE.Color(color).multiplyScalar(1.2);

      materialRef.current.uniforms.color.value = threeColor;
      materialRef.current.uniforms.emissive.value = emissiveColor;
      materialRef.current.uniforms.emissiveIntensity.value = 0.6;
      materialRef.current.uniforms.selected.value = isSelected ? 1.0 : 0.0;
    }
  }, [color, isSelected]);

  // Update time uniform for subtle animation
  useFrame(({ clock }) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = clock.getElapsedTime();
    }
  });

  // Calculate a slight hover effect based on whether the item is active
  const [hoverY, setHoverY] = useState(0);
  useFrame(({ clock }) => {
    if (isActive) {
      setHoverY(Math.sin(clock.getElapsedTime() * 2) * 0.2);
    } else {
      setHoverY(0);
    }
  });

  return (
    <group position={[position[0], position[1] + hoverY, position[2]]}>
      {/* Item cube with SDF material */}
      <mesh>
        <boxGeometry args={[ITEM_SIZE, ITEM_SIZE, ITEM_SIZE]} />
        <sDFItemMaterial ref={materialRef} />
      </mesh>

      {/* Weight label */}
      <Html position={[0, -ITEM_SIZE/2 - 0.3, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px'
        }}>
          W: {weight}
        </div>
      </Html>

      {/* Value label */}
      <Html position={[0, ITEM_SIZE/2 + 0.3, 0]} center>
        <div style={{
          color: 'gold',
          fontSize: '14px',
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px'
        }}>
          V: {value}
        </div>
      </Html>
    </group>
  );
};

// DP Table Cell component
const TableCell = ({ position, value, isHighlighted, isInPath }) => {
  const color = isInPath
    ? new THREE.Color(0x4caf50) // Green for path
    : isHighlighted
      ? new THREE.Color(0xffeb3b) // Yellow for highlighted
      : new THREE.Color(0x2196f3); // Blue for normal

  const opacity = isHighlighted || isInPath ? 0.8 : 0.5;

  return (
    <group position={position}>
      <mesh>
        <boxGeometry args={[GRID_CELL_SIZE, GRID_CELL_SIZE, 0.1]} />
        <meshStandardMaterial
          color={color}
          transparent
          opacity={opacity}
          emissive={color}
          emissiveIntensity={0.3}
        />
      </mesh>

      <Html position={[0, 0, 0.1]} center>
        <div style={{
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px'
        }}>
          {value}
        </div>
      </Html>
    </group>
  );
};

// Knapsack component
const Knapsack = ({ position, capacity, currentWeight, items }) => {
  // Calculate fill level based on current weight vs capacity
  const fillLevel = Math.min(1, currentWeight / capacity);

  return (
    <group position={position}>
      {/* Knapsack container */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[2, 2.5, 4, 32, 1, true]} />
        <meshStandardMaterial
          color="#8d6e63"
          side={THREE.DoubleSide}
          transparent
          opacity={0.8}
        />
      </mesh>

      {/* Knapsack bottom */}
      <mesh position={[0, -2, 0]} rotation={[Math.PI/2, 0, 0]}>
        <circleGeometry args={[2.5, 32]} />
        <meshStandardMaterial color="#5d4037" />
      </mesh>

      {/* Knapsack fill level */}
      <mesh position={[0, -2 + fillLevel * 4, 0]}>
        <cylinderGeometry args={[2, 2.5, 0.1, 32]} />
        <meshStandardMaterial
          color="#4fc3f7"
          transparent
          opacity={0.7}
          emissive="#4fc3f7"
          emissiveIntensity={0.3}
        />
      </mesh>

      {/* Capacity label */}
      <Html position={[0, 2.5, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '4px 8px',
          borderRadius: '4px'
        }}>
          {currentWeight} / {capacity}
        </div>
      </Html>

      {/* Selected items inside knapsack */}
      {items.map((item, index) => {
        // Position items in a spiral inside the knapsack
        const angle = index * 0.8;
        const radius = 1.2;
        const x = Math.sin(angle) * radius;
        const z = Math.cos(angle) * radius;
        const y = -1.5 + index * 0.5; // Stack items vertically

        return (
          <KnapsackItem
            key={`knapsack-item-${item.index}`}
            position={[x, y, z]}
            weight={item.weight}
            value={item.value}
            isSelected={true}
            isActive={false}
            color="#4caf50" // Green for selected items
          />
        );
      })}
    </group>
  );
};

// Main visualization component
const KnapsackVisualization = ({
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);

  // Set up camera position
  useEffect(() => {
    if (camera) {
      camera.position.set(15, 15, 25);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Define colors based on theme
  const colors = useMemo(() => ({
    item: isDark ? '#4fc3f7' : '#2196f3',         // Blue for regular items
    activeItem: isDark ? '#ffeb3b' : '#ffc107',   // Yellow for active item
    selectedItem: isDark ? '#69f0ae' : '#4caf50', // Green for selected items
    tableCell: isDark ? '#90caf9' : '#64b5f6',    // Light blue for table cells
    highlightedCell: isDark ? '#ffee58' : '#ffeb3b', // Yellow for highlighted cells
    pathCell: isDark ? '#81c784' : '#4caf50',     // Green for path cells
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.item, label: 'Available Item' },
    { color: colors.activeItem, label: 'Current Item' },
    { color: colors.selectedItem, label: 'Selected Item' },
    { color: colors.tableCell, label: 'DP Table Cell' },
    { color: colors.highlightedCell, label: 'Current Cell' },
    { color: colors.pathCell, label: 'Solution Path' },
  ], [colors]);

  // Update state based on current step
  useEffect(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('No steps available or invalid step index');
      return;
    }

    // Store steps in ref for access in animation frame
    stepsRef.current = steps;

    // Get current step data
    const currentStep = steps[step];
    console.log('Current step data:', currentStep);
    setCurrentStepData(currentStep);

    // Update last applied step
    lastAppliedStepRef.current = step;

    // Update total steps
    if (setTotalSteps) {
      setTotalSteps(steps.length);
    }

    // Update movements
    if (setMovements && currentStep.message) {
      setMovements([currentStep.message]);
    }
  }, [step, steps, setTotalSteps, setMovements]);

  // Helper function to calculate delay based on speed
  const calculateDelay = (speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  };

  // Auto-advance steps when in running state
  useEffect(() => {
    // Update refs
    stateRef.current = state;
    speedRef.current = speed;

    let timeoutId = null;

    if (state === 'running') {
      // If we're at the end, stop the simulation
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached end of steps, stopping simulation');
        setState('paused'); // Change state to paused instead of looping
        return;
      }

      // Calculate next step
      const nextStep = step + 1;
      console.log('Auto-advancing to step:', nextStep);

      // Calculate delay based on speed
      const delay = calculateDelay(speed);

      // Set a timer to advance to the next step
      timeoutId = setTimeout(() => {
        console.log('Timer fired, setting step to:', nextStep);
        setStep(nextStep);
      }, delay);
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, 0, 0];

  // Render the knapsack visualization
  const renderKnapsackVisualization = () => {
    if (!currentStepData) return null;

    const { weights, values, capacity, dp, currentItem, currentWeight, selectedItems } = currentStepData;

    // Calculate current knapsack weight
    const currentKnapsackWeight = selectedItems.reduce((total, itemIndex) => {
      return total + weights[itemIndex];
    }, 0);

    // Create an array of selected item objects
    const selectedItemObjects = selectedItems.map(itemIndex => ({
      index: itemIndex,
      weight: weights[itemIndex],
      value: values[itemIndex]
    }));

    // Determine if we're in the traceback phase
    const isTraceback = currentStepData.type.includes('traceback') ||
                        currentStepData.type.includes('include_in_solution') ||
                        currentStepData.type.includes('exclude_from_solution') ||
                        currentStepData.type === 'complete';

    return (
      <group>
        {/* Available items */}
        <group position={[-10, 0, 0]}>
          {weights.map((weight, index) => {
            const value = values[index];
            const isSelected = selectedItems.includes(index);
            const isActive = currentItem === index + 1;

            // Position items in a circular arrangement
            const angle = (index / weights.length) * Math.PI * 2;
            const radius = 6;
            const x = Math.sin(angle) * radius;
            const z = Math.cos(angle) * radius;

            // Don't show items that are already in the knapsack
            if (isSelected && isTraceback) {
              return null;
            }

            return (
              <KnapsackItem
                key={`item-${index}`}
                position={[x, 0, z]}
                weight={weight}
                value={value}
                isSelected={isSelected}
                isActive={isActive}
                color={isActive ? colors.activeItem : colors.item}
              />
            );
          })}
        </group>

        {/* Knapsack */}
        <Knapsack
          position={[10, 0, 0]}
          capacity={capacity}
          currentWeight={currentKnapsackWeight}
          items={selectedItemObjects}
        />

        {/* DP Table */}
        {dp && (
          <group position={[0, -8, 0]}>
            {dp.map((row, i) => (
              <group key={`row-${i}`}>
                {row.map((cell, j) => {
                  const x = (j - row.length / 2) * (GRID_CELL_SIZE + GRID_SPACING);
                  const z = (i - dp.length / 2) * (GRID_CELL_SIZE + GRID_SPACING);

                  // Determine if this cell is highlighted
                  const isHighlighted = i === currentItem && j === currentWeight;

                  // Determine if this cell is part of the solution path
                  const isInPath = isTraceback &&
                                  i === currentItem &&
                                  j === currentWeight;

                  return (
                    <TableCell
                      key={`cell-${i}-${j}`}
                      position={[x, 0, z]}
                      value={cell}
                      isHighlighted={isHighlighted}
                      isInPath={isInPath}
                    />
                  );
                })}
              </group>
            ))}

            {/* Row labels (item indices) */}
            {dp.map((row, i) => {
              if (i === 0) return null; // Skip the first row label (0)

              const x = -(row.length / 2) * (GRID_CELL_SIZE + GRID_SPACING) - GRID_CELL_SIZE;
              const z = (i - dp.length / 2) * (GRID_CELL_SIZE + GRID_SPACING);

              return (
                <Html key={`row-label-${i}`} position={[x, 0, z]} center>
                  <div style={{
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    textShadow: '0 0 5px rgba(0,0,0,0.5)',
                    userSelect: 'none'
                  }}>
                    Item {i}
                  </div>
                </Html>
              );
            })}

            {/* Column labels (weights) */}
            {dp[0] && dp[0].map((cell, j) => {
              const x = (j - dp[0].length / 2) * (GRID_CELL_SIZE + GRID_SPACING);
              const z = -(dp.length / 2) * (GRID_CELL_SIZE + GRID_SPACING) - GRID_CELL_SIZE;

              return (
                <Html key={`col-label-${j}`} position={[x, 0, z]} center>
                  <div style={{
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    textShadow: '0 0 5px rgba(0,0,0,0.5)',
                    userSelect: 'none'
                  }}>
                    W: {j}
                  </div>
                </Html>
              );
            })}
          </group>
        )}
      </group>
    );
  };

  // Return the 3D visualization
  return (
    <group>
      {/* Fixed Step board - stays at the top of the screen */}
      <FixedStepBoard
        description={currentStepData?.message || '0/1 Knapsack Algorithm'}
        currentStep={step}
        totalSteps={stepsRef.current.length || 1}
        stepData={currentStepData || {}}
      />

      {/* Fixed Color legend - stays at the bottom of the screen */}
      <FixedColorLegend
        items={legendItems}
        theme={muiTheme}
      />

      {/* Ambient light for overall scene illumination */}
      <ambientLight intensity={0.5} />

      {/* Directional lights for better 3D definition */}
      <directionalLight position={[10, 10, 5]} intensity={0.7} color="#ffffff" />
      <directionalLight position={[-10, -10, -5]} intensity={0.3} color={isDark ? '#6666ff' : '#66ccff'} />

      {/* Spotlight to highlight the main visualization */}
      <spotLight
        position={[0, 25, 0]}
        angle={0.3}
        penumbra={0.8}
        intensity={0.8}
        castShadow
        color={isDark ? '#ffffff' : '#ffffff'}
      />

      {/* Visualization */}
      <group position={position} rotation={rotation}>
        {renderKnapsackVisualization()}
      </group>

      {/* Add a subtle fog effect for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

      {/* Add a grid helper for better spatial reference */}
      <gridHelper
        args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
        position={[0, -12, 0]}
        rotation={[0, 0, 0]}
      />
    </group>
  );
};

export default KnapsackVisualization;
