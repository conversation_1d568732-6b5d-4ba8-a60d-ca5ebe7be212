# Simulation Architecture API Reference

This document provides detailed API documentation for all components and utilities in the simulation architecture.

## Table of Contents

- [Contexts](#contexts)
  - [StepContext](#stepcontext)
  - [SimulationContext](#simulationcontext)
  - [AlgorithmDataContext](#algorithmdatacontext)
- [Components](#components)
  - [SimulationEngine](#simulationengine)
  - [StepProcessor](#stepprocessor)
  - [StepVisualizer](#stepvisualizer)
- [Utilities](#utilities)
  - [Step Utilities](#step-utilities)
  - [Animation Utilities](#animation-utilities)

## Contexts

### StepContext

Manages algorithm steps and their execution.

#### Provider

```jsx
<StepProvider>
  {/* Children */}
</StepProvider>
```

#### Hook

```javascript
const {
  // Step data
  steps,
  setSteps,
  currentStep,
  currentStepIndex,
  totalSteps,

  // Step navigation
  goToStep,
  goToNextStep,
  goToPreviousStep,
  goToFirstStep,
  goToLastStep,
  setCurrentStepIndex,

  // Step animation
  isAnimating,
  startStepAnimation,
  stopStepAnimation,

  // Step metadata
  stepMetadata,
  updateStepMetadata,
  getStepMetadata
} = useStep();
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `steps` | `Array` | Array of step objects |
| `setSteps` | `Function` | Function to set the steps array |
| `currentStep` | `Object` | The current step object |
| `currentStepIndex` | `number` | Index of the current step |
| `totalSteps` | `number` | Total number of steps |

#### Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `goToStep` | `index: number` | Go to a specific step |
| `goToNextStep` | None | Go to the next step |
| `goToPreviousStep` | None | Go to the previous step |
| `goToFirstStep` | None | Go to the first step |
| `goToLastStep` | None | Go to the last step |
| `setCurrentStepIndex` | `index: number` | Set the current step index |
| `startStepAnimation` | None | Start step animation |
| `stopStepAnimation` | None | Stop step animation |
| `updateStepMetadata` | `metadata: Object` | Update step metadata |
| `getStepMetadata` | `key: string` | Get step metadata value |

### SimulationContext

Manages the overall simulation state.

#### Provider

```jsx
<SimulationProvider>
  {/* Children */}
</SimulationProvider>
```

#### Hook

```javascript
const {
  // Simulation state
  state,
  setState,
  
  // Simulation speed
  speed,
  setSpeed,
  getDelay,
  
  // Auto-advance
  autoAdvance,
  setAutoAdvance,
  toggleAutoAdvance,
  
  // Simulation controls
  startSimulation,
  pauseSimulation,
  resetSimulation,
  
  // Simulation configuration
  config,
  updateConfig
} = useSimulation();
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `state` | `string` | Current simulation state ('idle', 'running', 'paused', 'completed') |
| `speed` | `number` | Simulation speed (1-10) |
| `autoAdvance` | `boolean` | Whether to automatically advance to the next step |
| `config` | `Object` | Simulation configuration |

#### Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `setState` | `state: string` | Set the simulation state |
| `setSpeed` | `speed: number` | Set the simulation speed |
| `getDelay` | None | Get the delay based on speed |
| `setAutoAdvance` | `autoAdvance: boolean` | Set auto-advance |
| `toggleAutoAdvance` | None | Toggle auto-advance |
| `startSimulation` | None | Start the simulation |
| `pauseSimulation` | None | Pause the simulation |
| `resetSimulation` | None | Reset the simulation |
| `updateConfig` | `config: Object` | Update simulation configuration |

### AlgorithmDataContext

Manages algorithm-specific data structures.

#### Provider

```jsx
<AlgorithmDataProvider>
  {/* Children */}
</AlgorithmDataProvider>
```

#### Hook

```javascript
const {
  // Algorithm data
  inputData,
  outputData,
  intermediateState,
  algorithmConfig,

  // Data management functions
  updateInputData,
  updateOutputData,
  updateIntermediateState,
  getIntermediateState,
  resetIntermediateState,
  updateAlgorithmConfig,
  resetAlgorithmData
} = useAlgorithmData();
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `inputData` | `any` | Input data for the algorithm |
| `outputData` | `any` | Output data from the algorithm |
| `intermediateState` | `Object` | Intermediate state during algorithm execution |
| `algorithmConfig` | `Object` | Algorithm configuration |

#### Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `updateInputData` | `data: any` | Update input data |
| `updateOutputData` | `data: any` | Update output data |
| `updateIntermediateState` | `state: Object` | Update intermediate state |
| `getIntermediateState` | `key: string, defaultValue: any` | Get intermediate state value |
| `resetIntermediateState` | None | Reset intermediate state |
| `updateAlgorithmConfig` | `config: Object` | Update algorithm configuration |
| `resetAlgorithmData` | None | Reset all algorithm data |

## Components

### SimulationEngine

Core component that handles step execution and transitions.

#### Props

```jsx
<SimulationEngine
  onStepStart={handleStepStart}
  onStepComplete={handleStepComplete}
  onSimulationComplete={handleSimulationComplete}
  stepProcessor={processStep}
>
  {/* Optional children */}
</SimulationEngine>
```

| Prop | Type | Description |
|------|------|-------------|
| `onStepStart` | `Function` | Callback when a step starts |
| `onStepComplete` | `Function` | Callback when a step completes |
| `onSimulationComplete` | `Function` | Callback when simulation completes |
| `stepProcessor` | `Function` | Function to process each step |
| `children` | `ReactNode` | Optional children to render |

### StepProcessor

Component that processes individual algorithm steps.

#### Props

```jsx
<StepProcessor
  stepHandlers={{
    comparison: handleComparison,
    swap: handleSwap
  }}
  onStepStart={handleStepStart}
  onStepProgress={handleStepProgress}
  onStepComplete={handleStepComplete}
  getStepDuration={calculateStepDuration}
/>
```

| Prop | Type | Description |
|------|------|-------------|
| `stepHandlers` | `Object` | Map of step types to handler functions |
| `onStepStart` | `Function` | Callback when a step starts |
| `onStepProgress` | `Function` | Callback with step progress (0-1) |
| `onStepComplete` | `Function` | Callback when a step completes |
| `getStepDuration` | `Function` | Function to get step duration based on type |

### StepVisualizer

Component that visualizes algorithm steps.

#### Props

```jsx
<StepVisualizer
  visualizers={{
    comparison: ComparisonVisualizer,
    swap: SwapVisualizer
  }}
  defaultVisualizer={DefaultVisualizer}
  onRender={handleRender}
/>
```

| Prop | Type | Description |
|------|------|-------------|
| `visualizers` | `Object` | Map of step types to visualizer components |
| `defaultVisualizer` | `Component` | Default visualizer component |
| `onRender` | `Function` | Callback when a step is rendered |

## Utilities

### Step Utilities

Utility functions for step generation and processing.

#### Step Creators

```javascript
import {
  createStep,
  createInitialStep,
  createCompletionStep,
  createComparisonStep,
  createSwapStep,
  createMergeStep,
  createSplitStep,
  createPlaceStep
} from '../utils/stepUtils';
```

| Function | Parameters | Description |
|----------|------------|-------------|
| `createStep` | `type, data, message, metadata` | Create a step object |
| `createInitialStep` | `data, message, metadata` | Create an initial step |
| `createCompletionStep` | `data, message, metadata` | Create a completion step |
| `createComparisonStep` | `data, indices, message, metadata` | Create a comparison step |
| `createSwapStep` | `data, indices, message, metadata` | Create a swap step |
| `createMergeStep` | `data, mergeInfo, message, metadata` | Create a merge step |
| `createSplitStep` | `data, splitInfo, message, metadata` | Create a split step |
| `createPlaceStep` | `data, placeInfo, message, metadata` | Create a place step |

#### Other Utilities

```javascript
import {
  validateStep,
  formatStepMessage,
  deepClone,
  generateRandomArray,
  parseCustomArray
} from '../utils/stepUtils';
```

| Function | Parameters | Description |
|----------|------------|-------------|
| `validateStep` | `step` | Validate a step object |
| `formatStepMessage` | `message, values` | Format a step message with colored indices |
| `deepClone` | `obj` | Deep clone an array or object |
| `generateRandomArray` | `length, min, max` | Generate a random array of integers |
| `parseCustomArray` | `input` | Parse a custom array input string |

### Animation Utilities

Utility functions for animations.

#### Interpolation Functions

```javascript
import {
  lerp,
  lerpVector,
  easeInQuad,
  easeOutQuad,
  easeInOutQuad,
  easeInCubic,
  easeOutCubic,
  easeInOutCubic,
  elastic
} from '../utils/animationUtils';
```

| Function | Parameters | Description |
|----------|------------|-------------|
| `lerp` | `a, b, t` | Linear interpolation between two values |
| `lerpVector` | `a, b, t` | Linear interpolation between two 3D vectors |
| `easeInQuad` | `t` | Ease In Quad easing function |
| `easeOutQuad` | `t` | Ease Out Quad easing function |
| `easeInOutQuad` | `t` | Ease In Out Quad easing function |
| `easeInCubic` | `t` | Ease In Cubic easing function |
| `easeOutCubic` | `t` | Ease Out Cubic easing function |
| `easeInOutCubic` | `t` | Ease In Out Cubic easing function |
| `elastic` | `t` | Elastic easing function |

#### Animation Helpers

```javascript
import {
  calculateDuration,
  calculateLevitation,
  calculateRotation,
  calculateScale,
  interpolateColor,
  rgbToHex
} from '../utils/animationUtils';
```

| Function | Parameters | Description |
|----------|------------|-------------|
| `calculateDuration` | `stepType, speed` | Calculate animation duration based on step type and speed |
| `calculateLevitation` | `time, amplitude, frequency` | Calculate a levitation animation value |
| `calculateRotation` | `progress, startAngle, endAngle` | Calculate a rotation animation value |
| `calculateScale` | `progress, startScale, endScale, easingFn` | Calculate a scale animation value |
| `interpolateColor` | `startColor, endColor, progress` | Calculate a color interpolation |
| `rgbToHex` | `rgb` | Convert RGB color to hex string |
