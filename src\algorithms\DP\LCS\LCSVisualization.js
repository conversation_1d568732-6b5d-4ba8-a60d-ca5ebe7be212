// LCSVisualization.js
// This file contains the 3D visualization for the Longest Common Subsequence algorithm

import React, { useState, useEffect, useRef } from 'react';
import { PerspectiveCamera } from '@react-three/drei';
import { Html } from '@react-three/drei';
import { useSpring, animated } from '@react-spring/three';
import { generateLCSSteps } from './LCSAlgorithm';

// Constants for visualization
const CELL_SIZE = 1.0;
const CELL_SPACING = 0.2;
const CELL_HEIGHT = 0.3;
const GRID_Y_POSITION = 0;

// Cell component for the DP table
const Cell = ({ position, value, color, isHighlighted, isPath, onClick }) => {
  // Animation for highlighting
  const { scale, cellColor } = useSpring({
    scale: isHighlighted ? 1.2 : 1,
    cellColor: color,
    config: { tension: 170, friction: 26 }
  });

  return (
    <animated.group position={position} scale={scale}>
      <animated.mesh castShadow receiveShadow onClick={onClick}>
        <boxGeometry args={[CELL_SIZE, CELL_HEIGHT, CELL_SIZE]} />
        <animated.meshStandardMaterial color={cellColor} metalness={0.3} roughness={0.7} />
      </animated.mesh>
      <Html position={[0, CELL_HEIGHT + 0.1, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: isPath ? 'bold' : 'normal',
          textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
          userSelect: 'none',
          pointerEvents: 'none'
        }}>
          {value}
        </div>
      </Html>
    </animated.group>
  );
};

// Main visualization component
const LCSVisualization = ({
  params,
  state,
  setState,
  step,
  setStep,
  steps,
  setSteps,
  setTotalSteps,
  theme,
  setMovements,
  speed = 5
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [dpTable, setDpTable] = useState(null);
  const [currentCell, setCurrentCell] = useState(null);
  const [pathCells, setPathCells] = useState([]);
  const groupRef = useRef();

  // Colors based on theme
  const colors = {
    cell: theme.palette.mode === 'dark' ? '#2c3e50' : '#ecf0f1',
    highlight: theme.palette.mode === 'dark' ? '#e74c3c' : '#e74c3c',
    match: theme.palette.mode === 'dark' ? '#2ecc71' : '#27ae60',
    mismatch: theme.palette.mode === 'dark' ? '#e67e22' : '#d35400',
    path: theme.palette.mode === 'dark' ? '#3498db' : '#2980b9',
    text: theme.palette.mode === 'dark' ? '#ffffff' : '#333333',
    grid: theme.palette.mode === 'dark' ? '#34495e' : '#bdc3c7',
  };

  // Initialize algorithm steps
  useEffect(() => {
    if (params && params.string1 && params.string2) {
      const { string1, string2 } = params;
      const result = generateLCSSteps(string1, string2);

      // Set steps locally
      const generatedSteps = result.steps;
      setSteps(generatedSteps);

      // Create an empty DP table for initialization (don't show any step yet)
      const m = string1.length;
      const n = string2.length;
      const emptyDp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));
      setDpTable(emptyDp);

      // Update total steps in parent component if the function exists
      if (typeof setTotalSteps === 'function') {
        setTotalSteps(generatedSteps.length);
      }

      // Set movements for the controller if the function exists
      if (typeof setMovements === 'function') {
        setMovements(generatedSteps.map(step => step.movement));
      }

      // Reset step to 0 to ensure we start from the beginning
      if (typeof setStep === 'function') {
        setStep(0);
      }
    }
  }, [params, setSteps, setTotalSteps, setMovements, setStep]);

  // Update visualization based on current step
  useEffect(() => {
    if (!steps || steps.length === 0) {
      return;
    }

    if (step < 0 || step >= steps.length) {
      return;
    }

    const currentStepData = steps[step];
    setCurrentStep(step);

    // Execute the algorithm step
    if (currentStepData.dp) {
      setDpTable(currentStepData.dp);
    }

    if (currentStepData.current) {
      setCurrentCell(currentStepData.current);
    } else {
      setCurrentCell(null);
    }

    if (currentStepData.path) {
      setPathCells(currentStepData.path);
    }

    // If we're in running state, advance to the next step after a delay
    if (state === 'running') {
      // Check if we've reached the last step
      if (step >= steps.length - 1) {
        // If we've reached the last step, set state to completed
        if (typeof setState === 'function') {
          setState('completed');
        }
      } else {
        // Otherwise, advance to the next step
        const timer = setTimeout(() => {
          if (typeof setStep === 'function') {
            setStep(step + 1);
          }
        }, 1000 / speed); // Adjust speed based on the speed setting

        return () => clearTimeout(timer);
      }
    }
  }, [step, steps, state, speed, setStep, setState]);

  // No rotation to keep the table stable

  // If no DP table yet, show loading message
  if (!dpTable) {
    return (
      <group position={[0, 0, 0]}>
        <Html position={[0, 2, 0]} center>
          <div style={{
            color: colors.text,
            fontSize: '18px',
            fontWeight: 'bold',
            textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
            userSelect: 'none',
            pointerEvents: 'none',
            backgroundColor: 'rgba(0,0,0,0.3)',
            padding: '10px 20px',
            borderRadius: '5px'
          }}>
            Initializing LCS Algorithm...
          </div>
        </Html>
      </group>
    );
  }

  // Calculate grid dimensions
  const rows = dpTable.length;
  const cols = dpTable[0]?.length || 0;

  // Calculate grid center offset for centering
  const gridWidth = cols * (CELL_SIZE + CELL_SPACING);
  const gridHeight = rows * (CELL_SIZE + CELL_SPACING);
  const offsetX = -gridWidth / 2 + CELL_SIZE / 2;
  const offsetZ = -gridHeight / 2 + CELL_SIZE / 2;

  return (
    <group ref={groupRef} position={[0, GRID_Y_POSITION, 0]} rotation={[-Math.PI / 6, 0, 0]}>
      {/* Add a camera with a fixed position for better visibility */}
      <PerspectiveCamera makeDefault position={[0, 10, 15]} fov={50} />
      {/* Grid for the DP table */}
      <group position={[offsetX, 0, offsetZ]}>
        {/* String labels for rows and columns */}
        {params?.string1?.split('').map((char, idx) => (
          <Html
            key={`row-${idx}`}
            position={[-CELL_SIZE - CELL_SPACING, 0, idx * (CELL_SIZE + CELL_SPACING) + CELL_SIZE]}
            center
          >
            <div style={{
              color: colors.text,
              fontSize: '14px',
              fontWeight: 'bold',
              textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
              userSelect: 'none',
              pointerEvents: 'none',
              backgroundColor: 'rgba(0,0,0,0.2)',
              padding: '2px 6px',
              borderRadius: '4px'
            }}>
              {char}
            </div>
          </Html>
        ))}

        {params?.string2?.split('').map((char, idx) => (
          <Html
            key={`col-${idx}`}
            position={[idx * (CELL_SIZE + CELL_SPACING) + CELL_SIZE, 0, -CELL_SIZE - CELL_SPACING]}
            center
          >
            <div style={{
              color: colors.text,
              fontSize: '14px',
              fontWeight: 'bold',
              textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
              userSelect: 'none',
              pointerEvents: 'none',
              backgroundColor: 'rgba(0,0,0,0.2)',
              padding: '2px 6px',
              borderRadius: '4px'
            }}>
              {char}
            </div>
          </Html>
        ))}

        {/* DP table cells */}
        {dpTable.map((row, i) => (
          row.map((value, j) => {
            // Determine cell color and highlight status
            const isCurrentCell = currentCell && currentCell.i === i && currentCell.j === j;
            const isPathCell = pathCells.some(cell => cell.i === i && cell.j === j);

            let cellColor = colors.cell;
            if (isPathCell) {
              cellColor = colors.path;
            } else if (isCurrentCell) {
              const stepData = steps[currentStep];
              cellColor = stepData.match ? colors.match : colors.mismatch;
            }

            return (
              <Cell
                key={`cell-${i}-${j}`}
                position={[j * (CELL_SIZE + CELL_SPACING), 0, i * (CELL_SIZE + CELL_SPACING)]}
                value={value}
                color={cellColor}
                isHighlighted={isCurrentCell}
                isPath={isPathCell}
              />
            );
          })
        ))}
      </group>

      {/* Current step information */}
      <Html position={[0, 8, 0]} center>
        <div style={{
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
          color: theme.palette.mode === 'dark' ? '#fff' : '#333',
          padding: '10px',
          borderRadius: '5px',
          width: '400px',
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          fontWeight: 'bold',
          boxShadow: '0 0 10px rgba(0,0,0,0.2)',
          pointerEvents: 'none',
          border: `2px solid ${theme.palette.primary.main}`
        }}>
          {steps[currentStep]?.movement || 'Initializing...'}
        </div>
      </Html>

      {/* Ground plane for better depth perception */}
      <mesh
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -2, 0]}
        receiveShadow
      >
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial
          color={theme.palette.mode === 'dark' ? '#1e272e' : '#dfe6e9'}
          roughness={0.8}
          metalness={0.2}
          transparent
          opacity={0.6}
        />
      </mesh>
    </group>
  );
};

export default LCSVisualization;
