// StepBoard2D.js
// A 2D step board component for displaying algorithm steps

import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, IconButton, useTheme } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { motion } from 'framer-motion';

/**
 * A 2D step board component for displaying algorithm steps
 *
 * @param {Object} props - Component props
 * @param {Array} props.steps - Array of step descriptions
 * @param {number} props.currentStep - Current step index
 * @param {function} props.onStepChange - Function to call when step changes
 * @param {Object} props.sx - Additional styles
 */
const StepBoard2D = ({
  steps = [],
  currentStep = 0,
  onStepChange = () => {},
  sx = {}
}) => {
  const theme = useTheme();
  const [step, setStep] = useState(currentStep);

  // Update step when currentStep prop changes
  useEffect(() => {
    setStep(currentStep);
  }, [currentStep]);

  // Handle step change
  const handleStepChange = (newStep) => {
    if (newStep >= 0 && newStep < steps.length) {
      setStep(newStep);
      onStepChange(newStep);
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 2,
        borderRadius: 2,
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        maxWidth: '100%',
        ...sx
      }}
    >
      {/* Background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.05,
          backgroundImage: `radial-gradient(${theme.palette.primary.main} 2px, transparent 2px)`,
          backgroundSize: '30px 30px',
          zIndex: 0
        }}
      />

      {/* Step content */}
      <motion.div
        key={step}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        style={{
          position: 'relative',
          zIndex: 1,
          minHeight: '120px',
          padding: '0 10px'
        }}
      >
        <Typography
          variant="body1"
          sx={{
            mb: 2,
            fontWeight: 600,
            fontFamily: '"Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif',
            fontSize: '1.05rem',
            lineHeight: 1.6,
            letterSpacing: '0.01em',
            color: theme.palette.mode === 'dark' ? '#f5f5f5' : '#333333',
            textShadow: theme.palette.mode === 'dark' ? '0 1px 2px rgba(0,0,0,0.3)' : 'none',
          }}
        >
          {steps[step] || 'No steps available'}
        </Typography>
      </motion.div>

      {/* Navigation controls */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 2,
          position: 'relative',
          zIndex: 1
        }}
      >
        <IconButton
          onClick={() => handleStepChange(step - 1)}
          disabled={step <= 0}
          color="primary"
          size="small"
        >
          <ArrowBackIcon />
        </IconButton>

        <Typography variant="caption" color="text.secondary">
          Step {step + 1} of {steps.length}
        </Typography>

        <IconButton
          onClick={() => handleStepChange(step + 1)}
          disabled={step >= steps.length - 1}
          color="primary"
          size="small"
        >
          <ArrowForwardIcon />
        </IconButton>
      </Box>
    </Paper>
  );
};

export default StepBoard2D;
