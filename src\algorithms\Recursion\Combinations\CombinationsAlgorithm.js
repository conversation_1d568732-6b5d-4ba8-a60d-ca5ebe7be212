// CombinationsAlgorithm.js
// Implementation of the Combinations algorithm using backtracking

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for generating all combinations of a given set
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateCombinationsSteps = (params) => {
  console.log('generateCombinationsSteps called with params:', params);
  const { elements = ['A', 'B', 'C', 'D'], k = 2, useNumbers = false } = params;
  
  // If useNumbers is true, convert elements to numbers 1 to n
  const inputElements = useNumbers 
    ? Array.from({ length: elements.length }, (_, i) => (i + 1).toString())
    : elements;
  
  const steps = [];
  const combinations = [];
  
  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize combination generator with elements [${inputElements.join(', ')}] and k = ${k}.`,
    elements: [...inputElements],
    k,
    currentCombination: [],
    combinations: [],
    currentIndex: -1,
    depth: 0,
    progressStep: 'init',
    pseudocodeLine: 1
  });
  
  // Generate combinations
  generateCombinations(inputElements, k, 0, [], steps, combinations, 0);
  
  // Add final step
  steps.push({
    type: 'complete',
    message: `Generated all ${combinations.length} combinations of size ${k}.`,
    elements: [...inputElements],
    k,
    currentCombination: [],
    combinations: [...combinations],
    currentIndex: -1,
    depth: 0,
    progressStep: 'complete',
    pseudocodeLine: 0
  });
  
  return { steps, combinations };
};

/**
 * Generate all combinations using backtracking
 * @param {Array} elements - Array of elements to combine
 * @param {Number} k - Size of each combination
 * @param {Number} startIndex - Starting index for the current recursion
 * @param {Array} currentCombination - Current combination being built
 * @param {Array} steps - Array to store steps
 * @param {Array} combinations - Array to store all combinations
 * @param {Number} depth - Current recursion depth
 */
const generateCombinations = (elements, k, startIndex, currentCombination, steps, combinations, depth) => {
  // If the current combination is complete
  if (currentCombination.length === k) {
    // Add the combination to the list
    combinations.push([...currentCombination]);
    
    steps.push({
      type: 'found',
      message: `Found combination: [${currentCombination.join(', ')}].`,
      elements: [...elements],
      k,
      currentCombination: [...currentCombination],
      combinations: [...combinations],
      currentIndex: -1,
      depth,
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    return;
  }
  
  // If we've gone through all elements or can't form a combination of size k
  if (startIndex >= elements.length || elements.length - startIndex < k - currentCombination.length) {
    steps.push({
      type: 'backtrack',
      message: `Cannot form a combination of size ${k} from the remaining elements. Backtracking.`,
      elements: [...elements],
      k,
      currentCombination: [...currentCombination],
      combinations: [...combinations],
      currentIndex: startIndex,
      depth,
      progressStep: 'process',
      pseudocodeLine: 5
    });
    
    return;
  }
  
  // Include the current element
  currentCombination.push(elements[startIndex]);
  
  steps.push({
    type: 'include',
    message: `Including element ${elements[startIndex]} at position ${currentCombination.length - 1}.`,
    elements: [...elements],
    k,
    currentCombination: [...currentCombination],
    combinations: [...combinations],
    currentIndex: startIndex,
    depth,
    progressStep: 'process',
    pseudocodeLine: 8
  });
  
  // Recursively generate combinations with this element included
  generateCombinations(
    elements, 
    k, 
    startIndex + 1, 
    currentCombination, 
    steps, 
    combinations,
    depth + 1
  );
  
  // Exclude the current element (backtrack)
  currentCombination.pop();
  
  steps.push({
    type: 'exclude',
    message: `Excluding element ${elements[startIndex]} and trying next element.`,
    elements: [...elements],
    k,
    currentCombination: [...currentCombination],
    combinations: [...combinations],
    currentIndex: startIndex,
    depth,
    progressStep: 'process',
    pseudocodeLine: 11
  });
  
  // Recursively generate combinations with this element excluded
  generateCombinations(
    elements, 
    k, 
    startIndex + 1, 
    currentCombination, 
    steps, 
    combinations,
    depth
  );
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const CombinationsAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Pseudocode for Combinations algorithm
  const pseudocode = [
    { line: 'function generateCombinations(elements, k, startIndex, currentCombination):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // If the current combination is complete', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if currentCombination.length == k:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        // Add the combination to the result list', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '        combinations.add(currentCombination.copy())', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '        return', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    // Include the current element', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    currentCombination.push(elements[startIndex])', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    // Recursively generate combinations with this element included', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '    generateCombinations(elements, k, startIndex + 1, currentCombination)', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '    // Exclude the current element (backtrack)', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '    currentCombination.pop()', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '    // Recursively generate combinations with this element excluded', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '    generateCombinations(elements, k, startIndex + 1, currentCombination)', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
  ];

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default CombinationsAlgorithm;
