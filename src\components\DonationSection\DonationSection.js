import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Divider,
  useTheme,
  Collapse,
} from '@mui/material';
import FavoriteIcon from '@mui/icons-material/Favorite';
import LocalCafeIcon from '@mui/icons-material/LocalCafe';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

const DonationSection = () => {
  const theme = useTheme();
  const [expanded, setExpanded] = React.useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  return (
    <Paper
      elevation={1}
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
        border: '1px solid',
        borderColor: theme.palette.divider,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.palette.mode === 'dark' ? '0 0 8px rgba(255, 255, 255, 0.1)' : '0 0 8px rgba(0, 0, 0, 0.1)',
          borderColor: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100',
        },
      }}
    >
      {/* Always visible section with Buy Me a Chai button */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 2,
        }}
      >
        <Button
          variant="contained"
          startIcon={<LocalCafeIcon />}
          endIcon={<FavoriteIcon />}
          onClick={handleExpandClick}
          sx={{
            borderRadius: 4,
            px: 3,
            py: 1,
            bgcolor: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100',
            '&:hover': {
              bgcolor: theme.palette.mode === 'dark' ? '#fb8c00' : '#d84315',
            },
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            width: { xs: '100%', sm: 'auto' },
          }}
        >
          Buy Me a Chai ☕
        </Button>
      </Box>

      <Collapse in={expanded}>
        <Divider />
        <Box sx={{ p: 3, position: 'relative' }}>
          {/* Shadow overlay to indicate scrollability */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '40px',
              background: `linear-gradient(to top, ${theme.palette.background.paper}, transparent)`,
              pointerEvents: 'none',
              zIndex: 1,
              opacity: 0.8,
              borderBottomLeftRadius: '8px',
              borderBottomRightRadius: '8px',
              animation: 'pulseOpacity 2s infinite ease-in-out',
              '@keyframes pulseOpacity': {
                '0%': { opacity: 0.5 },
                '50%': { opacity: 0.9 },
                '100%': { opacity: 0.5 },
              },
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <KeyboardArrowDownIcon
              sx={{
                color: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100',
                fontSize: '28px',
                opacity: 0.7,
                animation: 'bounce 1.5s infinite ease-in-out',
                '@keyframes bounce': {
                  '0%, 100%': { transform: 'translateY(0)' },
                  '50%': { transform: 'translateY(-5px)' },
                },
              }}
            />
          </Box>

          {/* Scrollable content with QR code at the top */}
          <Box sx={{
            maxHeight: '400px',
            overflowY: 'auto',
            scrollbarWidth: 'none',  // Firefox
            msOverflowStyle: 'none',  // IE/Edge
            '&::-webkit-scrollbar': {
              display: 'none',  // Chrome/Safari/Opera
            },
            position: 'relative',
            pb: 2, // Add padding at bottom to ensure content isn't hidden by shadow
          }}>
            {/* QR Code at the top of scrollable content */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                p: 2,
                mb: 3,
                border: '1px dashed',
                borderColor: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100',
                borderRadius: 2,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 153, 0, 0.05)' : 'rgba(230, 81, 0, 0.03)',
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, color: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100' }}>
                Scan to Support
              </Typography>

              <Box
                sx={{
                  width: 180,
                  height: 180,
                  bgcolor: 'white',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 2,
                  mb: 1,
                  p: 1,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                }}
              >
                <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center' }}>
                  QR Code for payment will appear here
                </Typography>
              </Box>

              <Typography variant="caption" sx={{ textAlign: 'center', color: 'text.secondary' }}>
                Every contribution matters
              </Typography>
            </Box>

            <Typography variant="h6" sx={{ mb: 2, color: theme.palette.mode === 'dark' ? '#ff9800' : '#e65100', fontWeight: 600, textAlign: 'center' }}>
              Namaste! 🙏
            </Typography>

            <Typography variant="body2" sx={{ mb: 2, textAlign: 'center' }}>
              This algorithm visualizer was created to help students and enthusiasts learn computer science fundamentals through interactive visualizations.
            </Typography>

            <Box sx={{
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 153, 0, 0.1)' : 'rgba(230, 81, 0, 0.05)',
              p: 2,
              borderRadius: 2,
              borderLeft: `4px solid ${theme.palette.mode === 'dark' ? '#ff9800' : '#e65100'}`,
              mb: 2
            }}>
              <Typography variant="body2" sx={{ fontStyle: 'italic', textAlign: 'center' }}>
                "Your chai donation helps fuel the development of more educational tools and keeps this project growing."
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ mb: 1.5, textAlign: 'center' }}>
              Your support allows me to dedicate more time to creating free educational resources. <span style={{ fontWeight: 'bold' }}>Dhanyavaad (Thank you)!</span> ❤️
            </Typography>
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

export default DonationSection;
