// SelectionSortConfig.js
// Comprehensive configuration for Selection Sort visualization

const SelectionSortConfig = {
  // ==================== MAIN ARRAY CONFIGURATION ====================
  mainArray: {
    // Bar dimensions and positioning
    bars: {
      width: 0.6,                    // Width of each main array bar
      spacing: 0.3,                  // Spacing between main array bars
      maxHeight: 3.8,                // Maximum height of main array bars
      baseOffset: [0, 0, 0],         // Offset from base platform position [x, y, z]
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance

      // Bar geometry
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties
      material: {
        roughness: 0.6,              // Material roughness (0 = mirror, 1 = rough)
        metalness: 0.2,              // Material metalness (0 = non-metal, 1 = metal)
        opacity: 1.0,                // Material opacity
        transparent: false,          offset: [0, 0.3, 0]// Enable transparency
      },

      // Base platform under each bar
      base: {
        enabled: true,               // Enable/disable base platform
        height: 0.05,                // Height of base platform
        widthScale: 1.0,             // Width scale factor for base (1.0 = full width)
        depthScale: 1.0,             // Depth scale factor for base (1.0 = full depth)
        material: {
          roughness: 0.8,            // Base material roughness
          metalness: 0.2,            // Base material metalness
          opacity: 0.7,              // Base opacity
          transparent: false,        // Enable transparency
        },
      },
    },

    // Value labels configuration
    valueLabels: {
      enabled: true,                 // Enable/disable value labels
      offset: [0, 0.3, 0],          // Offset from bar top [x, y, z]
      fontSize: '0.6rem',            // Font size for value labels
      fontWeight: 'bold',            // Font weight
      padding: {
        horizontal: 0.5,             // Horizontal padding inside label
        vertical: 0.1,               // Vertical padding inside label
      },
      borderRadius: 1.5,             // Border radius factor (theme.shape.borderRadius / this) - BOXY STYLE like QuickSort
      minWidth: '16px',              // Minimum width of label container
      elevation: 1,                  // Material-UI elevation
    },

    // Index labels configuration
    indexLabels: {
      enabled: true,                 // Enable/disable index labels
      offset: [0, 0.1, 0.4],         // Offset from bar base [x, y, z]
      fontSize: '10px',              // Font size for index labels
      fontWeight: 'normal',          // Font weight
      size: {
        width: '20px',               // Width of circular index label
        height: '20px',              // Height of circular index label
      },
      elevation: 1,                  // Material-UI elevation
    },
  },

  // ==================== SELECTION SORT SPECIFIC CONFIGURATION ====================
  selectionSort: {
    // Current position indicator (position being filled)
    currentPosition: {
      enabled: true,                 // Enable current position highlighting
      indicator: {
        enabled: true,               // Show "CURRENT" indicator
        text: 'CURRENT',             // Text to display
        offset: [0, 1.2, 0],        // Offset from bar top [x, y, z]
        fontSize: '0.45rem',         // Font size
        fontWeight: 'bold',          // Font weight
        padding: {
          horizontal: 0.6,           // Horizontal padding
          vertical: 0.15,            // Vertical padding
        },
        borderRadius: 0.3,           // Border radius factor
        elevation: 2,                // Material-UI elevation
      },
    },

    // Minimum element indicator
    minimumElement: {
      enabled: true,                 // Enable minimum element highlighting
      indicator: {
        enabled: true,               // Show "MIN" indicator
        text: 'MIN',                 // Text to display
        offset: [0, 1.2, 0],        // Offset from bar top [x, y, z]
        fontSize: '0.45rem',         // Font size
        fontWeight: 'bold',          // Font weight
        padding: {
          horizontal: 0.6,           // Horizontal padding
          vertical: 0.15,            // Vertical padding
        },
        borderRadius: 0.3,           // Border radius factor
        elevation: 2,                // Material-UI elevation
      },
    },

    // Comparison arrows
    comparisonArrows: {
      enabled: true,                 // Enable comparison arrows
      height: 0.8,                   // Height above bars
      arrowSize: 0.15,               // Size of arrow heads
      lineWidth: 0.05,               // Width of arrow lines
      opacity: 0.9,                  // Arrow opacity
      animationDuration: 300,        // Animation duration for arrows
    },
  },

  // ==================== BASE PLATFORM CONFIGURATION ====================
  basePlatform: {
    dimensions: {
      height: 0.2,                   // Height of the base platform
      lengthPadding: {
        left: 1,                     // Padding on the left side
        right: 1,                    // Padding on the right side
      },
      depth: 3,                      // Depth of the base platform
    },
    position: [0, -2, 0],            // MASTER position - controls entire main array group [x, y, z]
    material: {
      roughness: 0.8,                // Material roughness
      metalness: 0.1,                // Material metalness
    },
  },

  // ==================== CAMERA CONFIGURATION ====================
  camera: {
    position: [0, 3, 12],            // Default camera position [x, y, z]
    lookAt: [0, 0, 0],               // Camera look-at point [x, y, z]
    fov: 55,                         // Field of view in degrees
    near: 0.1,                       // Near clipping plane
    far: 1000,                       // Far clipping plane

    // Dynamic positioning based on array size
    dynamicPositioning: {
      enabled: true,                 // Enable dynamic camera positioning
      minDistance: 8,                // Minimum camera distance
      paddingFactor: 2,              // Padding factor for camera distance calculation
      heightOffset: 3,               // Additional height offset
    },
  },

  // ==================== LIGHTING CONFIGURATION ====================
  lighting: {
    ambient: {
      intensity: 0.6,                // Ambient light intensity
      color: '#ffffff',              // Ambient light color
    },
    directional: {
      position: [10, 10, 5],         // Directional light position [x, y, z]
      intensity: 1.0,                // Directional light intensity
      color: '#ffffff',              // Directional light color
      castShadow: true,              // Whether directional light casts shadows
    },
    // Additional lights can be added here
    pointLights: [],                 // Array of point light configurations
    spotLights: [],                  // Array of spot light configurations
  },

  // ==================== UI ELEMENTS CONFIGURATION ====================

  // Step board configuration (shows current step information)
  stepBoard: {
    enabled: true,                   // Enable/disable step board
    position: [0, 5, 0.5],          // Position of the step board [x, y, z]
    dimensions: {
      width: 12,                     // Width of the step board
      height: 1.5,                   // Height of the step board
      depth: 0.1,                    // Depth of the step board
    },
    material: {
      opacity: 0.9,                  // Material opacity
      transparent: true,             // Enable transparency
    },
    text: {
      fontSize: 'h6',                // Material-UI typography variant
      fontWeight: 'bold',            // Font weight
      align: 'center',               // Text alignment
      padding: 2,                    // Padding around text
    },
    border: {
      enabled: true,                 // Enable border
      width: 2,                      // Border width
      radius: 2,                     // Border radius
    },
    elevation: 3,                    // Material-UI elevation
  },

  // Color legend configuration
  colorLegend: {
    enabled: true,                   // Enable/disable color legend
    position: [0, -3.5, 0.5],       // Position of the color legend [x, y, z]
    itemSpacing: 2.5,                // Spacing between legend items

    // Individual legend item configuration
    items: {
      dimensions: {
        width: 0.8,                  // Width of color sample
        height: 0.4,                 // Height of color sample
        depth: 0.2,                  // Depth of color sample
      },
      material: {
        roughness: 0.5,              // Material roughness
        metalness: 0.3,              // Material metalness
      },
      label: {
        fontSize: 'caption',         // Material-UI typography variant
        fontWeight: 'normal',        // Font weight
        offset: [0, -0.8, 0],       // Offset from color sample [x, y, z]
        padding: {
          horizontal: 0.5,           // Horizontal padding
          vertical: 0.2,             // Vertical padding
        },
        borderRadius: 1,             // Border radius factor
        elevation: 1,                // Material-UI elevation
      },
    },

    // Legend items definition - Only colors actually used in SelectionSort
    legendItems: [
      { colorKey: 'bar', label: 'Default' },
      { colorKey: 'comparing', label: 'Comparing' },
      { colorKey: 'current', label: 'Current Position' },
      { colorKey: 'minimum', label: 'Minimum Element' },
      { colorKey: 'swapping', label: 'Swapping' },
      { colorKey: 'sorted', label: 'Sorted' }
    ],
  },

  // ==================== ANIMATION CONFIGURATION ====================
  animation: {
    // Timing configuration
    timing: {
      baseDuration: 1000,             // Base animation duration in ms
      speedFactor: 1.0,              // Speed multiplier (higher = faster)

      // Speed-based delays (calculated dynamically)
      delays: {
        slow: 3000,                  // Delay for slow speed (1-3)
        medium: 1500,                // Delay for medium speed (4-7)
        fast: 400,                   // Delay for fast speed (8-10)
      },
    },

    // Easing functions
    easing: {
      default: 'ease-in-out',        // Default easing function
      bars: 'ease-out',              // Easing for bar movements
      labels: 'ease-in-out',         // Easing for label animations
      camera: 'ease-in-out',         // Easing for camera movements
    },

    // Animation types
    types: {
      swap: {
        enabled: true,               // Enable swap animations for SelectionSort
        duration: 800,               // Duration for swap animations
        height: 1.5,                 // Height of swap arc
      },
      highlight: {
        enabled: true,               // Enable highlight animations
        duration: 300,               // Duration for highlight changes
        pulseEffect: true,           // Enable pulse effect for highlights
      },
      comparison: {
        enabled: true,               // Enable comparison animations
        duration: 500,               // Duration for comparison highlights
        arrowEnabled: true,          // Enable arrows for SelectionSort
      },
    },
  },

  // ==================== VISUAL SETTINGS ====================
  visual: {
    // Label visibility
    labels: {
      values: {
        enabled: true,               // Show value labels on bars
        adaptiveVisibility: false,   // Disable adaptive visibility temporarily
        visibilityThreshold: 0.0,    // Hide values when scale factor is below this
      },
      indices: {
        enabled: true,               // Show index labels on bars
        adaptiveVisibility: true,    // Hide labels for small bars
        visibilityThreshold: 0.0,    // Hide indices when scale factor is below this
      },
    },

    // Effects
    effects: {
      shadows: true,                 // Enable/disable shadows
      reflections: false,            // Enable/disable reflections
      bloom: false,                  // Enable/disable bloom effect
      antialiasing: true,            // Enable/disable antialiasing
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation animation
      disableDuringSimulation: true, // Disable levitation when algorithm is running
      amplitude: 0.1,                // Height of levitation movement (Y-axis)
      frequency: 1.0,                // Speed of levitation oscillation

      // Multi-axis movement
      movement: {
        y: {
          enabled: true,             // Enable Y-axis (vertical) movement
          amplitude: 0.1,            // Vertical movement amplitude
          frequency: 1.0,            // Vertical movement frequency
        },
        x: {
          enabled: false,            // Enable X-axis (horizontal) movement
          amplitude: 0.05,           // Horizontal movement amplitude
          frequency: 0.8,            // Horizontal movement frequency
        },
        z: {
          enabled: false,            // Enable Z-axis (depth) movement
          amplitude: 0.03,           // Depth movement amplitude
          frequency: 0.6,            // Depth movement frequency
        },
      },

      // Rotation effects
      rotation: {
        enabled: true,               // Enable rotation during levitation
        x: {
          enabled: true,             // Enable X-axis rotation
          amplitude: 0.01,           // X rotation amplitude (radians)
          frequency: 0.3,            // X rotation frequency
        },
        y: {
          enabled: true,             // Enable Y-axis rotation
          amplitude: 0.02,           // Y rotation amplitude (radians)
          frequency: 0.5,            // Y rotation frequency
        },
        z: {
          enabled: false,            // Enable Z-axis rotation
          amplitude: 0.005,          // Z rotation amplitude (radians)
          frequency: 0.4,            // Z rotation frequency
        },
      },

      // Advanced settings
      staggered: true,               // Enable staggered animation effects
      staggerDelay: 0.2,             // Delay between elements for staggered effect
      smoothTransition: true,        // Smooth transition when enabling/disabling
      transitionDuration: 1000,      // Duration for smooth transitions (ms)
    },
  },

  // ==================== COLOR CONFIGURATION ====================
  colors: {
    // Main color palette
    palette: {
      // Bar colors for different states
      bar: '#42a5f5',                // Default bar color (blue)
      comparing: '#ff9800',          // Comparing elements color (orange)
      swapping: '#f44336',           // Swapping elements color (red)
      current: '#9c27b0',            // Current position color (purple)
      minimum: '#e91e63',            // Minimum element color (pink)
      sorted: '#4caf50',             // Sorted elements color (green)

      // Platform and environment colors
      base: '#424242',               // Base platform color
      ground: '#303030',             // Ground color
      background: '#121212',         // Background color

      // UI element colors
      stepBoard: '#1e1e1e',          // Step board background
      legend: '#1e1e1e',             // Legend background
      text: '#ffffff',               // Text color
      border: '#555555',             // Border color
    },

    // Theme-specific overrides
    themes: {
      light: {
        // Light theme color overrides
        bar: '#1976d2',              // Darker blue for light theme
        comparing: '#f57c00',        // Darker orange for light theme
        swapping: '#d32f2f',         // Darker red for light theme
        current: '#7b1fa2',          // Darker purple for light theme
        minimum: '#c2185b',          // Darker pink for light theme
        sorted: '#388e3c',           // Darker green for light theme
        base: '#f0f0f0',             // Light base color
        ground: '#fafafa',           // Light ground color
        background: '#ffffff',       // Light background
        stepBoard: '#ffffff',        // Light step board
        legend: '#ffffff',           // Light legend
        text: '#212121',             // Dark text for light theme
        border: '#e0e0e0',           // Light border
      },
      dark: {
        // Dark theme color overrides (use palette defaults)
        bar: '#42a5f5',
        comparing: '#ff9800',
        swapping: '#f44336',
        current: '#9c27b0',
        minimum: '#e91e63',
        sorted: '#4caf50',
        base: '#424242',
        ground: '#303030',
        background: '#121212',
        stepBoard: '#1e1e1e',
        legend: '#1e1e1e',
        text: '#ffffff',
        border: '#555555',
      },
    },

    // Opacity settings
    opacity: {
      bars: {
        default: 1.0,                // Default bar opacity
        comparing: 1.0,              // Comparing bar opacity
        swapping: 1.0,               // Swapping bar opacity
        current: 1.0,                // Current bar opacity
        minimum: 1.0,                // Minimum bar opacity
        sorted: 1.0,                 // Sorted bar opacity
      },
      platform: 0.8,                // Platform opacity
      labels: 1.0,                   // Label opacity
      ui: 0.9,                       // UI element opacity
    },
  },

  // ==================== ALGORITHM-SPECIFIC CONFIGURATION ====================
  algorithm: {
    // Array generation
    array: {
      generation: {
        defaultSize: 10,             // Default array size
        minSize: 3,                  // Minimum array size
        maxSize: 20,                 // Maximum array size
        valueRange: {
          min: 1,                    // Minimum value in array
          max: 999,                  // Maximum value in array
        },
      },

      // Custom array validation
      validation: {
        enforceRange: true,          // Enforce min/max range for custom arrays
        warnOnLargeValues: true,     // Warn when values are very large
        largeValueThreshold: 1000,   // Threshold for large value warning
      },
    },

    // Selection sort specific settings
    selection: {
      showCurrentPosition: true,     // Show current position being filled
      showMinimumElement: true,      // Show minimum element in unsorted portion
      showComparisons: true,         // Show comparison operations
      animateSwaps: true,            // Animate swap operations
    },

    // Step generation
    steps: {
      showIntermediateSteps: true,   // Show all intermediate steps
      showComparisons: true,         // Show comparison steps
      showSwaps: true,               // Show swap operation steps
      combineOperations: false,      // Combine consecutive operations
    },
  },
};

export default SelectionSortConfig;
