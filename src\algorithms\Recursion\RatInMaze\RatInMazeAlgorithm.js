// RatInMazeAlgorithm.js
// Implementation of Rat in a Maze algorithm using backtracking

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for solving the Rat in a Maze problem using backtracking
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateRatInMazeSteps = (params) => {
  console.log('generateRatInMazeSteps called with params:', params);
  const { mazeSize = 4, customMaze = null, allowDiagonal = false } = params;
  const steps = [];
  const solutions = [];

  // Create a maze of the specified size or use the custom maze
  let maze;
  if (customMaze) {
    maze = JSON.parse(JSON.stringify(customMaze)); // Deep copy
  } else {
    maze = generateRandomMaze(mazeSize);
  }

  // Create a solution matrix initialized with 0's
  const solution = Array(maze.length).fill().map(() => Array(maze[0].length).fill(0));
  
  // Mark the starting position as part of the path
  solution[0][0] = 1;

  // Add initial step
  steps.push({
    type: 'init',
    message: 'Initialize Rat in a Maze solver with the given maze.',
    maze: JSON.parse(JSON.stringify(maze)),
    solution: JSON.parse(JSON.stringify(solution)),
    currentRow: 0,
    currentCol: 0,
    isValid: true,
    solutions: [],
    progressStep: 'init',
    pseudocodeLine: 1
  });

  // Solve the maze
  solveMaze(maze, 0, 0, solution, steps, solutions, allowDiagonal);

  // Add final step
  if (solutions.length > 0) {
    steps.push({
      type: 'complete',
      message: 'Maze solved successfully!',
      maze: JSON.parse(JSON.stringify(maze)),
      solution: JSON.parse(JSON.stringify(solutions[0])),
      currentRow: -1,
      currentCol: -1,
      isValid: true,
      solutions: JSON.parse(JSON.stringify(solutions)),
      progressStep: 'complete',
      pseudocodeLine: 0
    });
  } else {
    steps.push({
      type: 'no_solution',
      message: 'No solution exists for this maze.',
      maze: JSON.parse(JSON.stringify(maze)),
      solution: JSON.parse(JSON.stringify(solution)),
      currentRow: -1,
      currentCol: -1,
      isValid: false,
      solutions: [],
      progressStep: 'complete',
      pseudocodeLine: 0
    });
  }

  return { steps, solutions };
};

/**
 * Generate a random maze of the specified size
 * @param {Number} size - Size of the maze
 * @returns {Array} - 2D array representing the maze
 */
const generateRandomMaze = (size) => {
  const maze = Array(size).fill().map(() => Array(size).fill(0));
  
  // Ensure start and end positions are open
  maze[0][0] = 1; // Start position
  maze[size-1][size-1] = 1; // End position
  
  // Randomly fill the maze with obstacles (0) and paths (1)
  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      // Skip start and end positions
      if ((i === 0 && j === 0) || (i === size-1 && j === size-1)) {
        continue;
      }
      
      // 70% chance of being a path, 30% chance of being an obstacle
      maze[i][j] = Math.random() < 0.7 ? 1 : 0;
    }
  }
  
  return maze;
};

/**
 * Check if a move is valid
 * @param {Array} maze - The maze
 * @param {Number} row - Row index
 * @param {Number} col - Column index
 * @returns {Boolean} - Whether the move is valid
 */
const isValidMove = (maze, row, col) => {
  // Check if the position is within the maze and is a path (1)
  return (
    row >= 0 && row < maze.length &&
    col >= 0 && col < maze[0].length &&
    maze[row][col] === 1
  );
};

/**
 * Solve the maze using backtracking
 * @param {Array} maze - The maze
 * @param {Number} row - Current row
 * @param {Number} col - Current column
 * @param {Array} solution - Current solution path
 * @param {Array} steps - Array to store steps
 * @param {Array} solutions - Array to store solutions
 * @param {Boolean} allowDiagonal - Whether diagonal moves are allowed
 * @returns {Boolean} - Whether a solution was found
 */
const solveMaze = (maze, row, col, solution, steps, solutions, allowDiagonal) => {
  // If we've reached the destination
  if (row === maze.length - 1 && col === maze[0].length - 1) {
    // Add the solution to the solutions array
    solutions.push(JSON.parse(JSON.stringify(solution)));
    
    steps.push({
      type: 'found',
      message: `Found a path to the destination at (${row + 1}, ${col + 1})!`,
      maze: JSON.parse(JSON.stringify(maze)),
      solution: JSON.parse(JSON.stringify(solution)),
      currentRow: row,
      currentCol: col,
      isValid: true,
      solutions: [],
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    return true;
  }
  
  // Define possible moves: right, down, left, up (and diagonals if allowed)
  const moves = [
    { row: 0, col: 1, direction: 'right' },  // Right
    { row: 1, col: 0, direction: 'down' },   // Down
    { row: 0, col: -1, direction: 'left' },  // Left
    { row: -1, col: 0, direction: 'up' }     // Up
  ];
  
  // Add diagonal moves if allowed
  if (allowDiagonal) {
    moves.push(
      { row: 1, col: 1, direction: 'down-right' },   // Down-Right
      { row: 1, col: -1, direction: 'down-left' },   // Down-Left
      { row: -1, col: 1, direction: 'up-right' },    // Up-Right
      { row: -1, col: -1, direction: 'up-left' }     // Up-Left
    );
  }
  
  // Try each possible move
  for (const move of moves) {
    const newRow = row + move.row;
    const newCol = col + move.col;
    
    // Check if the move is valid
    if (isValidMove(maze, newRow, newCol) && solution[newRow][newCol] === 0) {
      // Mark the new position as part of the path
      solution[newRow][newCol] = 1;
      
      steps.push({
        type: 'move',
        message: `Moving ${move.direction} to position (${newRow + 1}, ${newCol + 1}).`,
        maze: JSON.parse(JSON.stringify(maze)),
        solution: JSON.parse(JSON.stringify(solution)),
        currentRow: newRow,
        currentCol: newCol,
        isValid: true,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 7
      });
      
      // Recursively solve from the new position
      if (solveMaze(maze, newRow, newCol, solution, steps, solutions, allowDiagonal)) {
        return true;
      }
      
      // If we get here, this path didn't work, so backtrack
      solution[newRow][newCol] = 0;
      
      steps.push({
        type: 'backtrack',
        message: `Backtracking from position (${newRow + 1}, ${newCol + 1}) as it doesn't lead to a solution.`,
        maze: JSON.parse(JSON.stringify(maze)),
        solution: JSON.parse(JSON.stringify(solution)),
        currentRow: row,
        currentCol: col,
        isValid: false,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 12
      });
    } else if (isValidMove(maze, newRow, newCol) && solution[newRow][newCol] === 1) {
      // This position is already part of our current path
      steps.push({
        type: 'skip',
        message: `Position (${newRow + 1}, ${newCol + 1}) is already part of our path. Skipping.`,
        maze: JSON.parse(JSON.stringify(maze)),
        solution: JSON.parse(JSON.stringify(solution)),
        currentRow: row,
        currentCol: col,
        isValid: false,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 6
      });
    } else {
      // This move is invalid
      steps.push({
        type: 'invalid',
        message: `Cannot move ${move.direction} to position (${newRow + 1}, ${newCol + 1}) as it's either outside the maze or blocked.`,
        maze: JSON.parse(JSON.stringify(maze)),
        solution: JSON.parse(JSON.stringify(solution)),
        currentRow: row,
        currentCol: col,
        isValid: false,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 6
      });
    }
  }
  
  // If we've tried all moves and none worked, return false
  return false;
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const RatInMazeAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Pseudocode for Rat in a Maze
  const pseudocode = [
    { line: 'function solveMaze(maze, row, col, solution):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // If we\'ve reached the destination', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if row == maze.length - 1 and col == maze[0].length - 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return true', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    // Try each possible move (right, down, left, up)', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    for each move in possibleMoves:', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '        newRow = row + move.row', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '        newCol = col + move.col', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '        if isValidMove(maze, newRow, newCol) and solution[newRow][newCol] == 0:', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '            // Mark this cell as part of the solution path', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '            solution[newRow][newCol] = 1', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            // Recursively solve from this new position', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '            if solveMaze(maze, newRow, newCol, solution):', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '                return true', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '            // If we get here, this path didn\'t work, so backtrack', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '            solution[newRow][newCol] = 0', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '    // If we\'ve tried all moves and none worked, return false', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
    { line: '    return false', highlight: currentAlgorithmStep?.pseudocodeLine === 18 },
    { line: '', highlight: false },
    { line: 'function isValidMove(maze, row, col):', highlight: currentAlgorithmStep?.pseudocodeLine === 19 },
    { line: '    // Check if the position is within the maze and is a path (1)', highlight: currentAlgorithmStep?.pseudocodeLine === 20 },
    { line: '    return row >= 0 and row < maze.length and', highlight: currentAlgorithmStep?.pseudocodeLine === 21 },
    { line: '           col >= 0 and col < maze[0].length and', highlight: currentAlgorithmStep?.pseudocodeLine === 22 },
    { line: '           maze[row][col] == 1', highlight: currentAlgorithmStep?.pseudocodeLine === 23 },
  ];

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default RatInMazeAlgorithm;
