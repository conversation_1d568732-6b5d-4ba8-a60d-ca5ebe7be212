// CoinChangeController.js
// This component provides the controls for Coin Change algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, TextField, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// Import algorithm functions
import CoinChangeAlgorithm from './CoinChangeAlgorithm';

const CoinChangeController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [coins, setCoins] = useState(params?.coins || [1, 2, 5]);
    const [amount, setAmount] = useState(params?.amount || 11);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { coins, amount });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { coins, amount });

        // Update params first
        onParamChange({
            coins,
            amount
        });

        // Set steps and movements directly
        try {
            const result = CoinChangeAlgorithm.generateCoinChangeSteps({
                coins,
                amount
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [coins, amount, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle amount change
    const handleAmountChange = useCallback((value) => {
        const newValue = parseInt(value, 10);
        if (!isNaN(newValue) && newValue >= 0 && newValue <= 100) {
            console.log('handleAmountChange called with value:', newValue);
            setAmount(newValue);
            resetAndGenerateSteps();
        }
    }, [resetAndGenerateSteps]);

    // Handle coin value change
    const handleCoinChange = useCallback((index, value) => {
        const newValue = parseInt(value, 10);
        if (isNaN(newValue) || newValue < 1) return;

        const newCoins = [...coins];
        newCoins[index] = newValue;
        setCoins(newCoins);
        resetAndGenerateSteps();
    }, [coins, resetAndGenerateSteps]);

    // Add a new coin
    const addCoin = useCallback(() => {
        if (coins.length < 10) { // Limit to 10 coins for visualization clarity
            const newCoins = [...coins, 1]; // Add a new coin with default value 1
            setCoins(newCoins);
            resetAndGenerateSteps();
        }
    }, [coins, resetAndGenerateSteps]);

    // Remove the last coin
    const removeCoin = useCallback(() => {
        if (coins.length > 1) { // Keep at least 1 coin
            const newCoins = coins.slice(0, -1);
            setCoins(newCoins);
            resetAndGenerateSteps();
        }
    }, [coins, resetAndGenerateSteps]);

    // Custom component for coins input
    const CoinsInput = ({ disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>Available Coins</Typography>
                    <Button 
                        startIcon={<AddIcon />} 
                        onClick={addCoin}
                        disabled={coins.length >= 10 || disabled}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                    >
                        Add Coin
                    </Button>
                    <Button 
                        startIcon={<RemoveIcon />} 
                        onClick={removeCoin}
                        disabled={coins.length <= 1 || disabled}
                        variant="outlined"
                        size="small"
                    >
                        Remove Coin
                    </Button>
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                    {coins.map((coin, index) => (
                        <TextField
                            key={index}
                            label={`Coin ${index + 1}`}
                            type="number"
                            size="small"
                            value={coin}
                            onChange={(e) => handleCoinChange(index, e.target.value)}
                            slotProps={{ input: { min: 1, max: 100 } }}
                            sx={{ width: 100 }}
                            disabled={disabled}
                        />
                    ))}
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Coin Set:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {coins.map((coin, i) => (
                            <Box key={i} sx={{ 
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: '50%',
                                width: 40,
                                height: 40,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: 'background.paper'
                            }}>
                                <Typography variant="body2">
                                    {coin}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Box>
        );
    };

    // Custom component for amount input
    const AmountInput = ({ value, onChange, disabled }) => {
        return (
            <TextField
                fullWidth
                label="Target Amount"
                type="number"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                slotProps={{ input: { min: 0, max: 100 } }}
                disabled={disabled}
                sx={{ mb: 2 }}
            />
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Coin Change algorithm
    const pseudocode = [
        { code: "function coinChange(coins, amount):", lineNumber: 1, indent: 0 },
        { code: "  // Create DP table", lineNumber: 2, indent: 1 },
        { code: "  // dp[i] = min coins needed for amount i", lineNumber: 3, indent: 1 },
        { code: "  dp = [0, ∞, ∞, ..., ∞] // Length amount+1", lineNumber: 4, indent: 1 },
        { code: "  coinUsed = [-1, -1, ..., -1] // Length amount+1", lineNumber: 5, indent: 1 },
        { code: "  for i = 1 to amount:", lineNumber: 6, indent: 1 },
        { code: "    for each coin in coins:", lineNumber: 7, indent: 2 },
        { code: "      if coin > i: continue // Skip if coin is too large", lineNumber: 8, indent: 3 },
        { code: "      // Try using this coin", lineNumber: 9, indent: 3 },
        { code: "      newMin = dp[i - coin] + 1", lineNumber: 10, indent: 3 },
        { code: "      if newMin < dp[i]:", lineNumber: 11, indent: 3 },
        { code: "        dp[i] = newMin", lineNumber: 12, indent: 4 },
        { code: "        coinUsed[i] = index of coin", lineNumber: 13, indent: 4 },
        { code: "      // else: keep current minimum", lineNumber: 14, indent: 3 },
        { code: "", lineNumber: 15, indent: 0 },
        { code: "  // Check if solution exists", lineNumber: 16, indent: 1 },
        { code: "  if dp[amount] == ∞:", lineNumber: 17, indent: 1 },
        { code: "    return -1 // No solution", lineNumber: 18, indent: 2 },
        { code: "", lineNumber: 19, indent: 0 },
        { code: "  // Trace back to find coins used", lineNumber: 20, indent: 1 },
        { code: "  while amount > 0:", lineNumber: 21, indent: 1 },
        { code: "    coin = coins[coinUsed[amount]]", lineNumber: 22, indent: 2 },
        { code: "    selectedCoins.add(coin)", lineNumber: 23, indent: 2 },
        { code: "    amount = amount - coin", lineNumber: 24, indent: 2 },
        { code: "  return dp[amount], selectedCoins", lineNumber: 25, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Coin Change Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Coin Change algorithm finds the minimum number of coins needed to make a specific amount of change, given a set of coin denominations.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(amount * n) where n is the number of coin denominations
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(amount) for the dynamic programming table
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to build a table of minimum coins needed for each amount
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • It can also track which coins were used to make the optimal solution
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'coins',
                        type: 'component',
                        label: 'Available Coins',
                        component: CoinsInput,
                        icon: MonetizationOnIcon
                    },
                    {
                        name: 'amount',
                        type: 'component',
                        label: 'Target Amount',
                        component: AmountInput,
                        componentProps: {
                            value: amount,
                            onChange: handleAmountChange
                        },
                        icon: AttachMoneyIcon
                    }
                ]}
                values={{
                    coins,
                    amount
                }}
                onChange={(newValues) => {
                    if (newValues.amount !== undefined && newValues.amount !== amount) {
                        handleAmountChange(newValues.amount);
                    }
                    // Coins are handled by the CoinsInput component
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default CoinChangeController;
