// algorithmUtils.js
// Utility functions for algorithms

// Configuration for algorithm visualizations
export const algorithmConfig = {
  // Default value range for sorting algorithms
  sortingValueRange: {
    min: 1,
    max: 1000 // Using a larger range (1-999) for more visual variety
  }
};

/**
 * Generate a random array of integers
 *
 * @param {number} length - Length of the array
 * @param {number} min - Minimum value (defaults to config value)
 * @param {number} max - Maximum value (defaults to config value)
 * @returns {Array} - Random array of integers
 */
export const generateRandomArray = (length, min = algorithmConfig.sortingValueRange.min, max = algorithmConfig.sortingValueRange.max) => {
  return Array.from({ length }, () => Math.floor(Math.random() * (max - min + 1)) + min);
};

/**
 * Delay execution for a specified time
 *
 * @param {number} ms - Milliseconds to delay
 * @returns {Promise} - Promise that resolves after the delay
 */
export const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Format time in milliseconds to a human-readable string
 *
 * @param {number} ms - Time in milliseconds
 * @returns {string} - Formatted time string
 */
export const formatTime = (ms) => {
  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
};

/**
 * Deep clone an object or array
 *
 * @param {any} obj - Object or array to clone
 * @returns {any} - Cloned object or array
 */
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if two arrays are equal
 *
 * @param {Array} arr1 - First array
 * @param {Array} arr2 - Second array
 * @returns {boolean} - Whether the arrays are equal
 */
export const arraysEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) return false;
  }
  return true;
};

/**
 * Swap two elements in an array
 *
 * @param {Array} arr - Array to modify
 * @param {number} i - First index
 * @param {number} j - Second index
 * @returns {Array} - Modified array
 */
export const swap = (arr, i, j) => {
  const result = [...arr];
  [result[i], result[j]] = [result[j], result[i]];
  return result;
};
