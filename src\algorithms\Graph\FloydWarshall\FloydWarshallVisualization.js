// FloydWarshallVisualization.js
// This file contains the 3D visualization for the Floyd-Warshall algorithm

import React, { useState, useEffect, useRef } from 'react';
import { PerspectiveCamera, Html } from '@react-three/drei';
import { useSpring, animated } from '@react-spring/three';
import * as THREE from 'three';
import { generateFloydWarshallSteps } from './FloydWarshallAlgorithm';

// Constants for visualization
const NODE_RADIUS = 0.8;
const NODE_SEGMENTS = 32;
const EDGE_RADIUS = 0.05;
const GRID_SIZE = 15;
const MATRIX_CELL_SIZE = 1.0;
const MATRIX_SPACING = 0.2;
const MATRIX_HEIGHT = 0.3;

// Node component for the graph
const Node = ({ position, index, isHighlighted, color }) => {
  const { scale, nodeColor } = useSpring({
    scale: isHighlighted ? 1.2 : 1,
    nodeColor: color,
    config: { tension: 170, friction: 26 }
  });

  return (
    <animated.group position={position} scale={scale}>
      <animated.mesh castShadow>
        <sphereGeometry args={[NODE_RADIUS, NODE_SEGMENTS, NODE_SEGMENTS]} />
        <animated.meshStandardMaterial color={nodeColor} metalness={0.3} roughness={0.7} />
      </animated.mesh>
      <Html position={[0, NODE_RADIUS + 0.3, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
          userSelect: 'none',
          pointerEvents: 'none',
          backgroundColor: 'rgba(0,0,0,0.5)',
          padding: '2px 8px',
          borderRadius: '12px'
        }}>
          {index}
        </div>
      </Html>
    </animated.group>
  );
};

// Edge component for the graph
const Edge = ({ start, end, weight, isHighlighted, color, thickness = 1 }) => {
  const edgeRef = useRef();
  
  // Calculate edge direction and length
  const direction = new THREE.Vector3().subVectors(end, start);
  const length = direction.length();
  
  // Create edge geometry
  useEffect(() => {
    if (edgeRef.current) {
      // Position at midpoint
      edgeRef.current.position.copy(start).add(direction.clone().multiplyScalar(0.5));
      
      // Orient along direction
      edgeRef.current.lookAt(end);
      
      // Scale to match length
      edgeRef.current.scale.set(1, 1, length);
    }
  }, [start, end, direction, length]);
  
  // Animation for highlighting
  const { edgeColor, edgeScale } = useSpring({
    edgeColor: color,
    edgeScale: isHighlighted ? 1.5 : thickness,
    config: { tension: 170, friction: 26 }
  });
  
  return (
    <group>
      <animated.mesh ref={edgeRef} castShadow>
        <cylinderGeometry args={[EDGE_RADIUS, EDGE_RADIUS, 1, 8]} />
        <animated.meshStandardMaterial color={edgeColor} metalness={0.3} roughness={0.7} />
        <animated.group scale-y={edgeScale} />
      </animated.mesh>
      
      {/* Weight label */}
      <Html position={[
        start.x + direction.x * 0.5,
        start.y + direction.y * 0.5 + 0.3,
        start.z + direction.z * 0.5
      ]} center>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
          userSelect: 'none',
          pointerEvents: 'none',
          backgroundColor: isHighlighted ? 'rgba(255,0,0,0.7)' : 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '10px',
          transition: 'background-color 0.3s'
        }}>
          {weight === Infinity ? '∞' : weight}
        </div>
      </Html>
    </group>
  );
};

// Matrix cell component
const MatrixCell = ({ position, value, isHighlighted, isUpdated, onClick }) => {
  // Determine cell color based on state
  let cellColor = '#2c3e50'; // Default
  if (isHighlighted) cellColor = '#e74c3c'; // Highlighted (comparing)
  if (isUpdated) cellColor = '#2ecc71'; // Updated (found shorter path)
  
  // Animation for highlighting
  const { scale, color } = useSpring({
    scale: isHighlighted || isUpdated ? 1.2 : 1,
    color: cellColor,
    config: { tension: 170, friction: 26 }
  });
  
  return (
    <animated.group position={position} scale={scale}>
      <animated.mesh castShadow receiveShadow onClick={onClick}>
        <boxGeometry args={[MATRIX_CELL_SIZE, MATRIX_HEIGHT, MATRIX_CELL_SIZE]} />
        <animated.meshStandardMaterial color={color} metalness={0.3} roughness={0.7} />
      </animated.mesh>
      <Html position={[0, MATRIX_HEIGHT + 0.1, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: isUpdated ? 'bold' : 'normal',
          textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
          userSelect: 'none',
          pointerEvents: 'none'
        }}>
          {value === Infinity ? '∞' : value}
        </div>
      </Html>
    </animated.group>
  );
};

// Main visualization component
const FloydWarshallVisualization = ({
  params,
  state,
  setState,
  step,
  setStep,
  steps,
  setSteps,
  setTotalSteps,
  theme,
  setMovements,
  speed = 5
}) => {
  // State for visualization
  const [graph, setGraph] = useState(null);
  const [distMatrix, setDistMatrix] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [nodePositions, setNodePositions] = useState([]);
  const [highlightedNodes, setHighlightedNodes] = useState([]);
  const [highlightedEdge, setHighlightedEdge] = useState(null);
  const [highlightedCells, setHighlightedCells] = useState([]);
  const [updatedCell, setUpdatedCell] = useState(null);
  const groupRef = useRef();
  
  // Colors based on theme
  const colors = {
    node: theme.palette.mode === 'dark' ? '#3498db' : '#2980b9',
    nodeHighlight: theme.palette.mode === 'dark' ? '#e74c3c' : '#c0392b',
    intermediateNode: theme.palette.mode === 'dark' ? '#f39c12' : '#d35400',
    edge: theme.palette.mode === 'dark' ? '#bdc3c7' : '#7f8c8d',
    edgeHighlight: theme.palette.mode === 'dark' ? '#e74c3c' : '#c0392b',
    updatedEdge: theme.palette.mode === 'dark' ? '#2ecc71' : '#27ae60',
    text: theme.palette.mode === 'dark' ? '#ffffff' : '#333333',
    background: theme.palette.mode === 'dark' ? '#1e272e' : '#dfe6e9',
  };
  
  // Initialize algorithm steps
  useEffect(() => {
    if (params) {
      const result = generateFloydWarshallSteps(params);
      
      // Set steps locally
      const generatedSteps = result.steps;
      setSteps(generatedSteps);
      
      // Set graph and initial distance matrix
      setGraph(result.graph);
      setDistMatrix(result.dist);
      
      // Generate node positions in a circle
      const positions = [];
      const vertices = params.vertices;
      const radius = Math.max(3, vertices * 0.8);
      
      for (let i = 0; i < vertices; i++) {
        const angle = (i / vertices) * Math.PI * 2;
        positions.push(new THREE.Vector3(
          radius * Math.cos(angle),
          0,
          radius * Math.sin(angle)
        ));
      }
      setNodePositions(positions);
      
      // Update total steps in parent component if the function exists
      if (typeof setTotalSteps === 'function') {
        setTotalSteps(generatedSteps.length);
      }
      
      // Set movements for the controller if the function exists
      if (typeof setMovements === 'function') {
        setMovements(generatedSteps.map(step => step.movement));
      }
      
      // Reset step to 0 to ensure we start from the beginning
      if (typeof setStep === 'function') {
        setStep(0);
      }
    }
  }, [params, setSteps, setTotalSteps, setMovements, setStep]);
  
  // Update visualization based on current step
  useEffect(() => {
    if (!steps || steps.length === 0) {
      return;
    }
    
    if (step < 0 || step >= steps.length) {
      return;
    }
    
    const currentStepData = steps[step];
    setCurrentStep(step);
    
    // Update distance matrix
    if (currentStepData.dist) {
      setDistMatrix(currentStepData.dist);
    }
    
    // Update highlighted elements based on step type
    switch (currentStepData.type) {
      case 'initialize':
        setHighlightedNodes([]);
        setHighlightedEdge(null);
        setHighlightedCells([]);
        setUpdatedCell(null);
        break;
        
      case 'new_intermediate':
        setHighlightedNodes([currentStepData.k]);
        setHighlightedEdge(null);
        setHighlightedCells([]);
        setUpdatedCell(null);
        break;
        
      case 'compare':
        setHighlightedNodes([currentStepData.i, currentStepData.j, currentStepData.k]);
        setHighlightedEdge({ from: currentStepData.i, to: currentStepData.j });
        setHighlightedCells([
          { i: currentStepData.i, j: currentStepData.j },
          { i: currentStepData.i, j: currentStepData.k },
          { i: currentStepData.k, j: currentStepData.j }
        ]);
        setUpdatedCell(null);
        break;
        
      case 'update':
        setHighlightedNodes([currentStepData.i, currentStepData.j, currentStepData.k]);
        setHighlightedEdge({ from: currentStepData.i, to: currentStepData.j });
        setHighlightedCells([
          { i: currentStepData.i, j: currentStepData.j },
          { i: currentStepData.i, j: currentStepData.k },
          { i: currentStepData.k, j: currentStepData.j }
        ]);
        setUpdatedCell({ i: currentStepData.i, j: currentStepData.j });
        break;
        
      case 'no_update':
        setHighlightedNodes([currentStepData.i, currentStepData.j, currentStepData.k]);
        setHighlightedEdge({ from: currentStepData.i, to: currentStepData.j });
        setHighlightedCells([
          { i: currentStepData.i, j: currentStepData.j },
          { i: currentStepData.i, j: currentStepData.k },
          { i: currentStepData.k, j: currentStepData.j }
        ]);
        setUpdatedCell(null);
        break;
        
      case 'complete':
        setHighlightedNodes([]);
        setHighlightedEdge(null);
        setHighlightedCells([]);
        setUpdatedCell(null);
        break;
        
      default:
        break;
    }
    
    // If we're in running state, advance to the next step after a delay
    if (state === 'running') {
      // Check if we've reached the last step
      if (step >= steps.length - 1) {
        // If we've reached the last step, set state to completed
        if (typeof setState === 'function') {
          setState('completed');
        }
      } else {
        // Otherwise, advance to the next step
        const timer = setTimeout(() => {
          if (typeof setStep === 'function') {
            setStep(step + 1);
          }
        }, 1000 / speed); // Adjust speed based on the speed setting
        
        return () => clearTimeout(timer);
      }
    }
  }, [step, steps, state, speed, setStep, setState]);
  
  // If no graph yet, show loading message
  if (!graph || !distMatrix || nodePositions.length === 0) {
    return (
      <group position={[0, 0, 0]}>
        <Html position={[0, 2, 0]} center>
          <div style={{
            color: colors.text,
            fontSize: '18px',
            fontWeight: 'bold',
            textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
            userSelect: 'none',
            pointerEvents: 'none',
            backgroundColor: 'rgba(0,0,0,0.3)',
            padding: '10px 20px',
            borderRadius: '5px'
          }}>
            Initializing Floyd-Warshall Algorithm...
          </div>
        </Html>
      </group>
    );
  }
  
  // Calculate matrix dimensions and position
  const vertices = graph.length;
  const matrixWidth = vertices * (MATRIX_CELL_SIZE + MATRIX_SPACING);
  const matrixOffsetX = -matrixWidth / 2 + MATRIX_CELL_SIZE / 2;
  const matrixOffsetZ = 8; // Position matrix in front of the graph
  
  return (
    <group ref={groupRef} position={[0, 0, 0]}>
      {/* Add a camera with a fixed position for better visibility */}
      <PerspectiveCamera makeDefault position={[0, 12, 20]} fov={50} />
      
      {/* Graph visualization */}
      <group position={[0, 0, -5]}>
        {/* Nodes */}
        {nodePositions.map((position, index) => (
          <Node
            key={`node-${index}`}
            position={position}
            index={index}
            isHighlighted={highlightedNodes.includes(index)}
            color={
              highlightedNodes.includes(index)
                ? index === steps[currentStep]?.k
                  ? colors.intermediateNode
                  : colors.nodeHighlight
                : colors.node
            }
          />
        ))}
        
        {/* Edges */}
        {graph.map((row, i) => 
          row.map((weight, j) => {
            if (i !== j && weight !== Infinity) {
              const isHighlighted = 
                highlightedEdge && 
                highlightedEdge.from === i && 
                highlightedEdge.to === j;
              
              const isUpdated = 
                updatedCell && 
                updatedCell.i === i && 
                updatedCell.j === j;
              
              return (
                <Edge
                  key={`edge-${i}-${j}`}
                  start={nodePositions[i]}
                  end={nodePositions[j]}
                  weight={weight}
                  isHighlighted={isHighlighted}
                  color={isUpdated ? colors.updatedEdge : isHighlighted ? colors.edgeHighlight : colors.edge}
                  thickness={isUpdated ? 1.5 : 1}
                />
              );
            }
            return null;
          })
        )}
      </group>
      
      {/* Distance matrix visualization */}
      <group position={[0, 0, matrixOffsetZ]} rotation={[-Math.PI / 6, 0, 0]}>
        {/* Matrix cells */}
        {distMatrix.map((row, i) => 
          row.map((value, j) => {
            const isHighlighted = highlightedCells.some(cell => cell.i === i && cell.j === j);
            const isUpdated = updatedCell && updatedCell.i === i && updatedCell.j === j;
            
            return (
              <MatrixCell
                key={`cell-${i}-${j}`}
                position={[
                  matrixOffsetX + j * (MATRIX_CELL_SIZE + MATRIX_SPACING),
                  0,
                  i * (MATRIX_CELL_SIZE + MATRIX_SPACING)
                ]}
                value={value}
                isHighlighted={isHighlighted}
                isUpdated={isUpdated}
              />
            );
          })
        )}
        
        {/* Row labels (vertex indices) */}
        {distMatrix.map((_, i) => (
          <Html
            key={`row-${i}`}
            position={[
              matrixOffsetX - MATRIX_CELL_SIZE - MATRIX_SPACING,
              0,
              i * (MATRIX_CELL_SIZE + MATRIX_SPACING)
            ]}
            center
          >
            <div style={{
              color: colors.text,
              fontSize: '14px',
              fontWeight: 'bold',
              textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
              userSelect: 'none',
              pointerEvents: 'none',
              backgroundColor: 'rgba(0,0,0,0.2)',
              padding: '2px 6px',
              borderRadius: '4px'
            }}>
              {i}
            </div>
          </Html>
        ))}
        
        {/* Column labels (vertex indices) */}
        {distMatrix[0].map((_, j) => (
          <Html
            key={`col-${j}`}
            position={[
              matrixOffsetX + j * (MATRIX_CELL_SIZE + MATRIX_SPACING),
              0,
              -MATRIX_CELL_SIZE - MATRIX_SPACING
            ]}
            center
          >
            <div style={{
              color: colors.text,
              fontSize: '14px',
              fontWeight: 'bold',
              textShadow: '1px 1px 1px rgba(0,0,0,0.5)',
              userSelect: 'none',
              pointerEvents: 'none',
              backgroundColor: 'rgba(0,0,0,0.2)',
              padding: '2px 6px',
              borderRadius: '4px'
            }}>
              {j}
            </div>
          </Html>
        ))}
      </group>
      
      {/* Current step information */}
      <Html position={[0, 8, 0]} center>
        <div style={{
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
          color: theme.palette.mode === 'dark' ? '#fff' : '#333',
          padding: '10px',
          borderRadius: '5px',
          width: '400px',
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          fontWeight: 'bold',
          boxShadow: '0 0 10px rgba(0,0,0,0.2)',
          pointerEvents: 'none',
          border: `2px solid ${theme.palette.primary.main}`
        }}>
          {steps[currentStep]?.movement || 'Initializing...'}
        </div>
      </Html>
      
      {/* Ground plane for better depth perception */}
      <mesh
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -2, 0]}
        receiveShadow
      >
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial
          color={colors.background}
          roughness={0.8}
          metalness={0.2}
          transparent
          opacity={0.6}
        />
      </mesh>
    </group>
  );
};

export default FloydWarshallVisualization;
