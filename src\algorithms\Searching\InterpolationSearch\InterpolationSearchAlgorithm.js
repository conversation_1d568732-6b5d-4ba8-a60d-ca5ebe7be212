// InterpolationSearchAlgorithm.js
// Implementation of the Interpolation Search algorithm with step generation

/**
 * Generates steps for the Interpolation Search algorithm
 * @param {Array} arr - The array to search in (must be sorted)
 * @param {number} target - The value to search for
 * @returns {Object} - Object containing steps and the result
 */
export const generateInterpolationSearchSteps = (arr, target) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Initialize Interpolation Search'
  });

  // Add step to explain that array must be sorted
  steps.push({
    type: 'checkSorted',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Interpolation Search requires a sorted array'
  });

  // Perform interpolation search
  let low = 0;
  let high = inputArray.length - 1;
  let found = false;
  let result = -1;
  let iterations = 0;
  const MAX_ITERATIONS = 100; // Safety to prevent infinite loops

  // Add step to start the search
  steps.push({
    type: 'startSearch',
    array: [...inputArray],
    target,
    low,
    high,
    found: false,
    movement: `Start search with low = ${low} and high = ${high}`
  });

  while (low <= high && target >= inputArray[low] && target <= inputArray[high] && iterations < MAX_ITERATIONS) {
    iterations++;

    // Calculate the probe position using the interpolation formula
    let probe;
    if (inputArray[high] === inputArray[low]) {
      // If all elements are the same, use the low index
      probe = low;
    } else {
      // Use the interpolation formula
      const numerator = (high - low) * (target - inputArray[low]);
      const denominator = inputArray[high] - inputArray[low];
      probe = low + Math.floor(numerator / denominator);
    }

    // Add step to show the interpolation calculation
    steps.push({
      type: 'calculateProbe',
      array: [...inputArray],
      target,
      low,
      high,
      probe,
      lowValue: inputArray[low],
      highValue: inputArray[high],
      formula: `probe = low + floor((high - low) * (target - arr[low]) / (arr[high] - arr[low]))`,
      calculation: `probe = ${low} + floor((${high} - ${low}) * (${target} - ${inputArray[low]}) / (${inputArray[high]} - ${inputArray[low]})) = ${probe}`,
      found: false,
      movement: `Calculate probe position using interpolation formula: ${probe}`
    });

    // Add step to show the comparison at the probe position
    steps.push({
      type: 'compare',
      array: [...inputArray],
      target,
      low,
      high,
      probe,
      probeValue: inputArray[probe],
      found: false,
      movement: `Compare ${inputArray[probe]} at index ${probe} with target ${target}`
    });

    // Check if the target is found
    if (inputArray[probe] === target) {
      found = true;
      result = probe;

      // Add step to show the target found
      steps.push({
        type: 'found',
        array: [...inputArray],
        target,
        low,
        high,
        probe,
        probeValue: inputArray[probe],
        found: true,
        result: probe,
        movement: `Target ${target} found at index ${probe}`
      });

      break;
    }

    // If the target is smaller, search in the left half
    if (inputArray[probe] > target) {
      high = probe - 1;

      // Add step to show the update of high
      steps.push({
        type: 'updateHigh',
        array: [...inputArray],
        target,
        low,
        high,
        probe,
        found: false,
        movement: `${inputArray[probe]} > ${target}, so update high = ${high}`
      });
    }
    // If the target is larger, search in the right half
    else {
      low = probe + 1;

      // Add step to show the update of low
      steps.push({
        type: 'updateLow',
        array: [...inputArray],
        target,
        low,
        high,
        probe,
        found: false,
        movement: `${inputArray[probe]} < ${target}, so update low = ${low}`
      });
    }
  }

  // If target not found, add a step to show that
  if (!found) {
    steps.push({
      type: 'notFound',
      array: [...inputArray],
      target,
      low,
      high,
      found: false,
      movement: `Target ${target} not found in the array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    target,
    low,
    high,
    found,
    result,
    movement: found ? `Interpolation Search complete: ${target} found at index ${result}` : `Interpolation Search complete: ${target} not found`
  });

  return { 
    steps, 
    result: {
      found,
      index: found ? result : -1
    }
  };
};

// Default export
const InterpolationSearchAlgorithm = {
  generateSteps: generateInterpolationSearchSteps
};

export default InterpolationSearchAlgorithm;
