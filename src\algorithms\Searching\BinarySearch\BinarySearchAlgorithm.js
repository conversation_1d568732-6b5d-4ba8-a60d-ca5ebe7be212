// BinarySearchAlgorithm.js
// Implementation of the Binary Search algorithm with step generation

/**
 * Generates steps for the Binary Search algorithm
 * @param {Array} arr - The sorted array to search in
 * @param {number} target - The value to search for
 * @returns {Object} - Object containing steps and the result
 */
export const generateBinarySearchSteps = (arr, target) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    target,
    left: 0,
    right: inputArray.length - 1,
    mid: -1,
    found: false,
    movement: 'Initialize Binary Search'
  });

  // Perform binary search
  let left = 0;
  let right = inputArray.length - 1;
  let found = false;
  let result = -1;

  while (left <= right) {
    // Calculate middle index
    const mid = Math.floor((left + right) / 2);

    // Add step to show the current search range
    steps.push({
      type: 'searchRange',
      array: [...inputArray],
      target,
      left,
      right,
      mid,
      found: false,
      movement: `Set search range from index ${left} to ${right}`
    });

    // Add step to show the middle element
    steps.push({
      type: 'selectMid',
      array: [...inputArray],
      target,
      left,
      right,
      mid,
      found: false,
      movement: `Select middle element at index ${mid} with value ${inputArray[mid]}`
    });

    // Compare middle element with target
    if (inputArray[mid] === target) {
      // Target found
      found = true;
      result = mid;

      // Add step to show the target found
      steps.push({
        type: 'found',
        array: [...inputArray],
        target,
        left,
        right,
        mid,
        found: true,
        result: mid,
        movement: `Target ${target} found at index ${mid}`
      });

      break;
    } else if (inputArray[mid] < target) {
      // Target is in the right half
      // Add step to show comparison
      steps.push({
        type: 'compare',
        array: [...inputArray],
        target,
        left,
        right,
        mid,
        compareResult: 'less',
        found: false,
        movement: `${inputArray[mid]} < ${target}, search in right half`
      });

      // Update left pointer
      left = mid + 1;

      // Add step to show the updated left pointer
      steps.push({
        type: 'updateLeft',
        array: [...inputArray],
        target,
        left,
        right,
        mid,
        found: false,
        movement: `Update left pointer to ${left}`
      });
    } else {
      // Target is in the left half
      // Add step to show comparison
      steps.push({
        type: 'compare',
        array: [...inputArray],
        target,
        left,
        right,
        mid,
        compareResult: 'greater',
        found: false,
        movement: `${inputArray[mid]} > ${target}, search in left half`
      });

      // Update right pointer
      right = mid - 1;

      // Add step to show the updated right pointer
      steps.push({
        type: 'updateRight',
        array: [...inputArray],
        target,
        left,
        right,
        mid,
        found: false,
        movement: `Update right pointer to ${right}`
      });
    }
  }

  // If target not found, add a step to show that
  if (!found) {
    steps.push({
      type: 'notFound',
      array: [...inputArray],
      target,
      left,
      right,
      mid: -1,
      found: false,
      movement: `Target ${target} not found in the array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    target,
    left,
    right,
    mid: found ? result : -1,
    found,
    result: found ? result : -1,
    movement: found ? `Binary Search complete: ${target} found at index ${result}` : `Binary Search complete: ${target} not found`
  });

  return { 
    steps, 
    result: {
      found,
      index: found ? result : -1
    }
  };
};

// Default export
const BinarySearchAlgorithm = {
  generateSteps: generateBinarySearchSteps
};

export default BinarySearchAlgorithm;
