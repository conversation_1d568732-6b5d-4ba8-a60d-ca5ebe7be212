// PrimsVisualization.js
// 3D visualization component for <PERSON><PERSON>'s algorithm

import React, { useState, useEffect, useRef, useMemo, memo, useCallback } from 'react';
import { useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
// Theme is defined directly in the component
// No need to import generatePrimsSteps as we're using steps from props
import * as THREE from 'three';
import { useSpring, animated } from '@react-spring/three';
import GraphLabelsOverlay from '../../../components/visualization/GraphLabelsOverlay';

// Import reusable visualization components
import { FixedColorLegend, FixedStepBoard, ColorfulGraphNode, ColorfulGraphEdge } from '../../../components/visualization';
// OrbitControls is provided by the parent component

// Constants for visualization
const NODE_RADIUS = 0.8; // Smaller radius for our new nodes
const NODE_SEGMENTS = 32;
const EDGE_WIDTH = 0.03; // Extremely thin edges

// Memoized Edge component to prevent re-renders
const MemoizedEdge = memo(({ id, start, end, weight, color, isHighlighted, isRelaxed, isNegativeCycle, theme }) => {
  return (
    <ColorfulGraphEdge
      key={id}
      start={start}
      end={end}
      weight={weight}
      color={color}
      isHighlighted={isHighlighted}
      isRelaxed={isRelaxed}
      isNegativeCycle={isNegativeCycle}
      curved={false}
      thickness={EDGE_WIDTH}
      nodeRadius={NODE_RADIUS}
      theme={theme}
    />
  );
}, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.id === nextProps.id &&
    prevProps.start.x === nextProps.start.x &&
    prevProps.start.y === nextProps.start.y &&
    prevProps.start.z === nextProps.start.z &&
    prevProps.end.x === nextProps.end.x &&
    prevProps.end.y === nextProps.end.y &&
    prevProps.end.z === nextProps.end.z &&
    prevProps.weight === nextProps.weight &&
    prevProps.color === nextProps.color &&
    prevProps.isHighlighted === nextProps.isHighlighted &&
    prevProps.isRelaxed === nextProps.isRelaxed &&
    prevProps.isNegativeCycle === nextProps.isNegativeCycle
  );
});

// Memoized Node component to prevent re-renders
const MemoizedNode = memo(({ id, position, color, label }) => {
  return (
    <ColorfulGraphNode
      key={id}
      position={position}
      radius={NODE_RADIUS}
      color={color}
      label={label}
      showLabel={true}
      segments={NODE_SEGMENTS}
    />
  );
}, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.id === nextProps.id &&
    prevProps.position[0] === nextProps.position[0] &&
    prevProps.position[1] === nextProps.position[1] &&
    prevProps.position[2] === nextProps.position[2] &&
    prevProps.color === nextProps.color &&
    prevProps.label === nextProps.label
  );
});

// Main visualization component
const PrimsVisualization = ({
  params,
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setSteps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera, gl } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // Canvas ref for HTML overlay
  const canvasRef = useRef(null);

  // State for graph data
  const [graphData, setGraphData] = useState({ nodes: [], edges: [] });
  const [nodePositions, setNodePositions] = useState({});
  const [mst, setMst] = useState([]);
  const [visited, setVisited] = useState([]);
  const [frontier, setFrontier] = useState([]);
  const [currentEdge, setCurrentEdge] = useState(null);
  const [rejected, setRejected] = useState(false);

  // Define colors based on theme
  const colors = useMemo(() => ({
    node: isDark ? '#4fc3f7' : '#2196f3', // Blue nodes
    edge: isDark ? '#9e9e9e' : '#757575', // Gray edges
    visitedNode: isDark ? '#69f0ae' : '#00c853', // Green for visited nodes
    currentEdge: isDark ? '#ffeb3b' : '#ffc107', // Yellow for current edge
    mstEdge: isDark ? '#69f0ae' : '#00c853', // Green for MST edges
    rejectedEdge: isDark ? '#ff5252' : '#f44336', // Red for rejected edges
    frontier: isDark ? '#ba68c8' : '#9c27b0', // Purple for frontier edges
  }), [isDark]);

  // Set canvas ref once
  useEffect(() => {
    canvasRef.current = gl.domElement;
  }, [gl]);

  // Set up camera position based on number of nodes
  useEffect(() => {
    // Only adjust camera when we have graph data
    if (camera && graphData && graphData.nodes) {
      const numNodes = graphData.nodes.length;

      // Scale camera distance based on number of nodes, but keep it closer
      const baseDistance = 20; // Reduced from 25 to pull scene closer
      const nodeScalingFactor = 1.5; // Reduced from 1.8
      const cameraDistance = baseDistance + (numNodes > 10 ? (numNodes - 10) * nodeScalingFactor : 0);

      // Position camera directly in front for better visibility of UI elements
      camera.position.set(
        0,                  // X position (centered)
        0,                  // Y position (centered)
        cameraDistance     // Z position (straight ahead)
      );

      // Look directly at the center of the scene
      camera.lookAt(0, 0, 0);

      // Set camera's far clipping plane to ensure all elements are visible
      camera.far = 2000;
      camera.updateProjectionMatrix();

      console.log('Number of nodes:', numNodes);
      console.log('Camera distance:', cameraDistance);
      console.log('Camera position:', camera.position);
    }
  }, [camera, graphData]);

  // Create lookup maps for faster checks
  const mstMap = useRef(new Map());
  const frontierMap = useRef(new Map());
  const visitedSet = useRef(new Set());

  // Refs to avoid re-renders
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);

  // Initialize graph data from params
  useEffect(() => {
    if (!params) return;

    console.log('PrimsVisualization: Using steps from props:', steps);

    // Use the steps from props
    if (steps && steps.length > 0) {
      // Get the graph from the first step
      const firstStep = steps[0];
      if (firstStep && firstStep.graph) {
        // Set graph data
        setGraphData({
          nodes: firstStep.graph.nodes || [],
          edges: firstStep.graph.edges || [],
        });
      }

      // Reset algorithm state
      setMst([]);
      setVisited([]);
      setFrontier([]);
      setCurrentEdge(null);
      setRejected(false);

      // Update steps ref
      stepsRef.current = steps;
      console.log('PrimsVisualization: Updated stepsRef with', steps.length, 'steps');
    } else {
      console.warn('PrimsVisualization: No steps provided in props');
    }
  }, [params, steps]);

  // Generate node positions
  useEffect(() => {
    if (!graphData.nodes || graphData.nodes.length === 0) return;

    const numNodes = graphData.nodes.length;
    const positions = {};

    // Scale radius based on number of nodes, but with an upper limit
    // to ensure the graph stays within the viewport
    const maxRadius = 12; // Maximum radius to prevent going out of viewport
    const baseRadius = Math.min(maxRadius, Math.max(6, numNodes * 0.8));

    // Calculate vertical space constraints to stay between step board and legend
    const verticalConstraint = 5; // Limit vertical expansion

    // Use a 3D distribution approach
    // For smaller graphs (< 15 nodes), use a spherical distribution
    // For larger graphs, use a multi-layer approach

    if (numNodes <= 15) {
      // Use a modified Fibonacci sphere algorithm for 3D distribution
      // This avoids placing nodes in the center while maintaining 3D distribution
      const goldenRatio = (1 + Math.sqrt(5)) / 2;
      const angleIncrement = Math.PI * 2 * goldenRatio;

      graphData.nodes.forEach((node, i) => {
        // Skip the exact center position by starting from 1 instead of 0
        const t = (i + 1) / (numNodes + 2);
        const inclination = Math.acos(1 - 2 * t);
        const azimuth = angleIncrement * i;

        // Add a small hollow sphere in the center by using a minimum radius
        const minRadius = baseRadius * 0.4; // Minimum distance from center
        const nodeRadius = minRadius + (baseRadius - minRadius) * Math.sin(inclination);

        // Convert spherical to cartesian coordinates
        const x = nodeRadius * Math.sin(inclination) * Math.cos(azimuth);
        // Constrain y-coordinate to stay within vertical limits
        const rawY = nodeRadius * Math.sin(inclination) * Math.sin(azimuth);
        const y = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawY));
        const z = nodeRadius * Math.cos(inclination);

        // Add small random offsets for more natural distribution
        // but constrain the y-offset to stay within vertical limits
        const randomOffsetXZ = baseRadius * 0.1;
        const randomOffsetY = baseRadius * 0.05; // Smaller vertical randomness

        positions[node.id] = [
          x + (Math.random() - 0.5) * randomOffsetXZ,
          Math.max(-verticalConstraint, Math.min(verticalConstraint, y + (Math.random() - 0.5) * randomOffsetY)),
          z + (Math.random() - 0.5) * randomOffsetXZ
        ];
      });
    } else {
      // For larger graphs, use a multi-layer approach for better visibility
      // Calculate number of layers based on node count
      const layerCount = Math.ceil(numNodes / 10);
      const nodesPerLayer = Math.ceil(numNodes / layerCount);

      graphData.nodes.forEach((node, i) => {
        // Determine which layer this node belongs to
        const layerIndex = Math.floor(i / nodesPerLayer);
        // Determine position within the layer
        const positionInLayer = i % nodesPerLayer;

        // Calculate angle based on position in layer
        const angle = (positionInLayer / nodesPerLayer) * Math.PI * 2;

        // Calculate radius for this layer with variation, but constrained
        const layerRadius = baseRadius * (1.0 + 0.2 * Math.sin(layerIndex * Math.PI / layerCount));

        // Calculate height based on layer with spacing proportional to available vertical space
        // Distribute layers evenly within the vertical constraint
        const maxLayerHeight = verticalConstraint * 1.8 / layerCount; // Ensure all layers fit
        const heightSpacing = Math.min(baseRadius * 1.2, maxLayerHeight);
        const rawY = (layerIndex - (layerCount - 1) / 2) * heightSpacing;

        // Constrain y value to stay within vertical limits
        const y = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawY));

        // Add a slight tilt to each layer for more 3D effect, but reduce tilt for larger graphs
        const tiltAngle = (layerIndex / layerCount) * Math.PI * 0.15;

        // Calculate x and z positions with tilt and small random offsets
        const baseX = Math.cos(angle) * layerRadius;
        const baseZ = Math.sin(angle) * layerRadius;

        // Apply tilt rotation with constrained random offsets
        const randomOffsetXZ = baseRadius * 0.1;
        const randomOffsetY = baseRadius * 0.05; // Smaller vertical randomness

        const x = baseX * Math.cos(tiltAngle) - y * Math.sin(tiltAngle) + (Math.random() - 0.5) * randomOffsetXZ;
        const rawAdjustedY = baseX * Math.sin(tiltAngle) + y * Math.cos(tiltAngle) + (Math.random() - 0.5) * randomOffsetY;
        const adjustedY = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawAdjustedY));
        const z = baseZ + (Math.random() - 0.5) * randomOffsetXZ;

        positions[node.id] = [x, adjustedY, z];
      });
    }

    setNodePositions(positions);
  }, [graphData.nodes]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.node, label: 'Node' },
    { color: colors.visitedNode, label: 'Visited Node' },
    { color: colors.edge, label: 'Edge' },
    { color: colors.currentEdge, label: 'Current Edge' },
    { color: colors.mstEdge, label: 'MST Edge' },
    { color: colors.rejectedEdge, label: 'Rejected Edge' },
    { color: colors.frontier, label: 'Frontier Edge' },
  ], [colors]);

  // Get the current step for the step board
  const currentStepData = useMemo(() => {
    if (step >= 0 && step < stepsRef.current.length) {
      const data = stepsRef.current[step];
      console.log('Current step data:', data);
      return data;
    }
    console.log('No step data available for step:', step);
    return null;
  }, [step]);

  // Apply step when step changes
  useEffect(() => {
    if (step !== lastAppliedStepRef.current) {
      console.log('Applying step:', step);
      applyStep(step);
      lastAppliedStepRef.current = step;
    }
  }, [step]);

  // Helper function to calculate delay based on speed
  const calculateDelay = useCallback((speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  }, []);

  // Handle automatic stepping when state is 'running'
  useEffect(() => {
    let timeoutId = null;

    // Update refs to access current values in timeouts
    stateRef.current = state;
    speedRef.current = speed;

    console.log('Auto-advance effect triggered, state:', state, 'step:', step, 'stepsRef.current.length:', stepsRef.current.length);

    // Only proceed if state is 'running'
    if (state === 'running') {
      // Check if we've reached the end (step is 0-indexed, so we need to check if it's equal to the length)
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached the last step, setting state to completed');
        // If we've reached the end, set state to completed
        setState('completed');
      } else {
        // We haven't reached the end, advance to the next step
        const nextStep = step + 1;
        console.log('Auto-advancing to next step:', nextStep, 'of', stepsRef.current.length, 'with speed:', speedRef.current);

        // Calculate delay based on speed
        const delay = calculateDelay(speedRef.current);

        // Set a timer to advance to the next step
        timeoutId = setTimeout(() => {
          console.log('Timer fired, setting step to:', nextStep);
          setStep(nextStep);
        }, delay);
      }
    } else if (state === 'idle') {
      // Reset visualization
      setMst([]);
      setVisited([]);
      setFrontier([]);
      setCurrentEdge(null);
      setRejected(false);

      // Reset maps
      mstMap.current.clear();
      frontierMap.current.clear();
      visitedSet.current.clear();

      // Reset last applied step
      lastAppliedStepRef.current = -1;

      // Apply the initial step
      if (stepsRef.current && stepsRef.current.length > 0) {
        applyStep(0);
      }
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Apply a step of the algorithm
  const applyStep = (stepIndex) => {
    if (stepIndex < 0 || stepIndex >= stepsRef.current.length) return;

    const currentStep = stepsRef.current[stepIndex];

    // Update visualization state based on step type
    const newMst = currentStep.mst || [];
    const newVisited = currentStep.visited || [];
    const newFrontier = currentStep.frontier || [];

    // Update state
    setMst(newMst);
    setVisited(newVisited);
    setFrontier(newFrontier);
    setCurrentEdge(currentStep.currentEdge);
    setRejected(currentStep.rejected || false);

    // Update lookup maps for faster rendering
    mstMap.current.clear();
    newMst.forEach(edge => mstMap.current.set(edge.id, true));

    frontierMap.current.clear();
    newFrontier.forEach(edge => frontierMap.current.set(edge.id, true));

    visitedSet.current.clear();
    newVisited.forEach(nodeId => visitedSet.current.add(nodeId));

    // Update last applied step
    lastAppliedStepRef.current = stepIndex;
  };

  // Create a levitating animation for the graph
  const { position, rotation } = useSpring({
    from: { position: [0, 0, 0], rotation: [0, 0, 0] },
    to: async (next) => {
      // Create an infinite loop of gentle floating animations
      while (true) {
        // Float up slightly
        await next({
          position: [0, 0.2, 0],
          rotation: [0.02, 0.01, 0],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
        // Float down slightly
        await next({
          position: [0, -0.1, 0],
          rotation: [-0.01, -0.02, 0],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
        // Return to neutral with slight rotation
        await next({
          position: [0, 0, 0],
          rotation: [0, 0.01, 0.01],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
      }
    },
    config: { mass: 10, tension: 50, friction: 30 }
  });

  // Memoized Graph component without animation
  const GraphVisualization = memo(() => {

    // Create stable references to the edges and nodes
    const edgeElements = useMemo(() => {
      return graphData.edges.map(edge => {
        const sourcePos = nodePositions[edge.source];
        const targetPos = nodePositions[edge.target];

        if (!sourcePos || !targetPos) return null;

        // Determine edge color and properties
        let edgeColor = colors.edge;
        let isHighlighted = false;
        let isInMST = mstMap.current.has(edge.id);
        let isCurrentEdge = currentEdge && currentEdge.id === edge.id;
        let isRejected = isCurrentEdge && rejected;
        let isInFrontier = frontierMap.current.has(edge.id);

        if (isInMST) {
          edgeColor = colors.mstEdge;
        } else if (isRejected) {
          edgeColor = colors.rejectedEdge;
        } else if (isCurrentEdge) {
          edgeColor = colors.currentEdge;
          isHighlighted = true;
        } else if (isInFrontier) {
          edgeColor = colors.frontier;
        }

        return (
          <MemoizedEdge
            key={edge.id}
            id={edge.id}
            start={new THREE.Vector3(...sourcePos)}
            end={new THREE.Vector3(...targetPos)}
            weight={edge.weight}
            color={edgeColor}
            isHighlighted={isHighlighted}
            isRelaxed={isInMST}
            isNegativeCycle={isRejected}
            theme={muiTheme}
          />
        );
      });
    }, [graphData.edges, nodePositions, currentEdge, rejected, colors, muiTheme]);

    // Create stable references to the nodes
    const nodeElements = useMemo(() => {
      return graphData.nodes.map(node => {
        const position = nodePositions[node.id];
        if (!position) return null;

        // Determine node color based on visited status
        let nodeColor = colors.node;

        // If node is visited, color it green
        if (visitedSet.current.has(node.id)) {
          nodeColor = colors.visitedNode;
        }

        return (
          <MemoizedNode
            key={node.id}
            id={node.id}
            position={position}
            color={nodeColor}
            label={node.id.toString()}
          />
        );
      });
    }, [graphData.nodes, nodePositions, colors]);

    return (
      <group>
        {/* Edges */}
        {edgeElements}
        {/* Nodes */}
        {nodeElements}
      </group>
    );
  }, [graphData, nodePositions, colors, muiTheme]);

  return (
    <>
      {/* 3D Scene */}
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Prim\'s Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Levitating graph visualization */}
        <animated.group position={position} rotation={rotation}>
          <GraphVisualization />
        </animated.group>

        {/* Add a subtle fog effect for depth perception */}
        <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
          position={[0, -12, 0]}
          rotation={[0, 0, 0]}
        />
      </group>

      {/* Graph Labels Overlay */}
      <GraphLabelsOverlay
        graphData={graphData}
        nodePositions={nodePositions}
        camera={camera}
        canvasRef={canvasRef}
        isDark={isDark}
      />
    </>
  );
};

export default PrimsVisualization;
