// HeapSortAlgorithm.js
// This file contains the implementation of the Heap Sort algorithm and the visualization component.

import React from 'react';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    keyword: isDark ? "#569cd6" : "#0000ff", // Blue for keywords (function, if, else)
    function: isDark ? "#dcdcaa" : "#795e26", // Yellow/brown for function name
    parameter: isDark ? "#9cdcfe" : "#001080", // Blue for parameters/variables
    comment: isDark ? "#6a9955" : "#008000", // Green for comments/actions
    operator: isDark ? "#d4d4d4" : "#000000", // Default/operator color
    number: isDark ? "#b5cea8" : "#098658", // Green for numbers
    string: isDark ? "#ce9178" : "#a31515", // Red for strings
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

/**
 * Generate steps for Heap Sort algorithm visualization
 * @param {Array} array - The array to sort
 * @returns {Object} - Object containing steps and sorted array
 */
export const generateHeapSortSteps = (array) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...array];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [],
    heapified: [],
    movement: 'Initialize Heap Sort'
  });

  // Track sorted indices
  const sortedIndices = new Set();

  // Track heapified indices
  const heapifiedIndices = new Set();

  // Function to get parent index
  const getParentIndex = (i) => Math.floor((i - 1) / 2);

  // Function to get left child index
  const getLeftChildIndex = (i) => 2 * i + 1;

  // Function to get right child index
  const getRightChildIndex = (i) => 2 * i + 2;

  // Heapify function (sift down)
  const heapify = (n, i) => {
    let largest = i; // Initialize largest as root
    const left = getLeftChildIndex(i);
    const right = getRightChildIndex(i);

    // Compare with left child
    if (left < n) {
      steps.push({
        type: 'compare',
        array: [...arr],
        comparing: [i, left],
        swapping: [],
        sorted: [...sortedIndices],
        heapified: [...heapifiedIndices],
        movement: `Compare parent at index ${i} (value ${arr[i]}) with left child at index ${left} (value ${arr[left]})`
      });

      if (arr[left] > arr[largest]) {
        largest = left;
      }
    }

    // Compare with right child
    if (right < n) {
      steps.push({
        type: 'compare',
        array: [...arr],
        comparing: [largest, right],
        swapping: [],
        sorted: [...sortedIndices],
        heapified: [...heapifiedIndices],
        movement: `Compare current largest at index ${largest} (value ${arr[largest]}) with right child at index ${right} (value ${arr[right]})`
      });

      if (arr[right] > arr[largest]) {
        largest = right;
      }
    }

    // If largest is not root
    if (largest !== i) {
      steps.push({
        type: 'swap',
        array: [...arr],
        comparing: [],
        swapping: [i, largest],
        sorted: [...sortedIndices],
        heapified: [...heapifiedIndices],
        movement: `Swap element at index ${i} (value ${arr[i]}) with element at index ${largest} (value ${arr[largest]})`
      });

      // Swap elements
      [arr[i], arr[largest]] = [arr[largest], arr[i]];

      steps.push({
        type: 'swapped',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        heapified: [...heapifiedIndices],
        movement: `Swapped element at index ${i} with element at index ${largest}`
      });

      // Recursively heapify the affected sub-tree
      heapify(n, largest);
    }
  };

  // Build max heap
  steps.push({
    type: 'info',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [...sortedIndices],
    heapified: [...heapifiedIndices],
    movement: 'Building max heap'
  });

  // Start from the last non-leaf node and heapify each node in reverse order
  for (let i = Math.floor(arr.length / 2) - 1; i >= 0; i--) {
    steps.push({
      type: 'info',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      heapified: [...heapifiedIndices],
      movement: `Heapifying subtree rooted at index ${i}`
    });

    heapify(arr.length, i);

    // Mark this node as heapified
    heapifiedIndices.add(i);

    steps.push({
      type: 'heapified',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      heapified: [...heapifiedIndices],
      movement: `Subtree rooted at index ${i} is now heapified`
    });
  }

  steps.push({
    type: 'info',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [...sortedIndices],
    heapified: [...heapifiedIndices],
    movement: 'Max heap built successfully'
  });

  // Extract elements from heap one by one
  for (let i = arr.length - 1; i > 0; i--) {
    steps.push({
      type: 'swap',
      array: [...arr],
      comparing: [],
      swapping: [0, i],
      sorted: [...sortedIndices],
      heapified: [...heapifiedIndices],
      movement: `Swap root (max element) at index 0 (value ${arr[0]}) with element at index ${i} (value ${arr[i]})`
    });

    // Move current root to end
    [arr[0], arr[i]] = [arr[i], arr[0]];

    // Mark the element as sorted
    sortedIndices.add(i);

    steps.push({
      type: 'swapped',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      heapified: [...heapifiedIndices],
      movement: `Element ${arr[i]} is now in its correct sorted position at index ${i}`
    });

    // Heapify the reduced heap
    steps.push({
      type: 'info',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      heapified: [...heapifiedIndices],
      movement: `Heapifying reduced heap of size ${i}`
    });

    heapify(i, 0);
  }

  // Mark the first element as sorted
  sortedIndices.add(0);

  steps.push({
    type: 'complete',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [...sortedIndices],
    heapified: [],
    movement: 'Heap Sort completed'
  });

  return { steps, sortedArray: arr };
};

/**
 * Heap Sort Algorithm component for displaying pseudocode and explanation
 */
export const HeapSortAlgorithm = ({ theme, step = 0 }) => {
  const colors = getSyntaxColors(theme);

  // Define algorithm steps
  const steps = [
    { line: 0, description: "Initialize Heap Sort" },
    { line: 1, description: "Build max heap" },
    { line: 2, description: "Heapify subtrees" },
    { line: 3, description: "Extract elements from heap" },
    { line: 4, description: "Move root to end" },
    { line: 5, description: "Heapify reduced heap" },
    { line: 6, description: "Heap Sort complete" },
  ];

  // Get the current step
  const currentStep = steps[Math.min(step % steps.length, steps.length - 1)];

  // Heap Sort pseudocode
  const pseudocode = [
    { code: "function heapSort(arr):", highlight: currentStep.line === 0 },
    { code: "  // Build max heap", highlight: currentStep.line === 1 },
    { code: "  for i = Math.floor(arr.length / 2) - 1 to 0:", highlight: currentStep.line === 1 },
    { code: "    heapify(arr, arr.length, i)", highlight: currentStep.line === 2 },
    { code: "", highlight: false },
    { code: "  // Extract elements from heap one by one", highlight: currentStep.line === 3 },
    { code: "  for i = arr.length - 1 to 1:", highlight: currentStep.line === 3 },
    { code: "    // Move current root to end", highlight: false },
    { code: "    swap arr[0] and arr[i]", highlight: currentStep.line === 4 },
    { code: "", highlight: false },
    { code: "    // Heapify the reduced heap", highlight: false },
    { code: "    heapify(arr, i, 0)", highlight: currentStep.line === 5 },
    { code: "", highlight: false },
    { code: "  return arr", highlight: currentStep.line === 6 },
    { code: "", highlight: false },
    { code: "function heapify(arr, n, i):", highlight: false },
    { code: "  largest = i  // Initialize largest as root", highlight: false },
    { code: "  left = 2 * i + 1  // Left child", highlight: false },
    { code: "  right = 2 * i + 2  // Right child", highlight: false },
    { code: "", highlight: false },
    { code: "  // If left child is larger than root", highlight: false },
    { code: "  if left < n and arr[left] > arr[largest]:", highlight: false },
    { code: "    largest = left", highlight: false },
    { code: "", highlight: false },
    { code: "  // If right child is larger than largest so far", highlight: false },
    { code: "  if right < n and arr[right] > arr[largest]:", highlight: false },
    { code: "    largest = right", highlight: false },
    { code: "", highlight: false },
    { code: "  // If largest is not root", highlight: false },
    { code: "  if largest != i:", highlight: false },
    { code: "    swap arr[i] and arr[largest]", highlight: false },
    { code: "", highlight: false },
    { code: "    // Recursively heapify the affected sub-tree", highlight: false },
    { code: "    heapify(arr, n, largest)", highlight: false },
  ];

  return (
    <Box
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
        '&::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
          },
        },
      }}
    >
      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {currentStep?.description || "Algorithm complete"}
        </Typography>
      </Paper>

      {/* Pseudocode */}
      <Box
        sx={{
          fontFamily: "monospace",
          fontSize: "0.9rem",
          lineHeight: 1.5,
          whiteSpace: "pre",
          p: 1,
          overflowX: "auto",
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        {pseudocode.map((line, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              bgcolor: line.highlight ? colors.highlight : "transparent",
              borderRadius: 1,
              px: 1,
              py: 0.25,
            }}
          >
            {line.highlight && (
              <ArrowRightIcon
                sx={{
                  color: colors.function,
                  mr: 1,
                  fontSize: "1.2rem",
                }}
              />
            )}
            <Box sx={{ ml: line.highlight ? 0 : 3 }}>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  color: colors.text,
                  fontWeight: line.highlight ? 600 : 400,
                }}
              >
                {line.code}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default HeapSortAlgorithm;
