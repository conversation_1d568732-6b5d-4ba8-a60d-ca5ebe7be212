// MergeSort/index.js
// Export only what's needed from this algorithm

import React from 'react';
import { SimulationContextProvider, SimulationEngine, StepProcessor } from '../../../simulation';
import MergeSortController from './MergeSortController';
import MergeSortVisualization from './MergeSortVisualization';

// Wrap the controller with the simulation context
const WrappedController = (props) => (
  <SimulationContextProvider>
    <MergeSortController {...props} />
    <SimulationEngine />
    <StepProcessor />
  </SimulationContextProvider>
);

export const metadata = {
  id: 'MergeSort',
  name: 'Merge Sort',
  description: 'An efficient, stable, comparison-based, divide and conquer sorting algorithm.',
  timeComplexity: 'O(n log n)',
  spaceComplexity: 'O(n)',
  defaultParams: {
    arraySize: 10,
    randomize: true,
    customArray: [],
  },
};

export const components = {
  visualization: MergeSortVisualization,
  controller: WrappedController,
};

export default {
  ...metadata,
  ...components,
};
