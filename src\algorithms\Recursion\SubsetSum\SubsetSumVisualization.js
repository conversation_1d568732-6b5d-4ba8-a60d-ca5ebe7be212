// SubsetSumVisualization.js
// 3D visualization component for the Subset Sum algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const NUMBER_SIZE = 0.8;
const NUMBER_SPACING = 1.5;
const LEVEL_HEIGHT = 2.5;
const ORBIT_RADIUS = 6;

const SubsetSumVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);
  const rotationRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 15, 15);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();
  
  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);
      
      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty data to show when no steps are available
      setCurrentStepData({
        numbers: [3, 34, 4, 12, 5, 2],
        target: 9,
        currentIndex: -1,
        currentSum: 0,
        dp: null,
        currentSubset: [],
        solutions: []
      });
    }
    
    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;
      
      // Get current time
      const currentTime = clock.getElapsedTime();
      
      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);
        
        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }
    
    // Rotate the visualization slowly
    rotationRef.current += 0.001;
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Number colors
    number: isDark ? '#2d3436' : '#636e72',
    numberText: isDark ? '#ffffff' : '#ffffff',
    currentNumber: isDark ? '#e84118' : '#ff7675',
    includedNumber: isDark ? '#0984e3' : '#74b9ff',
    
    // Subset colors
    subset: isDark ? '#00b894' : '#55efc4',
    subsetText: isDark ? '#2d3436' : '#2d3436',
    currentSubset: isDark ? '#fdcb6e' : '#ffeaa7',
    
    // Target colors
    target: isDark ? '#e84118' : '#ff7675',
    targetText: isDark ? '#ffffff' : '#ffffff',
    
    // DP table colors
    dpTrue: isDark ? '#00b894' : '#55efc4',
    dpFalse: isDark ? '#e84118' : '#ff7675',
    dpText: isDark ? '#ffffff' : '#ffffff',
    
    // Connection colors
    connection: isDark ? '#b2bec3' : '#dfe6e9',
    
    // Background colors
    background: isDark ? '#1e272e' : '#f5f6fa',
    
    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.number, label: 'Number' },
    { color: colors.currentNumber, label: 'Current Number' },
    { color: colors.includedNumber, label: 'Included Number' },
    { color: colors.subset, label: 'Valid Subset' },
    { color: colors.currentSubset, label: 'Current Subset' },
    { color: colors.target, label: 'Target Sum' },
  ], [colors]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, rotationRef.current, 0];

  // Create a number mesh
  const NumberCube = ({ position, number, color, isHighlighted }) => {
    return (
      <group position={position}>
        {/* Number cube */}
        <mesh castShadow>
          <boxGeometry args={[NUMBER_SIZE, NUMBER_SIZE, NUMBER_SIZE]} />
          <meshStandardMaterial color={color} />
        </mesh>
        
        {/* Number text */}
        <Html
          position={[0, 0, NUMBER_SIZE/2 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: colors.numberText,
            fontSize: `${NUMBER_SIZE * 20}px`,
            fontWeight: 'bold',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {number}
          </div>
        </Html>
        
        {/* Highlight effect */}
        {isHighlighted && (
          <mesh>
            <boxGeometry args={[NUMBER_SIZE + 0.1, NUMBER_SIZE + 0.1, NUMBER_SIZE + 0.1]} />
            <meshStandardMaterial color={colors.currentNumber} wireframe={true} />
          </mesh>
        )}
      </group>
    );
  };

  // Create a subset mesh
  const Subset = ({ position, numbers, isCurrent }) => {
    return (
      <group position={position}>
        {/* Subset background */}
        <mesh castShadow>
          <boxGeometry args={[numbers.length * NUMBER_SPACING * 0.8, NUMBER_SIZE, NUMBER_SIZE]} />
          <meshStandardMaterial color={isCurrent ? colors.currentSubset : colors.subset} />
        </mesh>
        
        {/* Subset numbers */}
        {numbers.map((number, index) => {
          const offset = (index - (numbers.length - 1) / 2) * NUMBER_SPACING * 0.7;
          return (
            <Html
              key={index}
              position={[offset, 0, NUMBER_SIZE/2 + 0.01]}
              center
              occlude
            >
              <div style={{
                color: colors.subsetText,
                fontSize: `${NUMBER_SIZE * 16}px`,
                fontWeight: 'bold',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                pointerEvents: 'none'
              }}>
                {number}
              </div>
            </Html>
          );
        })}
      </group>
    );
  };

  // Create a target sum mesh
  const TargetSum = ({ position, target }) => {
    return (
      <group position={position}>
        {/* Target cube */}
        <mesh castShadow>
          <sphereGeometry args={[NUMBER_SIZE * 0.7, 32, 32]} />
          <meshStandardMaterial color={colors.target} />
        </mesh>
        
        {/* Target text */}
        <Html
          position={[0, 0, 0]}
          center
          occlude
        >
          <div style={{
            color: colors.targetText,
            fontSize: `${NUMBER_SIZE * 16}px`,
            fontWeight: 'bold',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {target}
          </div>
        </Html>
      </group>
    );
  };

  // Create a DP table cell mesh
  const DPCell = ({ position, value, row, col }) => {
    return (
      <group position={position}>
        {/* Cell cube */}
        <mesh castShadow>
          <boxGeometry args={[NUMBER_SIZE * 0.8, NUMBER_SIZE * 0.8, NUMBER_SIZE * 0.2]} />
          <meshStandardMaterial color={value ? colors.dpTrue : colors.dpFalse} />
        </mesh>
        
        {/* Cell text */}
        <Html
          position={[0, 0, NUMBER_SIZE * 0.1 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: colors.dpText,
            fontSize: `${NUMBER_SIZE * 12}px`,
            fontWeight: 'bold',
            width: '20px',
            height: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {value ? 'T' : 'F'}
          </div>
        </Html>
      </group>
    );
  };

  // Render the Subset Sum visualization
  const renderSubsetSumVisualization = () => {
    if (!currentStepData) return null;

    const { numbers, target, currentIndex, currentSum, dp, currentSubset, solutions } = currentStepData;
    
    // Determine if we're using the DP approach
    const isDpApproach = dp !== null;
    
    // Calculate positions based on the number of elements
    const numNumbers = numbers.length;
    const angleStep = (2 * Math.PI) / numNumbers;
    
    return (
      <group position={position} rotation={rotation}>
        {/* Original numbers in a circle */}
        {numbers.map((number, index) => {
          const angle = index * angleStep;
          const x = Math.sin(angle) * ORBIT_RADIUS;
          const z = Math.cos(angle) * ORBIT_RADIUS;
          
          // Determine if this number is being used or is the current one
          const isIncluded = currentSubset.includes(number);
          const isCurrent = index === currentIndex;
          
          return (
            <NumberCube
              key={`number-${index}`}
              position={[x, 0, z]}
              number={number}
              color={isCurrent ? colors.currentNumber : isIncluded ? colors.includedNumber : colors.number}
              isHighlighted={isCurrent}
            />
          );
        })}
        
        {/* Target sum */}
        <TargetSum
          position={[0, 0, 0]}
          target={target}
        />
        
        {/* Current subset being built */}
        {currentSubset.length > 0 && (
          <Subset
            position={[0, LEVEL_HEIGHT, 0]}
            numbers={currentSubset}
            isCurrent={true}
          />
        )}
        
        {/* Current sum display */}
        {currentSum > 0 && (
          <Html position={[0, LEVEL_HEIGHT + 1.5, 0]}>
            <div style={{
              background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              color: isDark ? 'white' : 'black',
              padding: '8px 16px',
              borderRadius: '4px',
              fontWeight: 'bold',
              textAlign: 'center',
              whiteSpace: 'nowrap'
            }}>
              Current Sum: {currentSum}
            </div>
          </Html>
        )}
        
        {/* Found solutions */}
        {solutions.slice(0, 5).map((solution, index) => {
          // Calculate position in a spiral pattern
          const spiralAngle = index * 0.6;
          const spiralRadius = 3 + index * 0.3;
          const x = Math.sin(spiralAngle) * spiralRadius;
          const z = Math.cos(spiralAngle) * spiralRadius;
          
          return (
            <Subset
              key={`solution-${index}`}
              position={[x, LEVEL_HEIGHT * 2, z]}
              numbers={solution}
              isCurrent={false}
            />
          );
        })}
        
        {/* DP table visualization (simplified) */}
        {isDpApproach && dp && (
          <group position={[-ORBIT_RADIUS, -LEVEL_HEIGHT, -ORBIT_RADIUS]}>
            {/* Only show a portion of the DP table to avoid clutter */}
            {dp.slice(0, Math.min(5, dp.length)).map((row, i) => (
              row.slice(0, Math.min(5, row.length)).map((cell, j) => (
                <DPCell
                  key={`dp-${i}-${j}`}
                  position={[j * NUMBER_SIZE, 0, i * NUMBER_SIZE]}
                  value={cell}
                  row={i}
                  col={j}
                />
              ))
            ))}
            
            {/* DP table label */}
            <Html position={[2 * NUMBER_SIZE, 1, 2 * NUMBER_SIZE]}>
              <div style={{
                background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                color: isDark ? 'white' : 'black',
                padding: '4px 8px',
                borderRadius: '4px',
                fontWeight: 'bold',
                fontSize: '12px',
                textAlign: 'center',
                whiteSpace: 'nowrap'
              }}>
                DP Table (partial view)
              </div>
            </Html>
          </group>
        )}
        
        {/* Solutions count */}
        {solutions.length > 0 && (
          <Html position={[0, LEVEL_HEIGHT * 3, 0]}>
            <div style={{
              background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              color: isDark ? 'white' : 'black',
              padding: '8px 16px',
              borderRadius: '4px',
              fontWeight: 'bold',
              textAlign: 'center',
              whiteSpace: 'nowrap'
            }}>
              Found Subsets: {solutions.length}
            </div>
          </Html>
        )}
      </group>
    );
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Subset Sum Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position} rotation={rotation}>
          {renderSubsetSumVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default SubsetSumVisualization;
