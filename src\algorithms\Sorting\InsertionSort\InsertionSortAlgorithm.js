// InsertionSortAlgorithm.js
// This file contains the core logic for the Insertion Sort algorithm

/**
 * Generate steps for Insertion Sort algorithm visualization
 * @param {Array} array - The array to sort
 * @returns {Object} - Object containing steps and sorted array
 */
export const generateInsertionSortSteps = (array) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...array];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [], // Start with no elements sorted
    current: null,
    movement: 'Initialize Insertion Sort'
  });

  // Add step to mark first element as sorted
  steps.push({
    type: 'select',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [0], // First element is considered sorted initially
    current: 0,
    movement: `First element ${arr[0]} is considered sorted initially`
  });

  // Insertion Sort algorithm
  for (let i = 1; i < arr.length; i++) {
    // Current element to be inserted
    const current = arr[i];

    // Add step to show current element selection
    steps.push({
      type: 'select',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: Array.from({ length: i }, (_, idx) => idx),
      current: i,
      movement: `Select element at index ${i}: ${current}`
    });

    // Find position for insertion
    let j = i - 1;

    // Compare with elements in sorted portion
    while (j >= 0) {
      // Add comparison step
      steps.push({
        type: 'compare',
        array: [...arr],
        comparing: [j, j + 1],
        swapping: [],
        sorted: Array.from({ length: i }, (_, idx) => idx),
        current: i,
        movement: `Compare ${arr[j]} with ${current}`
      });

      // If element at j is greater than current, shift it right
      if (arr[j] > current) {
        // Shift element
        arr[j + 1] = arr[j];

        // Add shift step
        steps.push({
          type: 'shift',
          array: [...arr],
          comparing: [],
          swapping: [j, j + 1],
          sorted: Array.from({ length: i - 1 }, (_, idx) => idx), // Don't mark current element as sorted yet
          current: i,
          movement: `Shift element ${arr[j]} to position ${j + 1}`
        });

        j--;
      } else {
        // Found the correct position, break the loop
        break;
      }
    }

    // Place current element at the correct position
    arr[j + 1] = current;

    // Add insertion step
    steps.push({
      type: 'insert',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: Array.from({ length: i + 1 }, (_, idx) => idx), // Now mark the current element as sorted
      current: j + 1,
      movement: `Insert ${current} at position ${j + 1} and mark it as sorted`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: Array.from({ length: arr.length }, (_, idx) => idx),
    current: null,
    movement: 'Insertion Sort complete'
  });

  return { steps, sortedArray: arr };
};

// Default export
const InsertionSortAlgorithm = {
  generateSteps: generateInsertionSortSteps
};

export default InsertionSortAlgorithm;
