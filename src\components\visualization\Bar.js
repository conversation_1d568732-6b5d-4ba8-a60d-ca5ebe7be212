import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable 3D bar component for algorithm visualizations
 * 
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the bar
 * @param {number} props.height - Height of the bar
 * @param {number} props.width - Width of the bar
 * @param {string} props.color - Color of the bar
 * @param {number} props.value - Value represented by the bar
 * @param {number} props.index - Index of the bar in the array
 * @param {boolean} props.showValue - Whether to show the value label
 * @param {boolean} props.showIndex - Whether to show the index label
 * @param {boolean} props.showArrow - Whether to show an arrow above the bar
 * @param {number} props.depth - Depth of the bar (default: 0.5)
 * @param {number} props.opacity - Opacity of the bar (default: 1)
 * @returns {JSX.Element} - The rendered bar component
 */
const Bar = ({ 
    position, 
    height, 
    width, 
    color, 
    value, 
    index, 
    showValue = true, 
    showIndex = true, 
    showArrow = false,
    depth = 0.5,
    opacity = 1
}) => {
    const theme = useTheme();
    
    // Calculate a slightly darker color for the bar edges
    const getEdgeColor = (baseColor) => {
        // Convert hex to RGB
        const r = parseInt(baseColor.slice(1, 3), 16);
        const g = parseInt(baseColor.slice(3, 5), 16);
        const b = parseInt(baseColor.slice(5, 7), 16);

        // Darken the color
        const darkenFactor = 0.7;
        const newR = Math.floor(r * darkenFactor);
        const newG = Math.floor(g * darkenFactor);
        const newB = Math.floor(b * darkenFactor);

        // Convert back to hex
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    };

    const edgeColor = getEdgeColor(color);
    const barHeight = Math.max(0.1, height); // Ensure minimum height for visibility

    return (
        <group position={position}>
            {/* Main bar */}
            <mesh position={[0, barHeight / 2, 0]} castShadow receiveShadow>
                <boxGeometry args={[width, barHeight, depth]} />
                <meshStandardMaterial 
                    color={color} 
                    transparent={opacity < 1}
                    opacity={opacity}
                    metalness={0.3}
                    roughness={0.7}
                />
            </mesh>
            
            {/* Bar edges for better 3D definition */}
            <mesh position={[0, barHeight / 2, 0]} receiveShadow>
                <boxGeometry args={[width + 0.02, barHeight + 0.02, depth + 0.02]} />
                <meshStandardMaterial 
                    color={edgeColor} 
                    transparent={opacity < 1}
                    opacity={opacity}
                    metalness={0.4}
                    roughness={0.6}
                />
            </mesh>
            
            {/* Value label */}
            {showValue && (
                <Html position={[0, barHeight + 0.3, 0]} center>
                    <div style={{
                        color: theme.palette.text.primary,
                        background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.6)' : 'rgba(255,255,255,0.8)',
                        padding: '2px 6px',
                        borderRadius: '6px',
                        fontSize: '0.7rem',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        boxShadow: `0 2px 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)'}`,
                        border: `1px solid ${color}80`,
                        zIndex: 10,
                        pointerEvents: 'none'
                    }}>
                        {value}
                    </div>
                </Html>
            )}
            
            {/* Index label */}
            {showIndex && (
                <Html position={[0, -0.3, 0]} center>
                    <div style={{
                        color: theme.palette.text.secondary,
                        background: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.7)',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        fontSize: '0.7rem',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        textShadow: `0 0 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.3)'}`,
                        boxShadow: `0 1px 3px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.2)'}`
                    }}>
                        {index}
                    </div>
                </Html>
            )}
            
            {/* Current element arrow */}
            {showArrow && (
                <Html position={[0, barHeight + 1, 0]} center>
                    <div style={{
                        color: theme.palette.secondary.main,
                        background: 'transparent',
                        fontSize: '1.2rem',
                        fontWeight: 'bold',
                        textAlign: 'center'
                    }}>
                        ↓
                    </div>
                </Html>
            )}
        </group>
    );
};

export default Bar;
