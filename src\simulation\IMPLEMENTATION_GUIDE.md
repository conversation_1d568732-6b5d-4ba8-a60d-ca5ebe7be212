# Simulation Architecture Implementation Guide

This guide provides step-by-step instructions for implementing algorithm simulations using the new architecture.

## Table of Contents

1. [Overview](#overview)
2. [Step 1: Define Algorithm Steps](#step-1-define-algorithm-steps)
3. [Step 2: Create Step Visualizers](#step-2-create-step-visualizers)
4. [Step 3: Implement Algorithm Controller](#step-3-implement-algorithm-controller)
4. [Step 4: Connect to the Simulation Engine](#step-4-connect-to-the-simulation-engine)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

## Overview

The simulation architecture consists of three main components:

1. **Contexts** - Provide state management for simulations
2. **Components** - Handle step execution and visualization
3. **Utilities** - Provide helper functions for simulations

## Step 1: Define Algorithm Steps

Create a file for generating algorithm steps (e.g., `AlgorithmSteps.js`):

```javascript
// Import step utilities
import {
  createInitialStep,
  createCompletionStep,
  // Import other step creators as needed
} from '../../simulation/utils/stepUtils';

export const generateAlgorithmSteps = (input) => {
  // Validate input
  if (!isValidInput(input)) {
    return [];
  }

  // Initialize steps array
  const steps = [];

  // Add initial step
  steps.push(
    createInitialStep(
      deepClone(input),
      'Initial state description'
    )
  );

  // Implement algorithm logic and add steps
  // ...

  // Add final step
  steps.push(
    createCompletionStep(
      deepClone(result),
      'Algorithm complete description'
    )
  );

  return steps;
};
```

## Step 2: Create Step Visualizers

Create visualizer components for each step type:

```javascript
// Import React and necessary components
import React from 'react';
import { Box, Typography } from '@mui/material';

// Create visualizer for initial step
const InitialVisualizer = ({ step }) => (
  <Box>
    <Typography variant="h6">Initial State</Typography>
    {/* Render initial state visualization */}
  </Box>
);

// Create visualizers for other step types
// ...

// Create default visualizer for any step type not explicitly handled
const DefaultVisualizer = ({ step }) => (
  <Box>
    <Typography variant="h6">{step.type} Step</Typography>
    <Typography variant="body1">{step.message}</Typography>
  </Box>
);

// Export map of step types to visualizer components
export const visualizers = {
  initial: InitialVisualizer,
  // Map other step types to their visualizers
  // ...
};

// Export default visualizer
export const defaultVisualizer = DefaultVisualizer;
```

## Step 3: Implement Algorithm Controller

Create a controller component for the algorithm:

```javascript
// Import React and necessary components
import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Slider } from '@mui/material';

// Import simulation hooks
import {
  useStep,
  useSimulation,
  useAlgorithmData
} from '../../simulation/context';

// Import step generator
import { generateAlgorithmSteps } from './AlgorithmSteps';

const AlgorithmController = () => {
  // Get contexts
  const { 
    setSteps,
    goToFirstStep
  } = useStep();
  
  const { 
    resetSimulation
  } = useSimulation();
  
  const { 
    updateInputData,
    inputData
  } = useAlgorithmData();

  // Local state for algorithm parameters
  const [param1, setParam1] = useState(defaultValue1);
  const [param2, setParam2] = useState(defaultValue2);

  // Generate steps when parameters change
  useEffect(() => {
    // Skip if no input data
    if (!inputData) {
      return;
    }

    // Generate steps
    const steps = generateAlgorithmSteps(inputData, param1, param2);
    
    // Update steps
    setSteps(steps);
    
    // Reset simulation
    resetSimulation();
  }, [inputData, param1, param2, setSteps, resetSimulation]);

  // Handle parameter changes
  const handleParam1Change = (event, newValue) => {
    setParam1(newValue);
  };

  const handleParam2Change = (event, newValue) => {
    setParam2(newValue);
  };

  // Handle input data generation
  const handleGenerateInput = () => {
    // Generate input data
    const newInput = generateInput(param1, param2);
    
    // Update input data
    updateInputData(newInput);
  };

  return (
    <Box>
      {/* Render parameter controls */}
      <Box sx={{ mb: 2 }}>
        <Typography gutterBottom>Parameter 1: {param1}</Typography>
        <Slider
          value={param1}
          onChange={handleParam1Change}
          // Other slider props
        />
      </Box>

      {/* Render other controls */}
      {/* ... */}

      <Button 
        variant="contained" 
        onClick={handleGenerateInput}
      >
        Generate Input
      </Button>
    </Box>
  );
};

export default AlgorithmController;
```

## Step 4: Connect to the Simulation Engine

Create a main component that connects everything:

```javascript
// Import React and necessary components
import React from 'react';
import { Box } from '@mui/material';

// Import simulation components
import {
  SimulationContextProvider,
  SimulationEngine,
  StepProcessor,
  StepVisualizer
} from '../../simulation';

// Import algorithm components
import AlgorithmController from './AlgorithmController';
import { visualizers, defaultVisualizer } from './AlgorithmVisualizers';

const AlgorithmSimulation = () => {
  return (
    <SimulationContextProvider>
      <Box>
        {/* Render algorithm controller */}
        <AlgorithmController />
        
        {/* Render step visualizer */}
        <StepVisualizer 
          visualizers={visualizers}
          defaultVisualizer={defaultVisualizer}
        />
        
        {/* Include simulation engine and step processor */}
        <SimulationEngine />
        <StepProcessor />
      </Box>
    </SimulationContextProvider>
  );
};

export default AlgorithmSimulation;
```

## Best Practices

1. **Keep Step Generation Pure** - Step generation functions should be pure functions that don't depend on external state.

2. **Use Step Types Consistently** - Define a consistent set of step types for your algorithm and use them consistently.

3. **Provide Meaningful Messages** - Each step should have a clear, descriptive message that explains what's happening.

4. **Optimize Visualizations** - Visualizations should be optimized for performance, especially for large data sets.

5. **Handle Edge Cases** - Make sure your step generation handles edge cases like empty inputs or invalid parameters.

6. **Separate Logic from Visualization** - Keep algorithm logic separate from visualization logic.

7. **Use Deep Clones for Step Data** - Always use deep clones for step data to prevent unintended mutations.

## Troubleshooting

### Common Issues

1. **Steps Not Updating** - Make sure you're using deep clones for step data and properly updating the steps array.

2. **Visualizations Not Rendering** - Check that your visualizer components are correctly mapped to step types.

3. **Animation Issues** - Ensure that your step processor is correctly handling animation progress.

4. **Performance Problems** - Optimize your visualizations and consider using memoization for expensive calculations.

### Debugging Tips

1. **Log Step Data** - Add console logs to check step data at different points in the process.

2. **Use React DevTools** - Use React DevTools to inspect component props and state.

3. **Check Context Values** - Verify that context values are being correctly provided and consumed.

4. **Test Step Generation Separately** - Test your step generation function separately to ensure it's producing the expected steps.
