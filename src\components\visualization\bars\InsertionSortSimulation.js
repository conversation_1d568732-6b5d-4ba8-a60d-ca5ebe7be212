// InsertionSortSimulation.js - Clean insertion sort visualization component
// Consumes visualizationData from controller with minimal complexity

import React, { useMemo, useEffect } from 'react';
import { Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ThemeHtml from '../ThemeHtml';
import InsertionSortConfig from '../../../algorithms/Sorting/InsertionSort/InsertionSortConfig';

const InsertionSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true,
  shiftAnimation,
  placementAnimation,
  originalArray,
  onWidthChange
}) => {
  const theme = useTheme();

  // Get configuration for positioning
  const config = InsertionSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // SIMPLIFIED STATIC POSITIONING - No dynamic gaps but proper total width calculation
  const layoutData = useMemo(() => {
    if (!vizData?.mainArray?.values) return { barPositions: [], totalWidth: 0 };

    const values = vizData.mainArray.values;
    const baseSpacing = barWidth + barSpacing;
    const visualOffset = config.mainArray.bars.visualOffset;

    // Calculate STATIC bar positions - these never change
    const staticPositions = [];
    const barsOnlyWidth = (values.length * baseSpacing) - barSpacing;
    const startX = -barsOnlyWidth / 2 + visualOffset;

    for (let i = 0; i < values.length; i++) {
      staticPositions.push({
        index: i,
        xPos: startX + i * baseSpacing  // Static position - never changes
      });
    }

    // Calculate the ACTUAL total width covered by bars (from leftmost to rightmost edge)
    const leftmostEdge = staticPositions[0].xPos - barWidth / 2;
    const rightmostEdge = staticPositions[staticPositions.length - 1].xPos + barWidth / 2;
    const actualTotalWidth = rightmostEdge - leftmostEdge;

    // DEBUG: Log the calculations
    console.log('InsertionSort Static Layout:', {
      values: values.map((v, i) => `${i}:${v}`),
      barsOnlyWidth,
      actualTotalWidth,
      leftmostEdge: leftmostEdge.toFixed(2),
      rightmostEdge: rightmostEdge.toFixed(2),
      startX: startX.toFixed(2),
      positions: staticPositions.map(p => `${p.index}:${p.xPos.toFixed(1)}`)
    });

    return {
      barPositions: staticPositions,
      totalWidth: actualTotalWidth
    };
  }, [vizData?.mainArray?.values, barWidth, barSpacing]);

  // Extract values
  const { barPositions, totalWidth: actualTotalWidth } = layoutData;

  // Notify parent of width changes for platform sizing
  useEffect(() => {
    if (onWidthChange && actualTotalWidth > 0) {
      onWidthChange(actualTotalWidth);
    }
  }, [actualTotalWidth, onWidthChange]);

  // If no visualization data, return null
  if (!vizData) {
    return null;
  }

  // Render the main array bars with insertion sort highlights
  const renderMainArray = () => {
    if (!vizData.mainArray?.values) return null;

    // Use current step data with held element support
    const {
      values,
      sortedIndices = [],
      comparingIndices = [],
      shiftingIndices = [],
      currentIndex = null,
      heldElement = null
    } = vizData.mainArray;

    // Use original array for consistent maxValue calculation throughout the algorithm
    const maxValue = originalArray && originalArray.length > 0
      ? Math.max(...originalArray)
      : Math.max(...values.filter(v => v !== null));

    return values.map((value, index) => {
      // For gaps (null values), still render index label but no bar/value
      const isGap = value === null;
      const isCurrentElement = currentIndex === index;
      const isComparing = comparingIndices.includes(index);
      const isShifting = shiftingIndices.includes(index);
      const isSorted = sortedIndices.includes(index);

      // For gaps, we only render the index label
      if (isGap) {
        const barPosition = barPositions.find(pos => pos.index === index);
        const xPos = barPosition ? barPosition.xPos : 0;

        return (
          <group key={`gap-${index}`} position={[xPos, 0, 0]}>
            {/* Index label for gap position */}
            {showIndices && (
              <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
                <Paper
                  elevation={config.mainArray.indexLabels.elevation}
                  sx={{
                    width: config.mainArray.indexLabels.size.width,
                    height: config.mainArray.indexLabels.size.height,
                    borderRadius: '50%',
                    bgcolor: 'background.paper',
                    border: 1,
                    borderColor: 'divider',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    opacity: 0.5 // Dimmed to show it's empty
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.mainArray.indexLabels.fontSize,
                      fontWeight: config.mainArray.indexLabels.fontWeight
                    }}
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </Paper>
              </ThemeHtml>
            )}
          </group>
        );
      }

      const barHeight = (value / maxValue) * maxBarHeight;

      // Determine bar color based on state
      let barColor;
      if (isCurrentElement) {
        barColor = colors.merging; // Current element being inserted
      } else if (isComparing) {
        barColor = colors.comparing; // Elements being compared
      } else if (isShifting) {
        barColor = colors.swapping; // Elements being shifted
      } else if (isSorted) {
        barColor = colors.sorted; // Elements in sorted portion
      } else {
        barColor = colors.default || colors.bar;
      }

      // Use pre-calculated bar position
      const barPosition = barPositions.find(pos => pos.index === index);
      let xPos = barPosition ? barPosition.xPos : 0;
      let yPos = 0;
      let zPos = 0;

      // Apply shift animation if active
      if (shiftAnimation?.active && shiftAnimation.indices.includes(index)) {
        const [fromIndex, toIndex] = shiftAnimation.indices;
        const { progress } = shiftAnimation;

        // Determine if this is the element being shifted
        const isShiftingElement = index === fromIndex;

        if (isShiftingElement) {
          // Get positions for shift animation
          const fromBarPos = barPositions.find(pos => pos.index === fromIndex);
          const toBarPos = barPositions.find(pos => pos.index === toIndex);

          if (fromBarPos && toBarPos) {
            const startX = fromBarPos.xPos;
            const endX = toBarPos.xPos;
            const distance = endX - startX;
            const centerX = startX + distance / 2;
            const radius = Math.abs(distance) / 2;

            // Calculate position along arc path
            const angle = progress * Math.PI;
            xPos = centerX + Math.cos(Math.PI - angle) * radius;
            yPos = Math.sin(Math.PI - angle) * radius * (config.animation?.types?.shift?.height || 1.5);
            zPos = Math.sin(angle) * 0.5; // Slight z-axis movement for 3D effect

            console.log(`Shift animation - Bar ${index} (value ${value}):`, {
              originalPos: { x: startX, y: 0, z: 0 },
              targetPos: { x: endX, y: 0, z: 0 },
              currentPos: { x: xPos, y: yPos, z: zPos },
              progress: progress
            });
          }
        }
      }

      return (
        <group key={`main-${index}-${value}`} position={[xPos, yPos, zPos]}>
          {/* Base of the bar (like QuickSort) */}
          {config.mainArray.bars.base.enabled && (
            <mesh
              position={[0, 0, 0]}
              castShadow={config.visual.effects.shadows}
              receiveShadow={config.visual.effects.shadows}
            >
              <boxGeometry args={[
                barWidth * config.mainArray.bars.base.widthScale,
                config.mainArray.bars.base.height,
                barWidth * config.mainArray.bars.base.depthScale
              ]} />
              <meshStandardMaterial
                color={barColor}
                transparent={config.mainArray.bars.base.material.transparent}
                opacity={config.mainArray.bars.base.material.opacity}
                roughness={config.mainArray.bars.base.material.roughness}
                metalness={config.mainArray.bars.base.material.metalness}
              />
            </mesh>
          )}

          {/* The bar itself (narrower, sitting on top of base) */}
          <mesh
            position={[0, barHeight / 2, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              barWidth * config.mainArray.bars.geometry.widthScale,
              barHeight,
              barWidth * config.mainArray.bars.geometry.depthScale
            ]} />
            <meshStandardMaterial
              color={barColor}
              transparent={config.mainArray.bars.material.transparent}
              opacity={config.mainArray.bars.material.opacity}
              roughness={config.mainArray.bars.material.roughness}
              metalness={config.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {(() => {
            if (index === 0) {
              console.log('InsertionSortSimulation showValues:', showValues, 'for value:', value);
            }
            return showValues;
          })() && (
            <ThemeHtml position={[config.mainArray.valueLabels.offset[0], barHeight + config.mainArray.valueLabels.offset[1], config.mainArray.valueLabels.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.valueLabels.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.valueLabels.padding.horizontal),
                  py: theme.spacing(config.mainArray.valueLabels.padding.vertical),
                  minWidth: config.mainArray.valueLabels.minWidth,
                  borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                  border: 1,
                  borderColor: isCurrentElement ? colors.merging : 'divider',
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.valueLabels.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: isCurrentElement ? "bold" : config.mainArray.valueLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Index label */}
          {showIndices && (
            <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.indexLabels.elevation}
                sx={{
                  width: config.mainArray.indexLabels.size.width,
                  height: config.mainArray.indexLabels.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: isCurrentElement ? colors.merging : 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.indexLabels.fontSize,
                    fontWeight: isCurrentElement ? "bold" : config.mainArray.indexLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Current element indicator */}
          {isCurrentElement && (
            <ThemeHtml position={[config.mainArray.currentIndicator.offset[0], barHeight + config.mainArray.currentIndicator.offset[1], config.mainArray.currentIndicator.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.currentIndicator.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.currentIndicator.padding.horizontal),
                  py: theme.spacing(config.mainArray.currentIndicator.padding.vertical),
                  borderRadius: theme.shape.borderRadius / config.mainArray.currentIndicator.borderRadius,
                  border: 1,
                  borderColor: colors.merging,
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.currentIndicator.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: config.mainArray.currentIndicator.fontWeight
                  }}
                  color="text.primary"
                >
                  {config.mainArray.currentIndicator.text}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };

  // Render comparison arrows (like HeapSort - simple ↓ arrows above bars)
  const renderComparisonArrows = () => {
    if (!vizData.mainArray?.comparingIndices || vizData.mainArray.comparingIndices.length === 0) {
      return null;
    }

    const { comparingIndices } = vizData.mainArray;

    return comparingIndices.map((index) => {
      const barPosition = barPositions.find(pos => pos.index === index);
      const xPos = barPosition ? barPosition.xPos : 0;

      return (
        <group key={`arrow-${index}`} position={[xPos, 0, 0]}>
          {/* Comparison arrow - exactly like HeapSort */}
          <ThemeHtml
            position={[0, maxBarHeight + 1, 0]} // Above the bars
            center
            sprite
            theme={theme}
          >
            <div style={{
              color: colors.comparing || config.colors.palette.comparing,
              background: 'transparent',
              fontSize: '1.4rem', // Same as HeapSort
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: `0 0 8px ${colors.comparing || config.colors.palette.comparing}, 0 0 16px ${colors.comparing || config.colors.palette.comparing}`,
              userSelect: 'none',
              pointerEvents: 'none',
              transform: 'scale(1) rotate(0deg)', // Point down to bars
              animation: 'pulse 1s infinite alternate' // Same as HeapSort
            }}>
              ↓
            </div>
            <style>
              {`
              @keyframes pulse {
                0% { transform: scale(1.0) rotate(0deg); }
                100% { transform: scale(1.3) rotate(0deg); }
              }
              `}
            </style>
          </ThemeHtml>
        </group>
      );
    });
  };

  // Render held element (floating above the array) - VALUE ONLY
  const renderHeldElement = () => {
    const { heldElement } = vizData.mainArray || {};

    if (!heldElement) return null;

    const { value, currentPosition } = heldElement;
    let xPos = 0;
    let yPos = config.heldElement?.positioning?.yOffset || 1.5; // Use config for floating position

    // Check if using fixed position mode
    const useFixedPosition = config.heldElement?.positioning?.useFixedPosition || false;

    // Apply placement animation if active
    if (placementAnimation?.active && placementAnimation.element?.value === value) {
      const { fromPosition, toPosition, progress } = placementAnimation;

      // Get positions for placement animation
      const fromBarPos = barPositions.find(pos => pos.index === fromPosition);
      const toBarPos = barPositions.find(pos => pos.index === toPosition);

      if (toBarPos) {
        // Determine starting position based on fromPosition
        let startX, startY;

        if (fromPosition === 'fixed' || useFixedPosition) {
          // Start from fixed position
          startX = config.heldElement?.positioning?.fixedPosition?.x || 0;
          startY = config.heldElement?.positioning?.fixedPosition?.y || 1.5;
        } else if (fromBarPos) {
          // Start from bar position
          startX = fromBarPos.xPos;
          startY = config.heldElement?.positioning?.placement?.startYOffset || 1.5;
        } else {
          // Fallback to fixed position if fromBarPos not found
          startX = config.heldElement?.positioning?.fixedPosition?.x || 0;
          startY = config.heldElement?.positioning?.fixedPosition?.y || 1.5;
        }

        const endX = toBarPos.xPos;
        const endY = config.heldElement?.positioning?.placement?.endYOffset || 0;

        // Interpolate position
        xPos = startX + (endX - startX) * progress;
        yPos = startY + (endY - startY) * progress;

        console.log(`Placement animation - Held element ${value}:`, {
          fromPos: { x: startX, y: startY },
          toPos: { x: endX, y: endY },
          currentPos: { x: xPos, y: yPos },
          progress: progress
        });
      }
    } else {
      // Normal held element positioning
      if (useFixedPosition) {
        // Use fixed position from configuration
        xPos = config.heldElement?.positioning?.fixedPosition?.x || 0;
        yPos = config.heldElement?.positioning?.fixedPosition?.y || 1.5;
      } else {
        // Use position based on current array index
        const barPosition = barPositions.find(pos => pos.index === currentPosition);
        const baseXPos = barPosition ? barPosition.xPos : 0;
        const xOffset = config.heldElement?.positioning?.xOffset || 0;
        xPos = baseXPos + xOffset;
      }
    }

    // Use original array for consistent maxValue calculation (same as main array)
    const maxValue = originalArray && originalArray.length > 0
      ? Math.max(...originalArray)
      : Math.max(...vizData.mainArray.values.filter(v => v !== null));
    const barHeight = (value / maxValue) * maxBarHeight;

    // Get scaling factors from config
    const barScale = config.heldElement?.scaling?.barScale || 0.8;
    const labelScale = config.heldElement?.scaling?.labelScale || 0.9;

    // Get Z position based on fixed position mode
    const zPos = useFixedPosition
      ? (config.heldElement?.positioning?.fixedPosition?.z || 0.2)
      : (config.heldElement?.positioning?.zOffset || 0.2);

    return (
      <group key={`held-${value}`} position={[xPos, yPos, zPos]}>
        {/* Base of the held bar */}
        {config.heldElement?.visual?.base?.enabled && config.mainArray.bars.base.enabled && (
          <mesh
            position={[0, 0, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              barWidth * config.mainArray.bars.base.widthScale * barScale,
              config.mainArray.bars.base.height,
              barWidth * config.mainArray.bars.base.depthScale * barScale
            ]} />
            <meshStandardMaterial
              color={colors.merging} // Use merging color for held element
              transparent={config.heldElement?.visual?.base?.enabled || true}
              opacity={(config.mainArray.bars.base.material.opacity || 0.7) * (config.heldElement?.visual?.base?.opacity || 0.7)}
              roughness={config.heldElement?.visual?.bar?.material?.roughness || config.mainArray.bars.base.material.roughness}
              metalness={config.heldElement?.visual?.bar?.material?.metalness || config.mainArray.bars.base.material.metalness}
            />
          </mesh>
        )}

        {/* The held bar itself */}
        <mesh
          position={[0, (barHeight * barScale) / 2, 0]}
          castShadow={config.visual.effects.shadows}
          receiveShadow={config.visual.effects.shadows}
        >
          <boxGeometry args={[
            barWidth * config.mainArray.bars.geometry.widthScale * barScale,
            barHeight * barScale,
            barWidth * config.mainArray.bars.geometry.depthScale * barScale
          ]} />
          <meshStandardMaterial
            color={colors.merging} // Use merging color for held element
            transparent={config.heldElement?.visual?.bar?.transparency || true}
            opacity={config.heldElement?.visual?.bar?.opacity || 0.9}
            roughness={config.heldElement?.visual?.bar?.material?.roughness || config.mainArray.bars.material.roughness}
            metalness={config.heldElement?.visual?.bar?.material?.metalness || config.mainArray.bars.material.metalness}
            emissive={config.heldElement?.visual?.bar?.material?.emissive || 0}
          />
        </mesh>

        {/* Value label for held element */}
        {showValues && config.heldElement?.labels?.value?.enabled && (
          <ThemeHtml
            position={[
              config.heldElement?.labels?.value?.offset?.[0] || 0,
              (barHeight * barScale) + (config.heldElement?.labels?.value?.offset?.[1] || 0.4),
              config.heldElement?.labels?.value?.offset?.[2] || 0
            ]}
            center sprite occlude theme={theme}
          >
            <Paper
              elevation={config.heldElement?.labels?.value?.elevation || 2}
              sx={{
                px: theme.spacing(config.heldElement?.labels?.value?.padding?.horizontal || 0.6),
                py: theme.spacing(config.heldElement?.labels?.value?.padding?.vertical || 0.15),
                minWidth: config.mainArray.valueLabels.minWidth,
                borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                border: config.heldElement?.labels?.value?.border?.width || 2,
                borderColor: colors.merging,
                bgcolor: 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none',
                transform: `scale(${labelScale})` // Apply label scaling
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  fontSize: config.heldElement?.labels?.value?.fontSize || '0.55rem',
                  textAlign: 'center',
                  display: 'block',
                  fontWeight: config.heldElement?.labels?.value?.fontWeight || 'bold'
                }}
                color="text.primary"
              >
                {value}
              </Typography>
            </Paper>
          </ThemeHtml>
        )}

        {/* "HELD" indicator */}
        {config.heldElement?.labels?.indicator?.enabled && (
          <ThemeHtml
            position={[
              config.heldElement?.labels?.indicator?.offset?.[0] || 0,
              (barHeight * barScale) + (config.heldElement?.labels?.indicator?.offset?.[1] || 0.8),
              config.heldElement?.labels?.indicator?.offset?.[2] || 0
            ]}
            center sprite occlude theme={theme}
          >
            <Paper
              elevation={config.heldElement?.labels?.indicator?.elevation || 3}
              sx={{
                px: theme.spacing(config.heldElement?.labels?.indicator?.padding?.horizontal || 0.8),
                py: theme.spacing(config.heldElement?.labels?.indicator?.padding?.vertical || 0.2),
                borderRadius: theme.shape.borderRadius / (config.heldElement?.labels?.indicator?.borderRadius || 0.4),
                border: config.heldElement?.labels?.indicator?.border?.enabled ? (config.heldElement?.labels?.indicator?.border?.width || 1) : 0,
                borderColor: colors.merging,
                bgcolor: colors.merging,
                userSelect: 'none',
                pointerEvents: 'none',
                transform: `scale(${config.heldElement?.scaling?.indicatorScale || 0.8})` // Apply indicator scaling
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  fontSize: config.heldElement?.labels?.indicator?.fontSize || '0.5rem',
                  textAlign: 'center',
                  display: 'block',
                  fontWeight: config.heldElement?.labels?.indicator?.fontWeight || 'bold',
                  color: config.heldElement?.labels?.indicator?.textColor || 'white'
                }}
              >
                {config.heldElement?.labels?.indicator?.text || 'HELD'}
              </Typography>
            </Paper>
          </ThemeHtml>
        )}
      </group>
    );
  };

  return (
    <group>
      {renderMainArray()}
      {renderComparisonArrows()}
      {renderHeldElement()}
    </group>
  );
};

export default InsertionSortSimulation;
