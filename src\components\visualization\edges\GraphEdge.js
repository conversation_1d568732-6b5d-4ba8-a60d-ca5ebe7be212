// GraphEdge.js
// A reusable component for graph edges in 3D visualizations with enhanced SDF-based rendering

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Html, MeshTransmissionMaterial, Trail } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';
import EdgeParticles from './EdgeParticles';

const GraphEdge = ({
  start,
  end,
  weight,
  color = '#5c6bc0',
  isHighlighted = false,
  isRelaxed = false,
  isNegativeCycle = false,
  curved = false,
  curveHeight = 1.5,
  thickness = 0.15,
  nodeRadius = 1.0,
  onClick,
  showWeight = true,
  negativeWeightColor = '#f44336'
}) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  // Enhanced color handling with strong blue tones for dark theme
  const edgeColors = useMemo(() => {
    // Create color variations
    let baseColor, glowColor, bgColor, highlightColor;

    // Determine if this is a special state edge
    const isSpecialState = weight < 0 || isNegativeCycle || isRelaxed;

    // Adjust colors based on theme for better visibility
    if (isDark) {
      if (isSpecialState) {
        // Special state edges in dark theme
        if (weight < 0 || isNegativeCycle) {
          // Negative weight or negative cycle - use vibrant red in dark theme
          baseColor = new THREE.Color('#f44336'); // Red
          glowColor = new THREE.Color('#ff5252'); // Light red
          bgColor = new THREE.Color('#b71c1c'); // Dark red
          highlightColor = new THREE.Color('#ff8a80'); // Very light red
        } else if (isRelaxed) {
          // Relaxed edge (part of solution) - use vibrant green in dark theme
          baseColor = new THREE.Color('#4caf50'); // Green
          glowColor = new THREE.Color('#69f0ae'); // Light green
          bgColor = new THREE.Color('#2e7d32'); // Dark green
          highlightColor = new THREE.Color('#00e676'); // Very light green
        }

        // Apply additional brightness to make special states pop in dark theme
        baseColor.multiplyScalar(1.4);
        glowColor.multiplyScalar(1.6);
        bgColor.multiplyScalar(1.2);
        highlightColor.multiplyScalar(1.8);
      } else {
        // Regular edge in dark theme - use blue-gray tones
        baseColor = new THREE.Color('#78909c'); // Blue-gray
        glowColor = new THREE.Color('#b0bec5'); // Light blue-gray
        bgColor = new THREE.Color('#455a64'); // Dark blue-gray
        highlightColor = new THREE.Color('#cfd8dc'); // Very light blue-gray

        // Apply additional brightness for dark theme
        baseColor.multiplyScalar(1.2);
        glowColor.multiplyScalar(1.4);
        bgColor.multiplyScalar(1.0);
        highlightColor.multiplyScalar(1.6);
      }
    } else {
      // Light theme colors
      if (isSpecialState) {
        // Special state edges in light theme
        if (weight < 0 || isNegativeCycle) {
          // Negative weight or negative cycle - use darker red in light theme
          baseColor = new THREE.Color('#d32f2f'); // Darker red
          glowColor = new THREE.Color('#ff5252'); // Light red
          bgColor = new THREE.Color('#b71c1c'); // Very dark red
          highlightColor = new THREE.Color('#ff8a80'); // Very light red
        } else if (isRelaxed) {
          // Relaxed edge (part of solution) - use darker green in light theme
          baseColor = new THREE.Color('#388e3c'); // Darker green
          glowColor = new THREE.Color('#69f0ae'); // Light green
          bgColor = new THREE.Color('#1b5e20'); // Very dark green
          highlightColor = new THREE.Color('#00e676'); // Very light green
        }
      } else {
        // Regular edge in light theme - use darker blue-gray tones
        baseColor = new THREE.Color('#546e7a'); // Darker blue-gray
        glowColor = new THREE.Color('#90a4ae'); // Light blue-gray
        bgColor = new THREE.Color('#263238'); // Very dark blue-gray
        highlightColor = new THREE.Color('#cfd8dc'); // Very light blue-gray
      }

      // If a specific color was provided, use it instead of the default blue-gray
      if (!isSpecialState && color !== '#5c6bc0') {
        baseColor = new THREE.Color(color);

        // Darken and saturate the base color for light theme
        baseColor.offsetHSL(0, 0.2, -0.2); // Increase saturation, decrease lightness
        baseColor.multiplyScalar(0.7); // Reduce brightness by 30%

        // Create a saturated glow for light theme
        glowColor = new THREE.Color(color).offsetHSL(0, 0.3, -0.1);
        glowColor.multiplyScalar(0.8);

        // Create a dark background for light theme
        bgColor = new THREE.Color(color).offsetHSL(0, 0.1, -0.3);
        bgColor.multiplyScalar(0.5);

        // Create a saturated highlight for light theme
        highlightColor = new THREE.Color(color).offsetHSL(0, 0.4, -0.05);
        highlightColor.multiplyScalar(0.9);
      }
    }

    // If highlighted, make colors more intense
    if (isHighlighted) {
      baseColor.multiplyScalar(1.2);
      glowColor.multiplyScalar(1.3);
      bgColor.multiplyScalar(1.1);
      highlightColor.multiplyScalar(1.4);
    }

    return {
      base: baseColor,
      glow: glowColor,
      bg: bgColor,
      highlight: highlightColor
    };
  }, [color, isHighlighted, isRelaxed, isNegativeCycle, weight, negativeWeightColor, isDark]);

  // Calculate points for the edge
  const points = useMemo(() => {
    // Convert start and end to Vector3 if they're not already
    const startVec = start instanceof THREE.Vector3 ? start : new THREE.Vector3(...start);
    const endVec = end instanceof THREE.Vector3 ? end : new THREE.Vector3(...end);

    // Calculate direction vector
    const direction = new THREE.Vector3().subVectors(endVec, startVec).normalize();

    // Adjust start and end points to account for node radius
    const adjustedStart = new THREE.Vector3().addVectors(
      startVec,
      new THREE.Vector3().copy(direction).multiplyScalar(nodeRadius * 0.9)
    );

    const adjustedEnd = new THREE.Vector3().addVectors(
      endVec,
      new THREE.Vector3().copy(direction).multiplyScalar(-nodeRadius * 0.9)
    );

    // If curved, create a curved path
    if (curved) {
      // Calculate midpoint
      const midPoint = new THREE.Vector3().addVectors(adjustedStart, adjustedEnd).multiplyScalar(0.5);

      // Calculate perpendicular vector for curve control point
      const perpendicular = new THREE.Vector3(
        -direction.z,
        0,
        direction.x
      ).normalize().multiplyScalar(curveHeight);

      // Add perpendicular offset to midpoint
      const controlPoint = new THREE.Vector3().addVectors(midPoint, perpendicular);

      // Create a quadratic curve
      const curve = new THREE.QuadraticBezierCurve3(
        adjustedStart,
        controlPoint,
        adjustedEnd
      );

      // Sample points along the curve
      return curve.getPoints(20);
    } else {
      // Straight line
      return [adjustedStart, adjustedEnd];
    }
  }, [start, end, curved, curveHeight, nodeRadius]);

  // Calculate position for weight label
  const labelPosition = useMemo(() => {
    if (points.length < 2) return new THREE.Vector3();

    // For curved edges, use the middle point of the curve
    if (curved && points.length > 2) {
      const middleIndex = Math.floor(points.length / 2);
      return points[middleIndex].clone();
    }

    // For straight edges, calculate midpoint and add a small offset
    const midPoint = new THREE.Vector3().addVectors(points[0], points[points.length - 1]).multiplyScalar(0.5);

    // Calculate direction vector
    const direction = new THREE.Vector3().subVectors(points[points.length - 1], points[0]).normalize();

    // Calculate perpendicular vector for label offset
    const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x).normalize().multiplyScalar(thickness * 5);

    // Add perpendicular offset to midpoint
    return new THREE.Vector3().addVectors(midPoint, perpendicular);
  }, [points, curved, thickness]);

  // Create refs for animation
  const edgeRef = useRef();
  const glowRef = useRef();
  const highlightRef = useRef();

  // Animate edge
  useFrame(({ clock }) => {
    if (edgeRef.current && isHighlighted) {
      // Pulse effect for highlighted edges
      const pulse = 1 + Math.sin(clock.getElapsedTime() * 3) * 0.05;
      edgeRef.current.scale.set(1, pulse, pulse);
    }

    if (glowRef.current && isHighlighted) {
      // Glow effect for highlighted edges
      const glowPulse = 1 + Math.sin(clock.getElapsedTime() * 2) * 0.1;
      glowRef.current.scale.set(1, glowPulse, glowPulse);

      if (glowRef.current.material) {
        glowRef.current.material.opacity = 0.3 + Math.sin(clock.getElapsedTime() * 4) * 0.1;
      }
    }

    if (highlightRef.current && isHighlighted) {
      // Highlight effect for highlighted edges
      const highlightPulse = 1 + Math.sin(clock.getElapsedTime() * 5) * 0.15;
      highlightRef.current.scale.set(1, highlightPulse, highlightPulse);

      if (highlightRef.current.material) {
        highlightRef.current.material.opacity = 0.2 + Math.sin(clock.getElapsedTime() * 3) * 0.1;
      }
    }
  });

  // Determine if we should use SDF-based rendering
  // SDF rendering provides smoother edges and better visual quality
  const useSDFRendering = false; // Disabled for edges as it can cause performance issues

  return (
    <group onClick={onClick}>
      {/* Main edge tube */}
      <mesh ref={edgeRef}>
        <tubeGeometry args={[
          new THREE.CatmullRomCurve3(points),
          64, // More segments for smoother curve
          thickness, // Tube radius
          16, // Radial segments
          false
        ]} />
        {useSDFRendering ? (
          <MeshTransmissionMaterial
            backside={false}
            samples={4} // Lower samples for better performance
            thickness={1.0}
            chromaticAberration={0.02}
            anisotropy={0.1}
            distortion={0.0}
            distortionScale={0.2}
            temporalDistortion={0.0}
            iridescence={0.1}
            iridescenceIOR={1.1}
            iridescenceThicknessRange={[0, 1400]}
            color={edgeColors.base}
            transmissionSampler={false}
            reflectivity={0.1}
            roughness={0.2}
            metalness={0.1}
            envMapIntensity={1}
            clearcoat={0.8}
            clearcoatRoughness={0.2}
            resolution={128} // Lower resolution for better performance
          />
        ) : (
          <meshStandardMaterial
            color={edgeColors.base}
            emissive={edgeColors.glow}
            emissiveIntensity={0.5}
            roughness={0.2}
            metalness={0.8}
          />
        )}
      </mesh>

      {/* Glow tube */}
      <mesh ref={glowRef}>
        <tubeGeometry args={[
          new THREE.CatmullRomCurve3(points),
          32, // Fewer segments for better performance
          thickness * 1.5, // Larger radius for glow
          8, // Fewer radial segments for better performance
          false
        ]} />
        <meshBasicMaterial
          color={edgeColors.glow}
          transparent={true}
          opacity={0.2}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Highlight tube for highlighted edges */}
      {isHighlighted && (
        <mesh ref={highlightRef}>
          <tubeGeometry args={[
            new THREE.CatmullRomCurve3(points),
            32, // Fewer segments for better performance
            thickness * 2.0, // Even larger radius for highlight
            8, // Fewer radial segments for better performance
            false
          ]} />
          <meshBasicMaterial
            color={edgeColors.highlight}
            transparent={true}
            opacity={0.1}
            side={THREE.BackSide}
          />
        </mesh>
      )}

      {/* Animated particles for relaxed edges */}
      {isRelaxed && (
        <EdgeParticles
          points={points}
          color={edgeColors.highlight}
          count={10}
          size={thickness * 2}
          speed={1}
        />
      )}

      {/* Weight label */}
      {showWeight && (
        <Html position={labelPosition} center distanceFactor={15}>
          <div style={{
            color: isDark ? '#ffffff' : '#000000',
            backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: 'bold',
            textAlign: 'center',
            userSelect: 'none',
            pointerEvents: 'none',
          }}>
            {weight}
          </div>
        </Html>
      )}
    </group>
  );
};

export default GraphEdge;
