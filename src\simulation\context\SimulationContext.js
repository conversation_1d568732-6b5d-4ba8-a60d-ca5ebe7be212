// SimulationContext.js
// This context provides state management for algorithm simulations

import React, { createContext, useState, useContext, useCallback, useEffect, useMemo } from 'react';
import { useStep } from './StepContext';

// Simulation states
export const SimulationState = {
  IDLE: 'idle',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed'
};

// Create the context
const SimulationContext = createContext();

/**
 * SimulationProvider - Provides state management for algorithm simulations
 * 
 * This context manages:
 * - Simulation state (idle, running, paused, completed)
 * - Simulation speed
 * - Simulation controls (start, pause, reset)
 * - Simulation configuration
 */
export const SimulationProvider = ({ children }) => {
  // Get step context
  const { 
    currentStepIndex, 
    totalSteps, 
    goToNextStep, 
    goToFirstStep,
    startStepAnimation,
    stopStepAnimation
  } = useStep();

  // Simulation state
  const [state, setState] = useState(SimulationState.IDLE);
  const [speed, setSpeed] = useState(5); // 1-10 scale
  const [autoAdvance, setAutoAdvance] = useState(true);
  const [config, setConfig] = useState({});
  const [simulationInterval, setSimulationInterval] = useState(null);

  // Calculate delay based on speed
  const getDelay = useCallback(() => {
    // Speed 1 = 2000ms, Speed 10 = 200ms
    return 2200 - (speed * 200);
  }, [speed]);

  // Start simulation
  const startSimulation = useCallback(() => {
    // If we're at the end, go back to the beginning
    if (currentStepIndex >= totalSteps - 1) {
      goToFirstStep();
    }

    // Set state to running
    setState(SimulationState.RUNNING);

    // Start step animation
    startStepAnimation();

    // Clear any existing interval
    if (simulationInterval) {
      clearInterval(simulationInterval);
    }

    // Set up auto-advance if enabled
    if (autoAdvance) {
      const interval = setInterval(() => {
        // Check if we've reached the end
        if (currentStepIndex >= totalSteps - 1) {
          // Stop the simulation
          clearInterval(interval);
          setState(SimulationState.COMPLETED);
          stopStepAnimation();
          return;
        }

        // Advance to the next step
        goToNextStep();
      }, getDelay());

      // Store the interval ID
      setSimulationInterval(interval);
    }
  }, [
    currentStepIndex, 
    totalSteps, 
    goToFirstStep, 
    goToNextStep, 
    autoAdvance, 
    getDelay, 
    simulationInterval,
    startStepAnimation,
    stopStepAnimation
  ]);

  // Pause simulation
  const pauseSimulation = useCallback(() => {
    // Set state to paused
    setState(SimulationState.PAUSED);

    // Stop step animation
    stopStepAnimation();

    // Clear the interval
    if (simulationInterval) {
      clearInterval(simulationInterval);
      setSimulationInterval(null);
    }
  }, [simulationInterval, stopStepAnimation]);

  // Reset simulation
  const resetSimulation = useCallback(() => {
    // Set state to idle
    setState(SimulationState.IDLE);

    // Stop step animation
    stopStepAnimation();

    // Clear the interval
    if (simulationInterval) {
      clearInterval(simulationInterval);
      setSimulationInterval(null);
    }

    // Go to the first step
    goToFirstStep();
  }, [simulationInterval, goToFirstStep, stopStepAnimation]);

  // Update simulation configuration
  const updateConfig = useCallback((newConfig) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      ...newConfig
    }));
  }, []);

  // Set speed safely
  const setSpeedSafe = useCallback((newSpeed) => {
    // Convert to number if it's a string
    const numSpeed = typeof newSpeed === 'string' ? Number(newSpeed) : newSpeed;

    // Validate the input
    if (isNaN(numSpeed)) {
      return;
    }

    // Ensure speed is between 1-10
    const validSpeed = Math.max(1, Math.min(10, numSpeed));
    setSpeed(validSpeed);

    // If simulation is running, restart it with the new speed
    if (state === SimulationState.RUNNING) {
      pauseSimulation();
      startSimulation();
    }
  }, [state, pauseSimulation, startSimulation]);

  // Toggle auto-advance
  const toggleAutoAdvance = useCallback(() => {
    setAutoAdvance(prev => !prev);
  }, []);

  // Handle step changes
  useEffect(() => {
    // If we've reached the end, mark as completed
    if (currentStepIndex >= totalSteps - 1 && state === SimulationState.RUNNING) {
      setState(SimulationState.COMPLETED);
      stopStepAnimation();
      
      // Clear the interval
      if (simulationInterval) {
        clearInterval(simulationInterval);
        setSimulationInterval(null);
      }
    }
  }, [currentStepIndex, totalSteps, state, simulationInterval, stopStepAnimation]);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (simulationInterval) {
        clearInterval(simulationInterval);
      }
    };
  }, [simulationInterval]);

  // Create the value object
  const value = useMemo(() => ({
    // Simulation state
    state,
    setState,
    
    // Simulation speed
    speed,
    setSpeed: setSpeedSafe,
    getDelay,
    
    // Auto-advance
    autoAdvance,
    setAutoAdvance,
    toggleAutoAdvance,
    
    // Simulation controls
    startSimulation,
    pauseSimulation,
    resetSimulation,
    
    // Simulation configuration
    config,
    updateConfig
  }), [
    state, 
    speed, setSpeedSafe, getDelay,
    autoAdvance, setAutoAdvance, toggleAutoAdvance,
    startSimulation, pauseSimulation, resetSimulation,
    config, updateConfig
  ]);

  return (
    <SimulationContext.Provider value={value}>
      {children}
    </SimulationContext.Provider>
  );
};

/**
 * useSimulation - Custom hook to use the simulation context
 * 
 * @returns {Object} The simulation context value
 */
export const useSimulation = () => {
  const context = useContext(SimulationContext);
  if (context === undefined) {
    throw new Error('useSimulation must be used within a SimulationProvider');
  }
  return context;
};
