# Mathematical Algorithms

This directory contains implementations of various mathematical algorithms.

## Implemented Algorithms

Currently, there are no implemented mathematical algorithms.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `Sieve`)
2. Implement the required files:
   - `SieveAlgorithm.js` - Core algorithm logic
   - `SieveVisualization.js` - Visualization component
   - `SieveController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
