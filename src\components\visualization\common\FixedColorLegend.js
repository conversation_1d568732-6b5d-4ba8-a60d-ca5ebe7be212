import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Fixed color legend component that stays at the bottom of the screen regardless of camera rotation
 *
 * @param {Object} props - Component props
 * @param {Array} props.items - Array of legend items, each with color and label
 * @param {Object} props.theme - MUI theme object
 */
const FixedColorLegend = ({ items = [], theme }) => {
  // Always call useTheme hook first to follow React rules
  const themeFromContext = useTheme();
  // Then use provided theme or the one from context
  const muiTheme = theme || themeFromContext;
  const isDark = muiTheme?.palette?.mode === 'dark';

  // Default styles based on theme - minimalist approach
  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'row', // Display items in a row
      alignItems: 'center',
      justifyContent: 'center',
      padding: '4px 8px',
      backgroundColor: 'transparent', // Completely transparent background
      minWidth: '300px',
      maxWidth: '95%', // Increased to allow more space
      width: 'auto', // Allow container to size based on content
      margin: '0 auto',
      userSelect: 'none',
      pointerEvents: 'none',
      position: 'absolute',
      bottom: '8px', // Moved slightly closer to edge
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: 1000,
    },
    itemsContainer: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap', // Prevent wrapping to ensure single row
      justifyContent: 'center',
      gap: '8px', // Further reduced space between items
      overflowX: 'auto', // Allow horizontal scrolling if needed (shouldn't be visible)
      scrollbarWidth: 'none', // Hide scrollbar in Firefox
      msOverflowStyle: 'none', // Hide scrollbar in IE/Edge
    },
    item: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: '0',
      whiteSpace: 'nowrap', // Prevent text wrapping
      flexShrink: 0, // Prevent items from shrinking
    },
    colorBox: {
      width: '14px', // Even smaller color box
      height: '14px', // Even smaller color box
      borderRadius: '2px', // Smaller border radius
      marginRight: '4px', // Less margin
      boxShadow: isDark
        ? '0 1px 2px rgba(0, 0, 0, 0.5)'
        : '0 1px 2px rgba(0, 0, 0, 0.2)',
    },
    label: {
      fontSize: '12px', // Smaller font size
      color: isDark ? '#ffffff' : '#000000',
      textShadow: isDark
        ? '0 0 4px rgba(0, 0, 0, 0.9), 0 0 8px rgba(0, 0, 0, 0.7)'
        : '0 0 4px rgba(255, 255, 255, 0.9), 0 0 8px rgba(255, 255, 255, 0.7)',
    },
  };

  return (
    <Html
      prepend // This ensures the HTML is rendered in front of the scene
      fullscreen // This makes it cover the entire canvas
      portal={null} // This ensures it's rendered directly in the canvas
      style={{
        pointerEvents: 'none', // This ensures it doesn't block interactions with the scene
      }}
    >
      <style>
        {`
          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }
        `}
      </style>
      <div style={styles.container}>
        <div
          style={styles.itemsContainer}
          className="hide-scrollbar" // Add a class to target with CSS
        >
          {items.map((item, index) => (
            <div key={index} style={styles.item}>
              <div
                style={{
                  ...styles.colorBox,
                  backgroundColor: item.color,
                }}
              />
              <span style={styles.label}>{item.label}</span>
            </div>
          ))}
        </div>
      </div>
    </Html>
  );
};

export default FixedColorLegend;
