// AlgorithmController.js
// This component serves as a bridge between the AlgorithmVisualizer and the specific algorithm components.
// It dynamically loads the appropriate algorithm components based on the selected algorithm.

import React, { useState, useEffect } from 'react';
import { Typography, Paper } from '@mui/material';
import { useAlgorithm } from '../../context/AlgorithmContext';
import { getAlgorithm, getDefaultParams } from '../../algorithms/AlgorithmRegistry';

const AlgorithmController = ({
  algorithmId,
  params,
  onParamsChange,
  theme,
  setMovements
}) => {
  // Set default values for props
  const safeOnParamsChange = onParamsChange || (() => { });

  // Get algorithm state from context
  const {
    state, setState,
    step, setStep,
    totalSteps, setTotalSteps,
    steps,
    setMovements: contextSetMovements
  } = useAlgorithm();

  // Use context setMovements if prop is not provided
  const safeSetMovements = setMovements || contextSetMovements;

  // Use the params passed from the parent component
  const [algorithmParams, setAlgorithmParams] = useState({
    ...getDefaultParams(algorithmId),
    ...params,
  });

  // Get the algorithm from the registry
  const algorithm = getAlgorithm(algorithmId);

  // Reset state when component mounts or algorithm changes
  useEffect(() => {
    // console.log('AlgorithmController: Component mounted or algorithm changed');

    // Reset algorithm params to defaults
    const defaultParams = getDefaultParams(algorithmId);
    setAlgorithmParams({
      ...defaultParams,
      ...params,
    });

    // NOTE: We've removed the state reset here to prevent duplicate state changes
    // The state is now managed by AlgorithmVisualizer.js
  }, [algorithmId, params]);

  // We now handle all param changes in the first effect

  // We don't need these handlers anymore as they will be implemented in each algorithm controller

  // Handle parameter changes
  const handleParamChange = (newParams) => {
    // CRITICAL FIX: Check if only speed is being changed
    const isOnlySpeedChange =
      Object.keys(newParams).length === 1 &&
      Object.keys(newParams)[0] === 'speed';

    if (isOnlySpeedChange) {
      // console.log('Speed parameter change detected but IGNORED:', newParams.speed);
      // CRITICAL FIX: DO NOTHING for speed changes
      // Speed is now handled exclusively by the SpeedContext
      // This completely bypasses the parameter system for speed changes
      // and prevents the simulation from resetting
      return;
    } else {
      // For other parameter changes, merge with existing params
      const updatedParams = {
        ...algorithmParams,
        ...newParams,
      };
      setAlgorithmParams(updatedParams);

      // Notify parent component of parameter changes
      safeOnParamsChange(updatedParams);
    }
  };

  // If algorithm doesn't exist, show error
  if (!algorithm) {
    return (
      <Paper elevation={3} sx={{ p: 2 }}>
        <Typography variant="h6" color="error">
          Algorithm "{algorithmId}" not found
        </Typography>
      </Paper>
    );
  }

  // Get the controller component for this algorithm
  const ControllerComponent = algorithm.controller;

  // If controller doesn't exist yet, show placeholder
  if (!ControllerComponent) {
    return (
      <Paper elevation={3} sx={{ p: 2 }}>
        <Typography variant="body1">
          Controller for "{algorithm.name}" is not implemented yet.
        </Typography>
      </Paper>
    );
  }

  // We already have setAlgorithmArray from the useAlgorithm hook above

  // Render the controller component with direct props and safe functions
  // Add a key to force remount when the algorithm changes
  return (
    <ControllerComponent
      key={algorithmId} // Use algorithm ID as key to force remount
      params={algorithmParams}
      onParamChange={handleParamChange}
      state={state}
      setState={setState}
      step={step}
      setStep={setStep}
      totalSteps={totalSteps}
      setTotalSteps={setTotalSteps}
      theme={theme}
      steps={steps || []}
      setMovements={safeSetMovements}
    />
  );
};

export default AlgorithmController;
