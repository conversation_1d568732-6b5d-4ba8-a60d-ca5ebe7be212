// Kruskals/index.js
// Export only what's needed from this algorithm

// Import the visualization
import KruskalsVisualization from './KruskalsVisualization';
import KruskalsController from './KruskalsController';
import KruskalsAlgorithm from './KruskalsAlgorithm';

export const metadata = {
  id: 'Kruskals',
  name: 'Krus<PERSON>\'s Algorithm',
  description: 'A minimum spanning tree algorithm that finds an edge of the least possible weight that connects any two trees in the forest.',
  timeComplexity: 'O(E log E) or O(E log V)',
  spaceComplexity: 'O(V + E)',
  defaultParams: {
    nodes: 6,
    density: 0.7,
    minWeight: 1,
    maxWeight: 10,
    customEdges: [],
  },
};

export const components = {
  visualization: KruskalsVisualization, // Use the standard visualization
  controller: KruskalsController,
  algorithm: KruskalsAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
