import React from 'react';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable base component for sorting algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {number} props.width - Width of the base
 * @param {string|Object} props.color - Color of the base or colors object
 * @param {number} props.height - Height of the base (default: 0.2)
 * @param {boolean} props.showHighlight - Whether to show a highlight on the top edge (default: true)
 * @param {number} props.depth - Depth of the base (default: calculated from width)
 * @param {number} props.metalness - Metalness of the material (default: 0.2)
 * @param {number} props.roughness - Roughness of the material (default: 0.5)
 * @param {number} props.highlightHeight - Height of the highlight edge (default: 0.03)
 * @param {boolean} props.useThemeColors - Whether to use theme colors (default: true)
 * @returns {JSX.Element} - The rendered base component
 */
const SortingBase = ({
  width,
  color,
  height = 0.2,
  showHighlight = true,
  depth,
  metalness = 0.2,
  roughness = 0.5,
  highlightHeight = 0.03,
  useThemeColors = true
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Calculate depth if not provided
  const baseDepth = depth || width / 1.8;

  // Determine colors based on theme if useThemeColors is true
  let baseColor, highlightColor;

  if (useThemeColors) {
    // Use theme-aware colors
    baseColor = isDarkMode ? theme.palette.grey[900] : theme.palette.grey[100];
    highlightColor = isDarkMode ? theme.palette.grey[800] : theme.palette.grey[300];

    // Override with provided colors if available
    if (color) {
      if (typeof color === 'string') {
        baseColor = color;
        // Generate a slightly lighter color for highlight
        highlightColor = isDarkMode ? lightenColor(color, 0.1) : darkenColor(color, 0.1);
      } else {
        baseColor = color.base || baseColor;
        highlightColor = color.highlight || highlightColor;
      }
    }
  } else {
    // Use provided colors without theme awareness
    baseColor = typeof color === 'string' ? color : color?.base || '#f5f5f5';
    highlightColor = typeof color === 'string' ? '#e0e0e0' : color?.highlight || '#e0e0e0';
  }

  return (
    <group>
      {/* Main base platform with rounded edges */}
      <mesh position={[0, -height / 2 - 0.1, 0]} receiveShadow>
        <boxGeometry args={[width, height, baseDepth, 8, 8, 8]} />
        <meshStandardMaterial
          color={baseColor}
          metalness={metalness}
          roughness={roughness}
          // Add subtle emissive effect in dark mode
          emissive={isDarkMode ? baseColor : '#000000'}
          emissiveIntensity={isDarkMode ? 0.05 : 0}
        />
      </mesh>

      {/* Top edge highlight */}
      {showHighlight && (
        <mesh position={[0, 0, 0]} receiveShadow>
          <boxGeometry args={[width, highlightHeight, baseDepth]} />
          <meshStandardMaterial
            color={highlightColor}
            metalness={metalness + 0.1}
            roughness={roughness - 0.1}
          />
        </mesh>
      )}
    </group>
  );
};

// Helper function to lighten a color
function lightenColor(color, amount) {
  try {
    // Convert hex to RGB
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);

    // Lighten the color
    const lightenFactor = 1 + amount;
    const newR = Math.min(255, Math.floor(r * lightenFactor));
    const newG = Math.min(255, Math.floor(g * lightenFactor));
    const newB = Math.min(255, Math.floor(b * lightenFactor));

    // Convert back to hex
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  } catch (e) {
    console.error('Error lightening color:', e);
    return color;
  }
}

// Helper function to darken a color
function darkenColor(color, amount) {
  try {
    // Convert hex to RGB
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);

    // Darken the color
    const darkenFactor = 1 - amount;
    const newR = Math.floor(r * darkenFactor);
    const newG = Math.floor(g * darkenFactor);
    const newB = Math.floor(b * darkenFactor);

    // Convert back to hex
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  } catch (e) {
    console.error('Error darkening color:', e);
    return color;
  }
}

export default SortingBase;
