// WeightRangeInput.js
// A reusable component for weight range inputs that maintains focus during arrow key navigation

import React, { useState, useRef, useEffect } from 'react';
import { Box, TextField } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable component for weight range inputs that maintains focus during arrow key navigation
 * 
 * @param {Object} props - Component props
 * @param {number} props.minWeight - Minimum weight value
 * @param {number} props.maxWeight - Maximum weight value
 * @param {function} props.onMinWeightChange - Function to handle min weight changes
 * @param {function} props.onMaxWeightChange - Function to handle max weight changes
 * @param {boolean} props.disabled - Whether the inputs are disabled
 * @param {number} props.absoluteMin - Absolute minimum allowed value (default: 1)
 * @param {number} props.absoluteMax - Absolute maximum allowed value (default: 100)
 */
const WeightRangeInput = ({
  minWeight,
  maxWeight,
  onMinWeightChange,
  onMaxWeightChange,
  disabled = false,
  absoluteMin = 1,
  absoluteMax = 100
}) => {
  // Local state for input values
  const [localMinWeight, setLocalMinWeight] = useState(String(minWeight));
  const [localMaxWeight, setLocalMaxWeight] = useState(String(maxWeight));
  
  // Refs for input elements
  const minInputRef = useRef(null);
  const maxInputRef = useRef(null);
  
  // Refs for debounce timers
  const minTimerRef = useRef(null);
  const maxTimerRef = useRef(null);
  
  // Flags to track if we're handling arrow keys
  const isHandlingMinArrowKey = useRef(false);
  const isHandlingMaxArrowKey = useRef(false);
  
  // Update local values when props change
  useEffect(() => {
    setLocalMinWeight(String(minWeight));
  }, [minWeight]);
  
  useEffect(() => {
    setLocalMaxWeight(String(maxWeight));
  }, [maxWeight]);
  
  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (minTimerRef.current) clearTimeout(minTimerRef.current);
      if (maxTimerRef.current) clearTimeout(maxTimerRef.current);
    };
  }, []);
  
  // Handle min weight change
  const handleMinWeightChange = (e) => {
    const newValue = e.target.value;
    setLocalMinWeight(newValue);
    
    // Only update parent if not handling arrow keys
    if (!isHandlingMinArrowKey.current) {
      const numValue = parseInt(newValue, 10);
      if (!isNaN(numValue) && numValue >= absoluteMin && numValue < maxWeight) {
        // Use a small delay to avoid immediate state updates
        if (minTimerRef.current) clearTimeout(minTimerRef.current);
        minTimerRef.current = setTimeout(() => {
          onMinWeightChange(numValue);
          minTimerRef.current = null;
        }, 300);
      }
    }
  };
  
  // Handle max weight change
  const handleMaxWeightChange = (e) => {
    const newValue = e.target.value;
    setLocalMaxWeight(newValue);
    
    // Only update parent if not handling arrow keys
    if (!isHandlingMaxArrowKey.current) {
      const numValue = parseInt(newValue, 10);
      if (!isNaN(numValue) && numValue > minWeight && numValue <= absoluteMax) {
        // Use a small delay to avoid immediate state updates
        if (maxTimerRef.current) clearTimeout(maxTimerRef.current);
        maxTimerRef.current = setTimeout(() => {
          onMaxWeightChange(numValue);
          maxTimerRef.current = null;
        }, 300);
      }
    }
  };
  
  // Handle min weight key down
  const handleMinKeyDown = (e) => {
    if (e.key !== 'ArrowUp' && e.key !== 'ArrowDown') return;
    
    e.preventDefault();
    isHandlingMinArrowKey.current = true;
    
    const currentValue = parseInt(localMinWeight, 10) || 0;
    const step = e.key === 'ArrowUp' ? 1 : -1;
    const newValue = Math.max(absoluteMin, Math.min(maxWeight - 1, currentValue + step));
    
    setLocalMinWeight(String(newValue));
    
    // Debounce the update to parent
    if (minTimerRef.current) clearTimeout(minTimerRef.current);
    minTimerRef.current = setTimeout(() => {
      if (newValue >= absoluteMin && newValue < maxWeight) {
        onMinWeightChange(newValue);
      }
      minTimerRef.current = null;
    }, 500);
  };
  
  // Handle max weight key down
  const handleMaxKeyDown = (e) => {
    if (e.key !== 'ArrowUp' && e.key !== 'ArrowDown') return;
    
    e.preventDefault();
    isHandlingMaxArrowKey.current = true;
    
    const currentValue = parseInt(localMaxWeight, 10) || 0;
    const step = e.key === 'ArrowUp' ? 1 : -1;
    const newValue = Math.max(minWeight + 1, Math.min(absoluteMax, currentValue + step));
    
    setLocalMaxWeight(String(newValue));
    
    // Debounce the update to parent
    if (maxTimerRef.current) clearTimeout(maxTimerRef.current);
    maxTimerRef.current = setTimeout(() => {
      if (newValue > minWeight && newValue <= absoluteMax) {
        onMaxWeightChange(newValue);
      }
      maxTimerRef.current = null;
    }, 500);
  };
  
  // Handle key up events
  const handleMinKeyUp = () => {
    isHandlingMinArrowKey.current = false;
  };
  
  const handleMaxKeyUp = () => {
    isHandlingMaxArrowKey.current = false;
  };
  
  // Handle blur events
  const handleMinBlur = () => {
    const numValue = parseInt(localMinWeight, 10);
    if (!isNaN(numValue) && numValue >= absoluteMin && numValue < maxWeight) {
      onMinWeightChange(numValue);
    } else {
      // Reset to valid value
      setLocalMinWeight(String(minWeight));
    }
  };
  
  const handleMaxBlur = () => {
    const numValue = parseInt(localMaxWeight, 10);
    if (!isNaN(numValue) && numValue > minWeight && numValue <= absoluteMax) {
      onMaxWeightChange(numValue);
    } else {
      // Reset to valid value
      setLocalMaxWeight(String(maxWeight));
    }
  };
  
  return (
    <Box sx={{ display: 'flex', gap: 2, mt: 1, mb: 1 }}>
      <Box sx={{ width: '100%' }}>
        <TextField
          fullWidth
          size="small"
          label="Min Weight"
          value={localMinWeight}
          onChange={handleMinWeightChange}
          onKeyDown={handleMinKeyDown}
          onKeyUp={handleMinKeyUp}
          onBlur={handleMinBlur}
          disabled={disabled}
          type="number"
          helperText={`Min: ${absoluteMin}, Max: ${maxWeight - 1}`}
          inputRef={minInputRef}
          // Use the sx prop to apply min, max, and step attributes directly to the input element
          sx={{
            '& input': {
              // Hide the spinner buttons and set constraints
              WebkitAppearance: 'none',
              MozAppearance: 'textfield',
              min: absoluteMin,
              max: maxWeight - 1,
              step: 1
            }
          }}
        />
      </Box>
      
      <Box sx={{ width: '100%' }}>
        <TextField
          fullWidth
          size="small"
          label="Max Weight"
          value={localMaxWeight}
          onChange={handleMaxWeightChange}
          onKeyDown={handleMaxKeyDown}
          onKeyUp={handleMaxKeyUp}
          onBlur={handleMaxBlur}
          disabled={disabled}
          type="number"
          helperText={`Min: ${minWeight + 1}, Max: ${absoluteMax}`}
          inputRef={maxInputRef}
          // Use the sx prop to apply min, max, and step attributes directly to the input element
          sx={{
            '& input': {
              // Hide the spinner buttons and set constraints
              WebkitAppearance: 'none',
              MozAppearance: 'textfield',
              min: minWeight + 1,
              max: absoluteMax,
              step: 1
            }
          }}
        />
      </Box>
    </Box>
  );
};

WeightRangeInput.propTypes = {
  minWeight: PropTypes.number.isRequired,
  maxWeight: PropTypes.number.isRequired,
  onMinWeightChange: PropTypes.func.isRequired,
  onMaxWeightChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  absoluteMin: PropTypes.number,
  absoluteMax: PropTypes.number
};

export default WeightRangeInput;
