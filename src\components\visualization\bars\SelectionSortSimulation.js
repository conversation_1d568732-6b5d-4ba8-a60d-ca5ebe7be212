// SelectionSortSimulation.js - Clean selection sort visualization component
// Consumes visualizationData from controller with minimal complexity

import React, { useMemo, useEffect } from 'react';
import { Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ThemeHtml from '../ThemeHtml';
import SelectionSortConfig from '../../../algorithms/Sorting/SelectionSort/SelectionSortConfig';

const SelectionSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true,
  swapAnimation,
  originalArray,
  onWidthChange
}) => {
  const theme = useTheme();

  // Get configuration for positioning
  const config = SelectionSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // SIMPLIFIED STATIC POSITIONING - No dynamic gaps but proper total width calculation
  const layoutData = useMemo(() => {
    if (!vizData?.mainArray?.values) return { barPositions: [], totalWidth: 0 };

    const values = vizData.mainArray.values;
    const baseSpacing = barWidth + barSpacing;
    const visualOffset = config.mainArray.bars.visualOffset;

    // Calculate STATIC bar positions - these never change
    const staticPositions = [];
    const barsOnlyWidth = (values.length * baseSpacing) - barSpacing;
    const startX = -barsOnlyWidth / 2 + visualOffset;

    for (let i = 0; i < values.length; i++) {
      staticPositions.push({
        index: i,
        xPos: startX + i * baseSpacing  // Static position - never changes
      });
    }

    // Calculate the ACTUAL total width covered by bars (from leftmost to rightmost edge)
    const leftmostEdge = staticPositions[0].xPos - barWidth / 2;
    const rightmostEdge = staticPositions[staticPositions.length - 1].xPos + barWidth / 2;
    const actualTotalWidth = rightmostEdge - leftmostEdge;

    // DEBUG: Log the calculations
    console.log('SelectionSort Static Layout:', {
      values: values.map((v, i) => `${i}:${v}`),
      barsOnlyWidth,
      actualTotalWidth,
      leftmostEdge: leftmostEdge.toFixed(2),
      rightmostEdge: rightmostEdge.toFixed(2),
      startX: startX.toFixed(2),
      positions: staticPositions.map(p => `${p.index}:${p.xPos.toFixed(1)}`)
    });

    return {
      barPositions: staticPositions,
      totalWidth: actualTotalWidth
    };
  }, [vizData?.mainArray?.values, barWidth, barSpacing]);

  // Extract values
  const { barPositions, totalWidth: actualTotalWidth } = layoutData;

  // Notify parent of width changes for platform sizing
  useEffect(() => {
    if (onWidthChange && actualTotalWidth > 0) {
      onWidthChange(actualTotalWidth);
    }
  }, [actualTotalWidth, onWidthChange]);

  // If no visualization data, return null (this is normal during initial load)
  if (!vizData || !vizData.mainArray || !vizData.mainArray.values) {
    // Don't log warning during normal initialization
    return null;
  }

  // Handle algorithm completion state
  const isComplete = currentStep?.type === 'complete';

  // Handle completion state for final rendering

  // Render the main array bars with selection sort highlights
  const renderMainArray = () => {
    if (!vizData.mainArray?.values) return null;

    // Use current step data
    const {
      values,
      sortedIndices = [],
      comparingIndices = [],
      swappingIndices = [],
      currentIndex = null,
      minIndex = null
    } = vizData.mainArray;

    // Use original array for consistent maxValue calculation throughout the algorithm
    const maxValue = originalArray && originalArray.length > 0
      ? Math.max(...originalArray)
      : Math.max(...values.filter(v => v !== null));

    return values.map((value, index) => {
      // For gaps (null values), still render index label but no bar/value
      const isGap = value === null;
      const isCurrentElement = currentIndex === index;
      const isMinimumElement = minIndex === index;
      const isComparing = comparingIndices.includes(index);
      const isSwapping = swappingIndices.includes(index);
      const isSorted = sortedIndices.includes(index);

      // Sorted state tracking

      // Skip null values (shouldn't happen in SelectionSort, but safety check)
      if (isGap) {
        console.warn(`SelectionSort: Unexpected null value at index ${index}`);
      }

      // Value labels should always be visible when showValues is true

      // For gaps, we only render the index label
      if (isGap) {
        const barPosition = barPositions.find(pos => pos.index === index);
        const xPos = barPosition ? barPosition.xPos : 0;

        return (
          <group key={`gap-${index}`} position={[xPos, 0, 0]}>
            {/* Index label for gap position */}
            {showIndices && (
              <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
                <Paper
                  elevation={config.mainArray.indexLabels.elevation}
                  sx={{
                    width: config.mainArray.indexLabels.size.width,
                    height: config.mainArray.indexLabels.size.height,
                    borderRadius: '50%',
                    bgcolor: 'background.paper',
                    border: 1,
                    borderColor: 'divider',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    opacity: 0.5 // Dimmed to show it's empty
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: config.mainArray.indexLabels.fontSize,
                      fontWeight: config.mainArray.indexLabels.fontWeight
                    }}
                    color="text.secondary"
                  >
                    {index}
                  </Typography>
                </Paper>
              </ThemeHtml>
            )}
          </group>
        );
      }

      const barHeight = (value / maxValue) * maxBarHeight;

      // Determine bar color based on state - ONLY use config colors
      let barColor;
      if (isCurrentElement) {
        barColor = colors.current; // Current position being filled
      } else if (isMinimumElement) {
        barColor = colors.minimum; // Current minimum element
      } else if (isComparing) {
        barColor = colors.comparing; // Elements being compared
      } else if (isSwapping) {
        barColor = colors.swapping; // Elements being swapped
      } else if (isSorted) {
        barColor = colors.sorted; // Elements in sorted portion
      } else {
        barColor = colors.bar; // Default bar color
      }

      // Override color for swapping elements during animation
      // BUT: Don't override sorted bars to red - they should stay green
      if (swapAnimation?.active && swapAnimation.indices.includes(index)) {
        if (!isSorted) {
          // Only override non-sorted bars to red during animation
          barColor = colors.swapping;
        }
        // Sorted bars keep their green color even during animation
      }

      // Use pre-calculated bar position
      const barPosition = barPositions.find(pos => pos.index === index);
      let xPos = barPosition ? barPosition.xPos : 0;
      let yPos = 0;
      let zPos = 0;

      // Apply swap animation if active (exactly like HeapSort)
      if (swapAnimation?.active && swapAnimation.indices.includes(index)) {
        const [i, j] = swapAnimation.indices;
        const { progress } = swapAnimation;

        // Determine if this is the first or second bar in the swap
        const isFirstBar = index === i;
        const otherIndex = isFirstBar ? j : i;

        // Get positions for both bars
        const currentBarPos = barPositions.find(pos => pos.index === index);
        const otherBarPos = barPositions.find(pos => pos.index === otherIndex);

        if (currentBarPos && otherBarPos) {
          const startX = currentBarPos.xPos;
          const endX = otherBarPos.xPos;
          const distance = endX - startX;
          const centerX = startX + distance / 2;
          const radius = Math.abs(distance) / 2;

          // Calculate position along arc path
          const angle = progress * Math.PI;

          // Each bar moves in opposite direction along the arc (like HeapSort)
          let newXPos, newYPos;
          if (isFirstBar) {
            // First bar moves from left to right
            newXPos = centerX + Math.cos(Math.PI - angle) * radius;
            newYPos = Math.sin(Math.PI - angle) * radius * (config.animation?.types?.swap?.height || 1.5);
          } else {
            // Second bar moves from right to left (opposite direction)
            newXPos = centerX - Math.cos(Math.PI - angle) * radius;
            newYPos = Math.sin(Math.PI - angle) * radius * (config.animation?.types?.swap?.height || 1.5);
          }

          const newZPos = Math.sin(angle) * 0.5; // Slight z-axis movement for 3D effect

          // Smooth swap animation progress

          xPos = newXPos;
          yPos = newYPos;
          zPos = newZPos;
        }
      }

      return (
        <group key={`main-${index}-${isSorted}-${value}`} position={[xPos, yPos, zPos]}>
          {/* Base of the bar (like QuickSort) */}
          {config.mainArray.bars.base.enabled && (
            <mesh
              position={[0, 0, 0]}
              castShadow={config.visual.effects.shadows}
              receiveShadow={config.visual.effects.shadows}
            >
              <boxGeometry args={[
                barWidth * config.mainArray.bars.base.widthScale,
                config.mainArray.bars.base.height,
                barWidth * config.mainArray.bars.base.depthScale
              ]} />
              <meshStandardMaterial
                color={barColor}
                transparent={config.mainArray.bars.base.material.transparent}
                opacity={config.mainArray.bars.base.material.opacity}
                roughness={config.mainArray.bars.base.material.roughness}
                metalness={config.mainArray.bars.base.material.metalness}
              />
            </mesh>
          )}

          {/* The bar itself (narrower, sitting on top of base) */}
          <mesh
            position={[0, barHeight / 2, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              barWidth * config.mainArray.bars.geometry.widthScale,
              barHeight,
              barWidth * config.mainArray.bars.geometry.depthScale
            ]} />
            <meshStandardMaterial
              color={barColor}
              transparent={config.mainArray.bars.material.transparent}
              opacity={config.mainArray.bars.material.opacity}
              roughness={config.mainArray.bars.material.roughness}
              metalness={config.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {(showValues || isSorted) && (
            <ThemeHtml
              key={`value-label-${index}-${isSorted}-${value}`}
              position={[config.mainArray.valueLabels.offset[0], barHeight + config.mainArray.valueLabels.offset[1], config.mainArray.valueLabels.offset[2]]}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={config.mainArray.valueLabels.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.valueLabels.padding.horizontal),
                  py: theme.spacing(config.mainArray.valueLabels.padding.vertical),
                  minWidth: config.mainArray.valueLabels.minWidth,
                  borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                  border: 1,
                  borderColor: isCurrentElement ? colors.current : isMinimumElement ? colors.minimum : isSorted ? colors.sorted : 'divider',
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.valueLabels.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: (isCurrentElement || isMinimumElement || isSorted) ? "bold" : config.mainArray.valueLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Index label */}
          {showIndices && (
            <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.indexLabels.elevation}
                sx={{
                  width: config.mainArray.indexLabels.size.width,
                  height: config.mainArray.indexLabels.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: isCurrentElement ? colors.current : isMinimumElement ? colors.minimum : 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.indexLabels.fontSize,
                    fontWeight: (isCurrentElement || isMinimumElement) ? "bold" : config.mainArray.indexLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };

  // Render comparison arrows (like HeapSort - simple ↓ arrows above bars)
  const renderComparisonArrows = () => {
    if (!vizData.mainArray?.comparingIndices || vizData.mainArray.comparingIndices.length === 0) {
      return null;
    }

    const { comparingIndices } = vizData.mainArray;

    return comparingIndices.map((index) => {
      const barPosition = barPositions.find(pos => pos.index === index);
      const xPos = barPosition ? barPosition.xPos : 0;

      return (
        <group key={`arrow-${index}`} position={[xPos, 0, 0]}>
          {/* Comparison arrow - exactly like HeapSort */}
          <ThemeHtml
            position={[0, maxBarHeight + 1, 0]} // Above the bars
            center
            sprite
            theme={theme}
          >
            <div style={{
              color: colors.comparing || config.colors.palette.comparing,
              background: 'transparent',
              fontSize: '1.4rem', // Same as HeapSort
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: `0 0 8px ${colors.comparing || config.colors.palette.comparing}, 0 0 16px ${colors.comparing || config.colors.palette.comparing}`,
              userSelect: 'none',
              pointerEvents: 'none',
              transform: 'scale(1) rotate(0deg)', // Point down to bars
              animation: 'pulse 1s infinite alternate' // Same as HeapSort
            }}>
              ↓
            </div>
            <style>
              {`
              @keyframes pulse {
                0% { transform: scale(1.0) rotate(0deg); }
                100% { transform: scale(1.3) rotate(0deg); }
              }
              `}
            </style>
          </ThemeHtml>
        </group>
      );
    });
  };

  return (
    <group>
      {renderMainArray()}
      {renderComparisonArrows()}
    </group>
  );
};

export default SelectionSortSimulation;
