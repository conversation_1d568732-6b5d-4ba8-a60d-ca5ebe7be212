// FibonacciVisualization.js
// 3D visualization component for <PERSON><PERSON><PERSON><PERSON> algorithm

import React, { useState, useEffect, useRef, useMemo, memo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html, shaderMaterial } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { extend } from '@react-three/fiber';

// Import reusable visualization components
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.8;  // Smaller bar width for cleaner look
const STACK_SPACING = 1.5;

// Create SDF shader material for smooth 3D bars
const SDFBarMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0x2196f3),
    emissive: new THREE.Color(0x2196f3),
    emissiveIntensity: 0.3,
    resolution: new THREE.Vector2(1, 1),
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for crystal-clear bars
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform vec2 resolution;

    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;

    // SDF for rounded box with sharper edges
    float sdRoundBox(vec3 p, vec3 b, float r) {
      vec3 q = abs(p) - b + r;
      return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }

    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdRoundBox(p, vec3(0.45, 0.45, 0.45), 0.05); // Sharper corners

      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);

      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);

      // Specular highlight for crystal-like appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 32.0);

      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);

      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = color * (0.2 + 0.8 * diffuse) +
                        emissive * (emissiveIntensity * (1.0 + pulse)) +
                        vec3(1.0) * specular * 0.5;

      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom material
extend({ SDFBarMaterial });

// Fibonacci Bar component for tornado effect visualization with SDF material
const FibonacciBar = memo(({ position, value, index, color, height }) => {
  // Ensure height is valid and safe
  const safeHeight = Math.max(0.5, height || 1.0);

  // Reference to update shader uniforms
  const materialRef = useRef();

  // Update shader uniforms
  useEffect(() => {
    if (materialRef.current) {
      // Convert color string to THREE.Color
      const threeColor = new THREE.Color(color);

      // Enhance the emissive color for more vibrant appearance
      const emissiveColor = new THREE.Color(color).multiplyScalar(1.2);

      materialRef.current.uniforms.color.value = threeColor;
      materialRef.current.uniforms.emissive.value = emissiveColor;
      materialRef.current.uniforms.emissiveIntensity.value = 0.6; // Increased intensity
    }
  }, [color]);

  // Update time uniform for subtle animation
  useFrame(({ clock }) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = clock.getElapsedTime();
    }
  });

  return (
    <group position={position}>
      {/* Bar with SDF material for smooth 3D effect */}
      <mesh position={[0, safeHeight / 2, 0]}>
        <boxGeometry args={[BAR_WIDTH, safeHeight, BAR_WIDTH]} />
        <sDFBarMaterial ref={materialRef} />
      </mesh>

      {/* Value text */}
      <group position={[0, safeHeight + 0.3, 0]}>
        <Html center distanceFactor={10}>
          <div style={{
            color: 'white',
            fontSize: '16px',
            fontWeight: 'bold',
            textShadow: `0 0 5px ${color}`,
            userSelect: 'none'
          }}>
            {value}
          </div>
        </Html>
      </group>

      {/* Index text */}
      <Html position={[0, -0.3, 0]} center distanceFactor={10}>
        <div style={{
          color: 'white',
          fontSize: '14px',
          userSelect: 'none'
        }}>
          {index}
        </div>
      </Html>
    </group>
  );
});

// Memoized Stack Item component for call stack visualization
const StackItem = memo(({ position, text, color }) => {
  return (
    <group position={position}>
      {/* Background plane with glow effect */}
      <mesh>
        <planeGeometry args={[6, 1.2]} />
        <meshStandardMaterial
          color={color}
          side={THREE.DoubleSide}
          emissive={color}
          emissiveIntensity={0.3}
          transparent
          opacity={0.9}
        />
      </mesh>

      {/* Text */}
      <Html position={[0, 0, 0.1]} center distanceFactor={10}>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          userSelect: 'none',
          width: '200px',
          textAlign: 'center',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          textShadow: '0 0 5px rgba(0,0,0,0.5)'
        }}>
          {text}
        </div>
      </Html>
    </group>
  );
});

// Memoized Memo Item component for memoization approach
const MemoItem = memo(({ position, n, value, color }) => {
  return (
    <group position={position}>
      {/* Background cube with glow effect */}
      <mesh>
        <boxGeometry args={[1.5, 1.5, 0.2]} />
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={0.5}
          transparent
          opacity={0.9}
        />
      </mesh>

      {/* Key text */}
      <Html position={[0, 0.4, 0.2]} center distanceFactor={10}>
        <div style={{
          color: 'white',
          fontSize: '12px',
          userSelect: 'none',
          textShadow: '0 0 5px rgba(0,0,0,0.5)'
        }}>
          {`n=${n}`}
        </div>
      </Html>

      {/* Value text */}
      <Html position={[0, -0.2, 0.2]} center distanceFactor={10}>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          userSelect: 'none',
          textShadow: '0 0 5px rgba(0,0,0,0.5)'
        }}>
          {value}
        </div>
      </Html>
    </group>
  );
});

// Main visualization component
const FibonacciVisualization = ({
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);

  // Set up camera position for optimal tornado viewing angle
  useEffect(() => {
    if (camera) {
      camera.position.set(15, 15, 15);
      camera.lookAt(0, 5, 0);
    }
  }, [camera]);

  // Define colors based on theme
  const colors = useMemo(() => ({
    bar: isDark ? '#4fc3f7' : '#2196f3',         // Blue for regular bars
    activeBar: isDark ? '#ffeb3b' : '#ffc107',   // Yellow for active bar
    resultBar: isDark ? '#69f0ae' : '#00c853',   // Green for result
    stackItem: isDark ? '#ba68c8' : '#9c27b0',   // Purple for stack items
    activeStackItem: isDark ? '#f06292' : '#e91e63', // Pink for active stack item
    memoItem: isDark ? '#ff9800' : '#ff5722',    // Orange for memo items
    activeMemoItem: isDark ? '#f44336' : '#d32f2f', // Red for active memo item
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.bar, label: 'Fibonacci Value' },
    { color: colors.activeBar, label: 'Current Value' },
    { color: colors.resultBar, label: 'Result' },
    { color: colors.stackItem, label: 'Call Stack' },
    { color: colors.memoItem, label: 'Memoized Value' },
  ], [colors]);

  // Update state based on current step
  useEffect(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('No steps available or invalid step index');
      return;
    }

    // Store steps in ref for access in animation frame
    stepsRef.current = steps;

    // Get current step data
    const currentStep = steps[step];
    console.log('Current step data:', currentStep);
    setCurrentStepData(currentStep);

    // Update last applied step
    lastAppliedStepRef.current = step;

    // Update total steps
    if (setTotalSteps) {
      setTotalSteps(steps.length);
    }

    // Update movements
    if (setMovements && currentStep.message) {
      setMovements([currentStep.message]);
    }
  }, [step, steps, setTotalSteps, setMovements]);

  // Helper function to calculate delay based on speed
  const calculateDelay = useCallback((speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  }, []);

  // Auto-advance steps when in running state
  useEffect(() => {
    // Update refs
    stateRef.current = state;
    speedRef.current = speed;

    let timeoutId = null;

    if (state === 'running') {
      // If we're at the end, stop the simulation
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached end of steps, stopping simulation');
        setState('paused'); // Change state to paused instead of looping
        return;
      }

      // Calculate next step
      const nextStep = step + 1;
      console.log('Auto-advancing to step:', nextStep);

      // Calculate delay based on speed
      const delay = calculateDelay(speed);

      // Set a timer to advance to the next step
      timeoutId = setTimeout(() => {
        console.log('Timer fired, setting step to:', nextStep);
        setStep(nextStep);
      }, delay);
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Render the tabulation approach visualization in 3D with tornado effect
  const renderTabulationVisualization = () => {
    if (!currentStepData || !currentStepData.dp) return null;

    const dp = currentStepData.dp;
    const currentIndex = currentStepData.currentIndex;
    const result = currentStepData.result;

    // Calculate parameters for the tornado spiral
    const startRadius = 2.0;  // Start from a larger radius in the center for more spacing
    const radiusIncrement = 0.7;  // Larger increment for more spacing between bars
    const startHeight = 0.5;  // Start with smaller heights
    const heightScale = 0.25;  // Slightly increased scale factor for more visible height differences
    const maxHeight = 10.0;   // Maximum height to prevent bars from being too tall
    const spiralTightness = 0.3;  // Less tight spiral for more spacing
    const yOffset = -5;       // Lower the entire tornado to keep it in view

    return (
      <group position={[0, yOffset, 0]}>
        {/* Fibonacci bars in tornado spiral formation */}
        {dp.map((value, index) => {
          // Calculate spiral position
          const angle = index * spiralTightness;
          const radius = startRadius + index * radiusIncrement;
          const xPos = Math.sin(angle) * radius;
          const zPos = Math.cos(angle) * radius;

          // Calculate height based on Fibonacci value (with safe limits)
          // Use logarithmic scaling for better visualization of exponential growth
          const safeValue = typeof value === 'number' && !isNaN(value) ? value : 1;
          const logScale = Math.log(safeValue + 1) / Math.log(10); // log10(value+1)
          const height = Math.min(maxHeight, Math.max(startHeight, logScale * 3 + safeValue * heightScale));

          // Calculate vertical position - larger values should be higher
          const yPos = height * 0.2; // Lift bars with larger values slightly higher

          // Determine if this is the active bar
          const isActive = index === currentIndex;

          // Determine color based on state
          const color = result !== null && index === dp.length - 1 ?
                        colors.resultBar :
                        isActive ? colors.activeBar : colors.bar;

          return (
            <FibonacciBar
              key={index}
              position={[xPos, yPos, zPos]}
              value={value}
              index={index}
              color={color}
              height={height}
            />
          );
        })}
      </group>
    );
  };

  // Render the memoization approach visualization in 3D with spiral effect
  const renderMemoizationVisualization = () => {
    if (!currentStepData || !currentStepData.dp) return null;

    const memo = currentStepData.dp;
    const callStack = currentStepData.callStack || [];
    const currentIndex = currentStepData.currentIndex;
    const result = currentStepData.result;

    // Convert memo object to array for visualization
    const memoEntries = Object.entries(memo).sort((a, b) => parseInt(a[0]) - parseInt(b[0]));

    return (
      <group>
        {/* Memoization Table Visualization - in a spiral */}
        <group position={[-8, 0, 0]}>
          {memoEntries.map(([key, value], index) => {
            // Calculate spiral position
            const angle = index * 0.5; // Angle increases with each item
            const radius = 1 + index * 0.3; // Radius increases with each item
            const xPos = Math.sin(angle) * radius;
            const zPos = Math.cos(angle) * radius;
            const yPos = index * 0.2; // Slight increase in height for each item

            // Determine if this is the active memo entry
            const isActive = parseInt(key) === currentIndex;
            // Determine color based on state
            const color = isActive ? colors.activeMemoItem : colors.memoItem;

            return (
              <MemoItem
                key={key}
                position={[xPos, yPos, zPos]}
                n={key}
                value={value}
                color={color}
              />
            );
          })}
        </group>

        {/* Call Stack Visualization - in a helix */}
        <group position={[8, 0, 0]}>
          {callStack.map((call, index) => {
            // Position stack items in a helix
            const angle = index * 0.3; // Angle increases with each item
            const radius = 3; // Fixed radius for the helix
            const xPos = Math.sin(angle) * radius;
            const zPos = Math.cos(angle) * radius;
            const yPos = (callStack.length - index) * STACK_SPACING; // Higher items are at the top

            // Determine if this is the active stack item (top of stack)
            const isActive = index === callStack.length - 1;
            // Determine color based on state
            const color = isActive ? colors.activeStackItem : colors.stackItem;

            // Format the text to display
            const text = call[1] !== null ?
              `Fibonacci(${call[0]}) = ${call[1]}` :
              `Fibonacci(${call[0]})`;

            return (
              <StackItem
                key={index}
                position={[xPos, yPos, zPos]}
                text={text}
                color={color}
              />
            );
          })}
        </group>

        {/* Result text */}
        {result !== null && (
          <Html position={[0, 8, 0]} center>
            <div style={{
              color: colors.resultBar,
              fontSize: '24px',
              fontWeight: 'bold',
              userSelect: 'none',
              textShadow: '0 0 5px rgba(0,0,0,0.5)'
            }}>
              {`Fibonacci(${currentStepData.n}) = ${result}`}
            </div>
          </Html>
        )}
      </group>
    );
  };

  // Return the 3D visualization
  return (
    <group>
      {/* Fixed Step board - stays at the top of the screen */}
      <FixedStepBoard
        description={currentStepData?.message || 'Fibonacci Sequence Algorithm'}
        currentStep={step}
        totalSteps={stepsRef.current.length || 1}
        stepData={currentStepData || {}}
      />

      {/* Fixed Color legend - stays at the bottom of the screen */}
      <FixedColorLegend
        items={legendItems}
        theme={muiTheme}
      />

      {/* Ambient light for overall scene illumination */}
      <ambientLight intensity={0.5} />

      {/* Directional lights for better 3D definition */}
      <directionalLight position={[10, 10, 5]} intensity={0.7} color="#ffffff" />
      <directionalLight position={[-10, -10, -5]} intensity={0.3} color={isDark ? '#6666ff' : '#66ccff'} />

      {/* Spotlight to highlight the main visualization */}
      <spotLight
        position={[0, 25, 0]}
        angle={0.3}
        penumbra={0.8}
        intensity={0.8}
        castShadow
        color={isDark ? '#ffffff' : '#ffffff'}
      />

      {/* Visualization */}
      <group>
        {currentStepData?.approach === 'tabulation'
          ? renderTabulationVisualization()
          : renderMemoizationVisualization()}
      </group>

      {/* Add a subtle fog effect for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

      {/* Add a grid helper for better spatial reference */}
      <gridHelper
        args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
        position={[0, -12, 0]}
        rotation={[0, 0, 0]}
      />
    </group>
  );
};

export default FibonacciVisualization;
