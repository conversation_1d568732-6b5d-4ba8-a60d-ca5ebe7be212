// KnapsackAlgorithm.js
// Implementation of 0/1 Knapsack algorithm using dynamic programming

/**
 * Generate steps for solving the 0/1 Knapsack problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateKnapsackSteps = (params) => {
  console.log('generateKnapsackSteps called with params:', params);
  const { weights = [2, 3, 4, 5], values = [3, 4, 5, 6], capacity = 8 } = params;
  const steps = [];

  // Validate input
  if (!Array.isArray(weights) || !Array.isArray(values) || weights.length !== values.length) {
    throw new Error('Weights and values must be arrays of the same length');
  }

  if (isNaN(capacity) || capacity < 0) {
    throw new Error('Capacity must be a non-negative number');
  }

  const n = weights.length;

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize 0/1 Knapsack algorithm with ${n} items and capacity ${capacity}`,
    weights: [...weights],
    values: [...values],
    capacity,
    dp: null,
    currentItem: null,
    currentWeight: null,
    selectedItems: [],
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table
  const dp = Array(n + 1).fill().map(() => Array(capacity + 1).fill(0));

  // Add DP table creation step
  steps.push({
    type: 'create_table',
    message: `Create a table of size ${n + 1}x${capacity + 1} initialized with zeros`,
    weights: [...weights],
    values: [...values],
    capacity,
    dp: JSON.parse(JSON.stringify(dp)),
    currentItem: null,
    currentWeight: null,
    selectedItems: [],
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  for (let i = 1; i <= n; i++) {
    for (let w = 0; w <= capacity; w++) {
      // Add step for current cell consideration
      steps.push({
        type: 'consider_cell',
        message: `Consider item ${i} (weight: ${weights[i-1]}, value: ${values[i-1]}) at capacity ${w}`,
        weights: [...weights],
        values: [...values],
        capacity,
        dp: JSON.parse(JSON.stringify(dp)),
        currentItem: i,
        currentWeight: w,
        selectedItems: [],
        result: null,
        pseudocodeLine: 6
      });

      // If the current item can fit in the knapsack
      if (weights[i - 1] <= w) {
        // Calculate the value if we include this item
        const valueWithItem = dp[i - 1][w - weights[i - 1]] + values[i - 1];
        // Calculate the value if we exclude this item
        const valueWithoutItem = dp[i - 1][w];

        // Add step for comparing inclusion vs exclusion
        steps.push({
          type: 'compare',
          message: `Item ${i} can fit. Compare: Include (${valueWithItem}) vs Exclude (${valueWithoutItem})`,
          weights: [...weights],
          values: [...values],
          capacity,
          dp: JSON.parse(JSON.stringify(dp)),
          currentItem: i,
          currentWeight: w,
          includeValue: valueWithItem,
          excludeValue: valueWithoutItem,
          selectedItems: [],
          result: null,
          pseudocodeLine: 8
        });

        // Choose the better option
        dp[i][w] = Math.max(valueWithItem, valueWithoutItem);

        // Add step for decision
        const decision = valueWithItem > valueWithoutItem ? 'include' : 'exclude';
        steps.push({
          type: 'decision',
          message: `Decision for item ${i} at capacity ${w}: ${decision} (value: ${dp[i][w]})`,
          weights: [...weights],
          values: [...values],
          capacity,
          dp: JSON.parse(JSON.stringify(dp)),
          currentItem: i,
          currentWeight: w,
          decision,
          selectedItems: [],
          result: null,
          pseudocodeLine: 9
        });
      } else {
        // If the item doesn't fit, we exclude it
        dp[i][w] = dp[i - 1][w];

        // Add step for exclusion due to weight constraint
        steps.push({
          type: 'exclude',
          message: `Item ${i} doesn't fit (weight ${weights[i-1]} > remaining capacity ${w}). Exclude it.`,
          weights: [...weights],
          values: [...values],
          capacity,
          dp: JSON.parse(JSON.stringify(dp)),
          currentItem: i,
          currentWeight: w,
          selectedItems: [],
          result: null,
          pseudocodeLine: 11
        });
      }
    }
  }

  // Trace back to find the selected items
  let remainingCapacity = capacity;
  const selectedItems = [];

  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    message: 'Start tracing back to find the selected items',
    weights: [...weights],
    values: [...values],
    capacity,
    dp: JSON.parse(JSON.stringify(dp)),
    currentItem: n,
    currentWeight: capacity,
    selectedItems: [],
    result: dp[n][capacity],
    pseudocodeLine: 14
  });

  for (let i = n; i > 0; i--) {
    // Check if this item was included
    if (dp[i][remainingCapacity] !== dp[i - 1][remainingCapacity]) {
      // This item was included
      selectedItems.push(i - 1); // 0-indexed
      remainingCapacity -= weights[i - 1];

      // Add step for including item in solution
      steps.push({
        type: 'include_in_solution',
        message: `Include item ${i} in the solution (weight: ${weights[i-1]}, value: ${values[i-1]})`,
        weights: [...weights],
        values: [...values],
        capacity,
        dp: JSON.parse(JSON.stringify(dp)),
        currentItem: i,
        currentWeight: remainingCapacity,
        selectedItems: [...selectedItems],
        result: dp[n][capacity],
        pseudocodeLine: 17
      });
    } else {
      // This item was not included
      steps.push({
        type: 'exclude_from_solution',
        message: `Exclude item ${i} from the solution`,
        weights: [...weights],
        values: [...values],
        capacity,
        dp: JSON.parse(JSON.stringify(dp)),
        currentItem: i,
        currentWeight: remainingCapacity,
        selectedItems: [...selectedItems],
        result: dp[n][capacity],
        pseudocodeLine: 19
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Maximum value: ${dp[n][capacity]}`,
    weights: [...weights],
    values: [...values],
    capacity,
    dp: JSON.parse(JSON.stringify(dp)),
    currentItem: null,
    currentWeight: null,
    selectedItems: [...selectedItems],
    result: dp[n][capacity],
    pseudocodeLine: 21
  });

  return { 
    steps, 
    result: dp[n][capacity],
    selectedItems: selectedItems.sort((a, b) => a - b) // Sort for consistent display
  };
};

// Create the algorithm object with helper functions
const KnapsackAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateKnapsackSteps
};

export default KnapsackAlgorithm;
