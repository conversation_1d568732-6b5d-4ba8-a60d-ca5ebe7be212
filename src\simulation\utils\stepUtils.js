// stepUtils.js
// Utility functions for step generation and processing

/**
 * Creates a step object with common properties
 * 
 * @param {string} type - The type of step
 * @param {Object} data - The data associated with the step
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The step object
 */
export const createStep = (type, data = {}, message = '', metadata = {}) => {
  return {
    type,
    data,
    message,
    metadata,
    timestamp: Date.now()
  };
};

/**
 * Creates an initial step for an algorithm
 * 
 * @param {Array|Object} data - The initial data for the algorithm
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The initial step object
 */
export const createInitialStep = (data, message = 'Initial state', metadata = {}) => {
  return createStep('initial', { data }, message, metadata);
};

/**
 * Creates a completion step for an algorithm
 * 
 * @param {Array|Object} data - The final data for the algorithm
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The completion step object
 */
export const createCompletionStep = (data, message = 'Algorithm complete', metadata = {}) => {
  return createStep('complete', { data }, message, metadata);
};

/**
 * Creates a comparison step for an algorithm
 * 
 * @param {Array|Object} data - The data being compared
 * @param {Array} indices - The indices being compared
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The comparison step object
 */
export const createComparisonStep = (data, indices, message = '', metadata = {}) => {
  return createStep('comparison', { data, indices }, message, metadata);
};

/**
 * Creates a swap step for an algorithm
 * 
 * @param {Array|Object} data - The data after the swap
 * @param {Array} indices - The indices being swapped
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The swap step object
 */
export const createSwapStep = (data, indices, message = '', metadata = {}) => {
  return createStep('swap', { data, indices }, message, metadata);
};

/**
 * Creates a merge step for an algorithm
 * 
 * @param {Array|Object} data - The data after the merge
 * @param {Object} mergeInfo - Information about the merge
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The merge step object
 */
export const createMergeStep = (data, mergeInfo, message = '', metadata = {}) => {
  return createStep('merge', { data, mergeInfo }, message, metadata);
};

/**
 * Creates a split step for an algorithm
 * 
 * @param {Array|Object} data - The data after the split
 * @param {Object} splitInfo - Information about the split
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The split step object
 */
export const createSplitStep = (data, splitInfo, message = '', metadata = {}) => {
  return createStep('split', { data, splitInfo }, message, metadata);
};

/**
 * Creates a place step for an algorithm
 * 
 * @param {Array|Object} data - The data after the placement
 * @param {Object} placeInfo - Information about the placement
 * @param {string} message - A human-readable description of the step
 * @param {Object} metadata - Additional information about the step
 * @returns {Object} The place step object
 */
export const createPlaceStep = (data, placeInfo, message = '', metadata = {}) => {
  return createStep('place', { data, placeInfo }, message, metadata);
};

/**
 * Validates a step object
 * 
 * @param {Object} step - The step object to validate
 * @returns {boolean} Whether the step is valid
 */
export const validateStep = (step) => {
  // Check if step is an object
  if (!step || typeof step !== 'object') {
    return false;
  }

  // Check if step has required properties
  if (!step.type || !step.data) {
    return false;
  }

  // Check if step type is valid
  const validTypes = [
    'initial', 'complete', 'comparison', 'swap', 
    'merge', 'split', 'place', 'description'
  ];
  if (!validTypes.includes(step.type)) {
    return false;
  }

  return true;
};

/**
 * Formats a step message with colored indices
 * 
 * @param {string} message - The message template
 * @param {Object} values - The values to insert into the template
 * @returns {Object} The formatted message with HTML
 */
export const formatStepMessage = (message, values = {}) => {
  // Replace placeholders with values
  let formattedMessage = message;
  
  // Replace index placeholders with colored spans
  Object.entries(values).forEach(([key, value]) => {
    const placeholder = `{${key}}`;
    
    // If the value is an index or array of indices, color it
    if (key.includes('index') || key.includes('indices')) {
      // For arrays of indices
      if (Array.isArray(value)) {
        const coloredIndices = value.map(index => 
          `<span class="index-highlight">${index}</span>`
        ).join(', ');
        formattedMessage = formattedMessage.replace(placeholder, coloredIndices);
      } 
      // For single indices
      else {
        formattedMessage = formattedMessage.replace(
          placeholder, 
          `<span class="index-highlight">${value}</span>`
        );
      }
    } 
    // For other values, just insert them
    else {
      formattedMessage = formattedMessage.replace(placeholder, value);
    }
  });

  return { __html: formattedMessage };
};

/**
 * Deep clones an array or object
 * 
 * @param {Array|Object} obj - The object to clone
 * @returns {Array|Object} A deep clone of the object
 */
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Generates a random array of integers
 * 
 * @param {number} length - The length of the array
 * @param {number} min - The minimum value (inclusive)
 * @param {number} max - The maximum value (inclusive)
 * @returns {Array} A random array of integers
 */
export const generateRandomArray = (length, min = 1, max = 100) => {
  return Array.from({ length }, () => 
    Math.floor(Math.random() * (max - min + 1)) + min
  );
};

/**
 * Parses a custom array input string
 * 
 * @param {string} input - The input string
 * @returns {Array|null} The parsed array or null if invalid
 */
export const parseCustomArray = (input) => {
  try {
    // Remove all whitespace and split by commas
    const values = input.replace(/\s/g, '').split(',');
    
    // Parse each value as an integer
    const parsedArray = values.map(val => {
      const num = parseInt(val, 10);
      if (isNaN(num)) {
        throw new Error('Invalid number');
      }
      return num;
    });
    
    return parsedArray;
  } catch (error) {
    return null;
  }
};
