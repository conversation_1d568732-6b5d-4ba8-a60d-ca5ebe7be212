// MatrixChainMultiplication/index.js
// Export only what's needed from this algorithm

import MatrixChainMultiplicationVisualization from './MatrixChainMultiplicationVisualization';
import MatrixChainMultiplicationController from './MatrixChainMultiplicationController';
import MatrixChainMultiplicationAlgorithm from './MatrixChainMultiplicationAlgorithm';

export const metadata = {
  id: 'MatrixChainMultiplication',
  name: 'Matrix Chain Multiplication',
  description: 'A dynamic programming algorithm that determines the most efficient way to multiply a sequence of matrices to minimize the total number of scalar multiplications.',
  timeComplexity: 'O(n³)',
  spaceComplexity: 'O(n²)',
  defaultParams: {
    dimensions: [30, 35, 15, 5, 10, 20, 25],
  },
};

export const components = {
  visualization: MatrixChainMultiplicationVisualization,
  controller: MatrixChainMultiplicationController,
  algorithm: MatrixChainMultiplicationAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
