// QuickSortAlgorithm.js
// This component displays the Quick Sort algorithm steps

import React from 'react';
import { Paper, Typography, Box, useTheme } from '@mui/material';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { useAlgorithm } from '../../../context/AlgorithmContext';

// Theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    // Syntax highlighting colors
    keyword: isDark ? "#569cd6" : "#0000ff", // Blue for keywords (function, if, else)
    function: isDark ? "#dcdcaa" : "#795e26", // Yellow/brown for function name
    parameter: isDark ? "#9cdcfe" : "#001080", // Blue for parameters/variables
    comment: isDark ? "#6a9955" : "#008000", // Green for comments/actions
    operator: isDark ? "#d4d4d4" : "#000000", // Default/operator color
    number: isDark ? "#b5cea8" : "#098658", // Green for numbers
    string: isDark ? "#ce9178" : "#a31515", // Red for strings

    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

/**
 * Generate steps for Quick Sort algorithm visualization
 * @param {Array} array - The array to sort
 * @returns {Object} - Object containing steps and sorted array
 */
export const generateQuickSortSteps = (array) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...array];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [],
    pivot: null,
    left: null,
    right: null,
    movement: 'Initialize Quick Sort'
  });

  // Track sorted indices
  const sortedIndices = new Set();

  // Recursive function to perform Quick Sort
  const quickSort = (start, end, depth = 0) => {
    // Base case: if the partition has 0 or 1 elements, it's already sorted
    if (start >= end) {
      if (start === end) {
        sortedIndices.add(start);
        steps.push({
          type: 'sorted',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: null,
          left: null,
          right: null,
          movement: `Mark index ${start} as sorted (single element partition)`
        });
      }
      return;
    }

    // Choose the pivot (using the last element for simplicity)
    const pivotIndex = end;
    const pivotValue = arr[pivotIndex];

    // Add step to show pivot selection
    steps.push({
      type: 'pivot',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: pivotIndex,
      left: start,
      right: end,
      movement: `Select pivot at index ${pivotIndex} with value ${pivotValue}`
    });

    // Partition the array
    let i = start - 1; // Index of smaller element

    for (let j = start; j < end; j++) {
      // Compare current element with pivot
      steps.push({
        type: 'compare',
        array: [...arr],
        comparing: [j, pivotIndex],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: pivotIndex,
        left: start,
        right: end,
        movement: `Compare element at index ${j} (${arr[j]}) with pivot (${pivotValue})`
      });

      // If current element is smaller than the pivot
      if (arr[j] < pivotValue) {
        i++; // Increment index of smaller element

        // Swap arr[i] and arr[j]
        if (i !== j) {
          steps.push({
            type: 'swap',
            array: [...arr],
            comparing: [],
            swapping: [i, j],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Swap elements at indices ${i} (${arr[i]}) and ${j} (${arr[j]})`
          });

          [arr[i], arr[j]] = [arr[j], arr[i]];

          steps.push({
            type: 'swapped',
            array: [...arr],
            comparing: [],
            swapping: [],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Swapped elements: ${arr[i]} and ${arr[j]}`
          });
        } else {
          steps.push({
            type: 'skip',
            array: [...arr],
            comparing: [],
            swapping: [],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Element at index ${i} (${arr[i]}) is already in the correct position`
          });
        }
      } else {
        steps.push({
          type: 'skip',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: pivotIndex,
          left: start,
          right: end,
          movement: `Skip element at index ${j} (${arr[j]}) as it's >= pivot (${pivotValue})`
        });
      }
    }

    // Place the pivot in its correct position
    const newPivotIndex = i + 1;

    if (newPivotIndex !== pivotIndex) {
      steps.push({
        type: 'swap',
        array: [...arr],
        comparing: [],
        swapping: [newPivotIndex, pivotIndex],
        sorted: [...sortedIndices],
        pivot: pivotIndex,
        left: start,
        right: end,
        movement: `Swap pivot at index ${pivotIndex} (${arr[pivotIndex]}) with element at index ${newPivotIndex} (${arr[newPivotIndex]})`
      });

      [arr[newPivotIndex], arr[pivotIndex]] = [arr[pivotIndex], arr[newPivotIndex]];

      steps.push({
        type: 'swapped',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: newPivotIndex,
        left: start,
        right: end,
        movement: `Placed pivot (${arr[newPivotIndex]}) at its correct position (index ${newPivotIndex})`
      });
    } else {
      steps.push({
        type: 'skip',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: pivotIndex,
        left: start,
        right: end,
        movement: `Pivot (${arr[pivotIndex]}) is already in its correct position (index ${pivotIndex})`
      });
    }

    // Mark the pivot as sorted
    sortedIndices.add(newPivotIndex);
    steps.push({
      type: 'sorted',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: newPivotIndex,
      left: start,
      right: end,
      movement: `Mark pivot at index ${newPivotIndex} as sorted`
    });

    // Recursively sort the sub-arrays
    if (start < newPivotIndex - 1) {
      steps.push({
        type: 'recurse',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: null,
        left: start,
        right: newPivotIndex - 1,
        movement: `Recursively sort left sub-array [${start}...${newPivotIndex - 1}]`
      });

      quickSort(start, newPivotIndex - 1, depth + 1);
    }

    if (newPivotIndex + 1 < end) {
      steps.push({
        type: 'recurse',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: null,
        left: newPivotIndex + 1,
        right: end,
        movement: `Recursively sort right sub-array [${newPivotIndex + 1}...${end}]`
      });

      quickSort(newPivotIndex + 1, end, depth + 1);
    }

    // If this is the top-level call and we've sorted everything
    if (depth === 0 && sortedIndices.size === arr.length) {
      steps.push({
        type: 'complete',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...Array(arr.length).keys()], // All indices are sorted
        pivot: null,
        left: null,
        right: null,
        movement: 'Quick Sort complete'
      });
    }
  };

  // Start the sorting process
  quickSort(0, arr.length - 1);

  return { steps, sortedArray: arr };
};

const QuickSortAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Determine which line to highlight based on the current step
  let highlightLine = 0; // Default to function declaration

  if (currentAlgorithmStep) {
    switch (currentAlgorithmStep.type) {
      case 'pivot':
        highlightLine = 2; // Choose pivot
        break;
      case 'compare':
        highlightLine = 4; // Compare elements
        break;
      case 'swap':
      case 'swapped':
        highlightLine = 5; // Swap elements
        break;
      case 'sorted':
        highlightLine = 6; // Place pivot
        break;
      case 'recurse':
        highlightLine = 7; // Recursive calls
        break;
      case 'complete':
        highlightLine = 8; // Return statement
        break;
      default:
        highlightLine = 0;
    }
  }

  // Get the step message
  const stepMessage = currentAlgorithmStep
    ? currentAlgorithmStep.movement || "Processing..."
    : step === 0
      ? "Initial state"
      : "Algorithm complete";

  // Function to render a code line with highlighting and arrow
  const CodeLine = ({ line, content, indent = 0 }) => (
    <Typography
      component="div"
      sx={{
        mb: 0.5,
        bgcolor: highlightLine === line ? colors.highlight : "rgba(0,0,0,0.03)",
        borderRadius: 1,
        p: 1,
        position: 'relative',
        border: `1px solid ${colors.border}`,
        opacity: highlightLine === line ? 1 : 0.7,
        transition: 'all 0.2s ease-in-out',
        pl: 1 + indent * 2, // Increase padding for indentation
      }}
    >
      {highlightLine === line && (
        <ArrowRightIcon
          sx={{
            position: 'absolute',
            left: -32,
            top: '50%',
            transform: 'translateY(-50%)',
            color: theme.palette.primary.main,
            fontSize: '2rem',
            animation: 'pulse 1.5s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
              '50%': { opacity: 1, transform: 'translateY(-50%) scale(1.1)' },
              '100%': { opacity: 0.5, transform: 'translateY(-50%) scale(0.9)' },
            },
          }}
        />
      )}
      {content}
    </Typography>
  );

  return (
    <Paper
      elevation={3}
      sx={{
        p: 2,
        bgcolor: colors.background,
        borderRadius: 2,
        overflowX: "auto",
        border: `1px solid ${colors.border}`,
        boxShadow: theme.shadows[3],
      }}
    >
      {/* Current step description */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          mb: 2,
          bgcolor: colors.stepBackground,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            color: colors.text,
            fontWeight: 500,
          }}
        >
          Step {step}: {stepMessage}
        </Typography>
      </Paper>

      {/* Code block */}
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          borderRadius: 1,
          border: `1px solid ${colors.border}`,
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.7)' : 'rgba(245, 245, 245, 0.9)',
          overflow: 'auto', // Add horizontal scrolling
          maxWidth: '100%', // Ensure it doesn't exceed container width
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
            },
          },
        }}
      >
        <Box
          sx={{
            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
            whiteSpace: "nowrap", // Prevent line wrapping
            fontSize: "0.9rem",
            lineHeight: 1.7,
            color: colors.text,
            p: 0.5,
            minWidth: 'min-content', // Ensure content doesn't shrink below its minimum width
          }}
        >
          {/* Function Signature */}
          <CodeLine
            line={0}
            content={<>
              <Box component="span" sx={{ color: colors.keyword }}>
                function
              </Box>{" "}
              <Box component="span" sx={{ color: colors.function }}>
                quickSort
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                (
              </Box>
              <Box component="span" sx={{ color: colors.parameter }}>
                arr, low, high
              </Box>
              <Box component="span" sx={{ color: colors.operator }}>
                ):
              </Box>
            </>}
          />

          {/* Base Case */}
          <Box sx={{ pl: 2 }}>
            <CodeLine
              line={1}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  if
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  low
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  {'>='}
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  high
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ):
                </Box>
                <Box component="span" sx={{ pl: 2, color: colors.keyword }}>
                  return
                </Box>
              </>}
            />

            {/* Choose Pivot */}
            <CodeLine
              line={2}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.parameter }}>
                  pivot
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  =
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr[high]
                </Box>
                <Box component="span" sx={{ color: colors.comment }}>
                  {" # Choose last element as pivot"}
                </Box>
              </>}
            />

            {/* Initialize Index */}
            <CodeLine
              line={3}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.parameter }}>
                  i
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  =
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  low
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  -
                </Box>{" "}
                <Box component="span" sx={{ color: colors.number }}>
                  1
                </Box>
                <Box component="span" sx={{ color: colors.comment }}>
                  {" # Index of smaller element"}
                </Box>
              </>}
            />

            {/* Partition Loop */}
            <CodeLine
              line={4}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  for
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  j
                </Box>{" "}
                <Box component="span" sx={{ color: colors.keyword }}>
                  in range
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  low
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ,
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  high
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  ):
                </Box>
              </>}
            />

            {/* Compare and Swap */}
            <CodeLine
              line={5}
              indent={2}
              content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  if
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr[j]
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  {'<'}
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  pivot
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  :
                </Box>
                <Box component="span" sx={{ pl: 2, color: colors.parameter }}>
                  i
                </Box>{" "}
                <Box component="span" sx={{ color: colors.operator }}>
                  +=
                </Box>{" "}
                <Box component="span" sx={{ color: colors.number }}>
                  1
                </Box>
                <Box component="span" sx={{ pl: 2, color: colors.keyword }}>
                  swap
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr[i], arr[j]
                </Box>
              </>}
            />

            {/* Place Pivot */}
            <CodeLine
              line={6}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  swap
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr[i+1], arr[high]
                </Box>
                <Box component="span" sx={{ color: colors.comment }}>
                  {" # Place pivot in correct position"}
                </Box>
              </>}
            />

            {/* Recursive Calls */}
            <CodeLine
              line={7}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.function }}>
                  quickSort
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr, low, i
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
                <Box component="span" sx={{ color: colors.comment }}>
                  {" # Sort left subarray"}
                </Box>
                <br />
                <Box component="span" sx={{ color: colors.function }}>
                  quickSort
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  (
                </Box>
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr, i+2, high
                </Box>
                <Box component="span" sx={{ color: colors.operator }}>
                  )
                </Box>
                <Box component="span" sx={{ color: colors.comment }}>
                  {" # Sort right subarray"}
                </Box>
              </>}
            />

            {/* Return */}
            <CodeLine
              line={8}
              indent={1}
              content={<>
                <Box component="span" sx={{ color: colors.keyword }}>
                  return
                </Box>{" "}
                <Box component="span" sx={{ color: colors.parameter }}>
                  arr
                </Box>
              </>}
            />
          </Box>
        </Box>
      </Paper>
    </Paper>
  );
};

export default QuickSortAlgorithm;
