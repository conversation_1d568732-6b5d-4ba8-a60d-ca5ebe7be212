// RefactoredMergeSortBars.js - A cleaner implementation of merge sort visualization
import React from 'react';
import { ArrayVisualization } from './MergeSortBarComponents';

/**
 * Render a result array for a specific merge operation
 * @param {Object} props - Component props
 * @param {Object} props.mergeOp - The current merge operation
 * @param {Object} props.currentStep - The current step of the algorithm
 * @param {Object} props.colors - Colors object
 * @param {Object} props.config - Configuration object
 * @param {Object} props.theme - MUI theme object
 * @returns {JSX.Element} The result array visualization
 */
export const ResultArrayVisualization = ({
  mergeOp,
  currentStep,
  colors,
  config,
  theme
}) => {
  if (!mergeOp) return null;

  // Extract configuration values
  const { barWidth, barSpacing, maxBarHeight } = config;
  const { rightContainerX, explanatoryConfig, resultArray } = config;

  // Get the size of the result array
  const size = mergeOp.high - mergeOp.low + 1;

  // Calculate the width and spacing for the result array bars
  const resultBarWidth = barWidth * 0.6; // Smaller bars for the result array
  const resultBarSpacing = barSpacing * 0.6;

  // Reduce the height of result bars
  const resultHeightRatio = 0.5; // Make result bars 50% of the height of normal bars

  // Get the result array position from the config
  const resultArrayX = rightContainerX + resultArray.labelPosition[0];
  const resultArrayY = explanatoryConfig.common.yPosition;

  // Check if we have a place step for this merge operation
  if (currentStep &&
    (currentStep.type === 'place_from_left_subarray' || currentStep.type === 'place_from_right_subarray') &&
    currentStep.indices &&
    currentStep.indices[0] === mergeOp.low && currentStep.indices[1] === mergeOp.high) {

    // Find the place info from the current step
    const placeInfo = currentStep.placeInfo;
    if (!placeInfo) return null;

    // Get the result array from the place info
    const result = placeInfo.result;
    if (!result) return null;

    // Get the source value and index
    const sourceValue = placeInfo.sourceValue;
    // We're not using sourceIndex but keeping it for future reference
    // const sourceIndex = currentStep.type === 'place_from_left_subarray' ? placeInfo.leftArrayIndex : placeInfo.rightArrayIndex;

    // Get the target index in the result array
    const targetIndex = placeInfo.resultIndex;

    // Create a modified result array with the source value at the target index
    const modifiedResult = [...result];
    if (targetIndex !== undefined && sourceValue !== undefined) {
      modifiedResult[targetIndex] = sourceValue;
    }

    // Determine the array type based on the current step
    const arrayType = currentStep.type === 'place_from_left_subarray' ? 'left' : 'right';

    return (
      <ArrayVisualization
        title="Result Array"
        values={modifiedResult}
        position={[resultArrayX, resultArrayY, 0]}
        colors={colors}
        arrayType={arrayType}
        barWidth={resultBarWidth}
        barSpacing={resultBarSpacing}
        maxBarHeight={maxBarHeight}
        scale={resultHeightRatio}
        theme={theme}
        showValues={true}
        showIndices={true}
        labelPosition={[0, resultArray.labelPosition[1], 0]}
      />
    );
  }

  // Check if the result array is complete or being copied back
  else if (currentStep &&
    (currentStep.type === 'result_array_complete' || currentStep.type === 'copy_back') &&
    currentStep.indices &&
    currentStep.indices[0] === mergeOp.low && currentStep.indices[1] === mergeOp.high) {

    // Find the result data from the current step
    let resultData = null;
    let result = null;

    // Try to get result data from the current step
    if (currentStep.type === 'result_array_complete' && currentStep.resultArray && currentStep.resultArray.result) {
      resultData = currentStep.resultArray;
      result = resultData.result;
      console.log("Found result array data for result_array_complete step:", result);
    }
    else if (currentStep.type === 'copy_back' && currentStep.copyInfo && currentStep.copyInfo.result) {
      resultData = currentStep.copyInfo;
      result = resultData.result;
      console.log("Found result array data for copy_back step:", result);
    }
    // If we still don't have result data, check the mergeOp
    else if (mergeOp.step && mergeOp.step.resultArray && mergeOp.step.resultArray.result) {
      resultData = mergeOp.step.resultArray;
      result = resultData.result;
      console.log("Found result array data from mergeOp step:", result);
    }
    else if (mergeOp.step && mergeOp.step.copyInfo && mergeOp.step.copyInfo.result) {
      resultData = mergeOp.step.copyInfo;
      result = resultData.result;
      console.log("Found copy info data from mergeOp step:", result);
    }

    // If we don't have result data, create a default array with placeholder values
    if (!result) {
      console.log("No result data found, creating default array");
      const size = mergeOp.high - mergeOp.low + 1;
      result = new Array(size).fill(1); // Use 1 instead of null to ensure bars are visible
    }

    // Add animation for copy_back step
    const isCopyingBack = currentStep.type === 'copy_back';

    return (
      <ArrayVisualization
        title={isCopyingBack ? 'Copying Back to Original Array' : 'Completed Result Array'}
        values={result}
        position={[resultArrayX, resultArrayY, 0]}
        colors={colors}
        arrayType="result"
        barWidth={resultBarWidth}
        barSpacing={resultBarSpacing}
        maxBarHeight={maxBarHeight}
        scale={resultHeightRatio}
        theme={theme}
        showValues={true}
        showIndices={true}
        labelPosition={[0, resultArray.labelPosition[1], 0]}
      />
    );
  }

  // Check if this is a create_result_array step
  else if (currentStep && currentStep.type === 'create_result_array') {
    // For create_result_array step, we want to show an empty result array with the correct size
    // Get the size from the step data if available
    const resultSize = currentStep.resultArray?.size || size;

    // Create an empty array of the specified size with placeholder values (0)
    // Using 0 instead of null so the bars are visible
    const emptyArray = new Array(resultSize).fill(0);

    return (
      <ArrayVisualization
        title="Empty Result Array"
        values={emptyArray}
        position={[resultArrayX, resultArrayY, 0]}
        colors={colors}
        arrayType="result"
        barWidth={resultBarWidth}
        barSpacing={resultBarSpacing}
        maxBarHeight={maxBarHeight}
        scale={resultHeightRatio}
        theme={theme}
        showValues={false} // Don't show values for empty array
        showIndices={true} // Show indices
        labelPosition={[0, resultArray.labelPosition[1], 0]}
      />
    );
  }
  // Default case: render empty result array
  else {
    // Create an empty array of the specified size
    const emptyArray = new Array(size).fill(null);

    return (
      <ArrayVisualization
        title="Result Array"
        values={emptyArray}
        position={[resultArrayX, resultArrayY, 0]}
        colors={colors}
        arrayType="result"
        barWidth={resultBarWidth}
        barSpacing={resultBarSpacing}
        maxBarHeight={maxBarHeight}
        scale={resultHeightRatio}
        theme={theme}
        showValues={false}
        showIndices={true}
        labelPosition={[0, resultArray.labelPosition[1], 0]}
      />
    );
  }
};

// Create a named export object
const exports = {
  ResultArrayVisualization
};

export default exports;
