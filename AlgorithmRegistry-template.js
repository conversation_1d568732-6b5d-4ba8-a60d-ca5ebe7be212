// AlgorithmRegistry.js
// This file serves as a central registry for all algorithms in the application.
// Each algorithm is registered with its metadata, visualization component, and controller component.

// ===== SORTING ALGORITHMS =====
// Import sorting algorithm components
import BubbleSortVisualization from './sorting/BubbleSort/BubbleSortVisualization';
import BubbleSortController from './sorting/BubbleSort/BubbleSortController';
import BubbleSortAlgorithm from './sorting/BubbleSort/BubbleSortAlgorithm';

import MergeSortVisualization from './sorting/MergeSort/MergeSortVisualization';
import MergeSortController from './sorting/MergeSort/MergeSortController';
import MergeSortAlgorithm from './sorting/MergeSort/MergeSortAlgorithm';

import QuickSortVisualization from './sorting/QuickSort/QuickSortVisualization';
import QuickSortController from './sorting/QuickSort/QuickSortController';
import QuickSortAlgorithm from './sorting/QuickSort/QuickSortAlgorithm';

import HeapSortVisualization from './sorting/HeapSort/HeapSortVisualization';
import HeapSortController from './sorting/HeapSort/HeapSortController';
import HeapSortAlgorithm from './sorting/HeapSort/HeapSortAlgorithm';

import InsertionSortVisualization from './sorting/InsertionSort/InsertionSortVisualization';
import InsertionSortController from './sorting/InsertionSort/InsertionSortController';
import InsertionSortAlgorithm from './sorting/InsertionSort/InsertionSortAlgorithm';

import SelectionSortVisualization from './sorting/SelectionSort/SelectionSortVisualization';
import SelectionSortController from './sorting/SelectionSort/SelectionSortController';
import SelectionSortAlgorithm from './sorting/SelectionSort/SelectionSortAlgorithm';

import RadixSortVisualization from './sorting/RadixSort/RadixSortVisualization';
import RadixSortController from './sorting/RadixSort/RadixSortController';
import RadixSortAlgorithm from './sorting/RadixSort/RadixSortAlgorithm';

import CountingSortVisualization from './sorting/CountingSort/CountingSortVisualization';
import CountingSortController from './sorting/CountingSort/CountingSortController';
import CountingSortAlgorithm from './sorting/CountingSort/CountingSortAlgorithm';

import BucketSortVisualization from './sorting/BucketSort/BucketSortVisualization';
import BucketSortController from './sorting/BucketSort/BucketSortController';
import BucketSortAlgorithm from './sorting/BucketSort/BucketSortAlgorithm';

// ===== SEARCHING ALGORITHMS =====
// Import searching algorithm components
import BFSVisualization from './searching/BFS/BFSVisualization';
import BFSController from './searching/BFS/BFSController';
import BFSAlgorithm from './searching/BFS/BFSAlgorithm';

import DFSVisualization from './searching/DFS/DFSVisualization';
import DFSController from './searching/DFS/DFSController';
import DFSAlgorithm from './searching/DFS/DFSAlgorithm';

// ===== GRAPH ALGORITHMS =====
// Import graph algorithm components
import DijkstraVisualization from './graph/Dijkstra/DijkstraVisualization';
import DijkstraController from './graph/Dijkstra/DijkstraController';
import DijkstraAlgorithm from './graph/Dijkstra/DijkstraAlgorithm';

import FloydWarshallVisualization from './graph/FloydWarshall/FloydWarshallVisualization';
import FloydWarshallController from './graph/FloydWarshall/FloydWarshallController';
import FloydWarshallAlgorithm from './graph/FloydWarshall/FloydWarshallAlgorithm';

// ===== DYNAMIC PROGRAMMING ALGORITHMS =====
// Import dynamic programming algorithm components
import LCSVisualization from './dp/LCS/LCSVisualization';
import LCSController from './dp/LCS/LCSController';
import LCSAlgorithm from './dp/LCS/LCSAlgorithm';

// ===== RECURSION & BACKTRACKING ALGORITHMS =====
// Import recursion algorithm components
import TowersOfHanoiVisualization from './recursion/TowersOfHanoi/TowersOfHanoiVisualization';
import TowersOfHanoiController from './recursion/TowersOfHanoi/TowersOfHanoiController';
import TowersOfHanoiAlgorithm from './recursion/TowersOfHanoi/TowersOfHanoiAlgorithm';

// Algorithm registry
const algorithms = {
  // ===== RECURSION & BACKTRACKING ALGORITHMS =====
  TowersOfHanoi: {
    id: 'TowersOfHanoi',
    name: 'Towers of Hanoi',
    description: 'A classic recursive algorithm for solving the Tower of Hanoi puzzle.',
    timeComplexity: 'O(2^n)',
    spaceComplexity: 'O(n)',
    visualization: TowersOfHanoiVisualization,
    controller: TowersOfHanoiController,
    algorithm: TowersOfHanoiAlgorithm,
    defaultParams: {
      numDiscs: 3,
    },
  },
  
  // ===== SORTING ALGORITHMS =====
  BubbleSort: {
    id: 'BubbleSort',
    name: 'Bubble Sort',
    description: 'A simple sorting algorithm that repeatedly steps through the list, compares adjacent elements and swaps them if they are in the wrong order.',
    timeComplexity: 'O(n²)',
    spaceComplexity: 'O(1)',
    visualization: BubbleSortVisualization,
    controller: BubbleSortController,
    algorithm: BubbleSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  QuickSort: {
    id: 'QuickSort',
    name: 'Quick Sort',
    description: 'A divide-and-conquer sorting algorithm that uses a pivot element to partition the array.',
    timeComplexity: 'O(n log n) average, O(n²) worst case',
    spaceComplexity: 'O(log n)',
    visualization: QuickSortVisualization,
    controller: QuickSortController,
    algorithm: QuickSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  MergeSort: {
    id: 'MergeSort',
    name: 'Merge Sort',
    description: 'An efficient, stable, comparison-based, divide and conquer sorting algorithm.',
    timeComplexity: 'O(n log n)',
    spaceComplexity: 'O(n)',
    visualization: MergeSortVisualization,
    controller: MergeSortController,
    algorithm: MergeSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  HeapSort: {
    id: 'HeapSort',
    name: 'Heap Sort',
    description: 'A comparison-based sorting algorithm that uses a binary heap data structure.',
    timeComplexity: 'O(n log n)',
    spaceComplexity: 'O(1)',
    visualization: HeapSortVisualization,
    controller: HeapSortController,
    algorithm: HeapSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  InsertionSort: {
    id: 'InsertionSort',
    name: 'Insertion Sort',
    description: 'A simple sorting algorithm that builds the final sorted array one item at a time by comparing each element with the already-sorted portion of the array.',
    timeComplexity: 'O(n²) worst and average case, O(n) best case',
    spaceComplexity: 'O(1)',
    visualization: InsertionSortVisualization,
    controller: InsertionSortController,
    algorithm: InsertionSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  SelectionSort: {
    id: 'SelectionSort',
    name: 'Selection Sort',
    description: 'A simple sorting algorithm that repeatedly finds the minimum element from the unsorted part of the array and puts it at the beginning.',
    timeComplexity: 'O(n²) in all cases',
    spaceComplexity: 'O(1)',
    visualization: SelectionSortVisualization,
    controller: SelectionSortController,
    algorithm: SelectionSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  RadixSort: {
    id: 'RadixSort',
    name: 'Radix Sort',
    description: 'A non-comparative sorting algorithm that sorts integers by processing individual digits, starting from the least significant digit to the most significant digit.',
    timeComplexity: 'O(n·k) where n is the number of elements and k is the number of digits',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    visualization: RadixSortVisualization,
    controller: RadixSortController,
    algorithm: RadixSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  CountingSort: {
    id: 'CountingSort',
    name: 'Counting Sort',
    description: 'A non-comparative sorting algorithm that works well when the range of input values is not significantly larger than the number of elements to be sorted.',
    timeComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the range of input',
    visualization: CountingSortVisualization,
    controller: CountingSortController,
    algorithm: CountingSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      randomize: true,
      customArray: [],
    },
  },
  BucketSort: {
    id: 'BucketSort',
    name: 'Bucket Sort',
    description: 'A distribution sort that works by distributing the elements into a number of buckets, then sorting each bucket individually, and finally concatenating the sorted buckets.',
    timeComplexity: 'O(n+k) average case, O(n²) worst case',
    spaceComplexity: 'O(n+k) where n is the number of elements and k is the number of buckets',
    visualization: BucketSortVisualization,
    controller: BucketSortController,
    algorithm: BucketSortAlgorithm,
    defaultParams: {
      arraySize: 10,
      bucketCount: 5,
      randomize: true,
      customArray: [],
    },
  },
  
  // ===== SEARCHING ALGORITHMS =====
  BFS: {
    id: 'BFS',
    name: 'Breadth First Search',
    description: 'An algorithm for traversing or searching tree or graph data structures.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: BFSVisualization,
    controller: BFSController,
    algorithm: BFSAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      targetNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  DFS: {
    id: 'DFS',
    name: 'Depth-First Search',
    description: 'An algorithm for traversing or searching tree or graph data structures using a stack.',
    timeComplexity: 'O(V + E)',
    spaceComplexity: 'O(V)',
    visualization: DFSVisualization,
    controller: DFSController,
    algorithm: DFSAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      targetNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  
  // ===== GRAPH ALGORITHMS =====
  Dijkstra: {
    id: 'Dijkstra',
    name: 'Dijkstra\'s Algorithm',
    description: 'An algorithm for finding the shortest paths between nodes in a graph.',
    timeComplexity: 'O((V + E) log V)',
    spaceComplexity: 'O(V)',
    visualization: DijkstraVisualization,
    controller: DijkstraController,
    algorithm: DijkstraAlgorithm,
    defaultParams: {
      nodes: 6,
      startNode: 0,
      endNode: 5,
      density: 0.5,
      customEdges: [],
    },
  },
  FloydWarshall: {
    id: 'FloydWarshall',
    name: 'Floyd-Warshall Algorithm',
    description: 'An algorithm for finding shortest paths in a weighted graph with positive or negative edge weights (but no negative cycles).',
    timeComplexity: 'O(V³)',
    spaceComplexity: 'O(V²)',
    visualization: FloydWarshallVisualization,
    controller: FloydWarshallController,
    algorithm: FloydWarshallAlgorithm,
    defaultParams: {
      vertices: 5,
      density: 0.7,
      minWeight: 1,
      maxWeight: 10,
      allowNegative: false,
      animationSpeed: 5,
    },
  },
  
  // ===== DYNAMIC PROGRAMMING ALGORITHMS =====
  LCS: {
    id: 'LCS',
    name: 'Longest Common Subsequence',
    description: 'A dynamic programming algorithm that finds the longest subsequence common to two sequences.',
    timeComplexity: 'O(m*n)',
    spaceComplexity: 'O(m*n)',
    visualization: LCSVisualization,
    controller: LCSController,
    algorithm: LCSAlgorithm,
    defaultParams: {
      string1: 'ABCBDAB',
      string2: 'BDCABA',
      animationSpeed: 5,
    },
  },
};

// Helper functions
export const getAlgorithmList = () => {
  return Object.values(algorithms).map(algo => ({
    id: algo.id,
    name: algo.name,
    description: algo.description,
    timeComplexity: algo.timeComplexity,
    spaceComplexity: algo.spaceComplexity,
  }));
};

export const getAlgorithm = (id) => {
  return algorithms[id] || null;
};

export const getDefaultParams = (id) => {
  return algorithms[id]?.defaultParams || {};
};

export default algorithms;
