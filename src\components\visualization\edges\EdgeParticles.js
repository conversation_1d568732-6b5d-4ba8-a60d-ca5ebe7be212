// EdgeParticles.js
// A component for creating flowing particles along an edge

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

const EdgeParticles = ({ points, thickness = 0.1, color = '#ffffff', particleCount = 8 }) => {
  // Create refs for all particles
  const particleRefs = useRef([]);
  
  // Initialize particle refs array
  useMemo(() => {
    particleRefs.current = Array(particleCount).fill().map(() => React.createRef());
  }, [particleCount]);
  
  // Create a curve from the points
  const curve = useMemo(() => {
    if (!points || points.length < 2) return null;
    return new THREE.CatmullRomCurve3(points);
  }, [points]);
  
  // Create particles
  const particles = useMemo(() => {
    if (!curve) return [];
    
    return Array(particleCount).fill().map((_, i) => {
      // Initial position along the curve
      const initialProgress = i / particleCount;
      
      return (
        <mesh 
          key={i} 
          ref={particleRefs.current[i]}
          scale={[thickness * 2, thickness * 2, thickness * 2]}
        >
          <sphereGeometry args={[1, 8, 8]} />
          <meshBasicMaterial 
            color={color} 
            transparent 
            opacity={0.8}
            emissive={color}
            emissiveIntensity={1.5}
          />
        </mesh>
      );
    });
  }, [curve, particleCount, thickness, color]);
  
  // Animate particles along the edge
  useFrame(({ clock }) => {
    if (!curve) return;
    
    particleRefs.current.forEach((ref, i) => {
      if (ref.current) {
        // Calculate progress along the curve (0 to 1)
        const initialOffset = i / particleCount;
        const time = clock.getElapsedTime() * 0.5;
        const progress = (initialOffset + time) % 1;
        
        // Get position along the curve
        const position = curve.getPointAt(progress);
        ref.current.position.copy(position);
        
        // Pulse size for additional effect
        const pulse = 1 + 0.3 * Math.sin(time * 5 + i * Math.PI / 2);
        ref.current.scale.set(
          thickness * 2 * pulse,
          thickness * 2 * pulse,
          thickness * 2 * pulse
        );
      }
    });
  });
  
  return <>{particles}</>;
};

export default EdgeParticles;
