// BridgesVisualization.js
// 3D visualization component for Bridges algorithm

import React, { useState, useEffect, useRef, useMemo, memo, useCallback } from 'react';
import { useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { useSpring, animated } from '@react-spring/three';
import GraphLabelsOverlay from '../../../components/visualization/GraphLabelsOverlay';

// Import reusable visualization components
import { FixedColorLegend, FixedStepBoard, ColorfulGraphNode, ColorfulGraphEdge } from '../../../components/visualization';

// Constants for visualization
const NODE_RADIUS = 0.8;
const NODE_SEGMENTS = 32;
const EDGE_WIDTH = 0.03;

// Memoized Edge component to prevent re-renders
const MemoizedEdge = memo(({ id, start, end, weight, color, isHighlighted, isBridge, theme }) => {
  // Use isRelaxed prop for bridges since it changes the color
  return (
    <ColorfulGraphEdge
      key={id}
      start={start}
      end={end}
      weight={weight}
      color={color}
      isHighlighted={isHighlighted}
      isRelaxed={isBridge} // Use isRelaxed for bridges
      curved={false}
      thickness={EDGE_WIDTH}
      nodeRadius={NODE_RADIUS}
      theme={theme}
      directed={false} // Bridges works on undirected graphs
    />
  );
}, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.id === nextProps.id &&
    prevProps.start.x === nextProps.start.x &&
    prevProps.start.y === nextProps.start.y &&
    prevProps.start.z === nextProps.start.z &&
    prevProps.end.x === nextProps.end.x &&
    prevProps.end.y === nextProps.end.y &&
    prevProps.end.z === nextProps.end.z &&
    prevProps.weight === nextProps.weight &&
    prevProps.color === nextProps.color &&
    prevProps.isHighlighted === nextProps.isHighlighted &&
    prevProps.isBridge === nextProps.isBridge
  );
});

// Memoized Node component to prevent re-renders
const MemoizedNode = memo(({ id, position, color, label, metadata }) => {
  return (
    <ColorfulGraphNode
      key={id}
      position={position}
      radius={NODE_RADIUS}
      color={color}
      label={label}
      showLabel={true}
      segments={NODE_SEGMENTS}
      metadata={metadata}
    />
  );
}, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.id === nextProps.id &&
    prevProps.position[0] === nextProps.position[0] &&
    prevProps.position[1] === nextProps.position[1] &&
    prevProps.position[2] === nextProps.position[2] &&
    prevProps.color === nextProps.color &&
    prevProps.label === nextProps.label &&
    prevProps.metadata === nextProps.metadata
  );
});

// Main visualization component
const BridgesVisualization = ({
  params,
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setSteps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera, gl } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // Canvas ref for HTML overlay
  const canvasRef = useRef(null);

  // State for graph data and visualization
  const [graphData, setGraphData] = useState({ nodes: [], edges: [] });
  const [nodePositions, setNodePositions] = useState({});
  const [currentStepData, setCurrentStepData] = useState(null);

  // State for algorithm-specific data
  const [visited, setVisited] = useState([]);
  const [discoveryTime, setDiscoveryTime] = useState({});
  const [lowTime, setLowTime] = useState({});
  const [parent, setParent] = useState({});
  const [bridges, setBridges] = useState([]);
  const [currentNode, setCurrentNode] = useState(null);
  const [currentEdge, setCurrentEdge] = useState(null);

  // Refs for current state
  const stepsRef = useRef([]);
  const visitedRef = useRef(new Set());
  const bridgesRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);

  // Define colors based on theme
  const colors = useMemo(() => ({
    node: isDark ? '#4fc3f7' : '#2196f3', // Blue nodes
    edge: isDark ? '#9e9e9e' : '#757575', // Gray edges
    visited: isDark ? '#69f0ae' : '#00c853', // Green for visited nodes
    currentNode: isDark ? '#ffeb3b' : '#ffc107', // Yellow for current node
    currentEdge: isDark ? '#ffeb3b' : '#ffc107', // Yellow for current edge
    bridge: isDark ? '#ff5252' : '#f44336', // Red for bridges
  }), [isDark]);

  // Set canvas ref once
  useEffect(() => {
    canvasRef.current = gl.domElement;
  }, [gl]);

  // Set up camera position based on number of nodes
  useEffect(() => {
    // Only adjust camera when we have graph data
    if (camera && graphData && graphData.nodes) {
      const numNodes = graphData.nodes.length;

      // Scale camera distance based on number of nodes, but keep it closer
      const baseDistance = 20; // Reduced from 25 to pull scene closer
      const nodeScalingFactor = 1.5; // Reduced from 1.8
      const cameraDistance = baseDistance + (numNodes > 10 ? (numNodes - 10) * nodeScalingFactor : 0);

      // Position camera directly in front for better visibility of UI elements
      camera.position.set(
        0,                  // X position (centered)
        0,                  // Y position (centered)
        cameraDistance     // Z position (straight ahead)
      );

      // Look directly at the center of the scene
      camera.lookAt(0, 0, 0);

      // Set camera's far clipping plane to ensure all elements are visible
      camera.far = 2000;
      camera.updateProjectionMatrix();

      console.log('Number of nodes:', numNodes);
      console.log('Camera distance:', cameraDistance);
      console.log('Camera position:', camera.position);
    }
  }, [camera, graphData]);

  // Initialize graph data from params
  useEffect(() => {
    if (!params) return;

    console.log('BridgesVisualization: Using steps from props:', steps);

    // Use the steps from props
    if (steps && steps.length > 0) {
      // Get the graph from the first step
      const firstStep = steps[0];
      if (firstStep && firstStep.graph) {
        // Set graph data
        setGraphData({
          nodes: firstStep.graph.nodes || [],
          edges: firstStep.graph.edges || [],
        });
      }

      // Reset algorithm state
      setVisited([]);
      setDiscoveryTime({});
      setLowTime({});
      setParent({});
      setBridges([]);
      setCurrentNode(null);
      setCurrentEdge(null);

      // Update steps ref
      stepsRef.current = steps;
      console.log('BridgesVisualization: Updated stepsRef with', steps.length, 'steps');
    } else {
      console.warn('BridgesVisualization: No steps provided in props');
    }
  }, [params, steps]);

  // Generate node positions
  useEffect(() => {
    if (!graphData.nodes || graphData.nodes.length === 0) return;

    const numNodes = graphData.nodes.length;
    const positions = {};

    // Scale radius based on number of nodes, but with an upper limit
    // to ensure the graph stays within the viewport
    const maxRadius = 12; // Maximum radius to prevent going out of viewport
    const baseRadius = Math.min(maxRadius, Math.max(6, numNodes * 0.8));

    // Calculate vertical space constraints to stay between step board and legend
    const verticalConstraint = 5; // Limit vertical expansion

    // Use a 3D distribution approach
    // For smaller graphs (< 15 nodes), use a spherical distribution
    // For larger graphs, use a multi-layer approach

    if (numNodes <= 15) {
      // Use a modified Fibonacci sphere algorithm for 3D distribution
      // This avoids placing nodes in the center while maintaining 3D distribution
      const goldenRatio = (1 + Math.sqrt(5)) / 2;
      const angleIncrement = Math.PI * 2 * goldenRatio;

      graphData.nodes.forEach((node, i) => {
        // Skip the exact center position by starting from 1 instead of 0
        const t = (i + 1) / (numNodes + 2);
        const inclination = Math.acos(1 - 2 * t);
        const azimuth = angleIncrement * i;

        // Add a small hollow sphere in the center by using a minimum radius
        const minRadius = baseRadius * 0.4; // Minimum distance from center
        const nodeRadius = minRadius + (baseRadius - minRadius) * Math.sin(inclination);

        // Convert spherical to cartesian coordinates
        const x = nodeRadius * Math.sin(inclination) * Math.cos(azimuth);
        // Constrain y-coordinate to stay within vertical limits
        const rawY = nodeRadius * Math.sin(inclination) * Math.sin(azimuth);
        const y = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawY));
        const z = nodeRadius * Math.cos(inclination);

        // Add small random offsets for more natural distribution
        // but constrain the y-offset to stay within vertical limits
        const randomOffsetXZ = baseRadius * 0.1;
        const randomOffsetY = baseRadius * 0.05; // Smaller vertical randomness

        positions[node.id] = [
          x + (Math.random() - 0.5) * randomOffsetXZ,
          Math.max(-verticalConstraint, Math.min(verticalConstraint, y + (Math.random() - 0.5) * randomOffsetY)),
          z + (Math.random() - 0.5) * randomOffsetXZ
        ];
      });
    } else {
      // For larger graphs, use a multi-layer approach for better visibility
      // Calculate number of layers based on node count
      const layerCount = Math.ceil(numNodes / 10);
      const nodesPerLayer = Math.ceil(numNodes / layerCount);

      graphData.nodes.forEach((node, i) => {
        // Determine which layer this node belongs to
        const layerIndex = Math.floor(i / nodesPerLayer);
        // Determine position within the layer
        const positionInLayer = i % nodesPerLayer;

        // Calculate angle based on position in layer
        const angle = (positionInLayer / nodesPerLayer) * Math.PI * 2;

        // Calculate radius for this layer with variation, but constrained
        const layerRadius = baseRadius * (1.0 + 0.2 * Math.sin(layerIndex * Math.PI / layerCount));

        // Calculate height based on layer with spacing proportional to available vertical space
        // Distribute layers evenly within the vertical constraint
        const maxLayerHeight = verticalConstraint * 1.8 / layerCount; // Ensure all layers fit
        const heightSpacing = Math.min(baseRadius * 1.2, maxLayerHeight);
        const rawY = (layerIndex - (layerCount - 1) / 2) * heightSpacing;

        // Constrain y value to stay within vertical limits
        const y = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawY));

        // Add a slight tilt to each layer for more 3D effect, but reduce tilt for larger graphs
        const tiltAngle = (layerIndex / layerCount) * Math.PI * 0.15;

        // Calculate x and z positions with tilt and small random offsets
        const baseX = Math.cos(angle) * layerRadius;
        const baseZ = Math.sin(angle) * layerRadius;

        // Apply tilt rotation with constrained random offsets
        const randomOffsetXZ = baseRadius * 0.1;
        const randomOffsetY = baseRadius * 0.05; // Smaller vertical randomness

        const x = baseX * Math.cos(tiltAngle) - y * Math.sin(tiltAngle) + (Math.random() - 0.5) * randomOffsetXZ;
        const rawAdjustedY = baseX * Math.sin(tiltAngle) + y * Math.cos(tiltAngle) + (Math.random() - 0.5) * randomOffsetY;
        const adjustedY = Math.max(-verticalConstraint, Math.min(verticalConstraint, rawAdjustedY));
        const z = baseZ + (Math.random() - 0.5) * randomOffsetXZ;

        positions[node.id] = [x, adjustedY, z];
      });
    }

    setNodePositions(positions);
  }, [graphData.nodes]);

  // Update state based on current step
  useEffect(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('No steps available or invalid step index');
      return;
    }

    // Store steps in ref for access in animation frame
    stepsRef.current = steps;

    // Get current step data
    const currentStep = steps[step];
    console.log('Current step data:', currentStep);
    setCurrentStepData(currentStep);

    // Update algorithm state
    if (currentStep.visited) {
      setVisited(currentStep.visited);
      visitedRef.current = new Set(currentStep.visited);
    }

    if (currentStep.discoveryTime) {
      setDiscoveryTime(currentStep.discoveryTime);
    }

    if (currentStep.lowTime) {
      setLowTime(currentStep.lowTime);
    }

    if (currentStep.parent) {
      setParent(currentStep.parent);
    }

    if (currentStep.bridges) {
      setBridges(currentStep.bridges);
      bridgesRef.current = currentStep.bridges;
    }

    // Update current node and edge
    setCurrentNode(currentStep.currentNode || null);
    setCurrentEdge(currentStep.currentEdge || null);

    // Update last applied step
    lastAppliedStepRef.current = step;

    // Update total steps
    if (setTotalSteps) {
      setTotalSteps(steps.length);
    }

    // Update movements
    if (setMovements && currentStep.message) {
      setMovements([currentStep.message]);
    }
  }, [step, steps, setTotalSteps, setMovements]);

  // Helper function to calculate delay based on speed
  const calculateDelay = useCallback((speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  }, []);

  // Auto-advance steps when in running state
  useEffect(() => {
    // Update refs
    stateRef.current = state;
    speedRef.current = speed;

    let timeoutId = null;

    if (state === 'running') {
      // If we're at the end, stop the simulation
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached end of steps, stopping simulation');
        setState('paused'); // Change state to paused instead of looping
        return;
      }

      // Calculate next step
      const nextStep = step + 1;
      console.log('Auto-advancing to step:', nextStep);

      // Calculate delay based on speed
      const delay = calculateDelay(speed);

      // Set a timer to advance to the next step
      timeoutId = setTimeout(() => {
        console.log('Timer fired, setting step to:', nextStep);
        setStep(nextStep);
      }, delay);
    } else if (state === 'idle') {
      // Reset visualization
      setVisited([]);
      setDiscoveryTime({});
      setLowTime({});
      setParent({});
      setBridges([]);
      setCurrentNode(null);
      setCurrentEdge(null);

      // Reset refs
      visitedRef.current = new Set();
      bridgesRef.current = [];

      // Reset last applied step
      lastAppliedStepRef.current = -1;

      // Apply the initial step
      if (stepsRef.current && stepsRef.current.length > 0) {
        applyStep(0);
      }
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Apply a step of the algorithm
  const applyStep = (stepIndex) => {
    if (stepIndex < 0 || stepIndex >= stepsRef.current.length) return;

    const currentStep = stepsRef.current[stepIndex];

    // Update visualization state based on step data
    const newVisited = currentStep.visited || [];
    const newBridges = currentStep.bridges || [];

    // Update state
    setVisited(newVisited);
    setDiscoveryTime(currentStep.discoveryTime || {});
    setLowTime(currentStep.lowTime || {});
    setParent(currentStep.parent || {});
    setBridges(newBridges);
    setCurrentNode(currentStep.currentNode || null);
    setCurrentEdge(currentStep.currentEdge || null);

    // Update sets for faster rendering
    visitedRef.current = new Set(newVisited);
    bridgesRef.current = newBridges;

    // Update current step data
    setCurrentStepData(currentStep);
  };

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.node, label: 'Unvisited Node' },
    { color: colors.visited, label: 'Visited Node' },
    { color: colors.currentNode, label: 'Current Node' },
    { color: colors.edge, label: 'Edge' },
    { color: colors.currentEdge, label: 'Current Edge' },
    { color: colors.bridge, label: 'Bridge' },
  ], [colors]);

  // Create a levitating animation for the graph
  const { position, rotation } = useSpring({
    from: { position: [0, 0, 0], rotation: [0, 0, 0] },
    to: async (next) => {
      // Create an infinite loop of gentle floating animations
      while (true) {
        // Float up slightly
        await next({
          position: [0, 0.2, 0],
          rotation: [0.02, 0.01, 0],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
        // Float down slightly
        await next({
          position: [0, -0.1, 0],
          rotation: [-0.01, -0.02, 0],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
        // Return to neutral with slight rotation
        await next({
          position: [0, 0, 0],
          rotation: [0, 0.01, 0.01],
          config: { mass: 10, tension: 50, friction: 30, duration: 3000 }
        });
      }
    },
    config: { mass: 10, tension: 50, friction: 30 }
  });

  // Helper function to get node color based on its state
  const getNodeColor = (nodeId) => {
    // Determine color based on state
    if (nodeId === currentNode) {
      return colors.currentNode;
    } else if (visited.includes(nodeId)) {
      return colors.visited;
    } else {
      return colors.node;
    }
  };

  // Helper function to check if an edge is a bridge
  const isBridge = (source, target) => {
    return bridges.some(bridge =>
      (bridge.source === source && bridge.target === target) ||
      (bridge.source === target && bridge.target === source)
    );
  };

  // Helper function to get edge color based on its state
  const getEdgeColor = (source, target) => {
    // Check if this is a bridge
    if (isBridge(source, target)) {
      return colors.bridge;
    }

    // Check if this is the current edge
    if (currentEdge &&
        ((currentEdge.source === source && currentEdge.target === target) ||
         (currentEdge.source === target && currentEdge.target === source))) {
      return colors.currentEdge;
    }

    return colors.edge;
  };

  // Helper function to get node metadata
  const getNodeMetadata = (nodeId) => {
    let metadata = '';

    if (discoveryTime[nodeId] !== undefined) {
      metadata += `d=${discoveryTime[nodeId]}`;
    }

    if (lowTime[nodeId] !== undefined) {
      metadata += metadata ? ', ' : '';
      metadata += `l=${lowTime[nodeId]}`;
    }

    return metadata || undefined;
  };

  // Memoized Graph component without animation
  const GraphVisualization = memo(() => {
    // Create stable references to the edges and nodes
    const edgeElements = useMemo(() => {
      return graphData.edges.map(edge => {
        const sourcePos = nodePositions[edge.source];
        const targetPos = nodePositions[edge.target];

        if (!sourcePos || !targetPos) return null;

        // Determine edge color and properties
        const edgeColor = getEdgeColor(edge.source, edge.target);
        const edgeIsBridge = isBridge(edge.source, edge.target);
        const isHighlighted = currentEdge &&
                             ((currentEdge.source === edge.source && currentEdge.target === edge.target) ||
                              (currentEdge.source === edge.target && currentEdge.target === edge.source));

        return (
          <MemoizedEdge
            key={edge.id}
            id={edge.id}
            start={new THREE.Vector3(...sourcePos)}
            end={new THREE.Vector3(...targetPos)}
            weight={edge.weight}
            color={edgeColor}
            isHighlighted={isHighlighted}
            isBridge={edgeIsBridge}
            theme={muiTheme}
          />
        );
      }).filter(Boolean);
    }, [graphData.edges, nodePositions, currentEdge, bridges]);

    // Create stable references to the nodes
    const nodeElements = useMemo(() => {
      return graphData.nodes.map(node => {
        const position = nodePositions[node.id];
        if (!position) return null;

        // Determine node color based on state
        const nodeColor = getNodeColor(node.id);

        // Get metadata for the node
        const metadata = getNodeMetadata(node.id);

        return (
          <MemoizedNode
            key={node.id}
            id={node.id}
            position={position}
            color={nodeColor}
            label={node.id.toString()}
            metadata={metadata}
          />
        );
      }).filter(Boolean);
    }, [graphData.nodes, nodePositions, visited, currentNode, discoveryTime, lowTime]);

    return (
      <group>
        {/* Edges */}
        {edgeElements}
        {/* Nodes */}
        {nodeElements}
      </group>
    );
  }, [graphData, nodePositions, visited, bridges, currentNode, currentEdge, discoveryTime, lowTime]);

  return (
    <>
      {/* 3D Scene */}
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Bridges Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Levitating graph visualization */}
        <animated.group position={position} rotation={rotation}>
          <GraphVisualization />
        </animated.group>

        {/* Add a subtle fog effect for depth perception */}
        <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
          position={[0, -12, 0]}
          rotation={[0, 0, 0]}
        />
      </group>

      {/* Graph Labels Overlay */}
      <GraphLabelsOverlay
        graphData={graphData}
        nodePositions={nodePositions}
        camera={camera}
        canvasRef={canvasRef}
        isDark={isDark}
      />
    </>
  );
};

export default BridgesVisualization;
