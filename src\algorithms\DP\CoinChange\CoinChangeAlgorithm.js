// CoinChangeAlgorithm.js
// Implementation of Coin Change algorithm using dynamic programming

/**
 * Generate steps for solving the Coin Change problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateCoinChangeSteps = (params) => {
  console.log('generateCoinChangeSteps called with params:', params);
  const { coins = [1, 2, 5], amount = 11 } = params;
  const steps = [];

  // Validate input
  if (!Array.isArray(coins) || coins.length === 0) {
    throw new Error('Coins must be a non-empty array');
  }
  if (typeof amount !== 'number' || amount < 0) {
    throw new Error('Amount must be a non-negative number');
  }

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Coin Change algorithm with coins [${coins.join(', ')}] and amount ${amount}`,
    coins,
    amount,
    dp: null,
    coinUsed: null,
    currentIndex: null,
    currentAmount: null,
    selectedCoins: null,
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table
  // dp[i] represents the minimum number of coins needed to make amount i
  const dp = Array(amount + 1).fill(Infinity);
  dp[0] = 0; // Base case: 0 coins needed to make amount 0

  // Create array to track which coin was used for each amount
  const coinUsed = Array(amount + 1).fill(-1);

  // Add DP table initialization step
  steps.push({
    type: 'initialize_table',
    message: `Initialize the DP table. dp[i] will store the minimum number of coins needed to make amount i.`,
    coins,
    amount,
    dp: [...dp],
    coinUsed: [...coinUsed],
    currentIndex: null,
    currentAmount: null,
    selectedCoins: null,
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  for (let i = 1; i <= amount; i++) {
    // Add step for current amount
    steps.push({
      type: 'consider_amount',
      message: `Consider amount ${i}`,
      coins,
      amount,
      dp: [...dp],
      coinUsed: [...coinUsed],
      currentIndex: null,
      currentAmount: i,
      selectedCoins: null,
      result: null,
      pseudocodeLine: 6
    });

    for (let j = 0; j < coins.length; j++) {
      const coin = coins[j];
      
      // Skip if coin value is greater than current amount
      if (coin > i) {
        steps.push({
          type: 'skip_coin',
          message: `Skip coin ${coin} because it's greater than the current amount ${i}`,
          coins,
          amount,
          dp: [...dp],
          coinUsed: [...coinUsed],
          currentIndex: j,
          currentAmount: i,
          currentCoin: coin,
          selectedCoins: null,
          result: null,
          pseudocodeLine: 8
        });
        continue;
      }
      
      // Add step for considering current coin
      steps.push({
        type: 'consider_coin',
        message: `Consider using coin ${coin} for amount ${i}`,
        coins,
        amount,
        dp: [...dp],
        coinUsed: [...coinUsed],
        currentIndex: j,
        currentAmount: i,
        currentCoin: coin,
        selectedCoins: null,
        result: null,
        pseudocodeLine: 9
      });

      // Calculate new minimum if we use this coin
      const remainingAmount = i - coin;
      const potentialMin = dp[remainingAmount] + 1;
      
      // Add step for calculating potential minimum
      steps.push({
        type: 'calculate_min',
        message: `Calculate potential minimum: dp[${i}-${coin}] + 1 = dp[${remainingAmount}] + 1 = ${dp[remainingAmount]} + 1 = ${potentialMin}`,
        coins,
        amount,
        dp: [...dp],
        coinUsed: [...coinUsed],
        currentIndex: j,
        currentAmount: i,
        currentCoin: coin,
        remainingAmount,
        potentialMin,
        selectedCoins: null,
        result: null,
        pseudocodeLine: 10
      });

      // Update if this gives a better solution
      if (potentialMin < dp[i]) {
        dp[i] = potentialMin;
        coinUsed[i] = j;
        
        // Add step for updating minimum
        steps.push({
          type: 'update_min',
          message: `Update minimum for amount ${i} to ${potentialMin} using coin ${coin}`,
          coins,
          amount,
          dp: [...dp],
          coinUsed: [...coinUsed],
          currentIndex: j,
          currentAmount: i,
          currentCoin: coin,
          selectedCoins: null,
          result: null,
          pseudocodeLine: 12
        });
      } else {
        // Add step for not updating minimum
        steps.push({
          type: 'no_update',
          message: `No update needed for amount ${i} as current minimum ${dp[i]} is already better than or equal to ${potentialMin}`,
          coins,
          amount,
          dp: [...dp],
          coinUsed: [...coinUsed],
          currentIndex: j,
          currentAmount: i,
          currentCoin: coin,
          selectedCoins: null,
          result: null,
          pseudocodeLine: 14
        });
      }
    }
  }

  // Trace back to find the coins used
  const selectedCoins = [];
  let remainingAmount = amount;
  
  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    message: 'Start tracing back to find the coins used',
    coins,
    amount,
    dp: [...dp],
    coinUsed: [...coinUsed],
    currentIndex: null,
    currentAmount: null,
    selectedCoins: [...selectedCoins],
    remainingAmount,
    result: dp[amount] === Infinity ? -1 : dp[amount],
    pseudocodeLine: 17
  });

  // Check if a solution exists
  if (dp[amount] === Infinity) {
    // Add step for no solution
    steps.push({
      type: 'no_solution',
      message: `No solution exists for amount ${amount} with the given coins`,
      coins,
      amount,
      dp: [...dp],
      coinUsed: [...coinUsed],
      currentIndex: null,
      currentAmount: null,
      selectedCoins: null,
      result: -1,
      pseudocodeLine: 18
    });
  } else {
    // Trace back to find the coins used
    while (remainingAmount > 0) {
      const coinIndex = coinUsed[remainingAmount];
      const coin = coins[coinIndex];
      selectedCoins.push(coin);
      
      // Add step for selecting coin
      steps.push({
        type: 'select_coin',
        message: `Select coin ${coin} for amount ${remainingAmount}`,
        coins,
        amount,
        dp: [...dp],
        coinUsed: [...coinUsed],
        currentIndex: coinIndex,
        currentAmount: remainingAmount,
        selectedCoins: [...selectedCoins],
        remainingAmount,
        result: dp[amount],
        pseudocodeLine: 21
      });
      
      remainingAmount -= coin;
      
      // Add step for updating remaining amount
      steps.push({
        type: 'update_remaining',
        message: `Update remaining amount: ${remainingAmount + coin} - ${coin} = ${remainingAmount}`,
        coins,
        amount,
        dp: [...dp],
        coinUsed: [...coinUsed],
        currentIndex: coinIndex,
        currentAmount: remainingAmount,
        selectedCoins: [...selectedCoins],
        remainingAmount,
        result: dp[amount],
        pseudocodeLine: 22
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: dp[amount] === Infinity 
      ? `Algorithm complete. No solution exists for amount ${amount} with the given coins.` 
      : `Algorithm complete. Minimum number of coins needed: ${dp[amount]}. Coins used: [${selectedCoins.join(', ')}]`,
    coins,
    amount,
    dp: [...dp],
    coinUsed: [...coinUsed],
    currentIndex: null,
    currentAmount: null,
    selectedCoins: selectedCoins.length > 0 ? [...selectedCoins] : null,
    result: dp[amount] === Infinity ? -1 : dp[amount],
    pseudocodeLine: 25
  });

  return { 
    steps, 
    result: dp[amount] === Infinity ? -1 : dp[amount],
    selectedCoins: selectedCoins.length > 0 ? selectedCoins : null
  };
};

// Create the algorithm object with helper functions
const CoinChangeAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateCoinChangeSteps
};

export default CoinChangeAlgorithm;
