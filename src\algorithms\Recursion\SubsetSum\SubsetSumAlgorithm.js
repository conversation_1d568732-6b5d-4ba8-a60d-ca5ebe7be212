// SubsetSumAlgorithm.js
// Implementation of the Subset Sum algorithm using dynamic programming

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for solving the Subset Sum problem
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateSubsetSumSteps = (params) => {
  console.log('generateSubsetSumSteps called with params:', params);
  const { numbers = [3, 34, 4, 12, 5, 2], target = 9, approach = 'dp' } = params;
  
  const steps = [];
  let solutions = [];
  
  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize Subset Sum solver with numbers [${numbers.join(', ')}] and target sum ${target}.`,
    numbers: [...numbers],
    target,
    currentIndex: -1,
    currentSum: 0,
    dp: null,
    currentSubset: [],
    solutions: [],
    progressStep: 'init',
    pseudocodeLine: 1
  });
  
  // Solve using the selected approach
  if (approach === 'dp') {
    // Dynamic Programming approach
    solveDynamicProgramming(numbers, target, steps);
    
    // Extract solutions from the DP table
    const lastStep = steps[steps.length - 1];
    if (lastStep && lastStep.dp && lastStep.dp[numbers.length][target]) {
      solutions = findAllSolutions(numbers, target);
    }
  } else {
    // Recursive approach
    const currentSubset = [];
    solveRecursive(numbers, target, 0, 0, currentSubset, steps, []);
    
    // Extract solutions from the steps
    solutions = steps
      .filter(step => step.type === 'found')
      .map(step => step.currentSubset);
  }
  
  // Add final step
  steps.push({
    type: 'complete',
    message: solutions.length > 0 
      ? `Found ${solutions.length} subset(s) that sum to ${target}.` 
      : `No subset found that sums to ${target}.`,
    numbers: [...numbers],
    target,
    currentIndex: -1,
    currentSum: 0,
    dp: steps.find(step => step.dp)?.dp || null,
    currentSubset: [],
    solutions,
    progressStep: 'complete',
    pseudocodeLine: 0
  });
  
  return { steps, solutions };
};

/**
 * Solve the Subset Sum problem using dynamic programming
 * @param {Array} numbers - Array of numbers
 * @param {Number} target - Target sum
 * @param {Array} steps - Array to store steps
 */
const solveDynamicProgramming = (numbers, target, steps) => {
  const n = numbers.length;
  
  // Create a 2D array dp[n+1][target+1]
  // dp[i][j] will be true if there is a subset of numbers[0..i-1] with sum equal to j
  const dp = Array(n + 1).fill().map(() => Array(target + 1).fill(false));
  
  // If sum is 0, then answer is true (empty subset)
  for (let i = 0; i <= n; i++) {
    dp[i][0] = true;
  }
  
  // Fill the dp table
  for (let i = 1; i <= n; i++) {
    for (let j = 1; j <= target; j++) {
      // If the current number is greater than the sum j, exclude it
      if (numbers[i - 1] > j) {
        dp[i][j] = dp[i - 1][j];
        
        steps.push({
          type: 'exclude',
          message: `Number ${numbers[i - 1]} is greater than current sum ${j}, so exclude it.`,
          numbers: [...numbers],
          target,
          currentIndex: i - 1,
          currentSum: j,
          dp: JSON.parse(JSON.stringify(dp)),
          currentSubset: [],
          solutions: [],
          progressStep: 'process',
          pseudocodeLine: 9
        });
      } else {
        // We can either include or exclude the current number
        // Include: dp[i-1][j-numbers[i-1]]
        // Exclude: dp[i-1][j]
        dp[i][j] = dp[i - 1][j - numbers[i - 1]] || dp[i - 1][j];
        
        const includeResult = dp[i - 1][j - numbers[i - 1]];
        const excludeResult = dp[i - 1][j];
        
        steps.push({
          type: includeResult ? 'include' : excludeResult ? 'exclude' : 'both',
          message: includeResult 
            ? `Including number ${numbers[i - 1]} gives a valid subset with sum ${j}.`
            : excludeResult 
              ? `Excluding number ${numbers[i - 1]} gives a valid subset with sum ${j}.`
              : `Both including and excluding number ${numbers[i - 1]} give valid subsets with sum ${j}.`,
          numbers: [...numbers],
          target,
          currentIndex: i - 1,
          currentSum: j,
          dp: JSON.parse(JSON.stringify(dp)),
          currentSubset: [],
          solutions: [],
          progressStep: 'process',
          pseudocodeLine: includeResult ? 11 : 9
        });
      }
    }
  }
  
  // Final result is stored in dp[n][target]
  const result = dp[n][target];
  
  steps.push({
    type: result ? 'found' : 'not_found',
    message: result 
      ? `Found a subset that sums to ${target}.` 
      : `No subset found that sums to ${target}.`,
    numbers: [...numbers],
    target,
    currentIndex: -1,
    currentSum: target,
    dp: JSON.parse(JSON.stringify(dp)),
    currentSubset: [],
    solutions: [],
    progressStep: 'process',
    pseudocodeLine: result ? 14 : 15
  });
  
  return result;
};

/**
 * Find all solutions from the DP table
 * @param {Array} numbers - Array of numbers
 * @param {Number} target - Target sum
 * @returns {Array} - Array of solutions
 */
const findAllSolutions = (numbers, target) => {
  const solutions = [];
  const findSolutions = (index, remainingSum, currentSubset) => {
    // Base case: if we've reached the target sum
    if (remainingSum === 0) {
      solutions.push([...currentSubset]);
      return;
    }
    
    // Base case: if we've gone through all numbers or remainingSum is negative
    if (index >= numbers.length || remainingSum < 0) {
      return;
    }
    
    // Include the current number
    currentSubset.push(numbers[index]);
    findSolutions(index + 1, remainingSum - numbers[index], currentSubset);
    
    // Exclude the current number
    currentSubset.pop();
    findSolutions(index + 1, remainingSum, currentSubset);
  };
  
  findSolutions(0, target, []);
  return solutions;
};

/**
 * Solve the Subset Sum problem using recursion with backtracking
 * @param {Array} numbers - Array of numbers
 * @param {Number} target - Target sum
 * @param {Number} index - Current index
 * @param {Number} currentSum - Current sum
 * @param {Array} currentSubset - Current subset
 * @param {Array} steps - Array to store steps
 * @param {Array} solutions - Array to store solutions
 * @returns {Boolean} - Whether a solution was found
 */
const solveRecursive = (numbers, target, index, currentSum, currentSubset, steps, solutions) => {
  // Base case: if current sum equals target
  if (currentSum === target) {
    steps.push({
      type: 'found',
      message: `Found a subset [${currentSubset.join(', ')}] that sums to ${target}.`,
      numbers: [...numbers],
      target,
      currentIndex: index - 1,
      currentSum,
      dp: null,
      currentSubset: [...currentSubset],
      solutions: [...solutions, [...currentSubset]],
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    solutions.push([...currentSubset]);
    return true;
  }
  
  // Base case: if we've gone through all numbers or currentSum > target
  if (index >= numbers.length || currentSum > target) {
    steps.push({
      type: 'backtrack',
      message: index >= numbers.length 
        ? `Reached the end of the array without finding a valid subset.` 
        : `Current sum ${currentSum} exceeds target ${target}, backtracking.`,
      numbers: [...numbers],
      target,
      currentIndex: index - 1,
      currentSum,
      dp: null,
      currentSubset: [...currentSubset],
      solutions: [...solutions],
      progressStep: 'process',
      pseudocodeLine: index >= numbers.length ? 5 : 6
    });
    
    return false;
  }
  
  // Include the current number
  currentSubset.push(numbers[index]);
  
  steps.push({
    type: 'include',
    message: `Including number ${numbers[index]} in the subset. Current sum: ${currentSum} + ${numbers[index]} = ${currentSum + numbers[index]}.`,
    numbers: [...numbers],
    target,
    currentIndex: index,
    currentSum: currentSum + numbers[index],
    dp: null,
    currentSubset: [...currentSubset],
    solutions: [...solutions],
    progressStep: 'process',
    pseudocodeLine: 8
  });
  
  // Recursively solve with the current number included
  const includeSolution = solveRecursive(
    numbers, 
    target, 
    index + 1, 
    currentSum + numbers[index], 
    currentSubset, 
    steps, 
    solutions
  );
  
  // Exclude the current number (backtrack)
  currentSubset.pop();
  
  steps.push({
    type: 'exclude',
    message: `Excluding number ${numbers[index]} from the subset. Backtracking to try other combinations.`,
    numbers: [...numbers],
    target,
    currentIndex: index,
    currentSum,
    dp: null,
    currentSubset: [...currentSubset],
    solutions: [...solutions],
    progressStep: 'process',
    pseudocodeLine: 11
  });
  
  // Recursively solve with the current number excluded
  const excludeSolution = solveRecursive(
    numbers, 
    target, 
    index + 1, 
    currentSum, 
    currentSubset, 
    steps, 
    solutions
  );
  
  // Return true if either inclusion or exclusion leads to a solution
  return includeSolution || excludeSolution;
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const SubsetSumAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Determine which pseudocode to show based on the approach
  const isDpApproach = currentAlgorithmStep?.dp !== null;

  // Pseudocode for Subset Sum algorithm (Dynamic Programming approach)
  const dpPseudocode = [
    { line: 'function subsetSumDP(numbers, target):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Create a 2D array dp[n+1][target+1]', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    // dp[i][j] will be true if there is a subset of numbers[0..i-1] with sum equal to j', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '    dp = new boolean[n+1][target+1]', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '', highlight: false },
    { line: '    // If sum is 0, then answer is true (empty subset)', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    for i = 0 to n:', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '        dp[i][0] = true', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '', highlight: false },
    { line: '    // Fill the dp table', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    for i = 1 to n:', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '        for j = 1 to target:', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '            // If current number is greater than sum j, exclude it', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            if numbers[i-1] > j:', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '                dp[i][j] = dp[i-1][j]', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '            else:', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '                // We can either include or exclude the current number', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '                dp[i][j] = dp[i-1][j-numbers[i-1]] || dp[i-1][j]', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '', highlight: false },
    { line: '    // Return the final result', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
    { line: '    return dp[n][target]', highlight: currentAlgorithmStep?.pseudocodeLine === 18 },
  ];

  // Pseudocode for Subset Sum algorithm (Recursive approach)
  const recursivePseudocode = [
    { line: 'function subsetSumRecursive(numbers, target, index, currentSum, currentSubset):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // Base case: if current sum equals target', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if currentSum == target:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        return true', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '', highlight: false },
    { line: '    // Base case: if we\'ve gone through all numbers', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    if index >= numbers.length || currentSum > target:', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '        return false', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '', highlight: false },
    { line: '    // Include the current number', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    currentSubset.push(numbers[index])', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '    includeSolution = subsetSumRecursive(numbers, target, index+1, currentSum+numbers[index], currentSubset)', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '', highlight: false },
    { line: '    // Exclude the current number (backtrack)', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '    currentSubset.pop()', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '    excludeSolution = subsetSumRecursive(numbers, target, index+1, currentSum, currentSubset)', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '', highlight: false },
    { line: '    // Return true if either inclusion or exclusion leads to a solution', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '    return includeSolution || excludeSolution', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
  ];

  // Choose the appropriate pseudocode
  const pseudocode = isDpApproach ? dpPseudocode : recursivePseudocode;

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default SubsetSumAlgorithm;
