// SelectionSortDetailedSteps.js
// Detailed step generation for Selection Sort algorithm visualization

/**
 * Generate detailed steps for Selection Sort algorithm visualization
 * @param {Array} inputArray - The array to sort
 * @returns {Array} - Array of detailed steps for visualization
 */
export const generateSelectionSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const steps = [];
  const sortedIndices = [];

  // Add initial step
  steps.push({
    type: 'initial',
    statement: `Selection Sort: Initial array [${arr.join(', ')}] - Ready to sort by selecting minimum elements`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [],
        comparingIndices: [],
        swappingIndices: [],
        currentIndex: null,
        minIndex: null,
      }
    }
  });

  // Selection Sort main loop
  for (let i = 0; i < arr.length - 1; i++) {
    // Show current position selection
    steps.push({
      type: 'select_position',
      statement: `Select position ${i} as starting point for finding minimum element`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          swappingIndices: [],
          currentIndex: i,
          minIndex: i,
        }
      }
    });

    let minIndex = i;

    // Find minimum element in remaining unsorted array
    for (let j = i + 1; j < arr.length; j++) {
      // Show comparison
      steps.push({
        type: 'compare',
        statement: `Compare ${arr[j]} at index ${j} with current minimum ${arr[minIndex]} at index ${minIndex}`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: [...sortedIndices],
            comparingIndices: [j, minIndex],
            swappingIndices: [],
            currentIndex: i,
            minIndex: minIndex,
          }
        }
      });

      if (arr[j] < arr[minIndex]) {
        // Found new minimum
        minIndex = j;
        steps.push({
          type: 'new_minimum',
          statement: `${arr[j]} < ${arr[minIndex === j ? i : minIndex]}, new minimum found at index ${j}`,
          visualizationData: {
            mainArray: {
              values: [...arr],
              sortedIndices: [...sortedIndices],
              comparingIndices: [],
              swappingIndices: [],
              currentIndex: i,
              minIndex: minIndex,
            }
          }
        });
      } else {
        // Current minimum is still smaller
        steps.push({
          type: 'keep_minimum',
          statement: `${arr[j]} ≥ ${arr[minIndex]}, keep current minimum at index ${minIndex}`,
          visualizationData: {
            mainArray: {
              values: [...arr],
              sortedIndices: [...sortedIndices],
              comparingIndices: [],
              swappingIndices: [],
              currentIndex: i,
              minIndex: minIndex,
            }
          }
        });
      }
    }

    // Swap if necessary
    if (minIndex !== i) {
      // Mark position as sorted before showing swap preparation
      const updatedSortedIndices = [...sortedIndices, i];

      // Show swap preparation with updated sorted indices
      steps.push({
        type: 'prepare_swap',
        statement: `Minimum element ${arr[minIndex]} found at index ${minIndex}, prepare to swap with ${arr[i]} at index ${i}`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: updatedSortedIndices, // Include current position as sorted
            comparingIndices: [],
            swappingIndices: [i, minIndex],
            currentIndex: i,
            minIndex: minIndex,
          }
        }
      });

      // Perform swap
      const originalValueAtI = arr[i];
      const originalValueAtMin = arr[minIndex];
      const temp = arr[i];
      arr[i] = arr[minIndex];
      arr[minIndex] = temp;

      // Update the main sortedIndices array
      sortedIndices.push(i);

      // Show swap result with updated sorted indices
      steps.push({
        type: 'swapped',
        statement: `Swapped: ${originalValueAtI} ↔ ${originalValueAtMin}. Position ${i} now contains the minimum element`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: [...sortedIndices], // Now includes the current position
            comparingIndices: [],
            swappingIndices: [],
            currentIndex: i,
            minIndex: null,
          }
        }
      });
    } else {
      // No swap needed - mark position as sorted immediately
      sortedIndices.push(i);

      steps.push({
        type: 'no_swap',
        statement: `Minimum element ${arr[i]} is already at position ${i}, no swap needed`,
        visualizationData: {
          mainArray: {
            values: [...arr],
            sortedIndices: [...sortedIndices], // Now includes the current position
            comparingIndices: [],
            swappingIndices: [],
            currentIndex: i,
            minIndex: null,
          }
        }
      });
    }

    // Position is already marked as sorted in the swap/no_swap step above
    // Add a progress step to show the current state
    steps.push({
      type: 'position_sorted',
      statement: `Position ${i} is now sorted with value ${arr[i]}. Sorted portion: [${sortedIndices.map(idx => arr[idx]).join(', ')}]`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          swappingIndices: [],
          currentIndex: null,
          minIndex: null,
        }
      }
    });

    // Show progress
    const sortedPortion = arr.slice(0, i + 1);
    const unsortedPortion = arr.slice(i + 1);
    steps.push({
      type: 'progress',
      statement: `Progress: Sorted [${sortedPortion.join(', ')}], Remaining [${unsortedPortion.join(', ')}]`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          sortedIndices: [...sortedIndices],
          comparingIndices: [],
          swappingIndices: [],
          currentIndex: null,
          minIndex: null,
        }
      }
    });
  }

  // Mark last element as sorted (it's automatically in correct position)
  sortedIndices.push(arr.length - 1);
  steps.push({
    type: 'complete',
    statement: `Selection Sort completed! Final sorted array: [${arr.join(', ')}]`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        sortedIndices: [...sortedIndices],
        comparingIndices: [],
        swappingIndices: [],
        currentIndex: null,
        minIndex: null,
      }
    }
  });

  return steps;
};

// Helper function to get step description for display
export const getStepDescription = (step) => {
  if (!step) return '';
  return step.statement || '';
};

// Helper function to get step type for styling
export const getStepType = (step) => {
  if (!step) return 'default';
  return step.type || 'default';
};

// Export default
export default {
  generateSelectionSortDetailedSteps,
  getStepDescription,
  getStepType,
};
