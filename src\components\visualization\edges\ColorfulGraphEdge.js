// ColorfulGraphEdge.js
// A simple, thin edge component to match the colorful nodes

import React, { useMemo } from 'react';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

const ColorfulGraphEdge = ({
  // Basic edge properties
  start,
  end,
  weight,
  color = '#ffffff', // Default to white
  isHighlighted = false,
  isRelaxed = false,
  isNegativeCycle = false,

  // Edge geometry properties
  curved = false,
  curveHeight = 1.5,
  thickness = 0.05, // Thin edge
  nodeRadius = 0.5,
  tubeSegments = 64, // Segments for smoother curve
  radialSegments = 8, // Radial segments for tube geometry

  // Edge material properties
  emissiveIntensity = 0.3,
  metalness = 0.3,
  roughness = 0.7,

  // Edge state colors
  highlightedColor = '#ffff00', // Bright yellow
  relaxedColor = '#69f0ae', // Bright green
  negativeWeightColor = '#ff5252', // Bright red

  // No weight label properties needed

  // Theme
  theme,

  // Interaction
  onClick
}) => {
  // Get theme from context if not provided as prop
  const themeFromContext = useTheme();
  const muiTheme = theme || themeFromContext;
  const isDark = muiTheme?.palette?.mode === 'dark';

  // Theme-aware colors are now handled directly in the Text component

  // No need for state management - labels are handled by the label manager

  // Determine edge color
  const edgeColor = useMemo(() => {
    if (isRelaxed) return relaxedColor;
    if (isNegativeCycle || weight < 0) return negativeWeightColor;
    if (isHighlighted) return highlightedColor;
    return color; // Use the provided color
  }, [isRelaxed, isNegativeCycle, isHighlighted, weight, relaxedColor, negativeWeightColor, highlightedColor, color]);

  // Calculate points for the edge
  const points = useMemo(() => {
    // Convert start and end to Vector3 if they're not already
    const startVec = start instanceof THREE.Vector3 ? start : new THREE.Vector3(...start);
    const endVec = end instanceof THREE.Vector3 ? end : new THREE.Vector3(...end);

    // Calculate direction vector
    const direction = new THREE.Vector3().subVectors(endVec, startVec).normalize();

    // Adjust start and end points to account for node radius
    const adjustedStart = new THREE.Vector3().addVectors(
      startVec,
      new THREE.Vector3().copy(direction).multiplyScalar(nodeRadius * 0.9)
    );

    const adjustedEnd = new THREE.Vector3().addVectors(
      endVec,
      new THREE.Vector3().copy(direction).multiplyScalar(-nodeRadius * 0.9)
    );

    // If curved, create a curved path
    if (curved) {
      // Calculate midpoint
      const midPoint = new THREE.Vector3().addVectors(adjustedStart, adjustedEnd).multiplyScalar(0.5);

      // Calculate perpendicular vector for curve control point
      const perpendicular = new THREE.Vector3(
        -direction.z,
        0,
        direction.x
      ).normalize().multiplyScalar(curveHeight);

      // Add perpendicular offset to midpoint
      const controlPoint = new THREE.Vector3().addVectors(midPoint, perpendicular);

      // Create a quadratic curve
      const curve = new THREE.QuadraticBezierCurve3(
        adjustedStart,
        controlPoint,
        adjustedEnd
      );

      // Sample points along the curve
      return curve.getPoints(20);
    } else {
      // Straight line
      return [adjustedStart, adjustedEnd];
    }
  }, [start, end, curved, curveHeight, nodeRadius]);

  // No label position calculation needed

  return (
    <group onClick={onClick}>
      {/* Simple thin edge */}
      <mesh>
        <tubeGeometry args={[
          new THREE.CatmullRomCurve3(points),
          tubeSegments, // Configurable segments for smoother curve
          thickness, // Configurable tube thickness
          radialSegments, // Configurable radial segments
          false
        ]} />
        <meshStandardMaterial
          color={edgeColor}
          emissive={edgeColor}
          emissiveIntensity={emissiveIntensity}
          metalness={metalness}
          roughness={roughness}
        />
      </mesh>

      {/* No weight label - removing to eliminate flickering */}
    </group>
  );
};

export default ColorfulGraphEdge;
