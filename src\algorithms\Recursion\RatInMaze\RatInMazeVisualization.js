// RatInMazeVisualization.js
// 3D visualization component for the Rat in a Maze algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const CELL_SIZE = 1.0;
const WALL_HEIGHT = 0.8;
const FLOOR_HEIGHT = 0.1;
const RAT_SIZE = 0.3;
const RAT_HEIGHT = 0.3;

const RatInMazeVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 15, 15);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();
  
  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);
      
      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty maze to show when no steps are available
      setCurrentStepData({
        maze: Array(4).fill().map(() => Array(4).fill(1)),
        solution: Array(4).fill().map(() => Array(4).fill(0)),
        currentRow: 0,
        currentCol: 0,
        isValid: true
      });
    }
    
    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;
      
      // Get current time
      const currentTime = clock.getElapsedTime();
      
      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);
        
        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Maze colors
    wall: isDark ? '#2d3436' : '#636e72',
    floor: isDark ? '#1e272e' : '#dfe6e9',
    path: isDark ? '#00b894' : '#55efc4',
    visited: isDark ? '#0984e3' : '#74b9ff',
    current: isDark ? '#e84118' : '#ff7675',
    start: isDark ? '#00b894' : '#55efc4',
    end: isDark ? '#e84118' : '#ff7675',
    
    // Rat colors
    rat: isDark ? '#fdcb6e' : '#ffeaa7',
    ratEyes: isDark ? '#2d3436' : '#2d3436',
    ratEars: isDark ? '#e17055' : '#e17055',
    
    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.wall, label: 'Wall' },
    { color: colors.floor, label: 'Floor' },
    { color: colors.path, label: 'Path' },
    { color: colors.visited, label: 'Visited' },
    { color: colors.current, label: 'Current Position' },
    { color: colors.start, label: 'Start' },
    { color: colors.end, label: 'End' },
  ], [colors]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, 0, 0];

  // Create a rat mesh
  const Rat = ({ position }) => {
    return (
      <group position={position}>
        {/* Rat body */}
        <mesh position={[0, RAT_HEIGHT / 2, 0]} castShadow>
          <sphereGeometry args={[RAT_SIZE, 16, 16]} />
          <meshStandardMaterial color={colors.rat} />
        </mesh>
        
        {/* Rat ears */}
        <mesh position={[-RAT_SIZE/2, RAT_HEIGHT/2 + RAT_SIZE/2, -RAT_SIZE/2]} castShadow>
          <sphereGeometry args={[RAT_SIZE/4, 8, 8]} />
          <meshStandardMaterial color={colors.ratEars} />
        </mesh>
        <mesh position={[RAT_SIZE/2, RAT_HEIGHT/2 + RAT_SIZE/2, -RAT_SIZE/2]} castShadow>
          <sphereGeometry args={[RAT_SIZE/4, 8, 8]} />
          <meshStandardMaterial color={colors.ratEars} />
        </mesh>
        
        {/* Rat eyes */}
        <mesh position={[-RAT_SIZE/3, RAT_HEIGHT/2 + RAT_SIZE/4, -RAT_SIZE/1.5]} castShadow>
          <sphereGeometry args={[RAT_SIZE/8, 8, 8]} />
          <meshStandardMaterial color={colors.ratEyes} />
        </mesh>
        <mesh position={[RAT_SIZE/3, RAT_HEIGHT/2 + RAT_SIZE/4, -RAT_SIZE/1.5]} castShadow>
          <sphereGeometry args={[RAT_SIZE/8, 8, 8]} />
          <meshStandardMaterial color={colors.ratEyes} />
        </mesh>
        
        {/* Rat nose */}
        <mesh position={[0, RAT_HEIGHT/2, -RAT_SIZE/1.2]} castShadow>
          <sphereGeometry args={[RAT_SIZE/10, 8, 8]} />
          <meshStandardMaterial color={colors.ratEyes} />
        </mesh>
        
        {/* Rat tail */}
        <mesh position={[0, RAT_HEIGHT/4, RAT_SIZE/1.5]} castShadow rotation={[Math.PI/4, 0, 0]}>
          <cylinderGeometry args={[RAT_SIZE/10, RAT_SIZE/20, RAT_SIZE, 8]} />
          <meshStandardMaterial color={colors.rat} />
        </mesh>
      </group>
    );
  };

  // Render the Rat in a Maze visualization
  const renderMazeVisualization = () => {
    if (!currentStepData) return null;

    const { maze, solution, currentRow, currentCol } = currentStepData;
    
    // Calculate maze size
    const mazeSize = maze.length;
    
    // Calculate offset to center the maze
    const offset = -(mazeSize * CELL_SIZE) / 2 + CELL_SIZE / 2;

    return (
      <group position={position} rotation={rotation}>
        {/* Maze floor */}
        <mesh
          position={[0, -FLOOR_HEIGHT/2, 0]}
          receiveShadow
        >
          <boxGeometry args={[mazeSize * CELL_SIZE + 1, FLOOR_HEIGHT, mazeSize * CELL_SIZE + 1]} />
          <meshStandardMaterial color={colors.floor} />
        </mesh>

        {/* Maze cells */}
        {maze.map((row, rowIndex) => (
          row.map((cell, colIndex) => {
            const x = offset + colIndex * CELL_SIZE;
            const z = offset + rowIndex * CELL_SIZE;
            
            // Determine if this is a special cell
            const isStart = rowIndex === 0 && colIndex === 0;
            const isEnd = rowIndex === mazeSize - 1 && colIndex === mazeSize - 1;
            const isCurrent = rowIndex === currentRow && colIndex === currentCol;
            const isPath = solution[rowIndex][colIndex] === 1;
            
            // Skip walls (they'll be rendered separately)
            if (cell === 0) {
              return (
                <mesh
                  key={`wall-${rowIndex}-${colIndex}`}
                  position={[x, WALL_HEIGHT/2, z]}
                  castShadow
                  receiveShadow
                >
                  <boxGeometry args={[CELL_SIZE - 0.05, WALL_HEIGHT, CELL_SIZE - 0.05]} />
                  <meshStandardMaterial color={colors.wall} />
                </mesh>
              );
            }
            
            // Determine cell color based on its state
            let cellColor;
            if (isStart) {
              cellColor = colors.start;
            } else if (isEnd) {
              cellColor = colors.end;
            } else if (isCurrent) {
              cellColor = colors.current;
            } else if (isPath) {
              cellColor = colors.path;
            } else {
              cellColor = colors.floor;
            }
            
            return (
              <group key={`cell-${rowIndex}-${colIndex}`}>
                {/* Cell floor with color based on state */}
                <mesh
                  position={[x, 0.01, z]}
                  receiveShadow
                >
                  <boxGeometry args={[CELL_SIZE - 0.05, 0.02, CELL_SIZE - 0.05]} />
                  <meshStandardMaterial color={cellColor} />
                </mesh>
                
                {/* Cell label */}
                <Html
                  position={[x, 0.1, z]}
                  center
                  occlude
                >
                  <div style={{
                    color: isDark ? 'white' : 'black',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    width: '30px',
                    height: '30px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}>
                    {isStart ? 'S' : isEnd ? 'E' : ''}
                  </div>
                </Html>
                
                {/* Rat at current position */}
                {isCurrent && (
                  <Rat position={[x, 0, z]} />
                )}
              </group>
            );
          })
        ))}
      </group>
    );
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Rat in a Maze Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position} rotation={rotation}>
          {renderMazeVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default RatInMazeVisualization;
