// PrimsAlgorithm.js
// Implementation of <PERSON><PERSON>'s algorithm with step generation

// No imports needed

/**
 * Generate a random weighted graph
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @param {number} minWeight - Minimum edge weight
 * @param {number} maxWeight - Maximum edge weight
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateRandomGraph = (numNodes, density = 0.7, minWeight = 1, maxWeight = 10) => {
  console.log(`Generating random graph with ${numNodes} nodes, density ${density}, weights ${minWeight}-${maxWeight}`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // First, create a minimum spanning tree to ensure the graph is connected
  const edges = [];
  let edgeId = 0;

  // Create a set of nodes that are already in the MST
  const inMST = new Set([0]);

  // Add n-1 edges to connect all nodes
  while (inMST.size < numNodes) {
    // Pick a random node from the MST
    const fromNode = Array.from(inMST)[Math.floor(Math.random() * inMST.size)];

    // Pick a random node not in the MST
    const notInMST = Array.from(Array(numNodes).keys()).filter(n => !inMST.has(n));
    const toNode = notInMST[Math.floor(Math.random() * notInMST.length)];

    // Add an edge between them
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source: fromNode,
      target: toNode,
      weight
    });

    // Add the new node to the MST
    inMST.add(toNode);
  }

  console.log(`Created minimum spanning tree with ${edges.length} edges`);

  // Now add additional edges based on density
  // Calculate how many additional edges to add (0 to maxAdditional)
  const maxPossibleEdges = (numNodes * (numNodes - 1)) / 2;
  const maxAdditional = Math.min(10 - edges.length, maxPossibleEdges - edges.length);
  const additionalEdges = Math.floor(maxAdditional * density);

  console.log(`Adding ${additionalEdges} additional edges based on density ${density}`);

  // Create a set of existing edges
  const existingEdges = new Set();
  edges.forEach(edge => {
    const minNode = Math.min(edge.source, edge.target);
    const maxNode = Math.max(edge.source, edge.target);
    existingEdges.add(`${minNode}-${maxNode}`);
  });

  // Create a list of possible additional edges
  const possibleAdditionalEdges = [];
  for (let i = 0; i < numNodes; i++) {
    for (let j = i + 1; j < numNodes; j++) {
      const edgeKey = `${i}-${j}`;
      if (!existingEdges.has(edgeKey)) {
        possibleAdditionalEdges.push([i, j]);
      }
    }
  }

  // Shuffle the possible additional edges
  for (let i = possibleAdditionalEdges.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [possibleAdditionalEdges[i], possibleAdditionalEdges[j]] = [possibleAdditionalEdges[j], possibleAdditionalEdges[i]];
  }

  // Add the additional edges
  for (let i = 0; i < additionalEdges && i < possibleAdditionalEdges.length; i++) {
    const [source, target] = possibleAdditionalEdges[i];
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source,
      target,
      weight
    });
  }

  console.log(`Final graph has ${edges.length} edges (${numNodes - 1} for MST + ${edges.length - (numNodes - 1)} additional)`);

  return { nodes, edges };
};

/**
 * Generate a custom graph from an edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} edgeList - Array of edges [from, to, weight]
 * @returns {Object} - Graph representation with nodes and edges
 */
export const generateCustomGraph = (numNodes, edgeList) => {
  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges from the edge list
  const edges = [];

  edgeList.forEach(([source, target, weight], index) => {
    if (source >= 0 && source < numNodes && target >= 0 && target < numNodes) {
      edges.push({
        id: index,
        source,
        target,
        weight
      });
    }
  });

  return { nodes, edges };
};

/**
 * Create an adjacency list from a graph
 * @param {Object} graph - Graph with nodes and edges
 * @returns {Array} - Adjacency list
 */
const createAdjacencyList = (graph) => {
  const { nodes, edges } = graph;
  const adjList = Array(nodes.length).fill().map(() => []);

  edges.forEach(edge => {
    const { source, target, weight, id } = edge;
    // For undirected graph, add edges in both directions
    adjList[source].push({ node: target, weight, edgeId: id });
    adjList[target].push({ node: source, weight, edgeId: id });
  });

  return adjList;
};

/**
 * Generate steps for Prim's algorithm
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generatePrimsSteps = (params) => {
  console.log('generatePrimsSteps called with params:', params);
  const { nodes: numNodes, density, minWeight, maxWeight, startNode = 0, customEdges } = params;
  const steps = [];

  // Generate graph
  let graph;
  if (customEdges && customEdges.length > 0) {
    console.log('Generating custom graph with edges:', customEdges);
    graph = generateCustomGraph(numNodes, customEdges);
  } else {
    console.log('Generating random graph with params:', { numNodes, density, minWeight, maxWeight });
    graph = generateRandomGraph(numNodes, density, minWeight, maxWeight);
  }
  console.log('Generated graph:', graph);

  const { nodes, edges } = graph;

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Prim's algorithm. Start from node ${startNode}.`,
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    visited: [startNode],
    frontier: [],
    currentEdge: null,
    rejected: false,
    progressStep: 'initialize',
    pseudocodeLine: 1 // Line number in pseudocode
  });

  // Create adjacency list
  const adjList = createAdjacencyList(graph);

  // Add step for creating adjacency list
  steps.push({
    type: 'create_adj_list',
    message: 'Create adjacency list from the graph.',
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    visited: [startNode],
    frontier: [],
    currentEdge: null,
    rejected: false,
    progressStep: 'initialize',
    pseudocodeLine: 5 // Create adjacency list
  });

  // Initialize visited set and MST
  const visited = new Set([startNode]);
  const mst = [];

  // Add step for initializing visited set
  steps.push({
    type: 'init_visited',
    message: `Add start node ${startNode} to visited set.`,
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    visited: Array.from(visited),
    frontier: [],
    currentEdge: null,
    rejected: false,
    progressStep: 'process',
    pseudocodeLine: 8 // Add start vertex to visited set
  });

  // Initialize frontier with edges from start node
  let frontier = [];
  adjList[startNode].forEach(({ node, weight, edgeId }) => {
    if (!visited.has(node)) {
      const edge = edges.find(e => e.id === edgeId);
      frontier.push(edge);
    }
  });

  // Add step for initializing frontier
  steps.push({
    type: 'init_frontier',
    message: `Add all edges from node ${startNode} to frontier.`,
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [],
    visited: Array.from(visited),
    frontier: [...frontier],
    currentEdge: null,
    rejected: false,
    progressStep: 'process',
    pseudocodeLine: 13 // Add all edges from start to frontier
  });

  // Process edges until MST is complete or frontier is empty
  while (frontier.length > 0 && mst.length < nodes.length - 1) {
    // Sort frontier by weight
    frontier.sort((a, b) => a.weight - b.weight);

    // Get minimum weight edge
    const minEdge = frontier.shift();
    const { source, target, weight } = minEdge;

    // Determine which node is in the visited set
    const fromNode = visited.has(source) ? source : target;
    const toNode = fromNode === source ? target : source;

    // Add step for considering an edge
    steps.push({
      type: 'consider',
      message: `Consider edge ${fromNode} → ${toNode} with weight ${weight}.`,
      graph: { nodes: [...nodes], edges: [...edges] },
      mst: [...mst],
      visited: Array.from(visited),
      frontier: [...frontier],
      currentEdge: minEdge,
      rejected: false,
      progressStep: 'process',
      pseudocodeLine: 18 // Get minimum weight edge from frontier
    });

    // Check if the target node is already visited
    if (visited.has(toNode)) {
      // Both nodes are already in the MST, skip this edge
      steps.push({
        type: 'reject',
        message: `Reject edge ${fromNode} → ${toNode}. Both nodes are already in the MST.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        visited: Array.from(visited),
        frontier: [...frontier],
        currentEdge: minEdge,
        rejected: true,
        progressStep: 'process',
        pseudocodeLine: 22 // Check if edge connects to unvisited node
      });
    } else {
      // Add edge to MST
      mst.push(minEdge);
      visited.add(toNode);

      // Add step for adding edge to MST
      steps.push({
        type: 'add_to_mst',
        message: `Add edge ${fromNode} → ${toNode} to MST. Add node ${toNode} to visited set.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        visited: Array.from(visited),
        frontier: [...frontier],
        currentEdge: minEdge,
        rejected: false,
        progressStep: 'process',
        pseudocodeLine: 24 // Add edge to MST
      });

      // Add edges from the new node to the frontier
      adjList[toNode].forEach(({ node, weight, edgeId }) => {
        if (!visited.has(node)) {
          const edge = edges.find(e => e.id === edgeId);
          frontier.push(edge);
        }
      });

      // Add step for updating frontier
      steps.push({
        type: 'update_frontier',
        message: `Add edges from node ${toNode} to frontier.`,
        graph: { nodes: [...nodes], edges: [...edges] },
        mst: [...mst],
        visited: Array.from(visited),
        frontier: [...frontier],
        currentEdge: null,
        rejected: false,
        progressStep: 'process',
        pseudocodeLine: 31 // Add edges from new node to frontier
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Prim's algorithm complete. MST has ${mst.length} edges with total weight ${mst.reduce((sum, edge) => sum + edge.weight, 0)}.`,
    graph: { nodes: [...nodes], edges: [...edges] },
    mst: [...mst],
    visited: Array.from(visited),
    frontier: [],
    currentEdge: null,
    rejected: false,
    progressStep: 'complete',
    pseudocodeLine: 33 // Return MST
  });

  console.log('Generated steps:', steps.length, 'steps');
  const result = {
    steps,
    graph,
    mst
  };
  console.log('Returning result:', result);
  return result;
};

// End of file

// Export the algorithm functions
export default {
  generatePrimsSteps,
  generateRandomGraph,
  generateCustomGraph
};
