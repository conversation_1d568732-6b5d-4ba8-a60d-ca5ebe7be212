# Bucket Sort Algorithm

## Overview

Bucket Sort is a distribution sorting algorithm that works by distributing the elements of an array into a number of buckets. Each bucket is then sorted individually, either using a different sorting algorithm or by recursively applying the bucket sort algorithm. Finally, the elements are gathered from each bucket in order, resulting in a sorted array.

## How Bucket Sort Works

1. **Create Buckets**: Create n empty buckets (or arrays).
2. **Distribute**: Put array elements into different buckets based on their values.
3. **Sort Buckets**: Sort individual buckets using another sorting algorithm (typically insertion sort).
4. **Concatenate**: Gather elements from each bucket in order.

## Algorithm Steps

```
function bucketSort(arr, bucketCount):
    min = findMinimum(arr), max = findMaximum(arr)
    buckets = new Array(bucketCount).fill([])
    range = (max - min) / bucketCount + 1
    
    // Distribute elements into buckets
    for i = 0 to length(arr) - 1:
        bucketIndex = Math.floor((arr[i] - min) / range)
        buckets[bucketIndex].push(arr[i])
    
    // Sort each bucket
    for i = 0 to bucketCount - 1:
        sort(buckets[i]) // Using insertion sort
    
    // Concatenate buckets
    result = concatenate all buckets
    return result
```

## Time and Space Complexity

- **Time Complexity**:
  - Best Case: O(n+k) where n is the number of elements and k is the number of buckets
  - Average Case: O(n+k)
  - Worst Case: O(n²) when all elements are placed in a single bucket

- **Space Complexity**: O(n+k) where n is the number of elements and k is the number of buckets

## Visualization Guide

Our Bucket Sort visualization provides a step-by-step view of how the algorithm works:

### Visual Elements

- **Bars**: Represent the elements of the array to be sorted.
- **Buckets**: Containers that hold elements during the sorting process.
- **Colors**:
  - **Default**: Regular array elements
  - **Current**: Element currently being processed
  - **Bucket**: Buckets and elements being placed in buckets
  - **Sorting**: Elements being sorted within a bucket
  - **Sorted**: Elements that are in their final sorted position

### Step-by-Step Visualization

1. **Initialization**: The original unsorted array is displayed.
2. **Finding Min/Max**: The algorithm identifies the minimum and maximum values in the array.
3. **Creating Buckets**: Empty buckets are created based on the specified bucket count.
4. **Calculating Range**: The range for each bucket is calculated.
5. **Distribution**: Elements are distributed into buckets based on their values.
6. **Sorting Buckets**: Each bucket is sorted individually using insertion sort.
7. **Concatenation**: Elements from all buckets are concatenated to form the final sorted array.

### Controls

- **Array Size**: Adjust the number of elements in the array (3-20).
- **Number of Buckets**: Adjust the number of buckets used (2-10).
- **Randomize Array**: Generate a new random array.
- **Custom Array**: Enter your own array of numbers to sort.
- **Speed**: Adjust the animation speed.
- **Play/Pause/Reset**: Control the animation.
- **Step Forward/Backward**: Move through the algorithm one step at a time.

## When to Use Bucket Sort

Bucket Sort is most effective when:

1. The input is uniformly distributed over a range.
2. There are finite distinct keys.
3. The range of input values is not significantly larger than the number of elements.

## Advantages and Disadvantages

### Advantages

- Very fast when input is uniformly distributed.
- Can be faster than O(n log n) sorting algorithms like quicksort for certain distributions.
- Parallelizable: different buckets can be sorted in parallel.

### Disadvantages

- Not in-place: requires additional memory.
- Performance depends heavily on the distribution of the input.
- Can degrade to O(n²) in worst case.

## Implementation Notes

Our implementation uses:

- A dynamic number of buckets based on user input.
- Insertion sort for sorting individual buckets.
- A half-circle arrangement for better visualization of buckets.
- Dynamic sizing of buckets based on the number of elements they contain.

## Example

For the array [29, 25, 3, 49, 9, 37, 21, 43] with 5 buckets:

1. Find min (3) and max (49)
2. Calculate range: (49-3)/5+1 = 10.4
3. Distribute elements:
   - Bucket 0 (3-13.4): [3, 9]
   - Bucket 1 (13.4-23.8): [21]
   - Bucket 2 (23.8-34.2): [29, 25]
   - Bucket 3 (34.2-44.6): [37, 43]
   - Bucket 4 (44.6-55): [49]
4. Sort each bucket:
   - Bucket 0: [3, 9]
   - Bucket 1: [21]
   - Bucket 2: [25, 29]
   - Bucket 3: [37, 43]
   - Bucket 4: [49]
5. Concatenate: [3, 9, 21, 25, 29, 37, 43, 49]

## Conclusion

Bucket Sort is a versatile and efficient sorting algorithm when used with the right data distribution. Our visualization helps you understand how it works step by step, making it easier to grasp the concepts behind this distribution sort algorithm.
