import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

/**
 * A 3D step board component that displays the current step description using canvas textures
 * 
 * @param {Object} props - Component props
 * @param {Array} props.position - Position of the board [x, y, z]
 * @param {string} props.description - Description text to display
 * @param {Object} props.stepData - Additional data for the current step
 * @param {number} props.currentStep - Current step number
 * @param {number} props.totalSteps - Total number of steps
 * @param {boolean} props.showBackground - Whether to show the board background
 * @param {number} props.width - Width of the board
 * @param {number} props.height - Height of the board
 * @param {boolean} props.animate - Whether to animate the board
 * @returns {JSX.Element} - The rendered step board component
 */
const StepBoardCanvas = ({
    position = [0, 0, 0],
    description = '',
    stepData = {},
    currentStep = 0,
    totalSteps = 0,
    showBackground = false, // Hide background by default
    width = 15,
    height = 3,
    animate = true
}) => {
    const theme = useTheme();
    const boardRef = useRef();
    const textRef = useRef();

    // Use theme-aware colors
    const isDark = theme.palette.mode === 'dark';
    // Choose colors based on theme
    const backgroundColor = isDark ? '#2a2a2a' : '#f5f5f5'; // Dark gray in dark mode, light gray in light mode
    const boardEdgeColor = isDark ? '#3a3a3a' : '#e0e0e0'; // Slightly lighter in dark mode, slightly darker in light mode
    const textColor = isDark ? '#ffffff' : '#000000';

    // Enhance the description with only the current step number (not total steps)
    // Add 1 to currentStep for display since steps are 0-indexed internally but 1-indexed for display
    const displayStep = currentStep + 1;
    const enhancedDescription = description ? `Step ${displayStep}: ${description}` : `Step ${displayStep}`;

    // Animation
    useFrame(() => {
        if (animate && boardRef.current) {
            // Subtle floating animation
            boardRef.current.position.y = position[1] + Math.sin(Date.now() * 0.001) * 0.05;
        }
    });

    return (
        <group ref={boardRef} position={position}>
            {/* Background board */}
            {showBackground && (
                <mesh receiveShadow castShadow>
                    <boxGeometry args={[width, height, 0.1]} />
                    <meshStandardMaterial
                        color={backgroundColor}
                        roughness={isDark ? 0.7 : 0.3} // More rough in dark mode, smoother in light mode
                        metalness={isDark ? 0.0 : 0.1} // No metalness in dark mode, slight metalness in light mode
                        emissive={backgroundColor}
                        emissiveIntensity={isDark ? 0.05 : 0} // Slight glow in dark mode
                    />

                    {/* Board edges */}
                    <lineSegments>
                        <edgesGeometry attach="geometry" args={[new THREE.BoxGeometry(width, height, 0.1)]} />
                        <lineBasicMaterial
                            attach="material"
                            color={boardEdgeColor}
                            linewidth={isDark ? 1.5 : 1} // Slightly thicker lines in dark mode for better visibility
                        />
                    </lineSegments>
                </mesh>
            )}

            {/* Text using canvas texture */}
            <mesh position={[0, 0, 0.05]}>
                <planeGeometry args={[width * 0.9, height * 0.8]} />
                <meshStandardMaterial
                    color={textColor}
                    roughness={0.7}
                    metalness={0.1}
                    transparent
                    opacity={0.9}
                >
                    <canvasTexture
                        attach="map"
                        image={(() => {
                            // Create canvas for text
                            const canvas = document.createElement('canvas');
                            canvas.width = 1024;
                            canvas.height = 256;
                            const context = canvas.getContext('2d');
                            
                            // Clear canvas
                            context.clearRect(0, 0, canvas.width, canvas.height);
                            
                            // Set text properties
                            context.fillStyle = textColor;
                            context.font = '32px "Source Code Pro", "Roboto Mono", monospace';
                            context.textAlign = 'center';
                            context.textBaseline = 'middle';
                            
                            // Split description into multiple lines
                            const words = enhancedDescription.split(' ');
                            let line = '';
                            let lines = [];
                            const maxWidth = canvas.width - 40;
                            
                            for (let i = 0; i < words.length; i++) {
                                const testLine = line + words[i] + ' ';
                                const metrics = context.measureText(testLine);
                                if (metrics.width > maxWidth && i > 0) {
                                    lines.push(line);
                                    line = words[i] + ' ';
                                } else {
                                    line = testLine;
                                }
                            }
                            lines.push(line);
                            
                            // Draw lines
                            lines.forEach((line, index) => {
                                context.fillText(line, canvas.width / 2, canvas.height / 2 - ((lines.length - 1) * 20) / 2 + index * 40);
                            });
                            
                            return canvas;
                        })()}
                    />
                </meshStandardMaterial>
            </mesh>
        </group>
    );
};

export default StepBoardCanvas;
