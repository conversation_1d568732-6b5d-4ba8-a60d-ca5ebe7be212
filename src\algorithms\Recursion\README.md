# Recursion & Backtracking Algorithms

This directory contains implementations of various recursion and backtracking algorithms.

## Implemented Algorithms

- TowersOfHanoi - A classic recursive algorithm for solving the Tower of Hanoi puzzle.

## Adding a New Algorithm

To add a new algorithm to this category:

1. Create a new folder with the algorithm name (e.g., `NQueens`)
2. Implement the required files:
   - `NQueensAlgorithm.js` - Core algorithm logic
   - `NQueensVisualization.js` - Visualization component
   - `NQueensController.js` - UI controls
3. Register the algorithm in the `AlgorithmRegistry.js` file
