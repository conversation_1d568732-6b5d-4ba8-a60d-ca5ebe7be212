// QuickSortDetailedSteps.js
// Detailed step generation for QuickSort algorithm (like MergeSort)

/**
 * Generate detailed steps for Quick Sort algorithm visualization (like MergeSort)
 * @param {Array} array - The array to sort
 * @returns {Object} - Object containing steps and sorted array
 */
export const generateDetailedQuickSortSteps = (array) => {
  // Validate input
  if (!Array.isArray(array) || array.length === 0) {
    return { steps: [], sortedArray: [] };
  }

  // Create a copy of the array to avoid modifying the original
  const arr = [...array];
  const originalArray = [...array];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'initial',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [],
    pivot: null,
    left: null,
    right: null,
    movement: `Quick Sort: Initial Array [${arr.join(', ')}]`,
    initialArray: true
  });

  // Track sorted indices
  const sortedIndices = new Set();

  // Helper function to calculate partition level (for visualization depth)
  const getPartitionLevel = (start, end, arrayLength) => {
    const partitionSize = end - start + 1;
    const ratio = partitionSize / arrayLength;

    if (ratio === 1) return 0; // Full array
    if (ratio >= 0.5) return 1; // First level partition
    if (ratio >= 0.25) return 2; // Second level partition
    if (ratio >= 0.125) return 3; // Third level partition
    return 4; // Deeper levels
  };

  // Recursive function to perform Quick Sort with detailed steps
  const quickSort = (start, end, depth = 0) => {
    // Base case: if the partition has 0 or 1 elements, it's already sorted
    if (start >= end) {
      if (start === end && !sortedIndices.has(start)) {
        sortedIndices.add(start);
        steps.push({
          type: 'single_element_sorted',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: null,
          left: start,
          right: end,
          movement: `Single element at index ${start} is already sorted`,
          partitionInfo: {
            start,
            end,
            size: 1,
            level: getPartitionLevel(start, end, originalArray.length)
          }
        });
      }
      return;
    }

    // Add step for starting partition
    steps.push({
      type: 'start_partition',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: null,
      left: start,
      right: end,
      movement: `Start partitioning array segment [${start}...${end}]: [${arr.slice(start, end + 1).join(', ')}]`,
      partitionInfo: {
        start,
        end,
        size: end - start + 1,
        level: getPartitionLevel(start, end, originalArray.length),
        subarray: arr.slice(start, end + 1)
      }
    });

    // Choose the pivot (using the last element)
    const pivotIndex = end;
    const pivotValue = arr[pivotIndex];

    // Add step to show pivot selection with explanatory elements
    steps.push({
      type: 'select_pivot',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: pivotIndex,
      left: start,
      right: end,
      movement: `Select pivot: element at index ${pivotIndex} with value ${pivotValue}`,
      partitionInfo: {
        start,
        end,
        pivotIndex,
        pivotValue,
        level: getPartitionLevel(start, end, originalArray.length)
      },
      explanatoryElements: {
        pivotElement: {
          index: pivotIndex,
          value: pivotValue,
          originalIndex: pivotIndex
        }
      }
    });

    // Initialize partition pointers
    let i = start - 1; // Index of smaller element (left partition boundary)

    // Add step to show initial partition setup
    steps.push({
      type: 'initialize_pointers',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: pivotIndex,
      left: start,
      right: end,
      movement: `Initialize partition pointers: i = ${i} (left boundary), j will scan from ${start} to ${end - 1}`,
      partitionInfo: {
        start,
        end,
        pivotIndex,
        pivotValue,
        leftBoundary: i,
        level: getPartitionLevel(start, end, originalArray.length)
      }
    });

    // Partition the array with detailed steps
    for (let j = start; j < end; j++) {
      // Add step for current element being examined
      steps.push({
        type: 'examine_element',
        array: [...arr],
        comparing: [j],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: pivotIndex,
        left: start,
        right: end,
        movement: `Examine element at index ${j}: value ${arr[j]}`,
        partitionInfo: {
          start,
          end,
          pivotIndex,
          pivotValue,
          currentIndex: j,
          currentValue: arr[j],
          leftBoundary: i,
          level: getPartitionLevel(start, end, originalArray.length)
        }
      });

      // Compare current element with pivot
      steps.push({
        type: 'compare_with_pivot',
        array: [...arr],
        comparing: [j, pivotIndex],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: pivotIndex,
        left: start,
        right: end,
        movement: `Compare ${arr[j]} (index ${j}) with pivot ${pivotValue}: ${arr[j]} ${arr[j] < pivotValue ? '<' : '>='} ${pivotValue}`,
        partitionInfo: {
          start,
          end,
          pivotIndex,
          pivotValue,
          currentIndex: j,
          currentValue: arr[j],
          leftBoundary: i,
          comparisonResult: arr[j] < pivotValue ? 'smaller' : 'greater_or_equal',
          level: getPartitionLevel(start, end, originalArray.length)
        }
      });

      // If current element is smaller than the pivot
      if (arr[j] < pivotValue) {
        i++; // Increment left boundary

        // Add step for moving left boundary
        steps.push({
          type: 'move_left_boundary',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: pivotIndex,
          left: start,
          right: end,
          movement: `Element ${arr[j]} < pivot ${pivotValue}, increment left boundary: i = ${i}`,
          partitionInfo: {
            start,
            end,
            pivotIndex,
            pivotValue,
            currentIndex: j,
            currentValue: arr[j],
            leftBoundary: i,
            level: getPartitionLevel(start, end, originalArray.length)
          }
        });

        // Swap elements if needed
        if (i !== j) {
          steps.push({
            type: 'swap_to_left_partition',
            array: [...arr],
            comparing: [],
            swapping: [i, j],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Swap elements: ${arr[i]} (index ${i}) ↔ ${arr[j]} (index ${j})`,
            partitionInfo: {
              start,
              end,
              pivotIndex,
              pivotValue,
              swapIndices: [i, j],
              swapValues: [arr[i], arr[j]],
              leftBoundary: i,
              level: getPartitionLevel(start, end, originalArray.length)
            },
            explanatoryElements: {
              leftPartition: arr.slice(start, i + 1).map((val, idx) => ({ value: val, index: start + idx })),
              rightPartition: arr.slice(i + 1, end).map((val, idx) => ({ value: val, index: i + 1 + idx }))
            }
          });

          // Perform the swap
          [arr[i], arr[j]] = [arr[j], arr[i]];

          steps.push({
            type: 'swapped_to_left_partition',
            array: [...arr],
            comparing: [],
            swapping: [],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Swapped: ${arr[i]} now at index ${i}, ${arr[j]} now at index ${j}`,
            partitionInfo: {
              start,
              end,
              pivotIndex,
              pivotValue,
              leftBoundary: i,
              level: getPartitionLevel(start, end, originalArray.length)
            },
            explanatoryElements: {
              leftPartition: arr.slice(start, i + 1).map((val, idx) => ({ value: val, index: start + idx })),
              rightPartition: arr.slice(i + 1, end).map((val, idx) => ({ value: val, index: i + 1 + idx }))
            }
          });
        } else {
          steps.push({
            type: 'already_in_left_partition',
            array: [...arr],
            comparing: [],
            swapping: [],
            sorted: [...sortedIndices],
            pivot: pivotIndex,
            left: start,
            right: end,
            movement: `Element ${arr[i]} is already in the correct position for left partition`,
            partitionInfo: {
              start,
              end,
              pivotIndex,
              pivotValue,
              leftBoundary: i,
              level: getPartitionLevel(start, end, originalArray.length)
            }
          });
        }
      } else {
        // Element stays in right partition
        steps.push({
          type: 'stays_in_right_partition',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: pivotIndex,
          left: start,
          right: end,
          movement: `Element ${arr[j]} >= pivot ${pivotValue}, stays in right partition`,
          partitionInfo: {
            start,
            end,
            pivotIndex,
            pivotValue,
            currentIndex: j,
            currentValue: arr[j],
            leftBoundary: i,
            level: getPartitionLevel(start, end, originalArray.length)
          }
        });
      }

      // Show current partition state after each comparison
      if (j < end - 1) { // Don't show for the last element (pivot)
        steps.push({
          type: 'partition_state',
          array: [...arr],
          comparing: [],
          swapping: [],
          sorted: [...sortedIndices],
          pivot: pivotIndex,
          left: start,
          right: end,
          movement: `Current partitions: Left [${start}...${i}], Right [${i + 1}...${j}], Pivot at ${pivotIndex}`,
          partitionInfo: {
            start,
            end,
            pivotIndex,
            pivotValue,
            leftBoundary: i,
            currentIndex: j,
            level: getPartitionLevel(start, end, originalArray.length)
          },
          explanatoryElements: {
            leftPartition: i >= start ? arr.slice(start, i + 1).map((val, idx) => ({ value: val, index: start + idx })) : [],
            rightPartition: arr.slice(i + 1, j + 1).map((val, idx) => ({ value: val, index: i + 1 + idx })),
            pivotElement: {
              index: pivotIndex,
              value: pivotValue,
              originalIndex: pivotIndex
            }
          }
        });
      }
    }

    // Place the pivot in its correct position
    const newPivotIndex = i + 1;

    // Add step for final pivot placement
    steps.push({
      type: 'place_pivot',
      array: [...arr],
      comparing: [],
      swapping: newPivotIndex !== pivotIndex ? [newPivotIndex, pivotIndex] : [],
      sorted: [...sortedIndices],
      pivot: pivotIndex,
      left: start,
      right: end,
      movement: newPivotIndex !== pivotIndex ?
        `Place pivot in correct position: swap ${arr[pivotIndex]} (index ${pivotIndex}) with ${arr[newPivotIndex]} (index ${newPivotIndex})` :
        `Pivot ${arr[pivotIndex]} is already in correct position at index ${pivotIndex}`,
      partitionInfo: {
        start,
        end,
        pivotIndex,
        newPivotIndex,
        pivotValue,
        level: getPartitionLevel(start, end, originalArray.length)
      }
    });

    // Perform pivot swap if needed
    if (newPivotIndex !== pivotIndex) {
      [arr[newPivotIndex], arr[pivotIndex]] = [arr[pivotIndex], arr[newPivotIndex]];

      steps.push({
        type: 'pivot_placed',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: newPivotIndex,
        left: start,
        right: end,
        movement: `Pivot ${arr[newPivotIndex]} placed at correct position (index ${newPivotIndex})`,
        partitionInfo: {
          start,
          end,
          pivotIndex: newPivotIndex,
          pivotValue: arr[newPivotIndex],
          level: getPartitionLevel(start, end, originalArray.length)
        },
        explanatoryElements: {
          leftPartition: newPivotIndex > start ? arr.slice(start, newPivotIndex).map((val, idx) => ({ value: val, index: start + idx })) : [],
          rightPartition: newPivotIndex < end ? arr.slice(newPivotIndex + 1, end + 1).map((val, idx) => ({ value: val, index: newPivotIndex + 1 + idx })) : [],
          pivotElement: {
            index: newPivotIndex,
            value: arr[newPivotIndex],
            originalIndex: pivotIndex
          }
        }
      });
    }

    // Mark the pivot as sorted
    sortedIndices.add(newPivotIndex);
    steps.push({
      type: 'pivot_sorted',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: newPivotIndex,
      left: start,
      right: end,
      movement: `Pivot ${arr[newPivotIndex]} at index ${newPivotIndex} is now in its final sorted position`,
      partitionInfo: {
        start,
        end,
        pivotIndex: newPivotIndex,
        pivotValue: arr[newPivotIndex],
        level: getPartitionLevel(start, end, originalArray.length)
      }
    });

    // Add step for partition completion
    steps.push({
      type: 'partition_complete',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: [...sortedIndices],
      pivot: newPivotIndex,
      left: start,
      right: end,
      movement: `Partition complete: Left [${start}...${newPivotIndex - 1}], Pivot [${newPivotIndex}], Right [${newPivotIndex + 1}...${end}]`,
      partitionInfo: {
        start,
        end,
        pivotIndex: newPivotIndex,
        pivotValue: arr[newPivotIndex],
        leftPartitionStart: start,
        leftPartitionEnd: newPivotIndex - 1,
        rightPartitionStart: newPivotIndex + 1,
        rightPartitionEnd: end,
        level: getPartitionLevel(start, end, originalArray.length)
      },
      explanatoryElements: {
        leftPartition: newPivotIndex > start ? arr.slice(start, newPivotIndex).map((val, idx) => ({ value: val, index: start + idx })) : [],
        rightPartition: newPivotIndex < end ? arr.slice(newPivotIndex + 1, end + 1).map((val, idx) => ({ value: val, index: newPivotIndex + 1 + idx })) : [],
        pivotElement: {
          index: newPivotIndex,
          value: arr[newPivotIndex],
          originalIndex: pivotIndex
        }
      }
    });

    // Recursively sort the sub-arrays
    if (start < newPivotIndex - 1) {
      steps.push({
        type: 'recurse_left',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: null,
        left: start,
        right: newPivotIndex - 1,
        movement: `Recursively sort left partition [${start}...${newPivotIndex - 1}]: [${arr.slice(start, newPivotIndex).join(', ')}]`,
        partitionInfo: {
          start,
          end: newPivotIndex - 1,
          size: newPivotIndex - start,
          level: getPartitionLevel(start, newPivotIndex - 1, originalArray.length),
          subarray: arr.slice(start, newPivotIndex)
        }
      });

      quickSort(start, newPivotIndex - 1, depth + 1);
    }

    if (newPivotIndex + 1 < end) {
      steps.push({
        type: 'recurse_right',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: [...sortedIndices],
        pivot: null,
        left: newPivotIndex + 1,
        right: end,
        movement: `Recursively sort right partition [${newPivotIndex + 1}...${end}]: [${arr.slice(newPivotIndex + 1, end + 1).join(', ')}]`,
        partitionInfo: {
          start: newPivotIndex + 1,
          end,
          size: end - newPivotIndex,
          level: getPartitionLevel(newPivotIndex + 1, end, originalArray.length),
          subarray: arr.slice(newPivotIndex + 1, end + 1)
        }
      });

      quickSort(newPivotIndex + 1, end, depth + 1);
    }
  };

  // Start the sorting process
  quickSort(0, arr.length - 1);

  // Add final completion step
  steps.push({
    type: 'complete',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [...Array(arr.length).keys()], // All indices are sorted
    pivot: null,
    left: null,
    right: null,
    movement: `Quick Sort complete: [${arr.join(', ')}]`,
    data: {
      data: [...arr],
      isSorted: true
    }
  });

  return { steps, sortedArray: arr };
};
