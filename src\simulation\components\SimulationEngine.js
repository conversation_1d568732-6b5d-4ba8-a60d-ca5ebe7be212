// SimulationEngine.js
// Core component that handles step execution and transitions

import React, { useEffect, useRef } from 'react';
import { useStep } from '../context/StepContext';
import { useSimulation, SimulationState } from '../context/SimulationContext';
import { useAlgorithmData } from '../context/AlgorithmDataContext';

/**
 * SimulationEngine - Core component that handles step execution and transitions
 * 
 * This component doesn't render anything visible but handles the logic for
 * processing algorithm steps and managing transitions between them.
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onStepStart - Callback when a step starts
 * @param {Function} props.onStepComplete - Callback when a step completes
 * @param {Function} props.onSimulationComplete - Callback when simulation completes
 * @param {Function} props.stepProcessor - Function to process each step
 */
const SimulationEngine = ({
  onStepStart,
  onStepComplete,
  onSimulationComplete,
  stepProcessor,
  children
}) => {
  // Get contexts
  const { 
    steps, 
    currentStep, 
    currentStepIndex, 
    totalSteps,
    isAnimating,
    startStepAnimation,
    stopStepAnimation
  } = useStep();
  
  const { 
    state, 
    setState,
    startSimulation,
    pauseSimulation,
    resetSimulation
  } = useSimulation();
  
  const { 
    updateIntermediateState 
  } = useAlgorithmData();

  // Track the last processed step to prevent infinite loops
  const lastProcessedStepRef = useRef(null);

  // Process the current step when it changes
  useEffect(() => {
    // Skip if we don't have steps or a current step
    if (!steps || !steps.length || !currentStep) {
      return;
    }

    // Skip if we've already processed this step
    const stepKey = `${currentStepIndex}`;
    if (lastProcessedStepRef.current === stepKey) {
      return;
    }

    // Update the last processed step
    lastProcessedStepRef.current = stepKey;

    // Call the onStepStart callback
    if (onStepStart) {
      onStepStart(currentStep, currentStepIndex);
    }

    // Process the step if a processor is provided
    if (stepProcessor) {
      // Start animation for the step
      startStepAnimation();

      // Process the step
      const result = stepProcessor(currentStep, currentStepIndex);

      // Update intermediate state with the result
      if (result) {
        updateIntermediateState(result);
      }
    } else {
      // If no processor is provided, just complete the step
      if (onStepComplete) {
        onStepComplete(currentStep, currentStepIndex);
      }
    }
  }, [
    steps, 
    currentStep, 
    currentStepIndex, 
    onStepStart, 
    onStepComplete, 
    stepProcessor, 
    startStepAnimation,
    updateIntermediateState
  ]);

  // Handle simulation completion
  useEffect(() => {
    // Check if we've reached the end of the simulation
    if (currentStepIndex === totalSteps - 1 && state === SimulationState.RUNNING) {
      // Set state to completed
      setState(SimulationState.COMPLETED);

      // Stop animation
      stopStepAnimation();

      // Call the onSimulationComplete callback
      if (onSimulationComplete) {
        onSimulationComplete();
      }
    }
  }, [
    currentStepIndex, 
    totalSteps, 
    state, 
    setState, 
    stopStepAnimation, 
    onSimulationComplete
  ]);

  // Reset when state changes to idle
  useEffect(() => {
    if (state === SimulationState.IDLE) {
      // Reset the last processed step
      lastProcessedStepRef.current = null;
    }
  }, [state]);

  // Render children if provided
  return children ? children : null;
};

export default SimulationEngine;
