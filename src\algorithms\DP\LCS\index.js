// LCS/index.js
// Export only what's needed from this algorithm

import LCSVisualization from './LCSVisualization';
import LCSController from './LCSController';
import LCSAlgorithm from './LCSAlgorithm';

export const metadata = {
  id: 'LCS',
  name: 'Longest Common Subsequence',
  description: 'A dynamic programming algorithm that finds the longest subsequence common to two sequences.',
  timeComplexity: 'O(m*n)',
  spaceComplexity: 'O(m*n)',
  defaultParams: {
    string1: 'ABCBDAB',
    string2: 'BDCABA',
    animationSpeed: 5,
  },
};

export const components = {
  visualization: LCSVisualization,
  controller: LCSController,
  algorithm: LCSAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
