/**
 * Utility functions for working with localStorage
 */

/**
 * Save a value to localStorage with error handling
 * 
 * @param {string} key - The key to store the value under
 * @param {any} value - The value to store (will be JSON stringified)
 * @returns {boolean} - Whether the operation was successful
 */
export const saveToStorage = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error saving to localStorage (key: ${key}):`, error);
    return false;
  }
};

/**
 * Get a value from localStorage with error handling
 * 
 * @param {string} key - The key to retrieve
 * @param {any} defaultValue - The default value to return if the key doesn't exist
 * @returns {any} - The retrieved value or defaultValue
 */
export const getFromStorage = (key, defaultValue) => {
  try {
    const value = localStorage.getItem(key);
    return value !== null ? JSON.parse(value) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

/**
 * Remove a value from localStorage with error handling
 * 
 * @param {string} key - The key to remove
 * @returns {boolean} - Whether the operation was successful
 */
export const removeFromStorage = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing from localStorage (key: ${key}):`, error);
    return false;
  }
};

/**
 * Clear all values from localStorage with error handling
 * 
 * @returns {boolean} - Whether the operation was successful
 */
export const clearStorage = () => {
  try {
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};
