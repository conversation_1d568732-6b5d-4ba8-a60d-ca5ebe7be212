# Migration Guide: Moving to the New Simulation Architecture

This guide provides step-by-step instructions for migrating existing algorithm implementations to the new simulation architecture.

## Table of Contents

1. [Overview](#overview)
2. [Step 1: Extract Step Generation Logic](#step-1-extract-step-generation-logic)
3. [Step 2: Create Step Visualizers](#step-2-create-step-visualizers)
3. [Step 3: Update Controller Component](#step-3-update-controller-component)
4. [Step 4: Update Visualization Component](#step-4-update-visualization-component)
5. [Step 5: Connect to the Simulation Engine](#step-5-connect-to-the-simulation-engine)
6. [Migration Checklist](#migration-checklist)
7. [Troubleshooting](#troubleshooting)

## Overview

The migration process involves:

1. Extracting step generation logic from existing controllers
2. Creating step visualizers for each step type
3. Updating controller components to use the new context system
4. Updating visualization components to use the new step visualizer
5. Connecting everything to the simulation engine

## Step 1: Extract Step Generation Logic

1. **Identify Step Generation Code** - Find the code in your controller that generates algorithm steps.

2. **Create a New File** - Create a new file (e.g., `AlgorithmSteps.js`) for the step generation logic.

3. **Extract the Logic** - Move the step generation logic to the new file, making it a pure function.

4. **Update Step Format** - Update the step format to match the new architecture:

   **Before:**
   ```javascript
   steps.push({
     type: 'comparison',
     indices: [i, j],
     array: [...array],
     message: `Compare elements at indices ${i} and ${j}`
   });
   ```

   **After:**
   ```javascript
   steps.push(
     createComparisonStep(
       deepClone(array),
       [i, j],
       `Compare elements at indices ${i} and ${j}`
     )
   );
   ```

## Step 2: Create Step Visualizers

1. **Identify Visualization Logic** - Find the code in your visualization component that renders different step types.

2. **Create Visualizer Components** - Create separate components for each step type:

   ```javascript
   const ComparisonVisualizer = ({ step }) => {
     const { data, indices } = step.data;
     
     return (
       <Box>
         {/* Render comparison visualization */}
       </Box>
     );
   };
   ```

3. **Create Visualizer Map** - Create a map of step types to visualizer components:

   ```javascript
   export const visualizers = {
     comparison: ComparisonVisualizer,
     swap: SwapVisualizer,
     // Map other step types to their visualizers
   };
   ```

## Step 3: Update Controller Component

1. **Remove State Management** - Remove local state for algorithm state, steps, etc.

2. **Use Context Hooks** - Use the new context hooks:

   **Before:**
   ```javascript
   const [steps, setSteps] = useState([]);
   const [currentStep, setCurrentStep] = useState(0);
   const [state, setState] = useState('idle');
   ```

   **After:**
   ```javascript
   const { 
     setSteps,
     goToFirstStep
   } = useStep();
   
   const { 
     state,
     setState,
     resetSimulation
   } = useSimulation();
   
   const { 
     updateInputData
   } = useAlgorithmData();
   ```

3. **Update Step Generation** - Update the step generation code to use the extracted function:

   **Before:**
   ```javascript
   const generatedSteps = generateSteps(array);
   setSteps(generatedSteps);
   ```

   **After:**
   ```javascript
   const steps = generateAlgorithmSteps(inputData, param1, param2);
   setSteps(steps);
   resetSimulation();
   ```

## Step 4: Update Visualization Component

1. **Remove Visualization Logic** - Remove the code that renders different step types.

2. **Use StepVisualizer** - Use the new StepVisualizer component:

   **Before:**
   ```jsx
   return (
     <Canvas>
       {currentStep.type === 'comparison' && (
         /* Render comparison visualization */
       )}
       {currentStep.type === 'swap' && (
         /* Render swap visualization */
       )}
       {/* Other step types */}
     </Canvas>
   );
   ```

   **After:**
   ```jsx
   return (
     <Canvas>
       <StepVisualizer 
         visualizers={visualizers}
         defaultVisualizer={DefaultVisualizer}
       />
     </Canvas>
   );
   ```

## Step 5: Connect to the Simulation Engine

1. **Wrap Components with Provider** - Wrap your components with the SimulationContextProvider:

   ```jsx
   const AlgorithmSimulation = () => {
     return (
       <SimulationContextProvider>
         <AlgorithmController />
         <AlgorithmVisualizer />
         <SimulationEngine />
         <StepProcessor />
       </SimulationContextProvider>
     );
   };
   ```

2. **Add Simulation Engine** - Add the SimulationEngine and StepProcessor components:

   ```jsx
   <SimulationEngine />
   <StepProcessor />
   ```

## Migration Checklist

Use this checklist to ensure you've completed all the necessary steps:

- [ ] Extracted step generation logic to a separate file
- [ ] Updated step format to use the new step creators
- [ ] Created visualizer components for each step type
- [ ] Created a map of step types to visualizer components
- [ ] Updated controller to use context hooks
- [ ] Removed local state management for algorithm state
- [ ] Updated visualization component to use StepVisualizer
- [ ] Wrapped components with SimulationContextProvider
- [ ] Added SimulationEngine and StepProcessor components
- [ ] Tested the migrated implementation

## Troubleshooting

### Common Migration Issues

1. **Missing Step Properties** - If steps are missing properties, check that you're using the correct step creators and providing all necessary data.

2. **Context Not Available** - If context hooks throw errors, make sure your components are wrapped with the SimulationContextProvider.

3. **Visualizations Not Rendering** - Check that your visualizer components are correctly mapped to step types and receiving the expected props.

4. **Steps Not Advancing** - Ensure that the SimulationEngine and StepProcessor components are included in your component tree.

### Debugging Tips

1. **Compare with Example** - Compare your implementation with the MergeSortExample to identify differences.

2. **Check Context Values** - Use console.log to check context values at different points in your components.

3. **Inspect Step Data** - Log step data to ensure it has the expected format and properties.

4. **Test Components Individually** - Test each component individually to isolate issues.
