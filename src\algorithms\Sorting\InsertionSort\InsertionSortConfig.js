// InsertionSortConfig.js - Comprehensive configuration for Insertion Sort visualization
// This file contains ALL configurable aspects of the InsertionSort visualization

const InsertionSortConfig = {
  // ==================== MAIN ARRAY CONFIGURATION ====================
  mainArray: {
    // Bar dimensions and positioning
    bars: {
      width: 0.6,                    // Width of each main array bar
      spacing: 0.3,                  // Spacing between main array bars
      maxHeight: 3.8,                // Maximum height of main array bars
      baseOffset: [0, 0, 0],         // Offset from base platform position [x, y, z]
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance

      // Bar geometry
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties
      material: {
        roughness: 0.5,              // Material roughness (0 = mirror, 1 = rough)
        metalness: 0.3,              // Material metalness (0 = non-metal, 1 = metal)
        opacity: 0.9,                // Bar opacity
        transparent: false,          // Enable transparency
      },

      // Base platform under each bar
      base: {
        enabled: true,               // Enable/disable base platform
        height: 0.05,                // Height of base platform
        widthScale: 1.0,             // Width scale factor for base (1.0 = full width)
        depthScale: 1.0,             // Depth scale factor for base (1.0 = full depth)
        material: {
          roughness: 0.8,            // Base material roughness
          metalness: 0.2,            // Base material metalness
          opacity: 0.7,              // Base opacity
          transparent: false,        // Enable transparency
        },
      },
    },

    // Value labels (numbers on top of bars)
    valueLabels: {
      enabled: true,                 // Show/hide value labels
      offset: [0, 0.3, 0],          // Offset from bar top [x, y, z]
      fontSize: '0.6rem',           // Font size for value labels
      fontWeight: 'bold',           // Font weight for value labels
      padding: {
        horizontal: 0.5,             // Horizontal padding inside label
        vertical: 0.1,               // Vertical padding inside label
      },
      borderRadius: 1.5,             // Border radius factor (theme.shape.borderRadius / this) - BOXY STYLE like QuickSort
      minWidth: '16px',              // Minimum width of label container
      elevation: 1,                  // Material-UI elevation
    },

    // Index labels (numbers below bars)
    indexLabels: {
      enabled: true,                 // Show/hide index labels
      offset: [0, 0.1, 0.4],         // Offset from bar base [x, y, z]
      fontSize: '10px',             // Font size for index labels
      fontWeight: 'normal',         // Font weight for index labels
      size: {
        width: '20px',               // Width of circular index label
        height: '20px',              // Height of circular index label
      },
      elevation: 1,                  // Material-UI elevation
    },

    // Current element indicator (special label for current element)
    currentIndicator: {
      enabled: true,                 // Show/hide current indicator
      offset: [0, 0.8, 0],          // Offset from bar top [x, y, z]
      text: 'CURRENT',               // Text to display
      fontSize: '0.5rem',           // Font size for current indicator
      fontWeight: 'bold',           // Font weight for current indicator
      padding: {
        horizontal: 0.5,             // Horizontal padding inside indicator
        vertical: 0.1,               // Vertical padding inside indicator
      },
      borderRadius: 0.5,             // Border radius factor
      elevation: 2,                  // Material-UI elevation
    },
  },

  // ==================== HELD ELEMENT CONFIGURATION ====================
  heldElement: {
    // Positioning configuration
    positioning: {
      enabled: true,                 // Enable/disable held element display
      yOffset: 6,                  // Height above main array (reduced from maxBarHeight + 2)
      xOffset: 3.2,                    // Horizontal offset from original position
      zOffset: -2,                  // Forward offset for better visibility

      // Fixed position mode - keeps held element at same location regardless of array indices
      useFixedPosition: true,        // Use fixed position instead of calculating from array indices
      fixedPosition: {
        x: 0,                       // Fixed X position (negative = left side of array)
        y: 5.5,                      // Fixed Y position (height above array)
        z: -2,                      // Fixed Z position (forward offset)
      },

      // Placement animation positioning
      placement: {
        startYOffset: 1.5,           // Starting Y position for placement animation
        endYOffset: 0,               // Ending Y position (on platform)
        arcHeight: 0.5,              // Height of arc during placement animation
      },
    },

    // Scaling configuration
    scaling: {
      barScale: 0.5,                 // Scale factor for held bar (0.8 = 80% of normal size)
      labelScale: 1.5,               // Scale factor for held element labels
      indicatorScale: 2,           // Scale factor for "HELD" indicator
    },

    // Visual properties
    visual: {
      // Bar appearance
      bar: {
        opacity: 0.9,                // Opacity of held bar
        transparency: true,          // Enable transparency
        glowEffect: false,           // Enable glow effect around held element

        // Material overrides for held element
        material: {
          roughness: 0.4,            // Slightly more reflective than normal bars
          metalness: 0.4,            // Slightly more metallic
          emissive: 0.1,             // Slight emissive glow
        },
      },

      // Base platform for held element
      base: {
        enabled: true,               // Show base platform under held element
        opacity: 0.7,                // Base opacity (more transparent)
        scale: 0.8,                  // Scale factor for base
      },

      // Shadow configuration
      shadow: {
        enabled: true,               // Enable shadow for held element
        intensity: 0.8,              // Shadow intensity
        blur: 1.2,                   // Shadow blur amount
      },
    },

    // Labels configuration for held element
    labels: {
      // Value label
      value: {
        enabled: true,               // Show value label on held element
        offset: [0, 0.1, 0],        // Offset from held bar top [x, y, z]
        fontSize: '0.5rem',         // Slightly smaller font
        fontWeight: 'bold',          // Bold font weight
        border: {
          width: 1,                  // Thicker border for held element
          style: 'solid',            // Border style
        },
        padding: {
          horizontal: 0.6,           // Horizontal padding
          vertical: 0.15,            // Vertical padding
        },
        elevation: 2,                // Higher elevation for prominence
      },

      // "HELD" indicator
      indicator: {
        enabled: true,               // Show "HELD" indicator
        text: 'HELD',                // Text to display
        offset: [0, 1, 0],        // Offset from held bar top [x, y, z]
        fontSize: '0.5rem',          // Font size for indicator
        fontWeight: 'bold',          // Font weight
        padding: {
          horizontal: 0.8,           // Horizontal padding
          vertical: 0.2,             // Vertical padding
        },
        borderRadius: 0.4,           // Border radius factor
        elevation: 3,                // Material-UI elevation

        // Color configuration
        background: 'merging',       // Background color (uses color key)
        textColor: 'white',          // Text color
        border: {
          enabled: true,             // Enable border
          width: 1,                  // Border width
          color: 'merging',          // Border color (uses color key)
        },
      },
    },

    // Animation configuration for held element
    animation: {
      // Floating animation while held
      floating: {
        enabled: true,               // Enable floating animation
        amplitude: 0.05,             // Vertical floating amplitude
        frequency: 2.0,              // Floating frequency
        phase: 0,                    // Phase offset for floating
      },

      // Placement animation
      placement: {
        enabled: true,               // Enable placement animation
        duration: 600,               // Duration of placement animation (ms)
        easing: 'ease-out',          // Easing function
        trajectory: 'arc',           // Movement trajectory ('linear' or 'arc')
      },

      // Transitions
      transitions: {
        fadeIn: {
          enabled: true,             // Enable fade-in when element becomes held
          duration: 200,             // Fade-in duration (ms)
        },
        fadeOut: {
          enabled: true,             // Enable fade-out when element is placed
          duration: 200,             // Fade-out duration (ms)
        },
      },
    },
  },

  // ==================== BASE PLATFORM CONFIGURATION ====================
  basePlatform: {
    dimensions: {
      height: 0.2,                   // Height of the base platform
      lengthPadding: {
        left: 1,                     // Padding on the left side
        right: 1,                    // Padding on the right side
      },
      depth: 3,                      // Depth of the base platform
    },
    position: [0, -4, 0],            // MASTER position - controls entire main array group [x, y, z]
    material: {
      roughness: 0.8,                // Material roughness
      metalness: 0.1,                // Material metalness
    },
  },

  // ==================== CAMERA CONFIGURATION ====================
  camera: {
    position: [0, 3, 12],            // Default camera position [x, y, z]
    lookAt: [0, 0, 0],               // Camera look-at point [x, y, z]
    fov: 55,                         // Field of view in degrees
    near: 0.1,                       // Near clipping plane
    far: 1000,                       // Far clipping plane

    // Dynamic positioning based on array size
    dynamicPositioning: {
      enabled: true,                 // Enable dynamic camera positioning
      minDistance: 8,                // Minimum camera distance
      paddingFactor: 2,              // Padding factor for camera distance calculation
      heightOffset: 3,               // Additional height offset
    },
  },

  // ==================== LIGHTING CONFIGURATION ====================
  lighting: {
    ambient: {
      intensity: 0.6,                // Ambient light intensity
      color: '#ffffff',              // Ambient light color
    },
    directional: {
      position: [10, 10, 5],         // Directional light position [x, y, z]
      intensity: 1.0,                // Directional light intensity
      color: '#ffffff',              // Directional light color
      castShadow: true,              // Whether directional light casts shadows
    },
    // Additional lights can be added here
    pointLights: [],                 // Array of point light configurations
    spotLights: [],                  // Array of spot light configurations
  },

  // ==================== UI ELEMENTS CONFIGURATION ====================

  // Step board configuration (shows current step information)
  stepBoard: {
    enabled: true,                   // Enable/disable step board
    position: [0, 5, 0.5],          // Position of the step board [x, y, z]
    dimensions: {
      width: 12,                     // Width of the step board
      height: 1.5,                   // Height of the step board
      depth: 0.1,                    // Depth of the step board
    },
    material: {
      opacity: 0.9,                  // Material opacity
      transparent: true,             // Enable transparency
    },
    text: {
      fontSize: 'h6',                // Material-UI typography variant
      fontWeight: 'bold',            // Font weight
      align: 'center',               // Text alignment
      padding: 2,                    // Padding around text
    },
    border: {
      enabled: true,                 // Enable border
      width: 2,                      // Border width
      radius: 2,                     // Border radius
    },
    elevation: 3,                    // Material-UI elevation
  },

  // Color legend configuration
  colorLegend: {
    enabled: true,                   // Enable/disable color legend
    position: [0, -3.5, 0.5],       // Position of the color legend [x, y, z]
    itemSpacing: 2.5,                // Spacing between legend items

    // Individual legend item configuration
    items: {
      dimensions: {
        width: 0.8,                  // Width of color sample
        height: 0.4,                 // Height of color sample
        depth: 0.2,                  // Depth of color sample
      },
      material: {
        roughness: 0.5,              // Material roughness
        metalness: 0.3,              // Material metalness
      },
      label: {
        fontSize: 'caption',         // Material-UI typography variant
        fontWeight: 'normal',        // Font weight
        offset: [0, -0.8, 0],       // Offset from color sample [x, y, z]
        padding: {
          horizontal: 0.5,           // Horizontal padding
          vertical: 0.2,             // Vertical padding
        },
        borderRadius: 1,             // Border radius factor
        elevation: 1,                // Material-UI elevation
      },
    },

    // Legend items definition
    legendItems: [
      { colorKey: 'bar', label: 'Default' },
      { colorKey: 'comparing', label: 'Comparing Elements' },
      { colorKey: 'shifting', label: 'Shifting Elements' },
      { colorKey: 'current', label: 'Current Element' },
      { colorKey: 'sorted', label: 'Sorted Elements' }
    ],
  },

  // ==================== ANIMATION CONFIGURATION ====================
  animation: {
    // Timing configuration
    timing: {
      baseDuration: 500,             // Base animation duration in ms
      speedFactor: 1.0,              // Speed multiplier (higher = faster)

      // Speed-based delays (calculated dynamically)
      delays: {
        slow: 2000,                  // Delay for slow speed (1-3)
        medium: 1000,                // Delay for medium speed (4-7)
        fast: 400,                   // Delay for fast speed (8-10)
      },
    },

    // Easing functions
    easing: {
      default: 'ease-in-out',        // Default easing function
      bars: 'ease-out',              // Easing for bar movements
      labels: 'ease-in-out',         // Easing for label animations
      camera: 'ease-in-out',         // Easing for camera movements
    },

    // Animation types
    types: {
      swap: {
        enabled: false,              // Disable swap animations for InsertionSort
        duration: 800,               // Duration for swap animations
        height: 1.5,                 // Height of swap arc
      },
      highlight: {
        enabled: true,               // Enable highlight animations
        duration: 300,               // Duration for highlight changes
        pulseEffect: true,           // Enable pulse effect for highlights
      },
      comparison: {
        enabled: true,               // Enable comparison animations
        duration: 500,               // Duration for comparison highlights
        arrowEnabled: true,          // Enable arrows for InsertionSort
      },
      shift: {
        enabled: true,               // Enable shift animations
        duration: 600,               // Duration for shift operations
        smoothTransition: true,      // Enable smooth transition for shifts
      },
    },
  },

  // ==================== VISUAL SETTINGS ====================
  visual: {
    // Label visibility
    labels: {
      values: {
        enabled: true,               // Show value labels on bars
        adaptiveVisibility: false,   // Disable adaptive visibility temporarily
        visibilityThreshold: 0.0,    // Hide values when scale factor is below this
      },
      indices: {
        enabled: true,               // Show index labels on bars
        adaptiveVisibility: true,    // Hide labels for small bars
        visibilityThreshold: 0.0,    // Hide indices when scale factor is below this
      },
    },

    // Effects
    effects: {
      shadows: true,                 // Enable/disable shadows
      reflections: false,            // Enable/disable reflections
      bloom: false,                  // Enable/disable bloom effect
      antialiasing: true,            // Enable/disable antialiasing
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation animation
      disableDuringSimulation: true, // Disable levitation when algorithm is running
      amplitude: 0.1,                // Height of levitation movement (Y-axis)
      frequency: 1.0,                // Speed of levitation oscillation

      // Multi-axis movement
      movement: {
        y: {
          enabled: true,             // Enable Y-axis (vertical) movement
          amplitude: 0.1,            // Vertical movement amplitude
          frequency: 1.0,            // Vertical movement frequency
        },
        x: {
          enabled: false,            // Enable X-axis (horizontal) movement
          amplitude: 0.05,           // Horizontal movement amplitude
          frequency: 0.8,            // Horizontal movement frequency
        },
        z: {
          enabled: false,            // Enable Z-axis (depth) movement
          amplitude: 0.03,           // Depth movement amplitude
          frequency: 0.6,            // Depth movement frequency
        },
      },

      // Rotation effects
      rotation: {
        enabled: true,               // Enable rotation during levitation
        x: {
          enabled: true,             // Enable X-axis rotation
          amplitude: 0.01,           // X rotation amplitude (radians)
          frequency: 0.3,            // X rotation frequency
        },
        y: {
          enabled: true,             // Enable Y-axis rotation
          amplitude: 0.02,           // Y rotation amplitude (radians)
          frequency: 0.5,            // Y rotation frequency
        },
        z: {
          enabled: false,            // Enable Z-axis rotation
          amplitude: 0.005,          // Z rotation amplitude (radians)
          frequency: 0.4,            // Z rotation frequency
        },
      },

      // Advanced settings
      staggered: true,               // Enable staggered animation effects
      staggerDelay: 0.2,             // Delay between elements for staggered effect
      smoothTransition: true,        // Smooth transition when enabling/disabling
      transitionDuration: 1000,      // Duration for smooth transitions (ms)
    },
  },

  // ==================== COLOR CONFIGURATION ====================
  colors: {
    // Main color palette
    palette: {
      // Bar colors for different states
      bar: '#42a5f5',                // Default bar color (blue)
      comparing: '#ff9800',          // Comparing elements color (orange)
      shifting: '#f44336',           // Shifting elements color (red)
      current: '#9c27b0',            // Current element color (purple)
      sorted: '#4caf50',             // Sorted elements color (green)

      // Platform and environment colors
      base: '#424242',               // Base platform color
      ground: '#303030',             // Ground color
      background: '#121212',         // Background color

      // UI element colors
      stepBoard: '#1e1e1e',          // Step board background
      legend: '#1e1e1e',             // Legend background
      text: '#ffffff',               // Text color
      border: '#555555',             // Border color
    },

    // Theme-specific overrides
    themes: {
      light: {
        // Light theme color overrides
        bar: '#1976d2',              // Darker blue for light theme
        comparing: '#f57c00',        // Darker orange for light theme
        shifting: '#d32f2f',         // Darker red for light theme
        current: '#7b1fa2',          // Darker purple for light theme
        sorted: '#388e3c',           // Darker green for light theme
        base: '#f0f0f0',             // Light base color
        ground: '#fafafa',           // Light ground color
        background: '#ffffff',       // Light background
        stepBoard: '#ffffff',        // Light step board
        legend: '#ffffff',           // Light legend
        text: '#212121',             // Dark text for light theme
        border: '#e0e0e0',           // Light border
      },
      dark: {
        // Dark theme color overrides (use palette defaults)
        bar: '#42a5f5',
        comparing: '#ff9800',
        shifting: '#f44336',
        current: '#9c27b0',
        sorted: '#4caf50',
        base: '#424242',
        ground: '#303030',
        background: '#121212',
        stepBoard: '#1e1e1e',
        legend: '#1e1e1e',
        text: '#ffffff',
        border: '#555555',
      },
    },

    // Gradient configurations
    gradients: {
      enabled: false,                // Enable/disable gradient colors
      bars: {
        default: ['#42a5f5', '#1976d2'],     // Default bar gradient
        comparing: ['#ff9800', '#f57c00'],   // Comparing gradient
        shifting: ['#f44336', '#d32f2f'],    // Shifting gradient
        current: ['#9c27b0', '#7b1fa2'],     // Current gradient
        sorted: ['#4caf50', '#388e3c'],      // Sorted gradient
      },
    },

    // Opacity settings
    opacity: {
      bars: {
        default: 1.0,                // Default bar opacity
        comparing: 1.0,              // Comparing bar opacity
        shifting: 1.0,               // Shifting bar opacity
        current: 1.0,                // Current bar opacity
        sorted: 1.0,                 // Sorted bar opacity
      },
      platform: 0.8,                // Platform opacity
      labels: 1.0,                   // Label opacity
      ui: 0.9,                       // UI element opacity
    },

    // Special effects
    effects: {
      glow: {
        enabled: false,              // Enable/disable glow effects
        intensity: 0.5,              // Glow intensity
        colors: {
          comparing: '#ff9800',      // Glow color for comparing elements
          current: '#9c27b0',        // Glow color for current element
        },
      },
      pulse: {
        enabled: true,               // Enable/disable pulse effects
        frequency: 1.0,              // Pulse frequency
        amplitude: 0.1,              // Pulse amplitude
      },
    },
  },

  // ==================== ALGORITHM-SPECIFIC CONFIGURATION ====================
  algorithm: {
    // Array generation
    array: {
      generation: {
        defaultSize: 10,             // Default array size
        minSize: 3,                  // Minimum array size
        maxSize: 20,                 // Maximum array size
        valueRange: {
          min: 1,                    // Minimum value in array
          max: 999,                  // Maximum value in array
        },
      },

      // Custom array validation
      validation: {
        enforceRange: true,          // Enforce min/max range for custom arrays
        warnOnLargeValues: true,     // Warn when values are very large
        largeValueThreshold: 1000,   // Threshold for large value warning
      },
    },

    // Insertion sort specific settings
    insertion: {
      showCurrentElement: true,      // Show current element being inserted
      highlightSortedPortion: true,  // Highlight the sorted portion
      showShiftOperations: true,     // Show shift operations
      animateInsertion: true,        // Animate insertion operations
    },

    // Step generation
    steps: {
      showIntermediateSteps: true,   // Show all intermediate steps
      showComparisons: true,         // Show comparison steps
      showShifts: true,              // Show shift operation steps
      combineOperations: false,      // Combine consecutive operations
    },

    // Performance settings
    performance: {
      maxArraySize: 50,              // Maximum array size for performance
      enableOptimizations: true,     // Enable performance optimizations
      reducedAnimations: false,      // Reduce animations for large arrays
    },
  },
};

export default InsertionSortConfig;
