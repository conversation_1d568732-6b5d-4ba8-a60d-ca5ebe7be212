import React, { useRef } from 'react';
import { Html } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

/**
 * Reusable step board component for displaying algorithm steps with enhanced 3D text
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the step board
 * @param {number} props.step - Current step number
 * @param {number} props.totalSteps - Total number of steps
 * @param {string} props.description - Description of the current step
 * @param {Object} props.theme - MUI theme object
 */
const StepBoard = ({
  position = [0, 0, 0],
  step = 0,
  totalSteps = 0,
  description = '',
  theme
}) => {
  // Always call useTheme hook first to follow React rules
  const themeFromContext = useTheme();
  // Then use provided theme or the one from context
  const muiTheme = theme || themeFromContext;
  const isDark = muiTheme?.palette?.mode === 'dark';

  // Create ref for animation
  const boardRef = useRef();

  // Subtle floating animation
  useFrame(({ clock }) => {
    if (boardRef.current) {
      const floatY = Math.sin(clock.getElapsedTime() * 0.5) * 0.05;
      boardRef.current.position.y = position[1] + floatY;
    }
  });

  // Default styles based on theme
  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '15px 20px',
      borderRadius: '12px',
      backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
      boxShadow: isDark
        ? '0 8px 32px rgba(0, 0, 0, 0.5), 0 0 15px rgba(255, 255, 255, 0.1) inset'
        : '0 8px 32px rgba(0, 0, 0, 0.2), 0 0 15px rgba(255, 255, 255, 0.5) inset',
      border: isDark
        ? '1px solid rgba(255, 255, 255, 0.1)'
        : '1px solid rgba(0, 0, 0, 0.1)',
      minWidth: '400px',
      maxWidth: '90%',
      margin: '0 auto',
      userSelect: 'none',
      pointerEvents: 'none',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
    },
    header: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      marginBottom: '10px',
    },
    title: {
      margin: '0',
      fontSize: '18px',
      fontWeight: 'bold',
      color: isDark ? '#ffffff' : '#000000',
      textShadow: isDark
        ? '0 1px 3px rgba(0, 0, 0, 0.8)'
        : '0 1px 3px rgba(255, 255, 255, 0.8)',
    },
    stepCounter: {
      fontSize: '16px',
      fontWeight: 'bold',
      color: isDark ? '#bbdefb' : '#1976d2', // Light blue in dark mode, darker blue in light mode
      padding: '4px 10px',
      borderRadius: '16px',
      backgroundColor: isDark ? 'rgba(25, 118, 210, 0.2)' : 'rgba(25, 118, 210, 0.1)',
      border: isDark
        ? '1px solid rgba(25, 118, 210, 0.3)'
        : '1px solid rgba(25, 118, 210, 0.2)',
      boxShadow: isDark
        ? '0 2px 8px rgba(0, 0, 0, 0.3)'
        : '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    description: {
      margin: '10px 0 0 0',
      fontSize: '16px',
      lineHeight: '1.5',
      color: isDark ? '#e0e0e0' : '#424242',
      textAlign: 'center',
      maxWidth: '100%',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      textShadow: isDark
        ? '0 1px 2px rgba(0, 0, 0, 0.8)'
        : '0 1px 2px rgba(255, 255, 255, 0.8)',
    },
  };

  return (
    <group ref={boardRef} position={position}>
      <Html center>
        <div style={styles.container}>
          <div style={styles.header}>
            <h3 style={styles.title}>Algorithm Step</h3>
            <div style={styles.stepCounter}>
              {step} / {totalSteps}
            </div>
          </div>
          <p style={styles.description}>
            {description || 'No description available for this step.'}
          </p>
        </div>
      </Html>
    </group>
  );
};

export default StepBoard;
