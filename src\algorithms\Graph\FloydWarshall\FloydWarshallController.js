// FloydWarshallController.js
// This file contains the controller UI for the Floyd-Warshall algorithm

import React, { useState, useEffect, useRef } from 'react';
import { useSpeed } from '../../../context/SpeedContext';
import {
    Box,
    Typography,
    TextField,
    Button,
    Slider,
    Paper,
    IconButton,
    Tooltip,
    CircularProgress,
    Stack,
    LinearProgress,
    Collapse,
    useTheme,
    FormControlLabel,
    Switch,
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import SettingsIcon from '@mui/icons-material/Settings';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PendingIcon from '@mui/icons-material/Pending';
import TimelineIcon from '@mui/icons-material/Timeline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CodeIcon from '@mui/icons-material/Code';
import { generateFloydWarshallSteps } from './FloydWarshallAlgorithm';

const FloydWarshallController = (props) => {
    // Create a ref for the movement sequence container
    const movementContainerRef = useRef(null);

    // Extract properties from the props object with safety checks
    const {
        params = {},
        onParamChange = () => { },
        state = 'idle',
        step = 0,
        setStep = () => { },
        totalSteps = 0,
        setTotalSteps = () => { },
        setState = () => { },
        steps = [],
        setSteps = () => { },
        setMovements = () => { }
    } = props || {};

    // State for collapsible sections
    const [infoExpanded, setInfoExpanded] = useState(false);
    const [paramsExpanded, setParamsExpanded] = useState(true);
    const [controlsExpanded, setControlsExpanded] = useState(true);
    const [progressExpanded, setProgressExpanded] = useState(true);
    const [movementExpanded, setMovementExpanded] = useState(true);
    const [algorithmExpanded, setAlgorithmExpanded] = useState(true);

    // Local state for the controller
    const [vertices, setVertices] = useState(params.vertices || 5);
    const [density, setDensity] = useState(params.density || 0.7);
    const [minWeight, setMinWeight] = useState(params.minWeight || 1);
    const [maxWeight, setMaxWeight] = useState(params.maxWeight || 10);
    const [allowNegative, setAllowNegative] = useState(params.allowNegative || false);
    const [isCustomInput, setIsCustomInput] = useState(false);
    const [inputError, setInputError] = useState('');

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Get theme
    const theme = useTheme();

    // Initialize steps when component mounts or params change
    useEffect(() => {
        if (params) {
            setVertices(params.vertices || 5);
            setDensity(params.density || 0.7);
            setMinWeight(params.minWeight || 1);
            setMaxWeight(params.maxWeight || 10);
            setAllowNegative(params.allowNegative || false);
        }
    }, [params]);

    // Generate initial steps when component mounts
    useEffect(() => {
        // Generate steps on initial mount
        generateSteps();
    }, []);

    // Generate steps when parameters change - only when Apply button is clicked
    const generateSteps = () => {
        if (vertices) {
            const result = generateFloydWarshallSteps({
                vertices,
                density,
                minWeight,
                maxWeight,
                allowNegative
            });

            // Set steps and movements if the parent component provides these functions
            if (typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.movement));
            }
        }
    };

    // Scroll to current step in the movement sequence
    useEffect(() => {
        if (movementContainerRef.current) {
            const container = movementContainerRef.current;
            const activeElement = container.querySelector('.active-step');

            if (activeElement) {
                // Calculate the scroll position to keep it within the container
                const containerRect = container.getBoundingClientRect();
                const activeRect = activeElement.getBoundingClientRect();

                // Check if the active element is outside the visible area
                if (activeRect.top < containerRect.top || activeRect.bottom > containerRect.bottom) {
                    // Scroll only within the container
                    container.scrollTop = activeElement.offsetTop - container.offsetTop - (containerRect.height / 2) + (activeRect.height / 2);
                }
            }
        }
    }, [step]);

    // Handle parameter changes
    const handleVerticesChange = (e) => {
        const value = parseInt(e.target.value, 10);
        if (value >= 2 && value <= 10) {
            setVertices(value);
            setIsCustomInput(true);
            setInputError('');
        } else {
            setInputError('Vertices must be between 2 and 10');
        }
    };

    const handleDensityChange = (e, newValue) => {
        setDensity(newValue);
        setIsCustomInput(true);
    };

    const handleMinWeightChange = (e) => {
        const value = parseInt(e.target.value, 10);
        if (value >= 1 && value <= maxWeight) {
            setMinWeight(value);
            setIsCustomInput(true);
            setInputError('');
        } else {
            setInputError('Min weight must be between 1 and max weight');
        }
    };

    const handleMaxWeightChange = (e) => {
        const value = parseInt(e.target.value, 10);
        if (value >= minWeight) {
            setMaxWeight(value);
            setIsCustomInput(true);
            setInputError('');
        } else {
            setInputError('Max weight must be greater than or equal to min weight');
        }
    };

    const handleAllowNegativeChange = (e) => {
        setAllowNegative(e.target.checked);
        setIsCustomInput(true);
    };

    // Apply custom parameters
    const handleApplyParams = () => {
        if (inputError) {
            return;
        }

        // Update params
        onParamChange({
            ...params,
            vertices,
            density,
            minWeight,
            maxWeight,
            allowNegative
        });

        // Generate steps with the new parameters
        generateSteps();

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle speed change
    const handleSpeedChange = (_, newValue) => {
        setSpeed(newValue);
    };

    // Handle control button clicks
    const handleStart = () => {
        // If we're at step 0, make sure we're showing the first step
        if (step === 0) {
            setStep(0);
        }

        // Set state to running after a small delay to ensure UI updates
        setTimeout(() => {
            setState('running');
        }, 0);
    };

    const handlePause = () => {
        setState('paused');
    };

    const handleReset = () => {
        setStep(0);
        setState('idle');
    };

    const handleStepForward = () => {
        if (step < totalSteps - 1) {
            setState('paused');
            setStep(step + 1);
        }
    };

    const handleStepBackward = () => {
        if (step > 0) {
            setState('paused');
            setStep(step - 1);
        }
    };

    // Calculate progress percentage
    const progressPercentage = totalSteps > 0 ? ((step + 1) / totalSteps) * 100 : 0;

    // Pseudocode for Floyd-Warshall algorithm
    const pseudocode = [
        "function FloydWarshall(graph):",
        "    // Initialize distance matrix with direct edge weights",
        "    dist = copy(graph)",
        "",
        "    n = length(dist)",
        "",
        "    // Consider each vertex as an intermediate",
        "    for k = 0 to n-1:",
        "        for i = 0 to n-1:",
        "            for j = 0 to n-1:",
        "                // If path through k is shorter than direct path",
        "                if dist[i][k] + dist[k][j] < dist[i][j]:",
        "                    dist[i][j] = dist[i][k] + dist[k][j]",
        "",
        "    // Check for negative cycles",
        "    for i = 0 to n-1:",
        "        if dist[i][i] < 0:",
        "            return \"Graph contains negative cycle\"",
        "",
        "    return dist"
    ];

    return (
        <Stack spacing={1.5} sx={{ p: 1.5, height: '100%', overflowY: 'auto', bgcolor: 'background.default', borderRadius: 1 }}>
            {/* Algorithm Title */}
            <Typography variant="h6" gutterBottom>
                Floyd-Warshall Algorithm
            </Typography>

            {/* Collapsible Information Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        Information
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setInfoExpanded(!infoExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {infoExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={infoExpanded} sx={{ mb: 1.5 }}>
                    <Box sx={{ p: 1, bgcolor: 'action.hover', borderRadius: 1, fontSize: '0.85rem' }}>
                        <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                            About Floyd-Warshall Algorithm:
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            The Floyd-Warshall algorithm is an all-pairs shortest path algorithm that works on weighted graphs, including those with negative edge weights (but not negative cycles). It computes the shortest paths between all pairs of vertices in a single execution.
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                            Time Complexity:
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 0.5 }}>
                            - O(V³) where V is the number of vertices
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                            Space Complexity:
                        </Typography>
                        <Typography variant="body2">
                            - O(V²) for the distance matrix
                        </Typography>
                    </Box>
                </Collapse>
            </Paper>

            {/* Parameters Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                        <ViewArrayIcon fontSize="small" sx={{ mr: 0.5 }} /> Parameters
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setParamsExpanded(!paramsExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {paramsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={paramsExpanded}>
                    <Stack spacing={2}>
                        <Box>
                            <TextField
                                label="Number of Vertices"
                                type="number"
                                value={vertices}
                                onChange={handleVerticesChange}
                                fullWidth
                                margin="dense"
                                variant="outlined"
                                error={!!inputError && inputError.includes('Vertices')}
                                helperText={inputError && inputError.includes('Vertices') ? inputError : ''}
                                size="small"
                                InputProps={{ inputProps: { min: 2, max: 10 } }}
                            />
                        </Box>

                        <Box>
                            <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                                Edge Density: {density.toFixed(2)}
                            </Typography>
                            <Slider
                                value={density}
                                onChange={handleDensityChange}
                                aria-labelledby="density-slider"
                                valueLabelDisplay="auto"
                                step={0.1}
                                marks
                                min={0.1}
                                max={1}
                            />
                        </Box>

                        <Box sx={{ display: 'flex', gap: 2 }}>
                            <TextField
                                label="Min Weight"
                                type="number"
                                value={minWeight}
                                onChange={handleMinWeightChange}
                                fullWidth
                                margin="dense"
                                variant="outlined"
                                error={!!inputError && inputError.includes('Min weight')}
                                helperText={inputError && inputError.includes('Min weight') ? inputError : ''}
                                size="small"
                                InputProps={{ inputProps: { min: 1 } }}
                            />

                            <TextField
                                label="Max Weight"
                                type="number"
                                value={maxWeight}
                                onChange={handleMaxWeightChange}
                                fullWidth
                                margin="dense"
                                variant="outlined"
                                error={!!inputError && inputError.includes('Max weight')}
                                helperText={inputError && inputError.includes('Max weight') ? inputError : ''}
                                size="small"
                                InputProps={{ inputProps: { min: minWeight } }}
                            />
                        </Box>

                        <FormControlLabel
                            control={
                                <Switch
                                    checked={allowNegative}
                                    onChange={handleAllowNegativeChange}
                                    color="primary"
                                />
                            }
                            label="Allow Negative Weights"
                        />

                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleApplyParams}
                            disabled={!isCustomInput || !!inputError || state === 'running'}
                            sx={{ mt: 1 }}
                            fullWidth
                        >
                            Generate New Graph
                        </Button>
                    </Stack>
                </Collapse>
            </Paper>

            {/* Controls Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                        <SettingsIcon fontSize="small" sx={{ mr: 0.5 }} /> Controls
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setControlsExpanded(!controlsExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {controlsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={controlsExpanded}>
                    <Stack spacing={2}>
                        {/* Speed Control */}
                        <Box>
                            <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                                Speed
                            </Typography>
                            <Slider
                                value={speed}
                                onChange={handleSpeedChange}
                                aria-labelledby="speed-slider"
                                valueLabelDisplay="auto"
                                step={1}
                                marks
                                min={1}
                                max={10}
                            />
                        </Box>

                        {/* Play/Pause and Reset Buttons */}
                        <Stack direction="row" spacing={2} sx={{ '& > span': { flex: 1 }, '& button': { width: '100%' } }}>
                            {state === 'running' ? (
                                <Tooltip title="Pause">
                                    <span>
                                        <Button
                                            variant="contained"
                                            color="warning"
                                            startIcon={<PauseIcon />}
                                            onClick={handlePause}
                                        >
                                            Pause
                                        </Button>
                                    </span>
                                </Tooltip>
                            ) : (
                                <Tooltip title="Play">
                                    <span>
                                        <Button
                                            variant="contained"
                                            color="primary"
                                            startIcon={<PlayArrowIcon />}
                                            onClick={handleStart}
                                            disabled={step >= totalSteps - 1}
                                        >
                                            Play
                                        </Button>
                                    </span>
                                </Tooltip>
                            )}

                            <Tooltip title="Reset">
                                <span>
                                    <Button
                                        variant="outlined"
                                        startIcon={<RestartAltIcon />}
                                        onClick={handleReset}
                                        disabled={state === 'idle' && step === 0}
                                    >
                                        Reset
                                    </Button>
                                </span>
                            </Tooltip>
                        </Stack>

                        {/* Step Forward/Backward Buttons */}
                        <Stack direction="row" spacing={2} sx={{ '& > span': { flex: 1 }, '& button': { width: '100%' } }}>
                            <Tooltip title="Step Backward">
                                <span>
                                    <Button
                                        variant="outlined"
                                        startIcon={<SkipPreviousIcon />}
                                        onClick={handleStepBackward}
                                        disabled={step <= 0 || state === 'running'}
                                    >
                                        Back
                                    </Button>
                                </span>
                            </Tooltip>

                            <Tooltip title="Step Forward">
                                <span>
                                    <Button
                                        variant="outlined"
                                        endIcon={<SkipNextIcon />}
                                        onClick={handleStepForward}
                                        disabled={step >= totalSteps - 1 || state === 'running'}
                                    >
                                        Next
                                    </Button>
                                </span>
                            </Tooltip>
                        </Stack>
                    </Stack>
                </Collapse>
            </Paper>

            {/* Progress Indicator Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                        <TimelineIcon fontSize="small" sx={{ mr: 0.5 }} /> Progress
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setProgressExpanded(!progressExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {progressExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={progressExpanded}>
                    <Stack spacing={2}>
                        {/* Progress Bar */}
                        <Box sx={{ p: 1.5, bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
                            <Stack spacing={1.5}>
                                {/* Step Counter with Circular Progress */}
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                                        <CircularProgress
                                            variant="determinate"
                                            value={Math.round(progressPercentage) || 0}
                                            size={60}
                                            thickness={5}
                                            sx={{
                                                color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                                                '& .MuiCircularProgress-circle': {
                                                    strokeLinecap: 'round',
                                                },
                                            }}
                                        />
                                        <Box
                                            sx={{
                                                top: 0,
                                                left: 0,
                                                bottom: 0,
                                                right: 0,
                                                position: 'absolute',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Typography variant="caption" component="div" sx={{
                                                fontWeight: 'bold',
                                                color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.dark
                                            }}>
                                                {`${Math.round(progressPercentage) || 0}%`}
                                            </Typography>
                                        </Box>
                                    </Box>

                                    <Box sx={{ flex: 1 }}>
                                        <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                                            Step: {step + 1} / {totalSteps}
                                        </Typography>
                                        <LinearProgress
                                            variant="determinate"
                                            value={Math.round(progressPercentage) || 0}
                                            sx={{
                                                height: 8,
                                                borderRadius: 4,
                                                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                                                '& .MuiLinearProgress-bar': {
                                                    borderRadius: 4,
                                                    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                                                },
                                            }}
                                        />
                                    </Box>
                                </Stack>

                                {/* Status Indicator */}
                                <Box sx={{
                                    p: 1,
                                    borderRadius: 1,
                                    bgcolor: theme.palette.mode === 'dark' ?
                                        (state === 'idle' ? 'rgba(41, 182, 246, 0.2)' :
                                            state === 'running' ? 'rgba(76, 175, 80, 0.2)' :
                                                state === 'paused' ? 'rgba(255, 152, 0, 0.2)' :
                                                    state === 'completed' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(0, 0, 0, 0.1)') :
                                        (state === 'idle' ? 'rgba(41, 182, 246, 0.1)' :
                                            state === 'running' ? 'rgba(76, 175, 80, 0.1)' :
                                                state === 'paused' ? 'rgba(255, 152, 0, 0.1)' :
                                                    state === 'completed' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(0, 0, 0, 0.05)'),
                                    color: theme.palette.mode === 'dark' ?
                                        (state === 'idle' ? '#29B6F6' :
                                            state === 'running' ? '#4CAF50' :
                                                state === 'paused' ? '#FF9800' :
                                                    state === 'completed' ? '#4CAF50' : theme.palette.text.primary) :
                                        (state === 'idle' ? '#0288D1' :
                                            state === 'running' ? '#2E7D32' :
                                                state === 'paused' ? '#E65100' :
                                                    state === 'completed' ? '#2E7D32' : theme.palette.text.primary),
                                }}>
                                    <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
                                        {state === 'idle' && <HourglassEmptyIcon fontSize="small" />}
                                        {state === 'running' && <CircularProgress size={16} thickness={5} sx={{ color: 'inherit' }} />}
                                        {state === 'paused' && <PendingIcon fontSize="small" />}
                                        {state === 'completed' && <CheckCircleIcon fontSize="small" />}
                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                            Status: {state.charAt(0).toUpperCase() + state.slice(1)}
                                        </Typography>
                                    </Stack>
                                </Box>
                            </Stack>
                        </Box>
                    </Stack>
                </Collapse>
            </Paper>

            {/* Steps Sequence Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, mb: 1.5 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                        <FormatListNumberedIcon fontSize="small" sx={{ mr: 0.5 }} /> Steps Sequence
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setMovementExpanded(!movementExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {movementExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={movementExpanded}>
                    <Box
                        ref={movementContainerRef}
                        sx={{
                            height: '200px', // Fixed height
                            overflowY: 'auto',
                            p: 1,
                            bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                            borderRadius: 1,
                            '&::-webkit-scrollbar': {
                                width: '8px',
                                backgroundColor: 'transparent',
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                                borderRadius: '4px',
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                            },
                            // Add shadow to indicate scrollable content
                            boxShadow: 'inset 0 -10px 10px -10px rgba(0, 0, 0, 0.1)'
                        }}
                    >
                        {/* Only show steps if we've started the algorithm */}
                        {(state !== 'idle' ? steps.slice(0, step + 1) : []).map((stepData, index) => (
                            <Box
                                key={index}
                                className={index === step ? 'active-step' : ''}
                                sx={{
                                    p: 1,
                                    mb: 0.5,
                                    borderRadius: 1,
                                    bgcolor: index === step
                                        ? theme.palette.mode === 'dark' ? 'primary.dark' : 'primary.light'
                                        : 'transparent',
                                    color: index === step
                                        ? theme.palette.mode === 'dark' ? 'white' : 'primary.contrastText'
                                        : 'text.primary',
                                    transition: 'background-color 0.3s',
                                    cursor: 'pointer',
                                    '&:hover': {
                                        bgcolor: index !== step
                                            ? theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                                            : null
                                    }
                                }}
                                onClick={() => setStep(index)}
                            >
                                <Typography
                                    variant="body2"
                                    sx={{ fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace', fontSize: '0.85rem' }}
                                >
                                    {index + 1}. {
                                        stepData.type === 'compare' ? (
                                            <>
                                                Compare path from <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>{stepData.i}</Box> to <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>{stepData.j}</Box> via <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold' }}>{stepData.k}</Box>
                                            </>
                                        ) : stepData.type === 'update' ? (
                                            <>
                                                Update distance from <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>{stepData.i}</Box> to <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold' }}>{stepData.j}</Box> to <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>{stepData.newDist}</Box>
                                            </>
                                        ) : stepData.type === 'new_intermediate' ? (
                                            <>
                                                Consider vertex <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold' }}>{stepData.k}</Box> as intermediate
                                            </>
                                        ) : (
                                            stepData.movement
                                        )
                                    }
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Collapse>
            </Paper>

            {/* Algorithm Section */}
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
                <Box sx={{ position: 'relative', mb: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                        <CodeIcon fontSize="small" sx={{ mr: 0.5 }} /> Algorithm
                    </Typography>
                    <IconButton
                        size="small"
                        onClick={() => setAlgorithmExpanded(!algorithmExpanded)}
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: -10,
                            transform: 'translateY(-50%)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': { bgcolor: 'background.paper' }
                        }}
                    >
                        {algorithmExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={algorithmExpanded}>
                    <Box
                        sx={{
                            bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                            p: 1.5,
                            borderRadius: 1,
                            fontFamily: 'monospace',
                            fontSize: '0.9rem',
                            overflowX: 'auto',
                            mb: 2
                        }}
                    >
                        {pseudocode.map((line, index) => (
                            <Typography
                                key={index}
                                component="div"
                                sx={{
                                    whiteSpace: 'pre',
                                    color: line.includes('//') ? 'text.secondary' : 'text.primary',
                                    fontWeight: line.includes('function') ? 'bold' : 'normal',
                                    ml: line.startsWith(' ') ? 2 : 0
                                }}
                            >
                                {line || ' '}
                            </Typography>
                        ))}
                    </Box>
                </Collapse>
            </Paper>
        </Stack>
    );
};

export default FloydWarshallController;