// RadixSortConfig.js
// Configuration file for RadixSort algorithm visualization

const CONFIG = {
  // Main array configuration
  mainArray: {
    // Bar configuration (consistent with other sorting algorithms)
    bars: {
      width: 0.6,                    // Width of each bar (same as other sorting algorithms)
      spacing: 0.3,                  // Spacing between bars (same as other sorting algorithms)
      maxHeight: 3.8,                // Maximum height for bars (same as other sorting algorithms)
      baseOffset: [0, 0, 0],         // Offset from base platform
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance

      // Bar geometry (consistent with other sorting algorithms)
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties (consistent with other sorting algorithms)
      material: {
        roughness: 0.6,              // Material roughness (0 = mirror, 1 = rough)
        metalness: 0.2,              // Material metalness (0 = non-metal, 1 = metal)
        opacity: 1.0,                // Material opacity
        transparent: false,          // Enable transparency
      },

      // Base platform under each bar (consistent with other sorting algorithms)
      base: {
        enabled: true,               // Enable/disable base platform
        height: 0.05,                // Height of base platform
        widthScale: 1.0,             // Width scale factor for base
        depthScale: 1.0,             // Depth scale factor for base
        material: {
          roughness: 0.8,            // Base material roughness
          metalness: 0.2,            // Base material metalness
          opacity: 0.7,              // Base opacity
          transparent: false,        // Enable transparency
        },
      },
    },

    // Value labels configuration (consistent with other sorting algorithms)
    valueLabels: {
      enabled: true,                 // Enable/disable value labels
      offset: [0, 0.3, 0],          // Offset from bar top [x, y, z]
      fontSize: '0.6rem',            // Font size for value labels (same as other sorting algorithms)
      fontWeight: 'bold',            // Font weight (same as other sorting algorithms)
      padding: {
        horizontal: 0.5,             // Horizontal padding inside label
        vertical: 0.1,               // Vertical padding inside label
      },
      borderRadius: 1.5,             // Border radius factor (boxy style like other sorting algorithms)
      minWidth: '16px',              // Minimum width of label container
      elevation: 1,                  // Material-UI elevation
    },

    // Index labels configuration (consistent with other sorting algorithms)
    indexLabels: {
      enabled: true,                 // Enable/disable index labels
      offset: [0, 0.1, 0.4],         // Offset from bar base [x, y, z] (same as other sorting algorithms)
      fontSize: '10px',              // Font size for index labels (same as other sorting algorithms)
      fontWeight: 'normal',          // Font weight (same as other sorting algorithms)
      size: {
        width: '20px',               // Width of circular index label (same as other sorting algorithms)
        height: '20px',              // Height of circular index label (same as other sorting algorithms)
      },
      elevation: 1,                  // Material-UI elevation
    },

    // Animation configuration
    animation: {
      enabled: true,                 // Enable/disable animations
      duration: 800,                 // Base animation duration (ms)
      easing: 'easeInOutCubic',      // Animation easing function
      stagger: 100,                  // Stagger delay between elements (ms)
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation
      amplitude: 0.1,                // Levitation amplitude
      frequency: 0.5,                // Levitation frequency
      offset: 0,                     // Phase offset
    },
  },

  // Base platform configuration (consistent with other sorting algorithms)
  basePlatform: {
    dimensions: {
      height: 0.,                   // Height of the base platform (same as other sorting algorithms)
      lengthPadding: {
        left: 1,                     // Padding on the left side (same as other sorting algorithms)
        right: 1,                    // Padding on the right side (same as other sorting algorithms)
      },
      depth: 3,                      // Depth of the base platform (same as other sorting algorithms)
    },
    position: [0, -5, 0],            // MASTER position - controls entire main array group [x, y, z] (same as other sorting algorithms)
    material: {
      roughness: 0.8,                // Material roughness (same as other sorting algorithms)
      metalness: 0.1,                // Material metalness (same as other sorting algorithms)
    },
  },

  // ==================== CAMERA CONFIGURATION ====================
  camera: {
    position: [0, 4, 14],            // Default camera position [x, y, z] - slightly higher and further for bucket view
    lookAt: [0, -1, -2],             // Camera look-at point [x, y, z] - adjusted to center on main array and buckets
    fov: 55,                         // Field of view in degrees (same as other sorting algorithms)
    near: 0.1,                       // Near clipping plane (same as other sorting algorithms)
    far: 1000,                       // Far clipping plane (same as other sorting algorithms)

    // Dynamic positioning based on array size
    dynamicPositioning: {
      enabled: true,                 // Enable dynamic camera positioning
      minDistance: 10,               // Minimum camera distance (increased for bucket visibility)
      paddingFactor: 2.2,            // Padding factor for camera distance calculation (slightly increased)
      heightOffset: 4,               // Additional height offset (increased for better bucket view)
    },
  },

  // ==================== LIGHTING CONFIGURATION ====================
  lighting: {
    ambient: {
      intensity: 0.6,                // Ambient light intensity (same as other sorting algorithms)
      color: '#ffffff',              // Ambient light color (same as other sorting algorithms)
    },
    directional: {
      position: [10, 10, 5],         // Directional light position [x, y, z] (same as other sorting algorithms)
      intensity: 1.0,                // Directional light intensity (same as other sorting algorithms)
      color: '#ffffff',              // Directional light color (same as other sorting algorithms)
      castShadow: true,              // Whether directional light casts shadows (same as other sorting algorithms)
    },
    // Additional lights can be added here
    pointLights: [],                 // Array of point light configurations
    spotLights: [],                  // Array of spot light configurations
  },

  // Buckets configuration for RadixSort
  buckets: {
    // Bucket arrangement
    arrangement: {
      type: 'semicircle',            // Arrangement type: 'semicircle', 'grid', 'line'
      radius: -7.0,                   // Radius for semicircle arrangement
      position: [0, 6, -2],         // Base position for bucket arrangement [x, y, z]
      angleRange: Math.PI,           // Angle range for semicircle (π = 180°)
      startAngle: 0,                 // Starting angle for arrangement
    },

    // Individual bucket configuration
    bucket: {
      width: 1.5,                    // Width of each bucket
      height: 1.5,                   // Height of each bucket
      depth: 0.6,                    // Depth of each bucket
      wallThickness: 0.1,            // Thickness of bucket walls
      borderRadius: 0.05,            // Border radius for bucket edges
    },

    // Bucket labels
    labels: {
      enabled: true,                 // Enable/disable bucket labels
      offset: [0, -0.5, 0],         // Offset from bucket base [x, y, z]
      fontSize: '0.6rem',              // Font size for bucket labels
      fontWeight: 600,               // Font weight
      padding: {
        horizontal: 0.8,             // Horizontal padding
        vertical: 0.4,               // Vertical padding
      },
      minWidth: '2rem',            // Minimum width for labels
      borderRadius: 1.5,             // Border radius divisor
      elevation: 4,                  // Material-UI elevation
    },

    // Items in buckets
    items: {
      enabled: true,                 // Enable/disable showing items in buckets
      maxVisible: 5,                 // Maximum number of visible items per bucket
      spacing: 0.4,                 // Spacing between items in bucket
      scale: 0.8,                    // Scale factor for items in buckets
      offset: [0, 0.2, 0],          // Offset for items in bucket [x, y, z]

      // Item value labels configuration
      valueLabels: {
        enabled: true,               // Enable/disable item value labels
        offset: [0, 0.3, 0],        // Label position offset [x, y, z]
        fontSize: '0.6rem',          // Font size for item labels
        fontWeight: 'bold',          // Font weight for item labels
        elevation: 2,                // Material-UI elevation
        padding: {
          horizontal: 0.5,           // Horizontal padding (theme.spacing units)
          vertical: 0.2,             // Vertical padding (theme.spacing units)
        },
        minWidth: '16px',            // Minimum width for labels
        borderRadius: 2,             // Border radius divisor (theme.shape.borderRadius / this)
      },

      // Count labels configuration (for "+X more" labels)
      countLabels: {
        enabled: true,               // Enable/disable count labels
        offset: [0, 0.3, 0],        // Label position offset relative to bucket height [x, y, z]
        fontSize: '0.6rem',          // Font size for count labels
        fontWeight: 'bold',          // Font weight for count labels
        color: 'text.secondary',     // Text color (theme palette reference)
      },
    },

    // Bucket animations
    animation: {
      enabled: true,                 // Enable/disable bucket animations
      duration: 600,                 // Animation duration (ms)
      highlightDuration: 300,        // Highlight animation duration (ms)
      easing: 'easeInOutQuad',       // Animation easing function
    },
  },

  // Step board configuration (consistent with other sorting algorithms)
  stepBoard: {
    enabled: true,                   // Enable/disable step board
    position: [0, 5, 0.5],          // Position of the step board [x, y, z] (same as other sorting algorithms)
    dimensions: {
      width: 12,                     // Width of the step board (same as other sorting algorithms)
      height: 1.5,                   // Height of the step board (same as other sorting algorithms)
      depth: 0.1,                    // Depth of the step board (same as other sorting algorithms)
    },
    material: {
      opacity: 0.9,                  // Material opacity (same as other sorting algorithms)
      transparent: true,             // Enable transparency (same as other sorting algorithms)
    },
    text: {
      fontSize: 'h6',                // Material-UI typography variant (same as other sorting algorithms)
      fontWeight: 'bold',            // Font weight (same as other sorting algorithms)
      align: 'center',               // Text alignment (same as other sorting algorithms)
      padding: 2,                    // Padding around text (same as other sorting algorithms)
    },
    border: {
      enabled: true,                 // Enable border (same as other sorting algorithms)
      width: 2,                      // Border width (same as other sorting algorithms)
      radius: 2,                     // Border radius (same as other sorting algorithms)
    },
    elevation: 3,                    // Material-UI elevation (same as other sorting algorithms)
  },

  // Color legend configuration
  colorLegend: {
    enabled: true,                   // Enable/disable color legend
    position: [0, -3.5, 0.5],      // Position of color legend [x, y, z]
    itemSpacing: 0.3,                // Spacing between legend items
    fontSize: '0.85rem',             // Font size for legend text
    fontWeight: 500,                 // Font weight for legend text
    padding: 0.8,                    // Padding inside legend
    borderRadius: 2,                 // Border radius divisor
    elevation: 4,                    // Material-UI elevation
    maxItemsPerRow: 6,               // Maximum items per row

    // Legend items definition - Only colors actually used in RadixSort
    legendItems: [
      { colorKey: 'bar', label: 'Default' },
      { colorKey: 'current', label: 'Current Element' },
      { colorKey: 'bucket', label: 'Bucket' },
      { colorKey: 'activeBucket', label: 'Active Bucket' },
      { colorKey: 'sorted', label: 'Sorted' }
    ],
  },

  // Colors configuration
  colors: {
    // Theme-based colors
    themes: {
      light: {
        // Bar colors
        bar: '#42a5f5',              // Default bar color
        current: '#9c27b0',          // Current element being processed
        sorted: '#4caf50',           // Sorted elements

        // Bucket colors
        bucket: '#ff9800',           // Default bucket color
        activeBucket: '#f44336',     // Active bucket being filled
        bucketItem: '#2196f3',       // Items inside buckets

        // Platform and base colors
        platform: '#616161',         // Base platform color
        base: '#757575',             // Base color for bars

        // UI colors
        stepBoard: '#ffffff',        // Step board background
        legend: '#ffffff',           // Legend background
        text: '#000000',             // Text color
        border: '#e0e0e0',           // Border color
      },
      dark: {
        // Bar colors
        bar: '#64b5f6',              // Default bar color
        current: '#ba68c8',          // Current element being processed
        sorted: '#66bb6a',           // Sorted elements

        // Bucket colors
        bucket: '#ffb74d',           // Default bucket color
        activeBucket: '#ef5350',     // Active bucket being filled
        bucketItem: '#42a5f5',       // Items inside buckets

        // Platform and base colors
        platform: '#424242',         // Base platform color
        base: '#616161',             // Base color for bars

        // UI colors
        stepBoard: '#2d2d2d',        // Step board background
        legend: '#2d2d2d',           // Legend background
        text: '#ffffff',             // Text color
        border: '#424242',           // Border color
      },
    },
  },

  // Visual effects configuration
  visual: {
    // Effects
    effects: {
      shadows: true,                 // Enable/disable shadows
      reflections: false,            // Enable/disable reflections
      bloom: false,                  // Enable/disable bloom effect
      antialiasing: true,            // Enable/disable antialiasing
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation animation
      disableDuringSimulation: true, // Disable levitation when algorithm is running
      amplitude: 0.1,                // Height of levitation movement (Y-axis)
      frequency: 1.0,                // Speed of levitation oscillation

      // Multi-axis movement
      movement: {
        y: {
          enabled: true,             // Enable Y-axis (vertical) movement
          amplitude: 0.1,            // Vertical movement amplitude
          frequency: 1.0,            // Vertical movement frequency
        },
        x: {
          enabled: false,            // Enable X-axis (horizontal) movement
          amplitude: 0.05,           // Horizontal movement amplitude
          frequency: 0.8,            // Horizontal movement frequency
        },
        z: {
          enabled: false,            // Enable Z-axis (depth) movement
          amplitude: 0.03,           // Depth movement amplitude
          frequency: 0.6,            // Depth movement frequency
        },
      },

      // Rotation effects
      rotation: {
        enabled: true,               // Enable rotation during levitation
        x: {
          enabled: true,             // Enable X-axis rotation
          amplitude: 0.01,           // X rotation amplitude (radians)
          frequency: 0.3,            // X rotation frequency
        },
        y: {
          enabled: true,             // Enable Y-axis rotation
          amplitude: 0.02,           // Y rotation amplitude (radians)
          frequency: 0.5,            // Y rotation frequency
        },
        z: {
          enabled: false,            // Enable Z-axis rotation
          amplitude: 0.005,          // Z rotation amplitude (radians)
          frequency: 0.4,            // Z rotation frequency
        },
      },

      // Advanced settings
      staggered: true,               // Enable staggered animation effects
      staggerDelay: 0.2,             // Delay between elements for staggered effect
      smoothTransition: true,        // Smooth transition when enabling/disabling
      transitionDuration: 1000,      // Duration for smooth transitions (ms)
    },
  },

  // Animation configuration
  animation: {
    // General animation settings
    enabled: true,                   // Enable/disable all animations

    // Move animations (bars moving to/from buckets)
    move: {
      enabled: true,                 // Enable move animations
      duration: 1000,                // Duration for move animations (ms)
      arcHeight: 2.0,                // Height of movement arc
      easing: 'ease-in-out',         // Easing function
    },

    // Placement animations (items being placed in buckets)
    placement: {
      enabled: true,                 // Enable placement animations
      duration: 800,                 // Duration for placement animations (ms)
      scaleEffect: true,             // Scale effect during placement
      scaleAmount: 1.2,              // Scale multiplier during placement
    },

    // Collection animations (items being collected from buckets)
    collection: {
      enabled: true,                 // Enable collection animations
      duration: 800,                 // Duration for collection animations (ms)
      fadeEffect: true,              // Fade effect during collection
    },

    // Highlight animations
    highlight: {
      enabled: true,                 // Enable highlight animations
      duration: 300,                 // Duration for highlight changes
      pulseEffect: true,             // Enable pulse effect for highlights
    },
  },

  // Speed and timing configuration
  speed: {
    baseDelay: 1000,                 // Base delay in milliseconds
    minDelay: 100,                   // Minimum delay
    maxDelay: 3000,                  // Maximum delay
    animationDuration: 800,          // Animation duration
    enhancedCurve: true,             // Use enhanced exponential curve
  },

  // Responsive configuration
  responsive: {
    enabled: true,                   // Enable responsive behavior
    breakpoints: {
      small: 768,                    // Small screen breakpoint
      medium: 1024,                  // Medium screen breakpoint
      large: 1440,                   // Large screen breakpoint
    },
    scaling: {
      small: 0.7,                    // Scale factor for small screens
      medium: 0.85,                  // Scale factor for medium screens
      large: 1.0,                    // Scale factor for large screens
    },
  },
};

export default CONFIG;
