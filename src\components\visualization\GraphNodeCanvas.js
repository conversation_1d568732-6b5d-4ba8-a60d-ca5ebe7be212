// GraphNodeCanvas.js
// A reusable component for graph nodes in 3D visualizations with canvas-based labels

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import * as THREE from 'three';

const GraphNodeCanvas = ({
  position = [0, 0, 0],
  label = '',
  color = '#2196f3',
  isStart = false,
  isHighlighted = false,
  distance,
  radius = 0.8,
  segments = 32,
  onClick,
  showDistance = true,
  startIndicatorColor = '#4caf50'
}) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';
  const meshRef = useRef();
  const labelRef = useRef();
  const distanceRef = useRef();

  // Create canvas texture for node label
  const labelTexture = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 128; // Increased resolution
    canvas.height = 64; // Increased resolution
    const context = canvas.getContext('2d');

    // Clear canvas
    context.clearRect(0, 0, canvas.width, canvas.height);

    // Background for better readability
    context.fillStyle = isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)';
    context.beginPath();
    context.roundRect(0, 0, canvas.width, canvas.height, 16);
    context.fill();

    // Text with outline for better visibility
    context.strokeStyle = isDark ? '#000000' : '#ffffff';
    context.lineWidth = 4;
    context.font = 'bold 36px Arial'; // Larger font
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.strokeText(label, canvas.width / 2, canvas.height / 2);

    context.fillStyle = isDark ? '#ffffff' : '#000000';
    context.fillText(label, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, [label, isDark]);

  // Create canvas texture for distance label
  const distanceTexture = useMemo(() => {
    if (!showDistance || distance === undefined) return null;

    const canvas = document.createElement('canvas');
    canvas.width = 128; // Increased resolution
    canvas.height = 64; // Increased resolution
    const context = canvas.getContext('2d');

    // Background with border
    context.fillStyle = isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)';
    context.beginPath();
    context.roundRect(0, 0, canvas.width, canvas.height, 16);
    context.fill();

    // Add border
    context.strokeStyle = isDark ? '#ffffff' : '#000000';
    context.lineWidth = 2;
    context.beginPath();
    context.roundRect(2, 2, canvas.width - 4, canvas.height - 4, 14);
    context.stroke();

    // Text with outline for better visibility
    context.strokeStyle = isDark ? '#000000' : '#ffffff';
    context.lineWidth = 3;
    context.font = 'bold 28px Arial'; // Larger font
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    const displayText = distance === Infinity ? '∞' : distance.toString();
    context.strokeText(displayText, canvas.width / 2, canvas.height / 2);

    context.fillStyle = isDark ? '#ffffff' : '#000000';
    context.fillText(displayText, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, [distance, showDistance, isDark]);

  // Pulse animation for highlighted nodes
  useFrame(() => {
    if (meshRef.current && isHighlighted) {
      meshRef.current.scale.x = 1 + 0.1 * Math.sin(Date.now() * 0.005);
      meshRef.current.scale.y = 1 + 0.1 * Math.sin(Date.now() * 0.005);
      meshRef.current.scale.z = 1 + 0.1 * Math.sin(Date.now() * 0.005);
    }

    // Make labels always face the camera
    if (labelRef.current) {
      labelRef.current.lookAt(labelRef.current.parent.worldToLocal(
        new THREE.Vector3(0, 0, 0)
      ));
    }

    if (distanceRef.current) {
      distanceRef.current.lookAt(distanceRef.current.parent.worldToLocal(
        new THREE.Vector3(0, 0, 0)
      ));
    }
  });

  return (
    <group position={position}>
      {/* Node sphere */}
      <mesh ref={meshRef} onClick={onClick}>
        <sphereGeometry args={[radius, segments, segments]} />
        <meshStandardMaterial
          color={color}
          emissive={isHighlighted ? color : '#000000'}
          emissiveIntensity={isHighlighted ? 0.5 : 0}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Node label using canvas texture */}
      <mesh
        ref={labelRef}
        position={[0, radius * 1.2, 0]} // Position above the node
        scale={[1.2, 0.6, 1]} // Larger scale for better visibility
      >
        <planeGeometry args={[1, 1]} />
        <meshBasicMaterial
          map={labelTexture}
          transparent={true}
          depthTest={false}
          side={THREE.DoubleSide}
          alphaTest={0.1} // Prevent transparency issues
        />
      </mesh>

      {/* Distance label using canvas texture */}
      {showDistance && distance !== undefined && (
        <mesh
          ref={distanceRef}
          position={[0, -radius * 1.5, 0]} // Position below the node, relative to radius
          scale={[1, 0.5, 1]} // Larger scale for better visibility
        >
          <planeGeometry args={[1, 1]} />
          <meshBasicMaterial
            map={distanceTexture}
            transparent={true}
            depthTest={false}
            side={THREE.DoubleSide}
            alphaTest={0.1} // Prevent transparency issues
          />
        </mesh>
      )}

      {/* Start node indicator */}
      {isStart && (
        <mesh position={[0, radius * 1.5, 0]}>
          <sphereGeometry args={[radius * 0.3, 16, 16]} />
          <meshStandardMaterial color={startIndicatorColor} />
        </mesh>
      )}
    </group>
  );
};

export default GraphNodeCanvas;
