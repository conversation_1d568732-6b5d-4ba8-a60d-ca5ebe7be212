// HeapSortSimulation.js - Clean heap sort visualization component
// Consumes visualizationData from controller with minimal complexity

import React, { useMemo, useEffect } from 'react';
import { Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ThemeHtml from '../ThemeHtml';
import HeapSortConfig from '../../../algorithms/Sorting/HeapSort/HeapSortConfig';

const HeapSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true,
  swapAnimation = { active: false, indices: [-1, -1], progress: 0 },
  onWidthChange
}) => {
  const theme = useTheme();

  // Get configuration for positioning
  const config = HeapSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // PROPER STATIC POSITIONING - Following QuickSort pattern
  const layoutData = useMemo(() => {
    if (!vizData?.mainArray?.values) return { barPositions: [], totalWidth: 0 };

    const values = vizData.mainArray.values;
    const baseSpacing = barWidth + barSpacing;
    const visualOffset = config.mainArray.bars.visualOffset;

    // Calculate STATIC bar positions - these never change
    const staticPositions = [];
    const barsOnlyWidth = (values.length * baseSpacing) - barSpacing;
    const startX = -barsOnlyWidth / 2 + visualOffset;

    for (let i = 0; i < values.length; i++) {
      staticPositions.push({
        index: i,
        xPos: startX + i * baseSpacing  // Static position - never changes
      });
    }

    // Calculate the ACTUAL total width covered by bars (from leftmost to rightmost edge)
    const leftmostEdge = staticPositions[0].xPos - barWidth / 2;
    const rightmostEdge = staticPositions[staticPositions.length - 1].xPos + barWidth / 2;
    const actualTotalWidth = rightmostEdge - leftmostEdge;

    // DEBUG: Log the calculations
    console.log('HeapSort Static Layout:', {
      values: values.map((v, i) => `${i}:${v}`),
      barsOnlyWidth,
      actualTotalWidth,
      leftmostEdge: leftmostEdge.toFixed(2),
      rightmostEdge: rightmostEdge.toFixed(2),
      startX: startX.toFixed(2),
      positions: staticPositions.map(p => `${p.index}:${p.xPos.toFixed(1)}`)
    });

    return {
      barPositions: staticPositions,
      totalWidth: actualTotalWidth
    };
  }, [vizData?.mainArray?.values, barWidth, barSpacing]);

  // Extract values
  const { barPositions, totalWidth: actualTotalWidth } = layoutData;

  // Notify parent of width changes for platform sizing
  useEffect(() => {
    if (onWidthChange && actualTotalWidth > 0) {
      onWidthChange(actualTotalWidth);
    }
  }, [actualTotalWidth, onWidthChange]);

  // If no visualization data, return null
  if (!vizData) {
    return null;
  }

  // Render the main array bars with heap sort states
  const renderMainArray = () => {
    if (!vizData.mainArray?.values) return null;

    // Use the step data directly - visualization layer controls what data we get
    const {
      values,
      sortedIndices = [],
      comparingIndices = [],
      swappingIndices = [],
      heapifiedIndices = []
    } = vizData.mainArray;

    // Log the data being rendered
    if (swapAnimation.active) {
      console.log('HeapSortSimulation - Rendering during swap animation:', {
        values: values,
        swappingIndices: swappingIndices,
        animationActive: swapAnimation.active,
        animationIndices: swapAnimation.indices,
        animationProgress: swapAnimation.progress
      });
    }

    const maxValue = Math.max(...values);

    return values.map((value, index) => {
      const barHeight = (value / maxValue) * maxBarHeight;

      // Determine bar color based on state using config colors
      let barColor = colors.default || colors.bar || config.colors.palette.default;

      if (sortedIndices.includes(index)) {
        barColor = colors.sorted || config.colors.palette.sorted;
      } else if (swappingIndices.includes(index)) {
        barColor = colors.swapping || config.colors.palette.swapping;
      } else if (comparingIndices.includes(index)) {
        barColor = colors.comparing || config.colors.palette.comparing;
      } else if (heapifiedIndices.includes(index)) {
        barColor = colors.heapified || config.colors.palette.heapified;
      }

      // Use pre-calculated bar position with swap animation
      const barPosition = barPositions.find(pos => pos.index === index);
      let xPos = barPosition ? barPosition.xPos : 0;
      let yPos = 0;
      let zPos = 0;

      // Apply swap animation if active
      if (swapAnimation.active && swapAnimation.indices.includes(index)) {
        const [i, j] = swapAnimation.indices;
        const { progress } = swapAnimation;

        // Determine if this is the first or second bar in the swap
        const isFirstBar = index === i;
        const otherIndex = isFirstBar ? j : i;

        // Get positions for both bars
        const currentBarPos = barPositions.find(pos => pos.index === index);
        const otherBarPos = barPositions.find(pos => pos.index === otherIndex);

        if (currentBarPos && otherBarPos) {
          const startX = currentBarPos.xPos;
          const endX = otherBarPos.xPos;
          const distance = endX - startX;
          const centerX = startX + distance / 2;
          const radius = Math.abs(distance) / 2;

          // Calculate position along arc path
          const angle = progress * Math.PI;

          // Each bar moves in opposite direction along the arc
          let newXPos, newYPos;
          if (isFirstBar) {
            // First bar moves from left to right
            newXPos = centerX + Math.cos(Math.PI - angle) * radius;
            newYPos = Math.sin(Math.PI - angle) * radius * config.animation.types.swap.height;
          } else {
            // Second bar moves from right to left (opposite direction)
            newXPos = centerX - Math.cos(Math.PI - angle) * radius;
            newYPos = Math.sin(Math.PI - angle) * radius * config.animation.types.swap.height;
          }

          const newZPos = Math.sin(angle) * 0.5; // Slight z-axis movement for 3D effect

          console.log(`Bar ${index} (value ${value}) animation:`, {
            originalPos: { x: startX, y: 0, z: 0 },
            targetPos: { x: endX, y: 0, z: 0 },
            currentPos: { x: newXPos, y: newYPos, z: newZPos },
            progress: progress,
            isFirstBar: isFirstBar,
            otherIndex: otherIndex,
            barHeight: barHeight,
            barColor: barColor,
            willRender: true
          });

          xPos = newXPos;
          yPos = newYPos;
          zPos = newZPos;
        }
      }

      // Log all bars being rendered
      if (swapAnimation.active && swapAnimation.indices.includes(index)) {
        console.log(`Rendering animating bar ${index} (value ${value}) at position [${xPos}, ${yPos}, ${zPos}]`);
      }

      return (
        <group key={`main-${index}-${value}`} position={[xPos, yPos, zPos]}>
          {/* Base of the bar (like QuickSort) */}
          {config.mainArray.bars.base.enabled && (
            <mesh
              position={[0, 0, 0]}
              castShadow={config.visual.effects.shadows}
              receiveShadow={config.visual.effects.shadows}
            >
              <boxGeometry args={[
                barWidth * config.mainArray.bars.base.widthScale,
                config.mainArray.bars.base.height,
                barWidth * config.mainArray.bars.base.depthScale
              ]} />
              <meshStandardMaterial
                color={barColor}
                transparent={config.mainArray.bars.base.material.transparent}
                opacity={config.mainArray.bars.base.material.opacity}
                roughness={config.mainArray.bars.base.material.roughness}
                metalness={config.mainArray.bars.base.material.metalness}
              />
            </mesh>
          )}

          {/* The bar itself (narrower, sitting on top of base) */}
          <mesh
            position={[0, barHeight / 2, 0]}
            castShadow={config.visual.effects.shadows}
            receiveShadow={config.visual.effects.shadows}
          >
            <boxGeometry args={[
              barWidth * config.mainArray.bars.geometry.widthScale,
              barHeight,
              barWidth * config.mainArray.bars.geometry.depthScale
            ]} />
            <meshStandardMaterial
              color={barColor}
              transparent={config.mainArray.bars.material.transparent}
              opacity={config.mainArray.bars.material.opacity}
              roughness={config.mainArray.bars.material.roughness}
              metalness={config.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {(() => {
            if (index === 0) {
              console.log('HeapSortSimulation showValues:', showValues, 'for value:', value);
            }
            return showValues;
          })() && (
            <ThemeHtml position={[config.mainArray.valueLabels.offset[0], barHeight + config.mainArray.valueLabels.offset[1], config.mainArray.valueLabels.offset[2]]} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.valueLabels.elevation}
                sx={{
                  px: theme.spacing(config.mainArray.valueLabels.padding.horizontal),
                  py: theme.spacing(config.mainArray.valueLabels.padding.vertical),
                  minWidth: config.mainArray.valueLabels.minWidth,
                  borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                  border: 1,
                  borderColor: (swappingIndices.includes(index) || comparingIndices.includes(index) || heapifiedIndices.includes(index)) ? barColor : 'divider',
                  bgcolor: 'background.paper',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.valueLabels.fontSize,
                    textAlign: 'center',
                    display: 'block',
                    fontWeight: (swappingIndices.includes(index) || comparingIndices.includes(index) || heapifiedIndices.includes(index)) ? "bold" : config.mainArray.valueLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}

          {/* Index label */}
          {showIndices && (
            <ThemeHtml position={config.mainArray.indexLabels.offset} center sprite occlude theme={theme}>
              <Paper
                elevation={config.mainArray.indexLabels.elevation}
                sx={{
                  width: config.mainArray.indexLabels.size.width,
                  height: config.mainArray.indexLabels.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: (swappingIndices.includes(index) || comparingIndices.includes(index) || heapifiedIndices.includes(index)) ? barColor : 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.mainArray.indexLabels.fontSize,
                    fontWeight: (swappingIndices.includes(index) || comparingIndices.includes(index) || heapifiedIndices.includes(index)) ? "bold" : config.mainArray.indexLabels.fontWeight
                  }}
                  color="text.primary"
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };

  // Render comparison arrows (like QuickSort - simple ↓ arrows above bars)
  const renderComparisonArrows = () => {
    if (!vizData.mainArray?.comparingIndices || vizData.mainArray.comparingIndices.length === 0) {
      return null;
    }

    const { comparingIndices } = vizData.mainArray;

    return comparingIndices.map((index) => {
      const barPosition = barPositions.find(pos => pos.index === index);
      const xPos = barPosition ? barPosition.xPos : 0;

      return (
        <group key={`arrow-${index}`} position={[xPos, 0, 0]}>
          {/* Comparison arrow - exactly like QuickSort */}
          <ThemeHtml
            position={[0, maxBarHeight + 1, 0]} // Above the bars
            center
            sprite
            theme={theme}
          >
            <div style={{
              color: colors.comparing || config.colors.palette.comparing,
              background: 'transparent',
              fontSize: '1.4rem', // Same as QuickSort
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: `0 0 8px ${colors.comparing || config.colors.palette.comparing}, 0 0 16px ${colors.comparing || config.colors.palette.comparing}`,
              userSelect: 'none',
              pointerEvents: 'none',
              transform: 'scale(1) rotate(0deg)', // Point down to bars
              animation: 'pulse 1s infinite alternate' // Same as QuickSort
            }}>
              ↓
            </div>
            <style>
              {`
              @keyframes pulse {
                0% { transform: scale(1.0) rotate(0deg); }
                100% { transform: scale(1.3) rotate(0deg); }
              }
              `}
            </style>
          </ThemeHtml>
        </group>
      );
    });
  };

  return (
    <group>
      {renderMainArray()}
      {renderComparisonArrows()}
    </group>
  );
};

export default HeapSortSimulation;
