// BFSController.js
// This component provides the controls for BFS algorithm visualization

import React, { useState, useEffect, useRef } from 'react';
import { useSpeed } from '../../../context/SpeedContext';
import { generateBFSSteps } from './BFSAlgorithm';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Stack,
  Tooltip,
  IconButton,
  Collapse,
  Switch,
  FormControlLabel,
  Divider,
  LinearProgress,
  CircularProgress,
  useTheme,
  Slider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import SpeedIcon from '@mui/icons-material/Speed';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PendingIcon from '@mui/icons-material/Pending';
import SettingsIcon from '@mui/icons-material/Settings';
import TimelineIcon from '@mui/icons-material/Timeline';
import CodeIcon from '@mui/icons-material/Code';
import GraphIcon from '@mui/icons-material/AccountTree';
import { useDebounce } from '../../../hooks/useDebounce';
import BFSAlgorithm from './BFSAlgorithm';

const BFSController = (props) => {
  // Create a ref for the movement sequence container
  const movementContainerRef = React.useRef(null);

  // Extract properties from the props object with safety checks
  const {
    params = {},
    onParamChange = () => { },
    state = 'idle',
    step = 0,
    setStep = () => { },
    totalSteps = 0,
    setTotalSteps = () => { },
    setState = () => { },
    steps = [],
    setMovements = () => { }
  } = props || {};

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Get theme
  const theme = useTheme();

  // UI state
  const [paramsExpanded, setParamsExpanded] = useState(true);
  const [controlsExpanded, setControlsExpanded] = useState(true);
  const [progressExpanded, setProgressExpanded] = useState(true);
  const [algorithmExpanded, setAlgorithmExpanded] = useState(true);
  const [stepsExpanded, setStepsExpanded] = useState(true);
  const [movements, setLocalMovements] = useState([]);

  // Graph parameters
  const [numNodes, setNumNodes] = useState(params?.nodes || 6);
  const [startNode, setStartNode] = useState(params?.startNode || 0);
  const [targetNode, setTargetNode] = useState(params?.targetNode !== undefined ? params.targetNode : numNodes - 1);
  const [density, setDensity] = useState(params?.density || 0.5);
  const [useCustomGraph, setUseCustomGraph] = useState(false);
  const [customEdges, setCustomEdges] = useState('');
  const [customEdgesError, setCustomEdgesError] = useState('');

  // Debounced values
  const debouncedNumNodes = useDebounce(numNodes, 500);
  const debouncedStartNode = useDebounce(startNode, 500);
  const debouncedTargetNode = useDebounce(targetNode, 500);
  const debouncedDensity = useDebounce(density, 500);

  // Animation refs
  const animationRef = useRef(null);
  const stepsRef = useRef([]);

  // Set state to completed when step reaches totalSteps
  React.useEffect(() => {
    if (step >= totalSteps && totalSteps > 0) {
      setState('completed');
    }
  }, [step, totalSteps, setState]);

  // Reset state when component mounts
  React.useEffect(() => {
    setState('idle');
    setStep(0);

    // Force a reset after a short delay
    const timeoutId = setTimeout(() => {
      setState('idle');
      setStep(0);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []);

  // Ensure state is correct
  React.useEffect(() => {
    if (step === 0 && state !== 'idle' && state !== 'running') {
      setState('idle');
    }
  }, [step, state, setState]);

  // Update movements when steps change
  useEffect(() => {
    if (setMovements && Array.isArray(steps)) {
      const newMovements = steps.slice(0, step).map(step => step.movement).filter(Boolean);
      setLocalMovements(newMovements);
      setMovements(newMovements);
    }
  }, [step, steps, setMovements]);

  // Update parameters when debounced values change
  useEffect(() => {
    if (
      debouncedNumNodes !== params.nodes ||
      debouncedStartNode !== params.startNode ||
      debouncedTargetNode !== params.targetNode ||
      debouncedDensity !== params.density
    ) {
      // Validate parameters
      const validNumNodes = Math.max(3, Math.min(20, debouncedNumNodes));
      const validStartNode = Math.max(0, Math.min(validNumNodes - 1, debouncedStartNode));
      const validTargetNode = Math.max(0, Math.min(validNumNodes - 1, debouncedTargetNode));
      const validDensity = Math.max(0.1, Math.min(1, debouncedDensity));

      // Update parameters
      onParamChange({
        ...params,
        nodes: validNumNodes,
        startNode: validStartNode,
        targetNode: validTargetNode,
        density: validDensity,
        customEdges: params.customEdges || []
      });
    }
  }, [debouncedNumNodes, debouncedStartNode, debouncedTargetNode, debouncedDensity, params, onParamChange]);

  // Handle number of nodes change
  const handleNumNodesChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value >= 3 && value <= 20) {
      setNumNodes(value);

      // Adjust start and target nodes if needed
      if (startNode >= value) {
        setStartNode(value - 1);
      }
      if (targetNode >= value) {
        setTargetNode(value - 1);
      }
    }
  };

  // Handle start node change
  const handleStartNodeChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value >= 0 && value < numNodes && value !== targetNode) {
      setStartNode(value);
    }
  };

  // Handle target node change
  const handleTargetNodeChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value >= 0 && value < numNodes && value !== startNode) {
      setTargetNode(value);
    }
  };

  // Handle density change
  const handleDensityChange = (e, value) => {
    setDensity(value);
  };

  // Handle custom graph toggle
  const handleUseCustomGraphChange = (e) => {
    setUseCustomGraph(e.target.checked);

    // Update params
    onParamChange({
      ...params,
      useCustomGraph: e.target.checked,
      customEdges: e.target.checked ? params.customEdges || [] : []
    });
  };

  // Handle custom edges input change
  const handleCustomEdgesChange = (e) => {
    setCustomEdges(e.target.value);
    setCustomEdgesError('');
  };

  // Apply custom edges
  const applyCustomEdges = () => {
    try {
      // Parse the input as a list of edges [from, to]
      const edgePattern = /\s*\[\s*(\d+)\s*,\s*(\d+)\s*\]\s*/g;
      const edgesText = customEdges.replace(/\n/g, '');

      let match;
      const parsedEdges = [];

      while ((match = edgePattern.exec(edgesText)) !== null) {
        const from = parseInt(match[1], 10);
        const to = parseInt(match[2], 10);

        if (isNaN(from) || isNaN(to)) {
          throw new Error('Invalid edge format');
        }

        if (from < 0 || from >= numNodes || to < 0 || to >= numNodes) {
          throw new Error(`Edge [${from}, ${to}] contains invalid node indices`);
        }

        parsedEdges.push([from, to]);
      }

      if (parsedEdges.length === 0) {
        setCustomEdgesError('Please enter at least one valid edge');
        return;
      }

      // Update params
      onParamChange({
        ...params,
        customEdges: parsedEdges,
        useCustomGraph: true
      });

      setUseCustomGraph(true);
      setCustomEdgesError('');

      // Reset state
      setState('idle');
      setStep(0);
    } catch (error) {
      setCustomEdgesError(error.message || 'Invalid edge format. Use [from, to] notation.');
    }
  };

  // Handle speed change
  const handleSpeedChange = (e, value) => {
    setSpeed(value);
  };

  // Handle control button clicks
  const handleStart = () => {
    setTimeout(() => {
      setState('running');
    }, 0);
  };

  const handlePause = () => {
    setState('paused');
  };

  const handleReset = () => {
    setStep(0);
    setState('idle');
    setLocalMovements([]);
    if (setMovements) {
      setMovements([]);
    }
  };

  const handleStepForward = () => {
    if (step < totalSteps) {
      setState('paused');
      setStep(step + 1);

      if (step + 1 >= totalSteps) {
        setState('completed');
      }
    }
  };

  const handleStepBackward = () => {
    if (step > 0) {
      setState('paused');
      setStep(step - 1);
    }
  };

  // Generate node options
  const nodeOptions = [];
  for (let i = 0; i < numNodes; i++) {
    nodeOptions.push(
      <MenuItem key={i} value={i}>
        Node {i}
      </MenuItem>
    );
  }

  // Format movement text with colors
  const formatMovement = (movement) => {
    if (!movement) return '';

    // Format for initialization
    if (movement.includes('Initialize BFS')) {
      return (
        <>
          <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
            Initialize BFS:
          </Box>{' '}
          Start from node{' '}
          <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
            {movement.match(/node (\d+)/)[1]}
          </Box>
        </>
      );
    }

    // Format for enqueue start node
    if (movement.includes('Enqueue start node')) {
      const parts = movement.match(/Enqueue start node (\d+) and mark it as visited/i);
      if (parts) {
        return (
          <>
            Enqueue start node{' '}
            <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
              {parts[1]}
            </Box>{' '}
            and mark it as visited
          </>
        );
      }
    }

    // Format for dequeue node
    if (movement.includes('Dequeue node')) {
      const parts = movement.match(/Dequeue node (\d+) from the queue/i);
      if (parts) {
        return (
          <>
            Dequeue node{' '}
            <Box component="span" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
              {parts[1]}
            </Box>{' '}
            from the queue
          </>
        );
      }
    }

    // Format for check neighbor
    if (movement.includes('Check neighbor')) {
      const parts = movement.match(/Check neighbor (\d+) of node (\d+)/i);
      if (parts) {
        return (
          <>
            Check neighbor{' '}
            <Box component="span" sx={{ color: 'info.main', fontWeight: 'bold' }}>
              {parts[1]}
            </Box>{' '}
            of node{' '}
            <Box component="span" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
              {parts[2]}
            </Box>
          </>
        );
      }
    }

    // Format for enqueue neighbor
    if (movement.includes('Enqueue neighbor')) {
      const parts = movement.match(/Enqueue neighbor (\d+) and mark it as visited/i);
      if (parts) {
        return (
          <>
            Enqueue neighbor{' '}
            <Box component="span" sx={{ color: 'info.main', fontWeight: 'bold' }}>
              {parts[1]}
            </Box>{' '}
            and mark it as visited
          </>
        );
      }
    }

    // Format for skip neighbor
    if (movement.includes('Skip neighbor')) {
      const parts = movement.match(/Skip neighbor (\d+) as it is already visited/i);
      if (parts) {
        return (
          <>
            Skip neighbor{' '}
            <Box component="span" sx={{ color: 'text.secondary', fontWeight: 'bold' }}>
              {parts[1]}
            </Box>{' '}
            as it is already visited
          </>
        );
      }
    }

    // Format for found target
    if (movement.includes('Found target node')) {
      return (
        <>
          <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
            Found target node!
          </Box>
        </>
      );
    }

    // Format for BFS complete
    if (movement.includes('BFS complete')) {
      if (movement.includes('Target node not found')) {
        return (
          <>
            <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold' }}>
              BFS complete.
            </Box>{' '}
            Target node not found.
          </>
        );
      } else {
        const parts = movement.match(/BFS complete. Visited all reachable nodes from (\d+)/i);
        if (parts) {
          return (
            <>
              <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                BFS complete.
              </Box>{' '}
              Visited all reachable nodes from{' '}
              <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                {parts[1]}
              </Box>
            </>
          );
        }
      }
    }

    // Default case: return the original text
    return movement;
  };

  // Determine if the algorithm is running
  const isRunning = state === 'running';
  const isCompleted = state === 'completed';
  const isPaused = state === 'paused';
  const isIdle = state === 'idle';

  // Calculate progress percentage
  const progress = totalSteps > 0 ? (step / totalSteps) * 100 : 0;

  return (
    <Stack spacing={2} sx={{ p: 2, height: '100%', overflowY: 'auto', bgcolor: 'background.default', borderRadius: 1 }}>
      {/* Parameters Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <GraphIcon fontSize="small" sx={{ mr: 0.5 }} /> Graph Parameters
          </Typography>
          <IconButton
            size="small"
            onClick={() => setParamsExpanded(!paramsExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {paramsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={paramsExpanded}>
          <Box sx={{ mt: 1 }}>
            {/* Number of Nodes */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500 }}>
                Number of Nodes
              </Typography>
              <TextField
                value={numNodes}
                onChange={handleNumNodesChange}
                disabled={state === 'running' || useCustomGraph}
                inputProps={{
                  min: 3,
                  max: 20,
                  type: 'number',
                  style: { textAlign: 'center' }
                }}
                sx={{ width: '100%' }}
                size="small"
                variant="outlined"
              />
            </Box>

            {/* Start and Target Nodes */}
            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500 }}>
                  Start Node
                </Typography>
                <FormControl fullWidth size="small">
                  <Select
                    value={startNode}
                    onChange={handleStartNodeChange}
                    disabled={state === 'running'}
                  >
                    {nodeOptions}
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500 }}>
                  Target Node
                </Typography>
                <FormControl fullWidth size="small">
                  <Select
                    value={targetNode}
                    onChange={handleTargetNodeChange}
                    disabled={state === 'running'}
                  >
                    {nodeOptions}
                  </Select>
                </FormControl>
              </Box>
            </Stack>

            {/* Edge Density */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500 }}>
                Edge Density: {density.toFixed(2)}
              </Typography>
              <Slider
                value={density}
                onChange={handleDensityChange}
                min={0.1}
                max={1}
                step={0.05}
                disabled={state === 'running' || useCustomGraph}
                sx={{ width: '100%' }}
              />
              <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', mt: 0.5 }}>
                Lower values = fewer edges, higher values = more edges
              </Typography>
            </Box>

            {/* Custom Graph Toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={useCustomGraph}
                  onChange={handleUseCustomGraphChange}
                  disabled={state === 'running'}
                />
              }
              label="Use Custom Graph"
              sx={{ mb: 1 }}
            />

            {/* Custom Edges Input */}
            {useCustomGraph && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500 }}>
                  Custom Edges (format: [from, to])
                </Typography>
                <TextField
                  multiline
                  rows={4}
                  value={customEdges}
                  onChange={handleCustomEdgesChange}
                  disabled={state === 'running'}
                  placeholder="Example: [0, 1] [1, 2] [0, 2]"
                  error={!!customEdgesError}
                  helperText={customEdgesError}
                  sx={{ width: '100%' }}
                  size="small"
                  variant="outlined"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={applyCustomEdges}
                  disabled={state === 'running'}
                  sx={{ mt: 1 }}
                >
                  Apply Custom Edges
                </Button>
              </Box>
            )}
          </Box>
        </Collapse>
      </Paper>

      {/* Controls Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <SettingsIcon fontSize="small" sx={{ mr: 0.5 }} /> Controls
          </Typography>
          <IconButton
            size="small"
            onClick={() => setControlsExpanded(!controlsExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {controlsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={controlsExpanded}>
          <Box sx={{ mt: 1 }}>
            {/* Speed Control */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem', fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                <SpeedIcon fontSize="small" sx={{ mr: 0.5 }} /> Animation Speed: {speed}
              </Typography>
              <Slider
                value={speed}
                onChange={handleSpeedChange}
                min={1}
                max={10}
                step={1}
                marks
                sx={{ width: '100%' }}
              />
            </Box>

            {/* Control Buttons */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1, mb: 1 }}>
              <Tooltip title={isRunning ? "Pause" : "Start"}>
                <Button
                  variant="contained"
                  color={isRunning ? "warning" : "primary"}
                  startIcon={isRunning ? <PauseIcon /> : <PlayArrowIcon />}
                  onClick={isRunning ? handlePause : handleStart}
                  disabled={isCompleted}
                  fullWidth
                >
                  {isRunning ? "Pause" : "Start"}
                </Button>
              </Tooltip>
              <Tooltip title="Reset">
                <span>
                  <Button
                    variant="outlined"
                    startIcon={<RestartAltIcon />}
                    onClick={handleReset}
                    disabled={isRunning || (step === 0 && isIdle)}
                    fullWidth
                  >
                    Reset
                  </Button>
                </span>
              </Tooltip>
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1 }}>
              <Tooltip title="Step Backward">
                <span>
                  <Button
                    variant="outlined"
                    startIcon={<SkipPreviousIcon />}
                    onClick={handleStepBackward}
                    disabled={isRunning || step === 0}
                    fullWidth
                  >
                    Back
                  </Button>
                </span>
              </Tooltip>
              <Tooltip title="Step Forward">
                <span>
                  <Button
                    variant="outlined"
                    endIcon={<SkipNextIcon />}
                    onClick={handleStepForward}
                    disabled={isRunning || step >= totalSteps}
                    fullWidth
                  >
                    Next
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Box>
        </Collapse>
      </Paper>

      {/* Progress Indicator Section */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, mb: 1.5 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <TimelineIcon fontSize="small" sx={{ mr: 0.5 }} /> Progress
          </Typography>
          <IconButton
            size="small"
            onClick={() => setProgressExpanded(!progressExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {progressExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Collapse in={progressExpanded}>
          <Stack spacing={2}>
            {/* Progress Bar */}
            <Box sx={{ p: 1.5, bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
              <Stack spacing={1.5}>
                {/* Step Counter with Circular Progress */}
                <Stack direction="row" spacing={2} alignItems="center">
                  <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                    <CircularProgress
                      variant="determinate"
                      value={Math.round((step / totalSteps) * 100) || 0}
                      size={60}
                      thickness={5}
                      sx={{
                        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                        '& .MuiCircularProgress-circle': {
                          strokeLinecap: 'round',
                        },
                      }}
                    />
                    <Box
                      sx={{
                        top: 0,
                        left: 0,
                        bottom: 0,
                        right: 0,
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="caption" component="div" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                        {`${Math.round((step / totalSteps) * 100) || 0}%`}
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                      Step: {step} / {totalSteps}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.round((step / totalSteps) * 100) || 0}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                        },
                      }}
                    />
                  </Box>
                </Stack>

                {/* Status Indicator */}
                <Box sx={{
                  p: 1,
                  borderRadius: 1,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
                  color: state === 'idle' ? '#0288D1' :
                    state === 'running' ? '#2E7D32' :
                      state === 'paused' ? '#E65100' :
                        state === 'completed' ? '#2E7D32' : theme.palette.text.primary,
                }}>
                  <Stack direction="row" justifyContent="center" alignItems="center" spacing={1}>
                    {state === 'idle' && <HourglassEmptyIcon fontSize="small" />}
                    {state === 'running' && <CircularProgress size={16} thickness={5} sx={{ color: 'inherit' }} />}
                    {state === 'paused' && <PendingIcon fontSize="small" />}
                    {state === 'completed' && <CheckCircleIcon fontSize="small" />}
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      Status: {state.charAt(0).toUpperCase() + state.slice(1)}
                    </Typography>
                  </Stack>
                </Box>
              </Stack>
            </Box>
          </Stack>
        </Collapse>
      </Paper>

      {/* Steps Sequence */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, flex: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <FormatListNumberedIcon fontSize="small" sx={{ mr: 0.5 }} /> Steps Sequence
          </Typography>
          <IconButton
            size="small"
            onClick={() => setStepsExpanded(!stepsExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {stepsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
        <Collapse in={stepsExpanded}>
          <Box
            ref={movementContainerRef}
            className="steps-sequence-container"
            sx={{
              mt: 1,
              maxHeight: '200px',
              overflowY: 'auto',
              overflowX: 'hidden',
              border: '1px solid',
              borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
              borderRadius: 1,
              p: 1,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                },
              },
            }}
          >
            {movements.length > 0 ? (
              movements.map((movement, index) => (
                <Box
                  key={index}
                  sx={{
                    p: 1,
                    mb: 0.5,
                    borderRadius: 1,
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
                    border: '1px solid',
                    borderColor: 'divider',
                    fontSize: '0.85rem',
                  }}
                >
                  <Typography variant="body2" sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <Box
                      component="span"
                      sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minWidth: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        color: 'primary.contrastText',
                        mr: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 1}
                    </Box>
                    {formatMovement(movement)}
                  </Typography>
                </Box>
              ))
            ) : (
              <Typography variant="body2" sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                No steps executed yet. Press Start or Step Forward to begin.
              </Typography>
            )}
          </Box>
        </Collapse>
      </Paper>

      {/* Algorithm Visualization */}
      <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1 }}>
        <Box sx={{ position: 'relative', mb: 1 }}>
          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
            <CodeIcon fontSize="small" sx={{ mr: 0.5 }} /> BFS Algorithm
          </Typography>
          <IconButton
            size="small"
            onClick={() => setAlgorithmExpanded(!algorithmExpanded)}
            sx={{
              position: 'absolute',
              top: '50%',
              right: -10,
              transform: 'translateY(-50%)',
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { bgcolor: 'background.paper' }
            }}
          >
            {algorithmExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
        <Collapse in={algorithmExpanded}>
          <Box sx={{
            maxHeight: '400px',
            overflowY: 'auto',
            overflowX: 'auto',
            borderRadius: 1,
            '&::-webkit-scrollbar': {
              width: '8px',
              height: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
              '&:hover': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
              },
            },
          }}>
            <BFSAlgorithm step={step} />
          </Box>
        </Collapse>
      </Paper>
    </Stack>
  );
};

export default BFSController;