// PermutationsAlgorithm.js
// Implementation of the Permutations algorithm using backtracking

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for generating all permutations of a given set
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generatePermutationsSteps = (params) => {
  console.log('generatePermutationsSteps called with params:', params);
  const { elements = ['A', 'B', 'C'], useNumbers = false } = params;
  
  // If useNumbers is true, convert elements to numbers 1 to n
  const inputElements = useNumbers 
    ? Array.from({ length: elements.length }, (_, i) => (i + 1).toString())
    : elements;
  
  const steps = [];
  const permutations = [];
  
  // Add initial step
  steps.push({
    type: 'init',
    message: `Initialize permutation generator with elements: [${inputElements.join(', ')}].`,
    elements: [...inputElements],
    currentPermutation: [],
    permutations: [],
    currentIndex: -1,
    depth: 0,
    progressStep: 'init',
    pseudocodeLine: 1
  });
  
  // Generate permutations
  generatePermutations(inputElements, [], new Set(), steps, permutations, 0);
  
  // Add final step
  steps.push({
    type: 'complete',
    message: `Generated all ${permutations.length} permutations.`,
    elements: [...inputElements],
    currentPermutation: [],
    permutations: [...permutations],
    currentIndex: -1,
    depth: 0,
    progressStep: 'complete',
    pseudocodeLine: 0
  });
  
  return { steps, permutations };
};

/**
 * Generate all permutations using backtracking
 * @param {Array} elements - Array of elements to permute
 * @param {Array} currentPermutation - Current permutation being built
 * @param {Set} used - Set of indices that have been used
 * @param {Array} steps - Array to store steps
 * @param {Array} permutations - Array to store all permutations
 * @param {Number} depth - Current recursion depth
 */
const generatePermutations = (elements, currentPermutation, used, steps, permutations, depth) => {
  // If the current permutation is complete
  if (currentPermutation.length === elements.length) {
    // Add the permutation to the list
    permutations.push([...currentPermutation]);
    
    steps.push({
      type: 'found',
      message: `Found permutation: [${currentPermutation.join(', ')}].`,
      elements: [...elements],
      currentPermutation: [...currentPermutation],
      permutations: [...permutations],
      currentIndex: -1,
      depth,
      progressStep: 'process',
      pseudocodeLine: 3
    });
    
    return;
  }
  
  // Try each element that hasn't been used yet
  for (let i = 0; i < elements.length; i++) {
    if (!used.has(i)) {
      // Add this element to the current permutation
      currentPermutation.push(elements[i]);
      used.add(i);
      
      steps.push({
        type: 'add',
        message: `Adding element ${elements[i]} at position ${currentPermutation.length - 1}.`,
        elements: [...elements],
        currentPermutation: [...currentPermutation],
        permutations: [...permutations],
        currentIndex: i,
        depth,
        progressStep: 'process',
        pseudocodeLine: 7
      });
      
      // Recursively generate permutations with this element added
      generatePermutations(elements, currentPermutation, used, steps, permutations, depth + 1);
      
      // Backtrack: remove the element and mark it as unused
      used.delete(i);
      currentPermutation.pop();
      
      steps.push({
        type: 'backtrack',
        message: `Backtracking: removing element ${elements[i]} from position ${currentPermutation.length}.`,
        elements: [...elements],
        currentPermutation: [...currentPermutation],
        permutations: [...permutations],
        currentIndex: i,
        depth,
        progressStep: 'process',
        pseudocodeLine: 11
      });
    }
  }
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const PermutationsAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Pseudocode for Permutations algorithm
  const pseudocode = [
    { line: 'function generatePermutations(elements, currentPermutation, used):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // If the current permutation is complete', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if currentPermutation.length == elements.length:', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '        // Add the permutation to the result list', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '        permutations.add(currentPermutation.copy())', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '        return', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    // Try each element that hasn\'t been used yet', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    for i = 0 to elements.length - 1:', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '        if i not in used:', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '            // Add this element to the current permutation', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '            currentPermutation.push(elements[i])', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            used.add(i)', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '            // Recursively generate permutations with this element added', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '            generatePermutations(elements, currentPermutation, used)', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '            // Backtrack: remove the element and mark it as unused', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '            used.remove(i)', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '            currentPermutation.pop()', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
  ];

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default PermutationsAlgorithm;
