// MatrixChainMultiplicationController.js
// This component provides the controls for Matrix Chain Multiplication algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, TextField, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GridOnIcon from '@mui/icons-material/GridOn';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// Import algorithm functions
import MatrixChainMultiplicationAlgorithm from './MatrixChainMultiplicationAlgorithm';

const MatrixChainMultiplicationController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [dimensions, setDimensions] = useState(params?.dimensions || [30, 35, 15, 5, 10, 20, 25]);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { dimensions });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { dimensions });

        // Update params first
        onParamChange({
            dimensions
        });

        // Set steps and movements directly
        try {
            const result = MatrixChainMultiplicationAlgorithm.generateMatrixChainMultiplicationSteps({
                dimensions
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [dimensions, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle dimension changes
    const handleDimensionChange = useCallback((index, value) => {
        const newValue = parseInt(value, 10);
        if (isNaN(newValue) || newValue < 1) return;

        const newDimensions = [...dimensions];
        newDimensions[index] = newValue;
        setDimensions(newDimensions);
        resetAndGenerateSteps();
    }, [dimensions, resetAndGenerateSteps]);

    // Add a new matrix
    const addMatrix = useCallback(() => {
        if (dimensions.length < 10) { // Limit to 10 matrices for visualization clarity
            const newDimensions = [...dimensions, 20]; // Add a new dimension with default value 20
            setDimensions(newDimensions);
            resetAndGenerateSteps();
        }
    }, [dimensions, resetAndGenerateSteps]);

    // Remove the last matrix
    const removeMatrix = useCallback(() => {
        if (dimensions.length > 2) { // Keep at least 1 matrix (2 dimensions)
            const newDimensions = dimensions.slice(0, -1);
            setDimensions(newDimensions);
            resetAndGenerateSteps();
        }
    }, [dimensions, resetAndGenerateSteps]);

    // Custom component for dimensions input
    const DimensionsInput = ({ disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>Matrix Dimensions</Typography>
                    <Button 
                        startIcon={<AddIcon />} 
                        onClick={addMatrix}
                        disabled={dimensions.length >= 10 || disabled}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                    >
                        Add Matrix
                    </Button>
                    <Button 
                        startIcon={<RemoveIcon />} 
                        onClick={removeMatrix}
                        disabled={dimensions.length <= 2 || disabled}
                        variant="outlined"
                        size="small"
                    >
                        Remove Matrix
                    </Button>
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                    {dimensions.map((dim, index) => (
                        <Box key={index} sx={{ display: 'flex', alignItems: 'center' }}>
                            {index === 0 ? (
                                <TextField
                                    label={`Matrix 1 Rows`}
                                    type="number"
                                    size="small"
                                    value={dim}
                                    onChange={(e) => handleDimensionChange(index, e.target.value)}
                                    slotProps={{ input: { min: 1, max: 100 } }}
                                    sx={{ width: 120 }}
                                    disabled={disabled}
                                />
                            ) : index === dimensions.length - 1 ? (
                                <TextField
                                    label={`Matrix ${index} Cols`}
                                    type="number"
                                    size="small"
                                    value={dim}
                                    onChange={(e) => handleDimensionChange(index, e.target.value)}
                                    slotProps={{ input: { min: 1, max: 100 } }}
                                    sx={{ width: 120 }}
                                    disabled={disabled}
                                />
                            ) : (
                                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                    <Typography variant="caption" sx={{ textAlign: 'center' }}>
                                        Matrix {index} Cols
                                    </Typography>
                                    <Typography variant="caption" sx={{ textAlign: 'center' }}>
                                        Matrix {index + 1} Rows
                                    </Typography>
                                    <TextField
                                        type="number"
                                        size="small"
                                        value={dim}
                                        onChange={(e) => handleDimensionChange(index, e.target.value)}
                                        slotProps={{ input: { min: 1, max: 100 } }}
                                        sx={{ width: 120 }}
                                        disabled={disabled}
                                    />
                                </Box>
                            )}
                        </Box>
                    ))}
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Matrices:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                        {dimensions.slice(0, -1).map((rows, i) => (
                            <Box key={i} sx={{ 
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: 1,
                                p: 1,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center'
                            }}>
                                <Typography variant="body2">
                                    A{i}
                                </Typography>
                                <Typography variant="body2">
                                    {rows} × {dimensions[i + 1]}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Matrix Chain Multiplication algorithm
    const pseudocode = [
        { code: "function MatrixChainOrder(dimensions):", lineNumber: 1, indent: 0 },
        { code: "  n = dimensions.length - 1  // Number of matrices", lineNumber: 2, indent: 1 },
        { code: "  // Create tables for DP", lineNumber: 3, indent: 1 },
        { code: "  dp[1...n][1...n] = 0  // Minimum number of operations", lineNumber: 4, indent: 1 },
        { code: "  s[1...n][1...n] = 0  // Optimal split positions", lineNumber: 5, indent: 1 },
        { code: "  for l = 2 to n:  // l is chain length", lineNumber: 6, indent: 1 },
        { code: "    for i = 1 to n-l+1:  // i is start of chain", lineNumber: 7, indent: 2 },
        { code: "      j = i + l - 1  // j is end of chain", lineNumber: 8, indent: 3 },
        { code: "      dp[i][j] = INFINITY", lineNumber: 9, indent: 3 },
        { code: "      for k = i to j-1:  // Try each split position", lineNumber: 10, indent: 3 },
        { code: "        // Calculate cost for this split", lineNumber: 11, indent: 4 },
        { code: "        cost = dp[i][k] + dp[k+1][j] + dimensions[i-1] * dimensions[k] * dimensions[j]", lineNumber: 12, indent: 4 },
        { code: "        if cost < dp[i][j]:", lineNumber: 13, indent: 4 },
        { code: "          dp[i][j] = cost  // Update minimum cost", lineNumber: 14, indent: 5 },
        { code: "          s[i][j] = k  // Record optimal split position", lineNumber: 15, indent: 5 },
        { code: "  // Use s to construct optimal parenthesization", lineNumber: 16, indent: 1 },
        { code: "  parenthesization = PrintOptimalParenthesis(s, 1, n)", lineNumber: 17, indent: 1 },
        { code: "  // Return minimum number of scalar multiplications", lineNumber: 18, indent: 1 },
        { code: "  return dp[1][n], parenthesization", lineNumber: 19, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Matrix Chain Multiplication Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Matrix Chain Multiplication algorithm determines the most efficient way to multiply a sequence of matrices to minimize the total number of scalar multiplications.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(n³) where n is the number of matrices
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(n²) for the dynamic programming tables
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to find the optimal parenthesization
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • For matrices A₁, A₂, ..., Aₙ with dimensions p₀×p₁, p₁×p₂, ..., pₙ₋₁×pₙ, we need to find the optimal way to place parentheses
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'dimensions',
                        type: 'component',
                        label: 'Matrix Dimensions',
                        component: DimensionsInput,
                        icon: GridOnIcon
                    }
                ]}
                values={{
                    dimensions
                }}
                onChange={() => {
                    // Dimensions are handled by the DimensionsInput component
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default MatrixChainMultiplicationController;
