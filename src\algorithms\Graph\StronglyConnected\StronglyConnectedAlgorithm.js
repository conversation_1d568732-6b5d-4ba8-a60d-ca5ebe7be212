// StronglyConnectedAlgorithm.js
// Implementation of <PERSON><PERSON><PERSON><PERSON>'s algorithm for finding strongly connected components

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { getSyntaxColors } from '../../../utils/syntaxHighlighting';

/**
 * Generate a random directed graph
 * @param {number} numNodes - Number of nodes in the graph
 * @param {number} density - Edge density (0-1)
 * @param {number} minWeight - Minimum edge weight
 * @param {number} maxWeight - Maximum edge weight
 * @returns {Object} - Graph representation with nodes and edges
 */
const generateRandomGraph = (numNodes, density = 0.5, minWeight = 1, maxWeight = 10) => {
  console.log(`Generating random directed graph with ${numNodes} nodes, density ${density}`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges
  const edges = [];
  let edgeId = 0;

  // Create a connected graph first to ensure all nodes are reachable
  // Use a simple spanning tree approach
  const visited = new Set([0]); // Start with node 0
  const unvisited = new Set(Array.from({ length: numNodes - 1 }, (_, i) => i + 1));

  while (unvisited.size > 0) {
    // Pick a random visited node
    const fromNode = Array.from(visited)[Math.floor(Math.random() * visited.size)];

    // Pick a random unvisited node
    const toNode = Array.from(unvisited)[Math.floor(Math.random() * unvisited.size)];

    // Add a directed edge with random weight
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source: fromNode,
      target: toNode,
      weight
    });

    // Mark the target node as visited
    visited.add(toNode);
    unvisited.delete(toNode);
  }

  // Add additional random edges based on density
  const maxPossibleEdges = numNodes * (numNodes - 1); // Directed graph, so n(n-1) possible edges
  const targetEdgeCount = Math.floor(maxPossibleEdges * density);
  const additionalEdges = Math.max(0, targetEdgeCount - (numNodes - 1));

  // Keep track of existing edges to avoid duplicates
  const existingEdges = new Set();
  edges.forEach(edge => existingEdges.add(`${edge.source}-${edge.target}`));

  // Add random edges until we reach the target density
  for (let i = 0; i < additionalEdges; i++) {
    let source, target;
    let edgeKey;

    // Find a new edge that doesn't already exist
    do {
      source = Math.floor(Math.random() * numNodes);
      target = Math.floor(Math.random() * numNodes);
      edgeKey = `${source}-${target}`;
    } while (source === target || existingEdges.has(edgeKey));

    // Add the edge with random weight
    const weight = Math.floor(Math.random() * (maxWeight - minWeight + 1)) + minWeight;
    edges.push({
      id: edgeId++,
      source,
      target,
      weight
    });
    existingEdges.add(edgeKey);
  }

  return { nodes, edges };
};

/**
 * Generate a custom graph from edge list
 * @param {number} numNodes - Number of nodes in the graph
 * @param {Array} customEdges - Array of edges [from, to, weight]
 * @returns {Object} - Graph representation with nodes and edges
 */
const generateCustomGraph = (numNodes, customEdges) => {
  console.log(`Generating custom graph with ${numNodes} nodes and ${customEdges.length} edges`);

  // Create nodes
  const nodes = [];
  for (let i = 0; i < numNodes; i++) {
    nodes.push({ id: i });
  }

  // Create edges from custom edges
  const edges = [];
  let edgeId = 0;

  customEdges.forEach(([source, target, weight = 1]) => {
    if (source >= 0 && source < numNodes && target >= 0 && target < numNodes && source !== target) {
      edges.push({
        id: edgeId++,
        source,
        target,
        weight
      });
    }
  });

  return { nodes, edges };
};

/**
 * Perform a DFS traversal and fill the stack with nodes in order of finish time
 * @param {Object} graph - Adjacency list representation of the graph
 * @param {number} node - Current node
 * @param {Set} visited - Set of visited nodes
 * @param {Array} stack - Stack to store nodes in order of finish time
 * @param {Array} steps - Array to store algorithm steps
 * @param {Object} graphData - Original graph data with nodes and edges
 */
const fillOrder = (graph, node, visited, stack, steps, graphData) => {
  // Mark the current node as visited
  visited.add(node);

  // Add step for visiting the node
  steps.push({
    type: 'visit',
    message: `Visit node ${node} in the first DFS pass`,
    graph: graphData,
    visited: [...visited],
    stack: [...stack],
    currentNode: node,
    components: [],
    progressStep: 'first_dfs',
    pseudocodeLine: 5
  });

  // Recur for all adjacent vertices
  for (const neighbor of graph[node] || []) {
    if (!visited.has(neighbor)) {
      // Add step for exploring edge
      steps.push({
        type: 'explore',
        message: `Explore edge from ${node} to ${neighbor}`,
        graph: graphData,
        visited: [...visited],
        stack: [...stack],
        currentNode: node,
        currentEdge: { source: node, target: neighbor },
        components: [],
        progressStep: 'first_dfs',
        pseudocodeLine: 6
      });

      fillOrder(graph, neighbor, visited, stack, steps, graphData);
    }
  }

  // All descendants of node are processed, push it to stack
  stack.push(node);

  // Add step for pushing to stack
  steps.push({
    type: 'push',
    message: `Push node ${node} to stack (finished exploring all descendants)`,
    graph: graphData,
    visited: [...visited],
    stack: [...stack],
    currentNode: node,
    components: [],
    progressStep: 'first_dfs',
    pseudocodeLine: 7
  });
};

/**
 * Perform a DFS traversal on the transposed graph to find SCCs
 * @param {Object} transposedGraph - Transposed adjacency list
 * @param {number} node - Current node
 * @param {Set} visited - Set of visited nodes
 * @param {Array} component - Current component being built
 * @param {Array} steps - Array to store algorithm steps
 * @param {Object} graphData - Original graph data with nodes and edges
 * @param {Array} components - Array of all components found so far
 */
const dfsUtil = (transposedGraph, node, visited, component, steps, graphData, components) => {
  // Mark the current node as visited and add to component
  visited.add(node);
  component.push(node);

  // Add step for visiting the node in second DFS
  steps.push({
    type: 'component_visit',
    message: `Visit node ${node} in the second DFS pass, add to current component`,
    graph: graphData,
    visited: [...visited],
    currentNode: node,
    currentComponent: [...component],
    components: [...components, component.length > 1 ? [...component] : []].filter(c => c.length > 0),
    progressStep: 'second_dfs',
    pseudocodeLine: 12
  });

  // Recur for all adjacent vertices
  for (const neighbor of transposedGraph[node] || []) {
    if (!visited.has(neighbor)) {
      // Add step for exploring edge in transposed graph
      steps.push({
        type: 'component_explore',
        message: `Explore edge from ${node} to ${neighbor} in transposed graph`,
        graph: graphData,
        visited: [...visited],
        currentNode: node,
        currentEdge: { source: node, target: neighbor },
        currentComponent: [...component],
        components: [...components, component.length > 1 ? [...component] : []].filter(c => c.length > 0),
        progressStep: 'second_dfs',
        pseudocodeLine: 13
      });

      dfsUtil(transposedGraph, neighbor, visited, component, steps, graphData, components);
    }
  }
};

/**
 * Generate steps for Kosaraju's algorithm to find strongly connected components
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateStronglyConnectedSteps = (params) => {
  console.log('generateStronglyConnectedSteps called with params:', params);
  const { nodes: numNodes, density, minWeight = 1, maxWeight = 10, customEdges } = params;
  const steps = [];

  // Generate graph
  let graphData;
  if (customEdges && customEdges.length > 0) {
    console.log('Generating custom graph with edges:', customEdges);
    graphData = generateCustomGraph(numNodes, customEdges);
  } else {
    console.log('Generating random graph with params:', { numNodes, density, minWeight, maxWeight });
    graphData = generateRandomGraph(numNodes, density, minWeight, maxWeight);
  }
  console.log('Generated graph:', graphData);

  const { nodes, edges } = graphData;

  // Create adjacency list representation of the graph
  const graph = {};
  const transposedGraph = {};

  // Initialize adjacency lists
  for (let i = 0; i < numNodes; i++) {
    graph[i] = [];
    transposedGraph[i] = [];
  }

  // Fill adjacency lists
  edges.forEach(edge => {
    graph[edge.source].push(edge.target);
    transposedGraph[edge.target].push(edge.source); // Transpose the graph
  });

  // Add initial step
  steps.push({
    type: 'initialize',
    message: 'Initialize Kosaraju\'s algorithm for finding strongly connected components',
    graph: graphData,
    visited: [],
    stack: [],
    components: [],
    progressStep: 'initialize',
    pseudocodeLine: 1
  });

  // Step for creating the transposed graph
  steps.push({
    type: 'transpose',
    message: 'Create the transposed graph by reversing all edges',
    graph: graphData,
    visited: [],
    stack: [],
    components: [],
    progressStep: 'transpose',
    pseudocodeLine: 2
  });

  // First DFS to fill the stack
  const visited = new Set();
  const stack = [];

  // Step for starting first DFS
  steps.push({
    type: 'first_dfs_start',
    message: 'Start first DFS to fill the stack with nodes in order of finish time',
    graph: graphData,
    visited: [],
    stack: [],
    components: [],
    progressStep: 'first_dfs',
    pseudocodeLine: 4
  });

  // Perform first DFS for all nodes
  for (let i = 0; i < numNodes; i++) {
    if (!visited.has(i)) {
      fillOrder(graph, i, visited, stack, steps, graphData);
    }
  }

  // Step for completing first DFS
  steps.push({
    type: 'first_dfs_complete',
    message: 'Completed first DFS, stack contains nodes in order of finish time',
    graph: graphData,
    visited: [...visited],
    stack: [...stack],
    components: [],
    progressStep: 'first_dfs_complete',
    pseudocodeLine: 8
  });

  // Process the stack to find SCCs
  visited.clear();
  const components = [];

  // Step for starting second DFS
  steps.push({
    type: 'second_dfs_start',
    message: 'Start second DFS on transposed graph to find strongly connected components',
    graph: graphData,
    visited: [],
    stack: [...stack],
    components: [],
    progressStep: 'second_dfs',
    pseudocodeLine: 9
  });

  // Process nodes in order of finish time (reverse order of stack)
  while (stack.length > 0) {
    const node = stack.pop();

    // If not visited yet, it's the start of a new SCC
    if (!visited.has(node)) {
      const component = [];

      // Step for starting a new component
      steps.push({
        type: 'new_component',
        message: `Start a new strongly connected component from node ${node}`,
        graph: graphData,
        visited: [...visited],
        stack: [...stack],
        currentNode: node,
        components: [...components],
        progressStep: 'second_dfs',
        pseudocodeLine: 11
      });

      dfsUtil(transposedGraph, node, visited, component, steps, graphData, components);

      // Only add component if it has nodes
      if (component.length > 0) {
        components.push(component);

        // Step for completing a component
        steps.push({
          type: 'component_complete',
          message: `Completed strongly connected component: [${component.join(', ')}]`,
          graph: graphData,
          visited: [...visited],
          components: [...components],
          progressStep: 'component_complete',
          pseudocodeLine: 14
        });
      }
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Kosaraju's algorithm complete. Found ${components.length} strongly connected components.`,
    graph: graphData,
    components: [...components],
    progressStep: 'complete',
    pseudocodeLine: 16
  });

  console.log('Generated steps:', steps.length, 'steps');
  console.log('Found components:', components);

  const result = {
    steps,
    graph: graphData,
    components
  };

  console.log('Returning result:', result);
  return result;
};

/**
 * Strongly Connected Components Algorithm visualization component
 */
const StronglyConnectedAlgorithm = ({ step = 0 }) => {
  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Define the pseudocode for Kosaraju's algorithm
  const pseudocode = [
    { code: "function findSCCs(graph):", highlight: false },
    { code: "  // Create a transposed graph (reverse all edges)", highlight: step === 1 },
    { code: "  transposedGraph = createTransposedGraph(graph)", highlight: step === 1 },
    { code: "", highlight: false },
    { code: "  // First DFS to fill stack with nodes in order of finish time", highlight: step === 2 },
    { code: "  for each vertex v in graph:", highlight: step === 2 || step === 3 },
    { code: "    if v is not visited:", highlight: step === 3 },
    { code: "      fillOrder(v, visited, stack)", highlight: step === 4 },
    { code: "", highlight: false },
    { code: "  // Second DFS to find SCCs", highlight: step === 5 },
    { code: "  while stack is not empty:", highlight: step === 5 || step === 6 },
    { code: "    v = stack.pop()", highlight: step === 6 },
    { code: "    if v is not visited:", highlight: step === 7 },
    { code: "      DFS(v, transposedGraph, visited, component)", highlight: step === 8 },
    { code: "      components.add(component)", highlight: step === 9 },
    { code: "", highlight: false },
    { code: "  return components", highlight: step === 10 }
  ];

  return (
    <div style={{ fontFamily: 'monospace', fontSize: '14px', padding: '10px', overflowX: 'auto' }}>
      {pseudocode.map((line, index) => (
        <div
          key={index}
          style={{
            backgroundColor: line.highlight ? colors.lineHighlight : 'transparent',
            padding: '2px 5px',
            whiteSpace: 'pre',
            color: line.highlight ? colors.highlightedText : colors.text,
          }}
        >
          {line.code}
        </div>
      ))}
    </div>
  );
};

export default StronglyConnectedAlgorithm;

// Export helper functions for use in other files
StronglyConnectedAlgorithm.generateStronglyConnectedSteps = generateStronglyConnectedSteps;
StronglyConnectedAlgorithm.generateRandomGraph = generateRandomGraph;
StronglyConnectedAlgorithm.generateCustomGraph = generateCustomGraph;
