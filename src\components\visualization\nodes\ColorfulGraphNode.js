// ColorfulGraphNode.js
// A simple, colorful graph node component without SDF technology

import React, { useRef, useMemo, useEffect } from 'react';
import * as THREE from 'three';

// Cool color palette
const COOL_COLORS = [
  '#00bcd4', // <PERSON>an
  '#3f51b5', // Indigo
  '#9c27b0', // Purple
  '#e91e63', // Pink
  '#2196f3', // Blue
  '#009688', // Teal
  '#673ab7', // Deep Purple
  '#4caf50', // Green
  '#8bc34a', // Light Green
  '#cddc39', // Lime
];

const ColorfulGraphNode = ({
  position = [0, 0, 0],
  label = '',
  color = null, // We'll ignore this and use our cool colors
  isStart = false,
  isHighlighted = false,
  distance,
  radius = 0.8,
  segments = 32,
  onClick,
  showDistance = true,
  startIndicatorColor = '#ff9800' // Orange
}) => {
  // Generate a unique color based on the node's label/id
  const nodeColor = useMemo(() => {
    // Use the label (which is usually the node ID) to pick a color
    const colorIndex = parseInt(label) % COOL_COLORS.length;
    return COOL_COLORS[colorIndex >= 0 ? colorIndex : 0];
  }, [label]);

  // Create refs for animation
  const nodeRef = useRef();

  // No need for state management - labels are handled by the label manager

  // Disable animation to prevent flickering
  // We'll just set a fixed scale based on highlighted state
  useEffect(() => {
    if (nodeRef.current) {
      if (isHighlighted) {
        // Slightly larger scale for highlighted nodes
        nodeRef.current.scale.set(1.05, 1.05, 1.05);
      } else {
        // Normal scale for regular nodes
        nodeRef.current.scale.set(1, 1, 1);
      }
    }
  }, [isHighlighted]);

  return (
    <group position={position}>
      {/* Main node */}
      <mesh ref={nodeRef} onClick={onClick}>
        <sphereGeometry args={[radius, segments, segments]} />
        <meshStandardMaterial
          color={nodeColor}
          emissive={nodeColor}
          emissiveIntensity={0.5}
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* No label - removing to eliminate flickering */}

      {/* Distance label is now handled by StableDistanceLabel */}
      {/* No HTML label needed here */}

      {/* Start indicator */}
      {isStart && (
        <group position={[0, radius * 1.5, 0]}>
          <mesh>
            <coneGeometry args={[radius * 0.4, radius * 0.8, 16]} />
            <meshStandardMaterial
              color={startIndicatorColor}
              emissive={startIndicatorColor}
              emissiveIntensity={0.5}
              metalness={0.8}
              roughness={0.2}
            />
          </mesh>
        </group>
      )}
    </group>
  );
};

export default ColorfulGraphNode;
