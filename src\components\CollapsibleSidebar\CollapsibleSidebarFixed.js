// CollapsibleSidebarFixed.js
// A component that provides a collapsible sidebar with toggle button and localStorage persistence

import React, { useState, useEffect } from 'react';
import { Box, IconButton, useTheme } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

const CollapsibleSidebarFixed = ({
  children,
  width = 300,
  collapsedWidth = 50,
  position = 'left', // 'left' or 'right'
  bgcolor,
  borderSide = position === 'left' ? 'right' : 'left',
  id, // Optional unique identifier
  ...otherProps
}) => {
  const theme = useTheme();
  const storageKey = id ? `sidebar_collapsed_${id}` : `sidebar_collapsed_${position}`;
  
  // Initialize state with default value
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Load collapse state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(storageKey);
      if (savedState !== null) {
        const parsedState = JSON.parse(savedState);
        console.log(`[DEBUG] Loading sidebar state from localStorage: ${parsedState}`);
        setIsCollapsed(parsedState);
      }
    } catch (error) {
      console.error('Error loading sidebar state from localStorage:', error);
    }
  }, [storageKey]);
  
  // Save collapse state to localStorage when it changes
  useEffect(() => {
    try {
      console.log(`[DEBUG] Saving sidebar state to localStorage: ${isCollapsed}`);
      localStorage.setItem(storageKey, JSON.stringify(isCollapsed));
    } catch (error) {
      console.error('Error saving sidebar state to localStorage:', error);
    }
  }, [isCollapsed, storageKey]);

  // Toggle sidebar collapse state
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Determine border side
  const borderStyle = {
    [`border${borderSide.charAt(0).toUpperCase() + borderSide.slice(1)}`]: 1,
    borderColor: "divider",
  };

  // Determine toggle button position
  const toggleButtonPosition = position === 'left'
    ? { right: -15 }
    : { left: -15 };

  return (
    <Box
      sx={{
        width: isCollapsed ? collapsedWidth : width,
        bgcolor: bgcolor || (theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.8)'),
        display: "flex",
        flexDirection: "column",
        position: "relative",
        overflow: "hidden",
        boxShadow: theme.palette.mode === 'dark' ? '0px 0px 10px rgba(0, 0, 0, 0.5)' : '0px 0px 10px rgba(0, 0, 0, 0.1)',
        transition: 'width 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease',
        flexShrink: 0, // Prevent the sidebar from shrinking
        flexGrow: 0, // Prevent the sidebar from growing
        zIndex: 10, // Ensure sidebar is above other elements
        ...borderStyle,
        ...otherProps
      }}
    >
      {/* Toggle Button */}
      <IconButton
        onClick={toggleCollapse}
        size="small"
        sx={{
          position: 'absolute',
          top: 10,
          zIndex: 1200,
          bgcolor: theme.palette.background.paper,
          boxShadow: theme.palette.mode === 'dark' ? '0px 0px 5px rgba(255, 255, 255, 0.2)' : '0px 0px 5px rgba(0, 0, 0, 0.2)',
          '&:hover': {
            bgcolor: theme.palette.action.hover,
          },
          transition: 'all 0.3s ease',
          ...toggleButtonPosition
        }}
      >
        {position === 'left'
          ? (isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />)
          : (isCollapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />)
        }
      </IconButton>

      {/* Content */}
      <Box
        sx={{
          flex: 1,
          opacity: isCollapsed ? 0 : 1,
          visibility: isCollapsed ? 'hidden' : 'visible',
          transition: 'opacity 0.3s ease',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {children}
      </Box>

      {/* Collapsed View Content */}
      {isCollapsed && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            pt: 2,
            gap: 2,
            overflow: 'hidden',
          }}
        >
          {/* You can add collapsed view content here */}
        </Box>
      )}
    </Box>
  );
};

export default CollapsibleSidebarFixed;
