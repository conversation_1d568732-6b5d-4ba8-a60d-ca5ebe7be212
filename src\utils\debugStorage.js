/**
 * Debug utility to log localStorage operations
 */

// Original functions from storageUtils.js with added logging
export const saveToStorageWithLogging = (key, value) => {
  try {
    console.log(`[DEBUG] Saving to localStorage - Key: ${key}, Value:`, value);
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error saving to localStorage (key: ${key}):`, error);
    return false;
  }
};

export const getFromStorageWithLogging = (key, defaultValue) => {
  try {
    const value = localStorage.getItem(key);
    const parsedValue = value !== null ? JSON.parse(value) : defaultValue;
    console.log(`[DEBUG] Reading from localStorage - Key: ${key}, Value:`, parsedValue);
    return parsedValue;
  } catch (error) {
    console.error(`Error reading from localStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

// Function to dump all localStorage contents
export const dumpLocalStorage = () => {
  console.log('===== LOCALSTORAGE DUMP =====');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    try {
      const value = JSON.parse(localStorage.getItem(key));
      console.log(`${key}: `, value);
    } catch (e) {
      console.log(`${key}: `, localStorage.getItem(key));
    }
  }
  console.log('=============================');
};
