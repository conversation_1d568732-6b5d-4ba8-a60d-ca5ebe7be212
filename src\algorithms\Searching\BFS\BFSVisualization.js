// BFSVisualization.js
// 3D visualization component for Breadth-First Search algorithm

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { generateBFSSteps } from './BFSAlgorithm';

// Constants for visualization
const NODE_RADIUS = 0.5;
const NODE_SEGMENTS = 32;
const EDGE_RADIUS = 0.05;
const EDGE_SEGMENTS = 8;

// Node colors
const getNodeColors = (theme) => {
  const isDark = theme?.palette?.mode === 'dark';

  return {
    default: '#4fc3f7', // Default blue
    start: '#4caf50', // Green for start node
    target: '#e91e63', // Pink for target node
    current: '#ffeb3b', // Yellow for current node
    visited: '#9c27b0', // Purple for visited nodes
    queued: '#ff9800', // Orange for queued nodes
    path: '#ff5722', // Deep orange for path

    // Environment colors
    ground: isDark ? '#1e272e' : '#ecf0f1',
    edge: isDark ? '#90a4ae' : '#78909c',
    edgeHighlight: '#ff5722', // Orange for highlighted edges
    text: isDark ? '#ffffff' : '#000000',
    shadow: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
  };
};

// Helper function to get animation duration based on speed
const getAnimationDuration = (speed) => {
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  // Faster speed = shorter duration
  // Speed 1 = 800ms, Speed 10 = 80ms
  const duration = 880 - (validSpeed * 80);
  return duration;
};

// Helper function to get delay between steps based on speed
const getDelay = (speed) => {
  const validSpeed = Math.max(1, Math.min(10, speed || 5));
  // Faster speed = shorter delay
  // Speed 1 = 1000ms, Speed 10 = 100ms
  const delay = 1100 - (validSpeed * 100);
  return delay;
};

// Node component
const Node = ({ position, radius, color, label, isAnimating, onClick }) => {
  // Use ref for the node mesh
  const nodeRef = useRef();

  // Pulse animation for current node
  useFrame(({ clock }) => {
    if (nodeRef.current && isAnimating) {
      const scale = 1 + Math.sin(clock.getElapsedTime() * 5) * 0.1;
      nodeRef.current.scale.set(scale, scale, scale);
    }
  });

  return (
    <group position={position}>
      {/* Node sphere */}
      <mesh ref={nodeRef} onClick={onClick} castShadow>
        <sphereGeometry args={[radius, NODE_SEGMENTS, NODE_SEGMENTS]} />
        <meshStandardMaterial
          color={color}
          metalness={0.3}
          roughness={0.4}
          emissive={color}
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Node label */}
      <Html position={[0, radius * 1.5, 0]} center>
        <div style={{
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          textShadow: '0 0 3px rgba(0,0,0,0.8)',
          userSelect: 'none',
          pointerEvents: 'none',
          backgroundColor: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px',
        }}>
          {label}
        </div>
      </Html>
    </group>
  );
};

// Edge component
const Edge = ({ start, end, color, thickness, isAnimating }) => {
  // Calculate edge direction and length
  const direction = new THREE.Vector3().subVectors(end, start);
  const length = direction.length();

  // Create a quaternion to rotate the cylinder to point from start to end
  const quaternion = new THREE.Quaternion();
  const upVector = new THREE.Vector3(0, 1, 0);
  direction.normalize();
  quaternion.setFromUnitVectors(upVector, direction);

  // Calculate midpoint for weight label
  const midpoint = new THREE.Vector3().addVectors(
    start,
    direction.clone().multiplyScalar(length / 2)
  );

  // Use ref for the edge mesh
  const edgeRef = useRef();

  // Pulse animation for highlighted edge
  useFrame(({ clock }) => {
    if (edgeRef.current && isAnimating) {
      const scale = 1 + Math.sin(clock.getElapsedTime() * 5) * 0.2;
      edgeRef.current.scale.set(1, 1, scale);
    }
  });

  return (
    <group>
      {/* Edge cylinder */}
      <mesh
        ref={edgeRef}
        position={midpoint}
        rotation={new THREE.Euler().setFromQuaternion(quaternion)}
        castShadow
      >
        <cylinderGeometry args={[thickness, thickness, length, EDGE_SEGMENTS]} />
        <meshStandardMaterial
          color={color}
          metalness={0.3}
          roughness={0.6}
          transparent={true}
          opacity={0.8}
        />
      </mesh>
    </group>
  );
};

// Main visualization component
const BFSVisualization = ({
  // eslint-disable-next-line no-unused-vars
  params,
  state,
  step,
  setStep,
  // eslint-disable-next-line no-unused-vars
  setTotalSteps,
  setState,
  theme,
  steps,
  // eslint-disable-next-line no-unused-vars
  setSteps,
  setMovements,
}) => {
  // Get theme-aware colors
  const colors = useMemo(() => getNodeColors(theme), [theme?.palette?.mode]);

  // Get camera from Three.js context
  const { camera, scene } = useThree();

  // Get speed from context
  const { speed } = useSpeed();
  const speedRef = useRef(speed);

  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Store the steps for visualization
  const stepsRef = useRef([]);

  // Update stepsRef when steps are received from props
  useEffect(() => {
    if (Array.isArray(steps) && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Animation state
  const [graphData, setGraphData] = useState({
    nodes: [],
    edges: [],
    startNode: 0,
    targetNode: null,
  });

  const [nodeStates, setNodeStates] = useState({
    current: null,
    visited: new Set(),
    queued: new Set(),
    path: new Set(),
  });

  const [edgeStates, setEdgeStates] = useState({
    current: null,
    visited: new Set(),
    path: new Set(),
  });

  // Animation state for highlighting elements
  const [animation, setAnimation] = useState({
    active: false,
    type: null,
    target: null,
    progress: 0,
    startTime: 0,
  });

  // Refs for animation control
  const animatingRef = useRef(false);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const currentStepRef = useRef(step);
  const timeoutIdRef = useRef(null);

  // Update state ref when state changes
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Update step ref when step changes
  useEffect(() => {
    currentStepRef.current = step;
  }, [step]);

  // Initialize graph data from params
  useEffect(() => {
    // Default parameters
    const numNodes = params?.nodes || 6;
    const startNode = params?.startNode || 0;
    const targetNode = params?.targetNode !== undefined ? params.targetNode : numNodes - 1;
    const density = params?.density || 0.5;
    const customEdges = params?.customEdges || [];

    // Generate graph data
    let graph = {};

    if (customEdges.length > 0) {
      // Use custom edges if provided
      graph = generateCustomGraph(numNodes, customEdges);
    } else {
      // Generate random graph
      graph = generateRandomGraph(numNodes, density);
    }

    // Generate node positions in a circle
    const nodes = [];
    const radius = Math.max(3, numNodes * 0.5);

    for (let i = 0; i < numNodes; i++) {
      const angle = (i / numNodes) * Math.PI * 2;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;

      nodes.push({
        id: i,
        position: [x, 0, z],
      });
    }

    // Generate edges from the graph
    const edges = [];

    for (let from = 0; from < numNodes; from++) {
      for (const to of graph[from]) {
        edges.push({
          id: `${from}-${to}`,
          from: parseInt(from),
          to: parseInt(to),
        });
      }
    }

    // Set graph data
    setGraphData({
      nodes,
      edges,
      startNode,
      targetNode,
    });

    // Initialize node states
    setNodeStates({
      current: null,
      visited: new Set(),
      queued: new Set([startNode]), // Start node is initially queued
      path: new Set(),
    });

    // Initialize edge states
    setEdgeStates({
      current: null,
      visited: new Set(),
      path: new Set(),
    });

    // Reset animation state
    setAnimation({
      active: false,
      type: null,
      target: null,
      progress: 0,
      startTime: 0,
    });

    // Clear any pending timeouts
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    lastAppliedStepRef.current = -1;
    animatingRef.current = false;

    // Generate BFS steps
    const { steps: bfsSteps } = generateBFSSteps(graph, startNode, targetNode);

    // Set steps and total steps
    if (setSteps && typeof setSteps === 'function') {
      setSteps(bfsSteps);
    }

    if (setTotalSteps && typeof setTotalSteps === 'function') {
      setTotalSteps(bfsSteps.length);
    }

    // Adjust camera position for better view
    if (camera) {
      camera.position.set(0, 8, radius * 2);
      camera.lookAt(0, 0, 0);
    }
  }, [params, camera, setSteps, setTotalSteps]);

  // Helper function to generate a custom graph
  const generateCustomGraph = (numNodes, edges) => {
    const graph = {};

    // Initialize nodes
    for (let i = 0; i < numNodes; i++) {
      graph[i] = [];
    }

    // Add edges
    edges.forEach(([from, to]) => {
      if (from >= 0 && from < numNodes && to >= 0 && to < numNodes) {
        if (!graph[from].includes(to)) {
          graph[from].push(to);
        }
      }
    });

    return graph;
  };

  // Helper function to generate a random graph
  const generateRandomGraph = (numNodes, density) => {
    const graph = {};

    // Initialize nodes
    for (let i = 0; i < numNodes; i++) {
      graph[i] = [];
    }

    // Add random edges
    for (let i = 0; i < numNodes; i++) {
      for (let j = 0; j < numNodes; j++) {
        if (i !== j && Math.random() < density) {
          graph[i].push(j);
        }
      }
    }

    // Ensure the graph is connected
    for (let i = 0; i < numNodes - 1; i++) {
      // If there's no edge from i to i+1, add one
      if (!graph[i].includes(i+1)) {
        graph[i].push(i+1);
      }
      // If there's no edge from i+1 to i, add one
      if (!graph[i+1].includes(i)) {
        graph[i+1].push(i);
      }
    }

    return graph;
  };

  // Apply a single step of BFS algorithm
  const applyStep = (stepIndex) => {
    if (stepIndex < 0) {
      return;
    }

    const steps = stepsRef.current;
    if (!steps || !steps.length) {
      return;
    }

    if (stepIndex >= steps.length) {
      // All steps completed
      return;
    }

    // Get the current step data
    const currentStep = steps[stepIndex];

    // Update steps sequence if available
    if (currentStep.movement && setMovements) {
      setMovements(prev => {
        const newMovements = [...prev, currentStep.movement];
        return newMovements;
      });

      // Auto-scroll to the bottom of the steps sequence list
      setTimeout(() => {
        const stepsContainer = document.querySelector('.steps-sequence-container');
        if (stepsContainer) {
          stepsContainer.scrollTop = stepsContainer.scrollHeight;
        }
      }, 100);
    }

    // Process step data based on type
    switch (currentStep.type) {
      case 'initialize':
        // Initialize BFS
        setNodeStates({
          current: null,
          visited: new Set(),
          queued: new Set([graphData.startNode]),
          path: new Set(),
        });
        setEdgeStates({
          current: null,
          visited: new Set(),
          path: new Set(),
        });
        break;

      case 'enqueue':
        // Enqueue a node
        if (currentStep.neighbor !== undefined) {
          // Enqueue a neighbor
          setNodeStates(prev => {
            const queued = new Set(prev.queued);
            queued.add(currentStep.neighbor);
            return {
              ...prev,
              queued,
            };
          });

          // Highlight the edge
          const edgeId = `${currentStep.current}-${currentStep.neighbor}`;
          setEdgeStates(prev => {
            const visited = new Set(prev.visited);
            visited.add(edgeId);
            return {
              ...prev,
              current: edgeId,
              visited,
            };
          });

          // Start animation
          setAnimation({
            active: true,
            type: 'edge',
            target: edgeId,
            progress: 0,
            startTime: Date.now(),
          });
          animatingRef.current = true;

          // Schedule animation end
          timeoutIdRef.current = setTimeout(() => {
            setAnimation(prev => ({
              ...prev,
              active: false,
            }));
            animatingRef.current = false;
            timeoutIdRef.current = null;
          }, getAnimationDuration(speedRef.current));
        } else {
          // Enqueue the start node
          setNodeStates(prev => ({
            ...prev,
            current: currentStep.current,
            queued: new Set([currentStep.current]),
          }));
        }
        break;

      case 'dequeue':
        // Dequeue a node and mark it as visited
        setNodeStates(prev => {
          const queued = new Set(prev.queued);
          const visited = new Set(prev.visited);

          queued.delete(currentStep.current);
          visited.add(currentStep.current);

          return {
            ...prev,
            current: currentStep.current,
            queued,
            visited,
          };
        });

        // Start animation
        setAnimation({
          active: true,
          type: 'node',
          target: currentStep.current,
          progress: 0,
          startTime: Date.now(),
        });
        animatingRef.current = true;

        // Schedule animation end
        timeoutIdRef.current = setTimeout(() => {
          setAnimation(prev => ({
            ...prev,
            active: false,
          }));
          animatingRef.current = false;
          timeoutIdRef.current = null;
        }, getAnimationDuration(speedRef.current));
        break;

      case 'check':
        // Check a neighbor
        const edgeId = `${currentStep.current}-${currentStep.neighbor}`;
        setEdgeStates(prev => ({
          ...prev,
          current: edgeId,
        }));

        // Start animation
        setAnimation({
          active: true,
          type: 'edge',
          target: edgeId,
          progress: 0,
          startTime: Date.now(),
        });
        animatingRef.current = true;

        // Schedule animation end
        timeoutIdRef.current = setTimeout(() => {
          setAnimation(prev => ({
            ...prev,
            active: false,
          }));
          animatingRef.current = false;
          timeoutIdRef.current = null;
        }, getAnimationDuration(speedRef.current));
        break;

      case 'skip':
        // Skip a neighbor (already visited)
        break;

      case 'found':
      case 'complete':
        // Reconstruct the path if target node is found
        if (graphData.targetNode !== null && currentStep.type === 'found') {
          // Reconstruct the path
          const path = new Set();
          const pathEdges = new Set();

          // Find the path in the steps
          let current = graphData.targetNode;
          let prev = null;

          // Find the parent of each node in the path
          for (let i = 0; i <= stepIndex; i++) {
            const step = steps[i];
            if (step.type === 'enqueue' && step.neighbor === current) {
              prev = step.current;
              path.add(current);
              pathEdges.add(`${prev}-${current}`);
              current = prev;

              // If we've reached the start node, we're done
              if (current === graphData.startNode) {
                path.add(graphData.startNode);
                break;
              }

              // Start over to find the parent of the new current node
              i = -1;
            }
          }

          setNodeStates(prev => ({
            ...prev,
            path,
          }));

          setEdgeStates(prev => ({
            ...prev,
            path: pathEdges,
          }));
        }
        break;

      default:
        break;
    }

    // Update last applied step
    lastAppliedStepRef.current = stepIndex;
  };

  // Handle step changes
  useEffect(() => {
    // Skip if step is null or undefined
    if (step === null || step === undefined) {
      return;
    }

    // Skip if this step was already applied
    if (step === lastAppliedStepRef.current) {
      return;
    }

    // Determine direction of step change
    const direction = step > lastAppliedStepRef.current ? 1 : -1;

    // Only cancel animations when stepping backward
    if (direction < 0) {
      // Cancel any ongoing animations or timeouts
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      // Disable any active animations
      setAnimation({
        active: false,
        type: null,
        target: null,
        progress: 0,
        startTime: 0,
      });
      animatingRef.current = false;
    }

    // If step is 0, reset the visualization
    if (step === 0) {
      setNodeStates({
        current: null,
        visited: new Set(),
        queued: new Set([graphData.startNode]),
        path: new Set(),
      });

      setEdgeStates({
        current: null,
        visited: new Set(),
        path: new Set(),
      });
    }

    // Only process steps if they're within the valid range
    if (step >= 0 && step < (stepsRef.current?.length || 0)) {
      // Process step
      applyStep(step);
    } else if (step >= (stepsRef.current?.length || 0)) {
      // We've reached the end of steps
      lastAppliedStepRef.current = step;
    }
  }, [step, state, graphData.startNode, graphData.targetNode]);

  // Handle automatic stepping when state is 'running'
  useEffect(() => {
    let timeoutId = null;

    // Function to run the next step
    const runNextStep = () => {
      // Only proceed if state is still 'running'
      if (stateRef.current !== 'running') {
        return;
      }

      // Get the current step from the ref
      const currentStep = currentStepRef.current;

      // Stop if we've reached the end
      if (currentStep >= (stepsRef.current?.length || 0)) {
        // Set state to completed
        if (setState && typeof setState === 'function') {
          setState('completed');
        }
        return;
      }

      // Don't schedule next step if currently animating
      if (animatingRef.current) {
        // Check again after a short delay
        timeoutId = setTimeout(runNextStep, 100);
        return;
      }

      // Increment step
      if (setStep && typeof setStep === 'function') {
        const nextStep = currentStep + 1;
        setStep(nextStep);
        // Update the ref immediately
        currentStepRef.current = nextStep;
      }

      // Schedule the next step with a delay
      const delay = getDelay(speedRef.current);
      timeoutId = setTimeout(runNextStep, delay);
    };

    // Start or stop the animation based on state
    if (state === 'running') {
      // Start the animation
      timeoutId = setTimeout(runNextStep, 100);
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, setStep, setState]);

  // Update animation progress
  useFrame(() => {
    if (animation.active) {
      const elapsed = Date.now() - animation.startTime;
      const duration = getAnimationDuration(speedRef.current);
      const progress = Math.min(elapsed / duration, 1);

      setAnimation(prev => ({
        ...prev,
        progress,
      }));
    }
  });

  // Render the visualization
  return (
    <group position={[0, 0, 0]} rotation={[-0.1, 0, 0]}>
      {/* Enhanced Lighting Setup */}
      <ambientLight intensity={0.6} />
      <directionalLight
        position={[10, 10, 10]}
        intensity={0.8}
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      <directionalLight position={[-10, 10, -10]} intensity={0.4} />
      <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

      {/* Ground plane */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.5, 0]} receiveShadow>
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial color={colors.ground} roughness={0.8} metalness={0.2} />
      </mesh>

      {/* Grid */}
      <gridHelper args={[50, 50, '#444444', '#222222']} position={[0, -0.49, 0]} />

      {/* Render edges */}
      {graphData.edges.map(edge => {
        const fromNode = graphData.nodes.find(node => node.id === edge.from);
        const toNode = graphData.nodes.find(node => node.id === edge.to);

        if (!fromNode || !toNode) return null;

        // Determine edge color and thickness
        let edgeColor = colors.edge;
        let edgeThickness = EDGE_RADIUS;
        let isAnimating = false;

        if (edgeStates.path.has(edge.id)) {
          edgeColor = colors.path;
          edgeThickness = EDGE_RADIUS * 1.5;
        } else if (edge.id === edgeStates.current) {
          edgeColor = colors.edgeHighlight;
          edgeThickness = EDGE_RADIUS * 1.2;
          isAnimating = animation.active && animation.type === 'edge' && animation.target === edge.id;
        } else if (edgeStates.visited.has(edge.id)) {
          edgeColor = colors.visited;
          edgeThickness = EDGE_RADIUS * 1.2;
        }

        return (
          <Edge
            key={edge.id}
            start={new THREE.Vector3(...fromNode.position)}
            end={new THREE.Vector3(...toNode.position)}
            color={edgeColor}
            thickness={edgeThickness}
            isAnimating={isAnimating}
          />
        );
      })}

      {/* Render nodes */}
      {graphData.nodes.map(node => {
        // Determine node color
        let nodeColor = colors.default;
        let isAnimating = false;

        if (node.id === graphData.startNode) {
          nodeColor = colors.start;
        } else if (node.id === graphData.targetNode) {
          nodeColor = colors.target;
        } else if (nodeStates.path.has(node.id)) {
          nodeColor = colors.path;
        } else if (node.id === nodeStates.current) {
          nodeColor = colors.current;
          isAnimating = animation.active && animation.type === 'node' && animation.target === node.id;
        } else if (nodeStates.visited.has(node.id)) {
          nodeColor = colors.visited;
        } else if (nodeStates.queued.has(node.id)) {
          nodeColor = colors.queued;
        }

        return (
          <Node
            key={node.id}
            position={node.position}
            radius={NODE_RADIUS}
            color={nodeColor}
            label={`Node ${node.id}`}
            isAnimating={isAnimating}
          />
        );
      })}
    </group>
  );
};

export default BFSVisualization;
