// InsertionSortController.js
// Modern Insertion Sort controller following the established pattern

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// Import algorithm components
import { generateInsertionSortDetailedSteps } from './InsertionSortDetailedSteps';
import config from './InsertionSortConfig';

const InsertionSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        setTotalSteps,
        steps,
        setSteps,
        algorithmArray,
        setAlgorithmArray
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Generate array and steps when parameters change
    useEffect(() => {
        // Generate array based on parameters
        let array = [];
        if (customArray && customArray.length > 0) {
            array = [...customArray];
        } else if (randomize) {
            array = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 999) + 1
            );
        } else {
            // Generate reverse sorted array (worst case for insertion sort)
            array = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        console.log('InsertionSortController - Generated array:', array);

        // IMPORTANT: Set the array in the context so visualization can use it
        setAlgorithmArray(array);

        // Generate steps
        const generatedSteps = generateInsertionSortDetailedSteps(array);
        console.log('InsertionSortController - Generated steps:', generatedSteps.length);

        // Set total steps
        setTotalSteps(generatedSteps.length);

        // Set steps for visualization
        setSteps(generatedSteps);

        // Reset to initial state
        setState('idle');
        setStep(0);

        // Log completion
        console.log(`InsertionSortController - Total steps: ${generatedSteps.length}`);
    }, [arraySize, randomize, customArray, setAlgorithmArray, setSteps, setTotalSteps, setState, setStep]);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change with debounce
    const handleArraySizeChange = (value) => {
        // Validate the input
        if (value >= 3 && value <= 20) {
            // Clear any existing timeout
            if (arraySizeTimeoutId) {
                clearTimeout(arraySizeTimeoutId);
            }

            // Set a new timeout to debounce the change
            const timeoutId = setTimeout(() => {
                // Always reset when changing array size
                if (typeof onParamChange === 'function') {
                    onParamChange({ ...params, arraySize: value });
                }
                setState('idle');
                setStep(0);
            }, 300); // 300ms debounce

            setArraySizeTimeoutId(timeoutId);
        }
    };

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (arraySizeTimeoutId) {
                clearTimeout(arraySizeTimeoutId);
            }
        };
    }, [arraySizeTimeoutId]);

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({ ...params, randomize: checked });
        }
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
        setCustomArrayError('');
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // IMPORTANT: Set the array in the context directly
            console.log('InsertionSortController - Setting custom array in context:', parsedArray);
            setAlgorithmArray(parsedArray);

            // Generate steps for the custom array
            const generatedSteps = generateInsertionSortDetailedSteps(parsedArray);
            console.log('InsertionSortController - Generated steps for custom array:', generatedSteps.length);

            // Set the steps in the context
            setSteps(generatedSteps);
            setTotalSteps(generatedSteps.length);

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title={"Insertion Sort"}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Insertion Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Insertion sort is a simple sorting algorithm that builds the final sorted array one item at a time. It works by taking one element from the unsorted part and inserting it into its correct position in the sorted part.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n) when the array is already sorted
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n²)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n²) when the array is sorted in reverse order
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(1) - Insertion Sort is an in-place sorting algorithm
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={step > 0 ? (steps || []).slice(1).map(step => ({
                    description: step.statement || ''
                })) : []}
                currentStep={step > 0 ? step - 1 : 0}
                defaultExpanded
                renderStep={(_, index) => {
                    const currentStep = steps && steps[index + 1]; // Add 1 to skip initial step
                    const isCurrentStep = index === step - 1;

                    if (!currentStep) return null;

                    return (
                        <Typography
                            variant="body2"
                            component="div"
                            sx={{
                                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                                fontSize: '0.85rem',
                                fontWeight: isCurrentStep ? 'bold' : 'normal',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                mb: 0.75,
                                pb: 0.75,
                                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                    bgcolor: 'action.hover',
                                    borderRadius: '4px',
                                }
                            }}
                        >
                            <Box
                                component="span"
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: '24px',
                                    height: '24px',
                                    borderRadius: '12px',
                                    bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                                    color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                                    mr: 1.5,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold',
                                    flexShrink: 0,
                                    boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {index + 1}
                            </Box>
                            {currentStep.statement || 'Processing...'}
                        </Typography>
                    );
                }}
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Insertion Sort Algorithm"
                defaultExpanded
                currentStep={step >= 0 && steps && steps.length > 0 ?
                    // Map the detailed step types to corresponding line numbers
                    steps[step]?.type === 'initial' ? 0 :
                        steps[step]?.type === 'info' ? 1 :
                            steps[step]?.type === 'select' ? 3 :
                                steps[step]?.type === 'compare' ? 6 :
                                    steps[step]?.type === 'shift' ? 7 :
                                        steps[step]?.type === 'shifted' ? 8 :
                                            steps[step]?.type === 'found' ? 9 :
                                                steps[step]?.type === 'insert' ? 10 :
                                                    steps[step]?.type === 'progress' ? 11 :
                                                        steps[step]?.type === 'complete' ? 12 : 0
                    : 0
                }
                algorithm={[
                    { code: "function insertionSort(arr):", lineNumber: 0, indent: 0 },
                    { code: "# First element is considered sorted", lineNumber: 1, indent: 1 },
                    { code: "for i = 1 to len(arr) - 1:", lineNumber: 2, indent: 1 },
                    { code: "current = arr[i]  # Element to be inserted", lineNumber: 3, indent: 2 },
                    { code: "j = i - 1  # Start from sorted portion", lineNumber: 4, indent: 2 },
                    { code: "# Find correct position for insertion", lineNumber: 5, indent: 2 },
                    { code: "while j >= 0 and arr[j] > current:", lineNumber: 6, indent: 2 },
                    { code: "arr[j + 1] = arr[j]  # Shift element right", lineNumber: 7, indent: 3 },
                    { code: "j = j - 1  # Move to previous element", lineNumber: 8, indent: 3 },
                    { code: "# Found correct position", lineNumber: 9, indent: 2 },
                    { code: "arr[j + 1] = current  # Insert element", lineNumber: 10, indent: 2 },
                    { code: "# Element inserted, sorted portion grows", lineNumber: 11, indent: 2 },
                    { code: "# Array is completely sorted", lineNumber: 12, indent: 1 }
                ]}
            />
        </Box>
    );
};

export default InsertionSortController;
