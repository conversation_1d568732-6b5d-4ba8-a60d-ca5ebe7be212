// CountingSortBasic.js
// A bare-bones implementation of Counting Sort

/**
 * Performs a basic counting sort on an array of positive integers
 * @param {Array} arr - The array to sort
 * @returns {Array} - The sorted array
 */
export const countingSort = (arr) => {
  // Create a copy of the array
  const inputArray = [...arr];

  // Find the maximum element
  const max = Math.max(...inputArray);

  // Create count array
  const count = new Array(max + 1).fill(0);

  // Count occurrences
  for (let i = 0; i < inputArray.length; i++) {
    count[inputArray[i]]++;
  }

  // Create output array
  const output = [];

  // Build the sorted array
  for (let i = 0; i <= max; i++) {
    for (let j = 0; j < count[i]; j++) {
      output.push(i);
    }
  }

  return output;
};

/**
 * Generates minimal steps for visualization
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object with steps and sorted array
 */
export const generateBasicSteps = (arr) => {
  try {
    // Sort the array
    const sortedArray = countingSort(arr);

    // Create minimal steps
    const steps = [
      {
        type: 'init',
        array: [...arr],
        movement: 'Initialize Counting Sort'
      },
      {
        type: 'complete',
        array: sortedArray,
        sorted: Array.from({ length: sortedArray.length }, (_, i) => i),
        movement: 'Counting Sort complete'
      }
    ];

    console.log('Basic steps generated:', { steps, sortedArray });
    return { steps, sortedArray };
  } catch (error) {
    console.error('Error in generateBasicSteps:', error);
    return {
      steps: [
        {
          type: 'init',
          array: [...arr],
          movement: 'Initialize Counting Sort'
        },
        {
          type: 'complete',
          array: [...arr],
          sorted: Array.from({ length: arr.length }, (_, i) => i),
          movement: 'Error in Counting Sort'
        }
      ],
      sortedArray: [...arr]
    };
  }
};

export default {
  countingSort,
  generateSteps: generateBasicSteps
};
