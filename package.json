{"name": "algorithm-simulator", "version": "0.1.0", "private": true, "dependencies": {"@base-ui-components/react": "^1.0.0-alpha.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/base": "^5.0.0-beta.70", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@react-spring/three": "^9.7.5", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "framer-motion": "^12.6.3", "react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1", "react-spring": "^9.7.5", "three": "^0.175.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"prettier": "3.5.3"}}