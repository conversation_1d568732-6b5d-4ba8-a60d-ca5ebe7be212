// EditDistance/index.js
// Export only what's needed from this algorithm

import EditDistanceVisualization from './EditDistanceVisualization';
import EditDistanceController from './EditDistanceController';
import EditDistanceAlgorithm from './EditDistanceAlgorithm';

export const metadata = {
  id: 'EditDistance',
  name: 'Edit Distance',
  description: 'A dynamic programming algorithm that calculates the minimum number of operations (insertions, deletions, or substitutions) required to transform one string into another.',
  timeComplexity: 'O(m*n)',
  spaceComplexity: 'O(m*n)',
  defaultParams: {
    string1: 'kitten',
    string2: 'sitting',
  },
};

export const components = {
  visualization: EditDistanceVisualization,
  controller: EditDistanceController,
  algorithm: EditDistanceAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
