// BubbleSortController.js
// This component provides the controls for the Bubble Sort algorithm.

import React, { useEffect, useState, useRef } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';
import { generateRandomArray, algorithmConfig } from '../../../utils/algorithmUtils';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// No need to import BubbleSortAlgorithm as we're using the AlgorithmSection component directly

const BubbleSortController = (props) => {
  // Removed excessive logging

  // Destructure props
  const { params = {}, onParamChange = () => { } } = props;
  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Extract parameters with safety checks
  const paramArraySize = params?.arraySize || 10;
  // Local state for array size to make UI more responsive
  const [arraySize, setArraySize] = useState(paramArraySize);
  const randomize = params?.randomize !== undefined ? params.randomize : true;
  const customArray = params?.customArray || [];

  // State for custom array input
  const [customArrayInput, setCustomArrayInput] = useState('');
  const [customArrayError, setCustomArrayError] = useState('');
  const [useCustomArray, setUseCustomArray] = useState(false); // Initialize to false, independent of randomize

  // Debounce mechanism for array size changes
  const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

  // Function to generate steps for the bubble sort algorithm
  const generateStepsWithMessages = (array) => {
    if (!array || array.length <= 1) {
      return [];
    }

    const steps = [];
    const arrayCopy = [...array];
    const n = arrayCopy.length;

    // Add initial state step at index 0
    steps.push({
      type: 'initial',
      array: [...arrayCopy],
      message: `Initial array: [${arrayCopy.join(', ')}]`
    });

    for (let i = 0; i < n - 1; i++) {
      for (let j = 0; j < n - i - 1; j++) {
        // Add comparison step
        steps.push({
          type: 'compare',
          indices: [j, j + 1],
          values: [arrayCopy[j], arrayCopy[j + 1]],
          message: `Compare elements at indices ${j} and ${j + 1}: ${arrayCopy[j]} and ${arrayCopy[j + 1]}`
        });

        // Add swap step if needed
        if (arrayCopy[j] > arrayCopy[j + 1]) {
          const temp = arrayCopy[j];
          arrayCopy[j] = arrayCopy[j + 1];
          arrayCopy[j + 1] = temp;

          steps.push({
            type: 'swap',
            indices: [j, j + 1],
            values: [arrayCopy[j], arrayCopy[j + 1]],
            message: `Swap elements at indices ${j} and ${j + 1}: ${arrayCopy[j]} and ${arrayCopy[j + 1]}`
          });
        }

        // Add sorted step for the last element of each pass
        if (j === n - i - 2) {
          steps.push({
            type: 'sorted',
            index: n - i - 1,
            value: arrayCopy[n - i - 1],
            message: `Element at index ${n - i - 1} is now in its sorted position: ${arrayCopy[n - i - 1]}`
          });
        }
      }
    }

    // Mark the first element as sorted (it will be the smallest)
    // Only add this step if we have more than one element and it's not already marked as sorted
    if (n > 1 && !steps.some(step => step.type === 'sorted' && step.index === 0)) {
      steps.push({
        type: 'sorted',
        index: 0,
        value: arrayCopy[0],
        message: `Element at index 0 is now in its sorted position: ${arrayCopy[0]}`
      });
    }

    return steps;
  };

  // Get the necessary functions from context
  const { setAlgorithmArray, setSteps, setTotalSteps } = useAlgorithm();

  // Calculate total steps when array size changes
  useEffect(() => {
    // Generate array data
    let array = [];

    // Use custom array if provided
    if (customArray.length > 0) {
      array = [...customArray];
      console.log('BubbleSortController - Using custom array:', array);
    } else if (randomize) {
      // Generate random array using utility function with configured range
      array = generateRandomArray(arraySize);
      console.log('BubbleSortController - Generated random array:', array);
    } else {
      // Generate sorted array in reverse order (worst case for bubble sort)
      array = Array.from({ length: arraySize }, (_, i) => arraySize - i);
      console.log('BubbleSortController - Generated reverse sorted array:', array);
    }

    // IMPORTANT: Set the array in the context so visualization can use it
    setAlgorithmArray(array);

    // Generate steps
    const generatedSteps = generateStepsWithMessages(array);
    // console.log('BubbleSortController - Generated steps:', generatedSteps);

    // Set total steps
    setTotalSteps(generatedSteps.length);

    // Set steps for visualization
    setSteps(generatedSteps);

    // Log completion
    console.log(`BubbleSortController - Total steps: ${generatedSteps.length}`);
  }, [arraySize, randomize, customArray, setAlgorithmArray, setSteps, setTotalSteps]);

  // Simplified lifecycle logging
  useEffect(() => {
    // console.log('BubbleSortController - Component mounted');
    return () => {
      // console.log('BubbleSortController - Component unmounted');
    };
  }, []);

  // Create a ref to track previous params
  const prevParamsRef = useRef(params);

  // Update local state when params change from parent
  useEffect(() => {
    // Only update if params.arraySize has actually changed from previous props
    const prevParams = prevParamsRef.current;
    if (params?.arraySize !== undefined && params.arraySize !== prevParams?.arraySize) {
      console.log('Updating local arraySize state from props:', params.arraySize);
      setArraySize(params.arraySize);
    }
    // Update the ref with current params
    prevParamsRef.current = params;
  }, [params]); // Only depend on params, not on arraySize

  // Removed excessive logging useEffects that were causing performance issues

  // Create a ref to track previous state
  const prevStateRef = useRef(state);

  // Set state to completed when step reaches totalSteps
  useEffect(() => {
    // Store current state for next render
    prevStateRef.current = state;

    // Only log when actually reaching the final step
    if (step === totalSteps && step > 0) {
      console.log('BubbleSortController - Reached final step');
    }

    // Only update if we have valid steps and we're not in idle state
    if (totalSteps > 0 && state !== 'idle') {
      // If we've reached the last step, mark as completed (only if not already completed)
      if (step >= totalSteps && state !== 'completed') {
        setState('completed');
      }
      // If we were in completed state but stepped back, go to paused
      else if (state === 'completed' && step < totalSteps) {
        setState('paused');
      }
    }
  }, [step, totalSteps, state]); // Remove setState from dependencies

  // Create a ref to store the latest array size value
  const latestArraySizeRef = useRef(arraySize);

  // Handle array size change with debounce
  const handleArraySizeChange = (value) => {
    console.log('handleArraySizeChange called with value:', value);

    // Validate the input
    if (value >= 3 && value <= 20) {
      // Update the ref with the latest value immediately
      latestArraySizeRef.current = value;

      // Update local state immediately for responsive UI
      // This makes the slider move smoothly
      setArraySize(value);

      // Clear any existing timeout
      if (arraySizeTimeoutId) {
        clearTimeout(arraySizeTimeoutId);
      }

      // Set a new timeout to debounce the expensive operations
      const timeoutId = setTimeout(() => {
        // Get the latest value from the ref
        const latestValue = latestArraySizeRef.current;
        console.log('Debounce timeout fired with latest value:', latestValue);

        // Create a new params object with the latest value
        const newParams = { ...params };

        // Only update if the value has actually changed
        if (newParams.arraySize !== latestValue) {
          newParams.arraySize = latestValue;

          // Call onParamChange with the new params
          if (typeof onParamChange === 'function') {
            console.log('Calling onParamChange with:', newParams);
            onParamChange(newParams);
          } else {
            console.log('onParamChange is not a function');
          }

          // Reset state
          setState('idle');
          setStep(0);
        }
      }, 300); // 300ms debounce

      setArraySizeTimeoutId(timeoutId);
    }
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (arraySizeTimeoutId) {
        clearTimeout(arraySizeTimeoutId);
      }
    };
  }, [arraySizeTimeoutId]);

  // Handle randomize toggle
  const handleRandomizeChange = (value) => {
    // When enabling randomize, clear any custom array but don't affect useCustomArray toggle
    if (value) {
      onParamChange({ ...params, randomize: value, customArray: [] });
      // Don't update useCustomArray state to keep toggles independent
    } else {
      onParamChange({ ...params, randomize: value });
    }
    setState('idle');
    setStep(0);
  };

  // Handle custom array input change
  const handleCustomArrayInputChange = (value) => {
    setCustomArrayInput(value);
    setCustomArrayError('');
  };

  // Apply custom array
  const applyCustomArray = () => {
    // Parse the custom array input
    const inputStr = customArrayInput.trim();
    if (!inputStr) {
      setCustomArrayError('Please enter comma-separated numbers');
      return;
    }

    // Split by comma and parse each value
    const values = inputStr.split(',').map(val => val.trim());
    const parsedValues = [];

    for (const val of values) {
      const num = parseInt(val, 10);
      if (isNaN(num)) {
        setCustomArrayError(`Invalid number: ${val}`);
        return;
      }
      if (num < 1 || num > 9999) {
        setCustomArrayError('Numbers must be between 1 and 9999');
        return;
      }
      parsedValues.push(num);
    }

    if (parsedValues.length < 3) {
      setCustomArrayError('Please enter at least 3 numbers');
      return;
    }

    if (parsedValues.length > 20) {
      setCustomArrayError('Please enter at most 20 numbers');
      return;
    }

    // Apply the custom array without affecting randomize toggle
    onParamChange({ ...params, customArray: parsedValues });
    // No need to update useCustomArray as it should already be true when this function is called
    setState('idle');
    setStep(0);
  };

  return (
    <Box>
      {/* Information Section */}
      <InformationSection title={'Bubble Sort'}>
        <Box>
          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            About Bubble Sort:
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Bubble Sort is a simple comparison-based sorting algorithm. It repeatedly steps through the list, compares adjacent elements, and swaps them if they are in the wrong order.
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Time Complexity:
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Best Case: O(n) when the array is already sorted
          </Typography>
          <Typography variant="body2" sx={{ mb: 0.5 }}>
            - Average Case: O(n²)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            - Worst Case: O(n²) when the array is sorted in reverse order
          </Typography>

          <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
            Space Complexity:
          </Typography>
          <Typography variant="body2">
            O(1) - Bubble Sort is an in-place sorting algorithm
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'arraySize',
            type: 'slider',
            label: 'Array Size',
            min: 3,
            max: 20,
            step: 1,
            defaultValue: arraySize,
            icon: ViewArrayIcon,
            disableWhen: 'useCustomArray' // Disable array size when custom array is enabled
          },
          {
            name: 'randomize',
            type: 'switch',
            label: 'Randomize Array',
            defaultValue: randomize,
            icon: ShuffleIcon,
            disableWhen: 'useCustomArray' // Disable randomize when custom array is enabled
          },
          {
            name: 'useCustomArray',
            type: 'switch',
            label: 'Use Custom Array',
            defaultValue: useCustomArray, // Use the state value, independent of randomize
            // Allow toggling between randomize and custom array
            icon: FormatListNumberedIcon
          },
          {
            name: 'customArrayInput',
            type: 'customArray',
            label: 'Custom Array',
            // Don't use defaultValue here as we're using value in the values prop
            showOnlyWhen: 'useCustomArray',
            // Custom array should not be disabled
            error: customArrayError,
            helperText: "Enter comma-separated numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
            placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
            onApply: applyCustomArray,
            icon: FormatListNumberedIcon
          }
        ]}
        values={{
          arraySize,
          randomize,
          useCustomArray,
          customArrayInput
        }}
        onChange={(newValues) => {
          if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize && !useCustomArray) {
            // Only allow changing array size when custom array is not enabled
            handleArraySizeChange(newValues.arraySize);
          }
          if (newValues.randomize !== undefined && newValues.randomize !== randomize && !useCustomArray) {
            // Only allow changing randomize when custom array is not enabled
            handleRandomizeChange(newValues.randomize);
          }
          if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
            setUseCustomArray(newValues.useCustomArray);
            // Just update the state, don't affect randomize toggle
            setState('idle');
            setStep(0);
          }
          if (newValues.customArrayInput !== undefined) {
            handleCustomArrayInputChange(newValues.customArrayInput);
          }
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={() => setState('running')}
        onPause={() => setState('paused')}
        onReset={() => {
          setState('idle');
          setStep(0);
        }}
        onStepForward={() => {
          if (step < totalSteps) {
            setStep(step + 1);
            // If this will be the last step, mark as completed
            if (step + 1 >= totalSteps) {
              setState('completed');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        onStepBackward={() => {
          if (step > 0) {
            setStep(step - 1);
            // If we were in completed state, go back to paused
            if (state === 'completed') {
              setState('paused');
            }
            // If we were in idle state, go to paused
            else if (state === 'idle') {
              setState('paused');
            }
          }
        }}
        showStepControls={true}
      />

      {/* Progress Indicator Section */}
      <ProgressSection
        state={state}
        step={step}
        totalSteps={totalSteps}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={step}
        defaultExpanded
        renderStep={(_, index) => {
          const currentStep = steps && steps[index];
          const isCurrentStep = index === step;

          if (!currentStep) return null;

          return (
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                fontSize: '0.85rem',
                fontWeight: isCurrentStep ? 'bold' : 'normal',
                whiteSpace: 'nowrap',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: '20px',
                  height: '20px',
                  borderRadius: '10px',
                  bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                  color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                  mr: 1,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  flexShrink: 0,
                }}
              >
                {index + 1}
              </Box>
              {currentStep.type === 'initial' ? (
                <>
                  Initial array:{' '}
                  <Box component="span" sx={{ color: 'info.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    [{currentStep.array && currentStep.array.join(', ')}]
                  </Box>
                </>
              ) : currentStep.type === 'compare' ? (
                <>
                  Compare elements at indices{' '}
                  <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>{' '}
                  and{' '}
                  <Box component="span" sx={{ color: 'primary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>: {' '}
                  <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.values && currentStep.values[0]}
                  </Box>{' '}
                  and{' '}
                  <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.values && currentStep.values[1]}
                  </Box>
                </>
              ) : currentStep.type === 'swap' ? (
                <>
                  Swap elements at indices{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[0]}
                  </Box>{' '}
                  and{' '}
                  <Box component="span" sx={{ color: 'secondary.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.indices && currentStep.indices[1]}
                  </Box>
                </>
              ) : (
                <>
                  Element at index{' '}
                  <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.index !== undefined ? currentStep.index : ''}
                  </Box>{' '}
                  is now in its sorted position:{' '}
                  <Box component="span" sx={{ color: 'success.main', fontWeight: 'bold', display: 'inline', mx: 0.5 }}>
                    {currentStep.value !== undefined ? currentStep.value : ''}
                  </Box>
                </>
              )}
            </Typography>
          );
        }}
        emptyMessage="No steps yet. Start the algorithm to see the sequence."
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        title="Bubble Sort: Algorithm"
        defaultExpanded
        currentStep={step >= 0 && steps && steps.length > 0 ?
          // Map the current step to the corresponding line number
          steps[step]?.type === 'initial' ? 0 :
          steps[step]?.type === 'compare' ? 3 :
          steps[step]?.type === 'swap' ? 5 :
          steps[step]?.type === 'sorted' ? 6 : 0
          : 0
        }
        algorithm={[
          // Use BubbleSortAlgorithm component's algorithm steps
          { code: "function bubbleSort(arr):", lineNumber: 0, indent: 0 },
          { code: "for i in range(len(arr) - 1):", lineNumber: 1, indent: 1 },
          { code: "for j in range(len(arr) - i - 1):", lineNumber: 2, indent: 2 },
          { code: "# Compare adjacent elements", lineNumber: 3, indent: 3 },
          { code: "if arr[j] > arr[j + 1]:", lineNumber: 4, indent: 3 },
          { code: "arr[j], arr[j + 1] = arr[j + 1], arr[j]", lineNumber: 5, indent: 4 },
          { code: "return arr", lineNumber: 6, indent: 1 },
        ]}
      />
    </Box>
  );
};

export default BubbleSortController;