// AStar/index.js
// Export A* algorithm components

import AStarAlgorithm from './AStarAlgorithm';
import AStarController from './AStarController';
import AStarVisualization from './AStarVisualization';

export {
  AStarAlgorithm,
  AStarController,
  AStarVisualization
};

// Extract the generateAStarSteps function from the algorithm
const { generateAStarSteps } = AStarAlgorithm;

export default {
  name: 'A* Algorithm',
  category: 'Graph',
  component: AStarAlgorithm,
  controller: AStarController,
  visualization: AStarVisualization,
  description: 'A* is a pathfinding algorithm that combines the advantages of <PERSON><PERSON><PERSON>\'s algorithm and greedy best-first search. It uses a heuristic function to estimate the cost from the current node to the goal, which helps guide the search more efficiently.',
  details: `
    A* (pronounced "A-star") is a pathfinding algorithm that finds the shortest path between two nodes in a graph. It's widely used in artificial intelligence, robotics, and video games.

    Key features:
    - Combines <PERSON><PERSON><PERSON>'s algorithm and greedy best-first search
    - Uses a heuristic function to guide the search
    - Guarantees the shortest path if the heuristic is admissible
    - More efficient than <PERSON><PERSON><PERSON>'s algorithm for many problems

    Time Complexity: O((V + E) log V) in the worst case
    Space Complexity: O(V) for storing the open and closed sets
  `,
  pseudocode: `
    function aStar(graph, start, goal):
      // Initialize data structures
      openSet = {start}
      closedSet = {}
      gScore = {start: 0, all other nodes: ∞}
      fScore = {start: heuristic(start, goal), all other nodes: ∞}
      previous = {}

      while openSet is not empty:
        current = node in openSet with lowest fScore

        if current == goal:
          return reconstructPath(previous, goal)

        openSet.remove(current)
        closedSet.add(current)

        for each neighbor of current:
          if neighbor in closedSet:
            continue

          tentativeGScore = gScore[current] + distance(current, neighbor)

          if tentativeGScore < gScore[neighbor]:
            previous[neighbor] = current
            gScore[neighbor] = tentativeGScore
            fScore[neighbor] = gScore[neighbor] + heuristic(neighbor, goal)

            if neighbor not in openSet:
              openSet.add(neighbor)

      return failure (no path exists)
  `
};
