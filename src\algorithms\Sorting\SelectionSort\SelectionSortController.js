// SelectionSortController.js
// This component provides the controls for the Selection Sort algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';
import { generateSelectionSortDetailedSteps } from './SelectionSortDetailedSteps';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const SelectionSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        setTotalSteps,
        steps,
        setSteps,
        setAlgorithmArray
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Generate array and steps when parameters change
    useEffect(() => {
        let array;

        if (customArray && customArray.length > 0) {
            // Use custom array
            array = [...customArray];
        } else if (randomize) {
            // Generate random array
            array = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 999) + 1
            );
        } else {
            // Generate reverse sorted array (worst case for selection sort)
            array = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        console.log('SelectionSortController - Generated array:', array);

        // IMPORTANT: Set the array in the context so visualization can use it
        setAlgorithmArray(array);

        // Generate steps
        const generatedSteps = generateSelectionSortDetailedSteps(array);
        console.log('SelectionSortController - Generated steps:', generatedSteps.length);

        // Set total steps
        setTotalSteps(generatedSteps.length);

        // Set steps for visualization
        setSteps(generatedSteps);

        // Reset to initial state
        setState('idle');
        setStep(0);

        // Log completion
        console.log(`SelectionSortController - Total steps: ${generatedSteps.length}`);
    }, [arraySize, randomize, customArray, setAlgorithmArray, setSteps, setTotalSteps, setState, setStep]);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps && state !== 'completed') {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: []
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // IMPORTANT: Set the array in the context directly
            console.log('SelectionSortController - Setting custom array in context:', parsedArray);
            setAlgorithmArray(parsedArray);

            // Generate steps for the custom array
            const generatedSteps = generateSelectionSortDetailedSteps(parsedArray);
            console.log('SelectionSortController - Generated steps for custom array:', generatedSteps.length);

            // Set the steps in the context
            setSteps(generatedSteps);
            setTotalSteps(generatedSteps.length);

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title={"Selection Sort"}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Selection Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Selection Sort is a simple sorting algorithm that repeatedly finds the minimum element from the unsorted part of the array and puts it at the beginning. The algorithm maintains two subarrays: the sorted subarray and the unsorted subarray.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n²) - Selection Sort always makes the same number of comparisons
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n²)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n²)
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(1) - Selection Sort is an in-place sorting algorithm
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated numbers (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={step > 0 ? (steps || []).slice(1).map(step => ({
                    description: step.statement || ''
                })) : []}
                currentStep={step > 0 ? step - 1 : 0}
                defaultExpanded
                renderStep={(_, index) => {
                    const currentStep = steps && steps[index + 1]; // Add 1 to skip initial step
                    const isCurrentStep = index === step - 1;

                    if (!currentStep) return null;

                    return (
                        <Typography
                            variant="body2"
                            component="div"
                            sx={{
                                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                                fontSize: '0.85rem',
                                fontWeight: isCurrentStep ? 'bold' : 'normal',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                mb: 0.75,
                                pb: 0.75,
                                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                    bgcolor: 'action.hover',
                                    borderRadius: '4px',
                                }
                            }}
                        >
                            <Box
                                component="span"
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: '24px',
                                    height: '24px',
                                    borderRadius: '12px',
                                    bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                                    color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                                    mr: 1.5,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold',
                                    flexShrink: 0,
                                    boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {index + 1}
                            </Box>
                            {currentStep.statement || 'Processing...'}
                        </Typography>
                    );
                }}
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Selection Sort Algorithm"
                defaultExpanded
                currentStep={step > 0 && steps && steps.length > 0 ?
                    // Map the current step to the corresponding line number (use step-1 for 0-based array access)
                    steps[step - 1]?.type === 'select_position' ? 2 :
                        steps[step - 1]?.type === 'compare' ? 4 :
                            steps[step - 1]?.type === 'new_minimum' ? 5 :
                                steps[step - 1]?.type === 'keep_minimum' ? 4 :
                                    steps[step - 1]?.type === 'prepare_swap' ? 6 :
                                        steps[step - 1]?.type === 'swapped' ? 6 :
                                            steps[step - 1]?.type === 'no_swap' ? 6 :
                                                steps[step - 1]?.type === 'position_sorted' ? 6 :
                                                    steps[step - 1]?.type === 'complete' ? 7 : 1
                    : 0
                }
                algorithm={[
                    { code: "function selectionSort(arr):", lineNumber: 0, indent: 0 },
                    { code: "for i in range(0, len(arr) - 1):", lineNumber: 1, indent: 1 },
                    { code: "min_idx = i", lineNumber: 2, indent: 2 },
                    { code: "for j in range(i + 1, len(arr)):", lineNumber: 3, indent: 2 },
                    { code: "if arr[j] < arr[min_idx]:", lineNumber: 4, indent: 3 },
                    { code: "min_idx = j", lineNumber: 5, indent: 4 },
                    { code: "arr[i], arr[min_idx] = arr[min_idx], arr[i]", lineNumber: 6, indent: 2 },
                    { code: "return arr", lineNumber: 7, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default SelectionSortController;
