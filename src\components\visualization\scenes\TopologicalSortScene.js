// TopologicalSortScene.js
// A sample scene component for Topological Sort visualization

import React, { useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { useSpring, animated } from '@react-spring/three';

// Import the specialized node component
import TopologicalSortNode from '../nodes/TopologicalSortNode';

// Constants for visualization
const NODE_RADIUS = 0.8;
const EDGE_WIDTH = 0.03;

// Edge component for directed edges
const DirectedEdge = ({ start, end, color = '#757575', isHighlighted = false }) => {
  // Calculate direction vector
  const direction = new THREE.Vector3().subVectors(end, start).normalize();
  
  // Calculate length of the edge
  const length = start.distanceTo(end);
  
  // Calculate midpoint for the edge
  const midpoint = new THREE.Vector3().addVectors(
    start,
    new THREE.Vector3().copy(direction).multiplyScalar(length * 0.5)
  );
  
  // Calculate rotation to align with direction
  const quaternion = new THREE.Quaternion();
  const up = new THREE.Vector3(0, 1, 0);
  quaternion.setFromUnitVectors(up, direction);
  
  // Calculate Euler angles from quaternion
  const euler = new THREE.Euler().setFromQuaternion(quaternion);
  
  // Adjust start and end points to account for node radius
  const adjustedStart = new THREE.Vector3().copy(start).add(
    new THREE.Vector3().copy(direction).multiplyScalar(NODE_RADIUS * 1.1)
  );
  
  const adjustedEnd = new THREE.Vector3().copy(end).sub(
    new THREE.Vector3().copy(direction).multiplyScalar(NODE_RADIUS * 1.1)
  );
  
  // Recalculate length after adjustment
  const adjustedLength = adjustedStart.distanceTo(adjustedEnd);
  
  // Calculate midpoint for the adjusted edge
  const adjustedMidpoint = new THREE.Vector3().addVectors(
    adjustedStart,
    new THREE.Vector3().copy(direction).multiplyScalar(adjustedLength * 0.5)
  );
  
  return (
    <group>
      {/* Main edge line */}
      <mesh position={adjustedMidpoint} rotation={euler}>
        <cylinderGeometry 
          args={[EDGE_WIDTH, EDGE_WIDTH, adjustedLength, 8]} 
          rotateX={Math.PI / 2}
        />
        <meshStandardMaterial 
          color={color} 
          emissive={isHighlighted ? color : '#000000'}
          emissiveIntensity={isHighlighted ? 0.5 : 0}
        />
      </mesh>
      
      {/* Arrow head */}
      <mesh 
        position={new THREE.Vector3().copy(adjustedEnd).sub(
          new THREE.Vector3().copy(direction).multiplyScalar(NODE_RADIUS * 0.5)
        )} 
        rotation={euler}
      >
        <coneGeometry 
          args={[EDGE_WIDTH * 2.5, NODE_RADIUS * 0.8, 8]} 
          rotateX={Math.PI / 2}
        />
        <meshStandardMaterial 
          color={color}
          emissive={isHighlighted ? color : '#000000'}
          emissiveIntensity={isHighlighted ? 0.5 : 0}
        />
      </mesh>
    </group>
  );
};

// Sample Topological Sort Scene
const TopologicalSortScene = ({
  graphData = { nodes: [], edges: [] },
  nodePositions = {},
  visitedNodes = [],
  stackNodes = [],
  sortedNodes = [],
  currentNode = null,
  neighborNode = null,
  isDark = false,
}) => {
  // Get Three.js objects
  const { camera } = useThree();
  
  // Define colors based on theme
  const colors = useMemo(() => ({
    node: isDark ? '#4fc3f7' : '#2196f3', // Blue nodes
    edge: isDark ? '#9e9e9e' : '#757575', // Gray edges
    visitedNode: isDark ? '#69f0ae' : '#00c853', // Green for visited nodes
    currentNode: isDark ? '#ffeb3b' : '#ffc107', // Yellow for current node
    stackNode: isDark ? '#ba68c8' : '#9c27b0', // Purple for stack nodes
    sortedNode: isDark ? '#ff5252' : '#f44336', // Red for sorted nodes
    neighborNode: isDark ? '#ff9800' : '#ff9800', // Orange for neighbor node
    highlightedEdge: isDark ? '#ffeb3b' : '#ffc107', // Yellow for highlighted edges
  }), [isDark]);
  
  // Spring animation for camera position
  const { position, rotation } = useSpring({
    position: [0, 0, 15],
    rotation: [0, 0, 0],
    config: { mass: 10, tension: 50, friction: 30 }
  });
  
  // Create edges
  const edgeElements = useMemo(() => {
    return graphData.edges.map(edge => {
      const sourcePos = nodePositions[edge.source];
      const targetPos = nodePositions[edge.target];
      
      if (!sourcePos || !targetPos) return null;
      
      // Convert array positions to Vector3
      const startVec = new THREE.Vector3(...sourcePos);
      const endVec = new THREE.Vector3(...targetPos);
      
      // Determine if this edge should be highlighted
      const isHighlighted = 
        (currentNode === edge.source && neighborNode === edge.target);
      
      return (
        <DirectedEdge
          key={edge.id}
          start={startVec}
          end={endVec}
          color={isHighlighted ? colors.highlightedEdge : colors.edge}
          isHighlighted={isHighlighted}
        />
      );
    }).filter(Boolean);
  }, [graphData.edges, nodePositions, currentNode, neighborNode, colors]);
  
  // Create nodes
  const nodeElements = useMemo(() => {
    return graphData.nodes.map(node => {
      const position = nodePositions[node.id];
      if (!position) return null;
      
      // Determine node state
      const isVisited = visitedNodes.includes(node.id);
      const isInStack = stackNodes.includes(node.id);
      const isSorted = sortedNodes.includes(node.id);
      const isCurrent = node.id === currentNode;
      const isNeighbor = node.id === neighborNode;
      
      // Determine node color based on state
      let nodeColor = colors.node;
      if (isVisited) nodeColor = colors.visitedNode;
      if (isInStack) nodeColor = colors.stackNode;
      if (isSorted) nodeColor = colors.sortedNode;
      if (isCurrent) nodeColor = colors.currentNode;
      if (isNeighbor) nodeColor = colors.neighborNode;
      
      return (
        <TopologicalSortNode
          key={node.id}
          position={position}
          label={node.id.toString()}
          color={nodeColor}
          isVisited={isVisited}
          isInStack={isInStack}
          isSorted={isSorted}
          isCurrent={isCurrent}
          isNeighbor={isNeighbor}
          radius={NODE_RADIUS}
        />
      );
    }).filter(Boolean);
  }, [graphData.nodes, nodePositions, visitedNodes, stackNodes, sortedNodes, currentNode, neighborNode, colors]);
  
  return (
    <animated.group position={position} rotation={rotation}>
      {/* Edges */}
      {edgeElements}
      
      {/* Nodes */}
      {nodeElements}
      
      {/* Add a subtle fog effect for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />
      
      {/* Add a grid helper for better spatial reference */}
      <gridHelper
        args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
        position={[0, -12, 0]}
        rotation={[0, 0, 0]}
      />
    </animated.group>
  );
};

export default TopologicalSortScene;
