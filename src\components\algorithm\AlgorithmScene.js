// AlgorithmScene.js
// Base component for algorithm visualization scenes

import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import PropTypes from 'prop-types';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAlgorithm } from '../../context/AlgorithmContext';

/**
 * Base component for algorithm visualization scenes
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Scene content
 * @param {boolean} props.loading - Whether the scene is loading
 * @param {string} props.loadingMessage - Message to display while loading
 * @param {Object} props.cameraProps - Props for the camera
 * @param {boolean} props.showControls - Whether to show orbit controls
 */
const AlgorithmScene = ({
  children,
  loading = false,
  loadingMessage = 'Loading visualization...',
  cameraProps = { position: [0, 5, 10], fov: 60 },
  showControls = true,
}) => {
  // Get algorithm state from context
  const { state } = useAlgorithm();

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        bgcolor: 'background.default',
        borderRadius: 1,
      }}
    >
      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
          }}
        >
          <CircularProgress size={60} color="primary" />
          <Typography variant="subtitle1" color="white" sx={{ mt: 2, fontWeight: 'bold' }}>
            {loadingMessage}
          </Typography>
        </Box>
      )}

      {/* Canvas */}
      <Canvas
        camera={cameraProps}
        style={{
          width: '100%',
          height: '100%',
          display: 'block',
        }}
        shadows
      >
        {/* Scene Content */}
        {children}

        {/* Orbit Controls */}
        {showControls && (
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={50}
          />
        )}

        {/* Ambient Light */}
        <ambientLight intensity={0.5} />

        {/* Directional Light */}
        <directionalLight
          position={[10, 10, 10]}
          intensity={0.8}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
      </Canvas>

      {/* Status Indicator */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          px: 1.5,
          py: 0.5,
          borderRadius: 4,
          bgcolor: state === 'running' ? 'success.main' : state === 'paused' ? 'warning.main' : 'info.main',
          color: 'white',
          fontWeight: 'medium',
          fontSize: '0.75rem',
          textTransform: 'uppercase',
          boxShadow: 2,
        }}
      >
        {state === 'running' ? 'Running' : state === 'paused' ? 'Paused' : 'Ready'}
      </Box>
    </Box>
  );
};

AlgorithmScene.propTypes = {
  children: PropTypes.node.isRequired,
  loading: PropTypes.bool,
  loadingMessage: PropTypes.string,
  cameraProps: PropTypes.object,
  showControls: PropTypes.bool,
};

export default AlgorithmScene;
