// LISVisualization.js
// 3D visualization component for Longest Increasing Subsequence algorithm

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html, shaderMaterial } from '@react-three/drei';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { extend } from '@react-three/fiber';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const CELL_SIZE = 1.5;
const CELL_SPACING = 0.2;
const ELEMENT_SIZE = 1.2;
const ELEMENT_SPACING = 2.0;
const ELEMENT_HEIGHT = 0.3;

// Create SDF shader material for smooth 3D cells
const SDFCellMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0x2196f3),
    emissive: new THREE.Color(0x2196f3),
    emissiveIntensity: 0.3,
    highlighted: 0.0, // 0.0 = not highlighted, 1.0 = highlighted
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    
    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for crystal-clear cells
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform float highlighted;
    
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    
    // SDF for rounded box with sharper edges
    float sdRoundBox(vec3 p, vec3 b, float r) {
      vec3 q = abs(p) - b + r;
      return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }
    
    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdRoundBox(p, vec3(0.45, 0.45, 0.45), 0.05); // Sharper corners
      
      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);
      
      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);
      
      // Specular highlight for crystal-like appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 32.0);
      
      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);
      
      // Highlight effect - make it glow more when highlighted
      float highlightGlow = highlighted * 0.5 * (1.0 + sin(time * 3.0));
      
      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = color * (0.2 + 0.8 * diffuse) + 
                        emissive * (emissiveIntensity * (1.0 + pulse + highlightGlow)) + 
                        vec3(1.0) * specular * 0.5;
      
      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Create SDF shader material for smooth 3D elements
const SDFElementMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color(0x4caf50),
    emissive: new THREE.Color(0x4caf50),
    emissiveIntensity: 0.3,
    highlighted: 0.0, // 0.0 = not highlighted, 1.0 = highlighted
    selected: 0.0, // 0.0 = not selected, 1.0 = selected
    value: 0, // The numeric value of the element
    maxValue: 100, // The maximum value in the sequence
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    
    void main() {
      vUv = uv;
      vPosition = position;
      vNormal = normal;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader with SDF technique for crystal-clear elements
  `
    uniform float time;
    uniform vec3 color;
    uniform vec3 emissive;
    uniform float emissiveIntensity;
    uniform float highlighted;
    uniform float selected;
    uniform float value;
    uniform float maxValue;
    
    varying vec2 vUv;
    varying vec3 vPosition;
    varying vec3 vNormal;
    
    // SDF for rounded box with sharper edges
    float sdRoundBox(vec3 p, vec3 b, float r) {
      vec3 q = abs(p) - b + r;
      return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }
    
    void main() {
      // Calculate SDF
      vec3 p = vPosition;
      float d = sdRoundBox(p, vec3(0.45, 0.45, 0.1), 0.05); // Flatter box for elements
      
      // Crisp edges with minimal smoothing
      float smoothing = 0.01;
      float alpha = 1.0 - smoothstep(-smoothing, smoothing, d);
      
      // Enhanced lighting with specular highlight
      vec3 normal = normalize(vNormal);
      vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
      float diffuse = max(dot(normal, lightDir), 0.0);
      
      // Specular highlight for crystal-like appearance
      vec3 viewDir = normalize(vec3(0.0, 0.0, 1.0) - vPosition);
      vec3 halfDir = normalize(lightDir + viewDir);
      float specular = pow(max(dot(normal, halfDir), 0.0), 32.0);
      
      // Add subtle animation to the glow
      float pulse = 0.1 * sin(time * 1.5);
      
      // Highlight effect - make it glow more when highlighted or selected
      float highlightGlow = (highlighted + selected) * 0.5 * (1.0 + sin(time * 3.0));
      
      // Color gradient based on value
      float normalizedValue = value / maxValue;
      vec3 baseColor = mix(color, vec3(0.0, 0.8, 0.4), normalizedValue * 0.5);
      
      // Final color with enhanced emissive glow and specular highlight
      vec3 finalColor = baseColor * (0.2 + 0.8 * diffuse) + 
                        emissive * (emissiveIntensity * (1.0 + pulse + highlightGlow)) + 
                        vec3(1.0) * specular * 0.5;
      
      // Add selected tint
      finalColor = mix(finalColor, vec3(1.0, 0.8, 0.0), selected * 0.3);
      
      // Add highlighted tint
      finalColor = mix(finalColor, vec3(1.0, 1.0, 0.0), highlighted * 0.3);
      
      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom materials
extend({ SDFCellMaterial, SDFElementMaterial });

// Cell component representing a DP table cell
const TableCell = ({ position, value, isHighlighted, isInPath, isActive, color }) => {
  // Reference to update shader uniforms
  const materialRef = useRef();
  
  // Update shader uniforms
  useEffect(() => {
    if (materialRef.current) {
      // Convert color string to THREE.Color
      const threeColor = new THREE.Color(color);
      
      // Enhance the emissive color for more vibrant appearance
      const emissiveColor = new THREE.Color(color).multiplyScalar(1.2);
      
      materialRef.current.uniforms.color.value = threeColor;
      materialRef.current.uniforms.emissive.value = emissiveColor;
      materialRef.current.uniforms.emissiveIntensity.value = 0.6;
      materialRef.current.uniforms.highlighted.value = isHighlighted || isActive ? 1.0 : 0.0;
    }
  }, [color, isHighlighted, isActive]);
  
  // Update time uniform for subtle animation
  useFrame(({ clock }) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = clock.getElapsedTime();
    }
  });

  // Calculate a slight hover effect based on whether the cell is active
  const [hoverY, setHoverY] = useState(0);
  useFrame(({ clock }) => {
    if (isActive) {
      setHoverY(Math.sin(clock.getElapsedTime() * 2) * 0.1);
    } else {
      setHoverY(0);
    }
  });

  return (
    <group position={[position[0], position[1] + hoverY, position[2]]}>
      {/* Cell cube with SDF material */}
      <mesh>
        <boxGeometry args={[CELL_SIZE, CELL_SIZE, CELL_SIZE]} />
        <sDFCellMaterial ref={materialRef} />
      </mesh>
      
      {/* Value label */}
      <Html position={[0, 0, CELL_SIZE/2 + 0.1]} center>
        <div style={{ 
          color: 'white', 
          fontSize: '16px', 
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px'
        }}>
          {value}
        </div>
      </Html>
    </group>
  );
};

// Element component for visualizing sequence elements
const SequenceElement = ({ position, value, index, isActive, isComparing, isSelected, maxValue }) => {
  // Reference to update shader uniforms
  const materialRef = useRef();
  
  // Update shader uniforms
  useEffect(() => {
    if (materialRef.current) {
      // Base color for elements
      const elementColor = new THREE.Color(0x4caf50);
      
      materialRef.current.uniforms.color.value = elementColor;
      materialRef.current.uniforms.emissive.value = elementColor;
      materialRef.current.uniforms.emissiveIntensity.value = 0.6;
      materialRef.current.uniforms.highlighted.value = isActive || isComparing ? 1.0 : 0.0;
      materialRef.current.uniforms.selected.value = isSelected ? 1.0 : 0.0;
      materialRef.current.uniforms.value.value = value;
      materialRef.current.uniforms.maxValue.value = maxValue;
    }
  }, [value, maxValue, isActive, isComparing, isSelected]);
  
  // Update time uniform for subtle animation
  useFrame(({ clock }) => {
    if (materialRef.current) {
      materialRef.current.uniforms.time.value = clock.getElapsedTime();
    }
  });

  // Calculate a slight hover effect based on whether the element is active
  const [hoverY, setHoverY] = useState(0);
  useFrame(({ clock }) => {
    if (isActive) {
      setHoverY(Math.sin(clock.getElapsedTime() * 2) * 0.2);
    } else if (isComparing) {
      setHoverY(Math.sin(clock.getElapsedTime() * 1.5) * 0.1);
    } else if (isSelected) {
      setHoverY(Math.sin(clock.getElapsedTime() * 1.0) * 0.05);
    } else {
      setHoverY(0);
    }
  });

  // Scale the height based on the value for visual representation
  const height = Math.max(0.1, (value / maxValue) * 3);

  return (
    <group position={[position[0], position[1] + hoverY + height/2, position[2]]}>
      {/* Element box with SDF material */}
      <mesh>
        <boxGeometry args={[ELEMENT_SIZE, height, ELEMENT_HEIGHT]} />
        <sDFElementMaterial ref={materialRef} />
      </mesh>
      
      {/* Value label */}
      <Html position={[0, 0, ELEMENT_HEIGHT/2 + 0.1]} center>
        <div style={{ 
          color: 'white', 
          fontSize: '16px', 
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '2px 6px',
          borderRadius: '4px'
        }}>
          {value}
        </div>
      </Html>
      
      {/* Index label */}
      <Html position={[0, -height/2 - 0.3, 0]} center>
        <div style={{ 
          color: 'white', 
          fontSize: '14px', 
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none'
        }}>
          {index}
        </div>
      </Html>
    </group>
  );
};

// LIS visualization component
const LISVisual = ({ lis, position }) => {
  if (!lis || lis.length === 0) return null;
  
  return (
    <group position={position}>
      <Html center>
        <div style={{ 
          color: 'white', 
          fontSize: '18px', 
          fontWeight: 'bold',
          textShadow: '0 0 5px rgba(0,0,0,0.5)',
          userSelect: 'none',
          background: 'rgba(0,0,0,0.5)',
          padding: '8px 16px',
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <div>Longest Increasing Subsequence:</div>
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            justifyContent: 'center', 
            gap: '8px',
            marginTop: '8px'
          }}>
            {lis.map((value, index) => (
              <div key={index} style={{ 
                minWidth: '30px', 
                height: '30px', 
                borderRadius: '4px', 
                backgroundColor: '#4caf50', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                padding: '0 8px',
                boxShadow: '0 0 5px rgba(76, 175, 80, 0.8)'
              }}>
                {value}
              </div>
            ))}
          </div>
        </div>
      </Html>
    </group>
  );
};

// Main visualization component
const LISVisualization = ({
  state,
  step,
  setStep,
  setTotalSteps,
  setState,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();
  
  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // Get speed from context
  const { speed } = useSpeed();

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);
  
  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(speed);
  
  // Set up camera position
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 15, 20);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Define colors based on theme
  const colors = useMemo(() => ({
    cell: isDark ? '#90caf9' : '#64b5f6',         // Light blue for regular cells
    activeCell: isDark ? '#ffee58' : '#ffeb3b',   // Yellow for active cell
    pathCell: isDark ? '#81c784' : '#4caf50',     // Green for path cells
    element: isDark ? '#4caf50' : '#4caf50',      // Green for elements
    activeElement: isDark ? '#ffee58' : '#ffeb3b', // Yellow for active elements
    comparingElement: isDark ? '#ff9800' : '#ff9800', // Orange for comparing elements
    selectedElement: isDark ? '#81c784' : '#4caf50', // Green for selected elements
  }), [isDark]);
  
  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.cell, label: 'DP Table Cell' },
    { color: colors.activeCell, label: 'Current Cell' },
    { color: colors.pathCell, label: 'Optimal Solution' },
    { color: colors.element, label: 'Sequence Element' },
    { color: colors.activeElement, label: 'Current Element' },
    { color: colors.comparingElement, label: 'Comparing Element' },
    { color: colors.selectedElement, label: 'Selected Element' },
  ], [colors]);

  // Update state based on current step
  useEffect(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      console.log('No steps available or invalid step index');
      return;
    }

    // Store steps in ref for access in animation frame
    stepsRef.current = steps;

    // Get current step data
    const currentStep = steps[step];
    console.log('Current step data:', currentStep);
    setCurrentStepData(currentStep);

    // Update last applied step
    lastAppliedStepRef.current = step;

    // Update total steps
    if (setTotalSteps) {
      setTotalSteps(steps.length);
    }

    // Update movements
    if (setMovements && currentStep.message) {
      setMovements([currentStep.message]);
    }
  }, [step, steps, setTotalSteps, setMovements]);

  // Helper function to calculate delay based on speed
  const calculateDelay = useCallback((speed) => {
    // Use a maximum delay of 3000ms (3 seconds) at speed 1
    // Define a maximum speed value (10) for distribution
    const maxDelay = 3000;    // 3 seconds at speed 1
    const minDelay = 300;     // Minimum delay of 300ms
    const maxSpeed = 10;      // Maximum speed value for distribution

    // Calculate delay based on current speed and max speed
    // This creates a more even distribution across the speed range
    const speedRatio = (maxSpeed - speed + 1) / maxSpeed;
    const delay = Math.max(minDelay, maxDelay * speedRatio);

    console.log(`Calculated delay: ${delay.toFixed(0)}ms (Speed: ${speed}/${maxSpeed}, Ratio: ${speedRatio.toFixed(2)})`);
    return delay;
  }, []);

  // Auto-advance steps when in running state
  useEffect(() => {
    // Update refs
    stateRef.current = state;
    speedRef.current = speed;

    let timeoutId = null;

    if (state === 'running') {
      // If we're at the end, stop the simulation
      if (step >= stepsRef.current.length - 1) {
        console.log('Reached end of steps, stopping simulation');
        setState('paused'); // Change state to paused instead of looping
        return;
      }

      // Calculate next step
      const nextStep = step + 1;
      console.log('Auto-advancing to step:', nextStep);

      // Calculate delay based on speed
      const delay = calculateDelay(speed);

      // Set a timer to advance to the next step
      timeoutId = setTimeout(() => {
        console.log('Timer fired, setting step to:', nextStep);
        setStep(nextStep);
      }, delay);
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [state, step, speed, setStep, setState, calculateDelay]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, 0, 0];

  // Render the LIS visualization
  const renderLISVisualization = () => {
    if (!currentStepData) return null;

    const { sequence, dp, prev, currentIndex, currentJ, lis } = currentStepData;
    
    // Calculate offset to center the visualization
    const offsetX = -(sequence.length * (CELL_SIZE + CELL_SPACING)) / 2;
    
    // Find the maximum value in the sequence for scaling
    const maxValue = Math.max(...sequence);
    
    // Determine if we're in the traceback phase
    const isTraceback = currentStepData.type.includes('traceback') || 
                        currentStepData.type === 'complete' ||
                        currentStepData.type === 'select_element';
    
    return (
      <group>
        {/* DP Table */}
        {dp && (
          <group position={[0, 0, 0]}>
            {/* Table cells */}
            {dp.map((value, i) => {
              const x = offsetX + i * (CELL_SIZE + CELL_SPACING);
              
              // Determine if this cell is highlighted
              const isHighlighted = i === currentIndex;
              
              // Determine if this cell is active (being considered in the current step)
              const isActive = (currentStepData.type === 'potential_update' || 
                               currentStepData.type === 'update_lis' ||
                               currentStepData.type === 'no_update') && 
                              i === currentIndex;
              
              // Determine if this cell is in the optimal solution path
              const isInPath = isTraceback && lis && lis.includes(sequence[i]);
              
              // Determine cell color
              let cellColor;
              if (isInPath) {
                cellColor = colors.pathCell;
              } else if (isHighlighted || isActive) {
                cellColor = colors.activeCell;
              } else {
                cellColor = colors.cell;
              }
              
              return (
                <TableCell
                  key={`cell-${i}`}
                  position={[x, 0, 0]}
                  value={value}
                  isHighlighted={isHighlighted}
                  isInPath={isInPath}
                  isActive={isActive}
                  color={cellColor}
                />
              );
            })}
            
            {/* Index labels */}
            {dp.map((value, i) => {
              const x = offsetX + i * (CELL_SIZE + CELL_SPACING);
              const y = -CELL_SIZE;
              
              return (
                <Html key={`index-label-${i}`} position={[x, y, 0]} center>
                  <div style={{ 
                    color: 'white', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    textShadow: '0 0 5px rgba(0,0,0,0.5)',
                    userSelect: 'none'
                  }}>
                    {i}
                  </div>
                </Html>
              );
            })}
            
            {/* DP table label */}
            <Html position={[offsetX - CELL_SIZE * 2, 0, 0]} center>
              <div style={{ 
                color: 'white', 
                fontSize: '16px', 
                fontWeight: 'bold',
                textShadow: '0 0 5px rgba(0,0,0,0.5)',
                userSelect: 'none',
                background: 'rgba(0,0,0,0.5)',
                padding: '4px 8px',
                borderRadius: '4px'
              }}>
                DP Table
              </div>
            </Html>
          </group>
        )}
        
        {/* Sequence Visualization */}
        {sequence && (
          <group position={[0, -5, 0]}>
            {sequence.map((value, i) => {
              const x = offsetX + i * (CELL_SIZE + CELL_SPACING);
              
              // Determine if this element is active
              const isActive = i === currentIndex;
              
              // Determine if this element is being compared
              const isComparing = i === currentJ;
              
              // Determine if this element is in the LIS
              const isSelected = lis && lis.includes(value);
              
              return (
                <SequenceElement
                  key={`element-${i}`}
                  position={[x, 0, 0]}
                  value={value}
                  index={i}
                  isActive={isActive}
                  isComparing={isComparing}
                  isSelected={isSelected}
                  maxValue={maxValue}
                />
              );
            })}
            
            {/* Sequence label */}
            <Html position={[offsetX - CELL_SIZE * 2, 0, 0]} center>
              <div style={{ 
                color: 'white', 
                fontSize: '16px', 
                fontWeight: 'bold',
                textShadow: '0 0 5px rgba(0,0,0,0.5)',
                userSelect: 'none',
                background: 'rgba(0,0,0,0.5)',
                padding: '4px 8px',
                borderRadius: '4px'
              }}>
                Sequence
              </div>
            </Html>
          </group>
        )}
        
        {/* LIS Visualization */}
        {lis && lis.length > 0 && (
          <LISVisual
            lis={lis}
            position={[0, -8, 0]}
          />
        )}
        
        {/* Result Visualization */}
        {currentStepData.type === 'complete' && (
          <Html position={[0, 3, 0]} center>
            <div style={{ 
              color: 'white', 
              fontSize: '20px', 
              fontWeight: 'bold',
              textShadow: '0 0 5px rgba(0,0,0,0.5)',
              userSelect: 'none',
              background: 'rgba(0,0,0,0.5)',
              padding: '8px 16px',
              borderRadius: '8px'
            }}>
              Longest Increasing Subsequence Length: {currentStepData.result}
            </div>
          </Html>
        )}
      </group>
    );
  };

  // Return the 3D visualization
  return (
    <group>
      {/* Fixed Step board - stays at the top of the screen */}
      <FixedStepBoard
        description={currentStepData?.message || 'Longest Increasing Subsequence Algorithm'}
        currentStep={step}
        totalSteps={stepsRef.current.length || 1}
        stepData={currentStepData || {}}
      />

      {/* Fixed Color legend - stays at the bottom of the screen */}
      <FixedColorLegend
        items={legendItems}
        theme={muiTheme}
      />

      {/* Ambient light for overall scene illumination */}
      <ambientLight intensity={0.5} />
      
      {/* Directional lights for better 3D definition */}
      <directionalLight position={[10, 10, 5]} intensity={0.7} color="#ffffff" />
      <directionalLight position={[-10, -10, -5]} intensity={0.3} color={isDark ? '#6666ff' : '#66ccff'} />
      
      {/* Spotlight to highlight the main visualization */}
      <spotLight 
        position={[0, 25, 0]} 
        angle={0.3} 
        penumbra={0.8} 
        intensity={0.8} 
        castShadow 
        color={isDark ? '#ffffff' : '#ffffff'} 
      />

      {/* Visualization */}
      <group position={position} rotation={rotation}>
        {renderLISVisualization()}
      </group>

      {/* Add a subtle fog effect for depth perception */}
      <fog attach="fog" args={[isDark ? '#111111' : '#f0f0f0', 70, 250]} />

      {/* Add a grid helper for better spatial reference */}
      <gridHelper
        args={[80, 80, isDark ? '#444444' : '#cccccc', isDark ? '#222222' : '#e0e0e0']}
        position={[0, -12, 0]}
        rotation={[0, 0, 0]}
      />
    </group>
  );
};

export default LISVisualization;
