/**
 * Environment maps utility for Three.js scenes
 * Provides local HDR files for environment lighting
 */

// Map of preset names to local HDR file paths
const environmentMaps = {
  city: '/hdri/potsdamer_platz_1k.hdr',
  sunset: '/hdri/venice_sunset_1k.hdr',
  night: '/hdri/royal_esplanade_1k.hdr',
  // Add more mappings as needed
};

/**
 * Get the local file path for an environment preset
 * @param {string} preset - The preset name
 * @returns {string} - The local file path
 */
export const getEnvironmentMap = (preset) => {
  return environmentMaps[preset] || environmentMaps.city; // Default to city if preset not found
};

export default environmentMaps;
