// JumpSearchAlgorithm.js
// Implementation of the Jump Search algorithm with step generation

/**
 * Generates steps for the Jump Search algorithm
 * @param {Array} arr - The array to search in (must be sorted)
 * @param {number} target - The value to search for
 * @returns {Object} - Object containing steps and the result
 */
export const generateJumpSearchSteps = (arr, target) => {
  // Create a copy of the array to avoid modifying the original
  const inputArray = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Initialize Jump Search'
  });

  // Add step to explain that array must be sorted
  steps.push({
    type: 'checkSorted',
    array: [...inputArray],
    target,
    currentIndex: -1,
    found: false,
    movement: 'Jump Search requires a sorted array'
  });

  // Calculate the optimal jump size
  const n = inputArray.length;
  const jumpSize = Math.floor(Math.sqrt(n));

  // Add step to explain the jump size
  steps.push({
    type: 'setJumpSize',
    array: [...inputArray],
    target,
    jumpSize,
    currentIndex: -1,
    found: false,
    movement: `Set jump size to sqrt(n) = sqrt(${n}) ≈ ${jumpSize}`
  });

  // Perform jump search
  let prev = 0;
  let current = 0;
  let found = false;
  let result = -1;

  // Add step to start the search
  steps.push({
    type: 'startSearch',
    array: [...inputArray],
    target,
    prev,
    current,
    jumpSize,
    found: false,
    movement: `Start search from index ${prev}`
  });

  // Jump until we find a value greater than or equal to the target or reach the end
  while (current < n && inputArray[current] < target) {
    // Add step to show the current jump
    steps.push({
      type: 'jump',
      array: [...inputArray],
      target,
      prev,
      current,
      jumpSize,
      nextJump: Math.min(current + jumpSize, n - 1),
      found: false,
      movement: `Jump from index ${current} to index ${Math.min(current + jumpSize, n - 1)}`
    });

    // Update prev and current
    prev = current;
    current = Math.min(current + jumpSize, n - 1);

    // Add step to show the comparison at the current position
    steps.push({
      type: 'compare',
      array: [...inputArray],
      target,
      prev,
      current,
      jumpSize,
      currentValue: inputArray[current],
      found: false,
      movement: `Compare ${inputArray[current]} at index ${current} with target ${target}`
    });
  }

  // Add step to show that we've found a block where the target might be
  steps.push({
    type: 'foundBlock',
    array: [...inputArray],
    target,
    prev,
    current,
    jumpSize,
    found: false,
    movement: `Found block from index ${prev} to ${current} where target might be`
  });

  // Linear search in the identified block
  for (let i = prev; i <= current; i++) {
    // Add step to show the current element being considered
    steps.push({
      type: 'linearSearch',
      array: [...inputArray],
      target,
      prev,
      current,
      linearIndex: i,
      currentValue: inputArray[i],
      jumpSize,
      found: false,
      movement: `Linear search: examine element at index ${i} with value ${inputArray[i]}`
    });

    // Compare current element with target
    steps.push({
      type: 'linearCompare',
      array: [...inputArray],
      target,
      prev,
      current,
      linearIndex: i,
      currentValue: inputArray[i],
      jumpSize,
      found: false,
      movement: `Compare ${inputArray[i]} with target ${target}`
    });

    // Check if the current element is the target
    if (inputArray[i] === target) {
      found = true;
      result = i;

      // Add step to show the target found
      steps.push({
        type: 'found',
        array: [...inputArray],
        target,
        prev,
        current,
        linearIndex: i,
        currentValue: inputArray[i],
        jumpSize,
        found: true,
        result: i,
        movement: `Target ${target} found at index ${i}`
      });

      break;
    }

    // If we've passed the target, it's not in the array
    if (inputArray[i] > target) {
      // Add step to show that we've passed the target
      steps.push({
        type: 'passedTarget',
        array: [...inputArray],
        target,
        prev,
        current,
        linearIndex: i,
        currentValue: inputArray[i],
        jumpSize,
        found: false,
        movement: `Passed target: ${inputArray[i]} > ${target}, target not in array`
      });

      break;
    }
  }

  // If target not found, add a step to show that
  if (!found) {
    steps.push({
      type: 'notFound',
      array: [...inputArray],
      target,
      prev,
      current,
      jumpSize,
      found: false,
      movement: `Target ${target} not found in the array`
    });
  }

  // Add final step
  steps.push({
    type: 'complete',
    array: [...inputArray],
    target,
    prev,
    current,
    jumpSize,
    found,
    result,
    movement: found ? `Jump Search complete: ${target} found at index ${result}` : `Jump Search complete: ${target} not found`
  });

  return { 
    steps, 
    result: {
      found,
      index: found ? result : -1
    }
  };
};

// Default export
const JumpSearchAlgorithm = {
  generateSteps: generateJumpSearchSteps
};

export default JumpSearchAlgorithm;
