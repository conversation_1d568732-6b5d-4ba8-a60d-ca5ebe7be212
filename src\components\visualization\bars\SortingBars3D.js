import React, { useMemo } from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';
import Bar3D from './Bar3D';
import getAlgorithmColors from '../../../utils/algorithmColors';

// Create a memoized component for the index labels to prevent unnecessary re-renders
const MemoizedIndexLabel = React.memo(({ index, xPos, barWidth, theme }) => {
    return (
        <mesh key={`index-${index}`} position={[xPos, 0, 0]}>
            <Html
                position={[0, 0.1, barWidth/2 + 0.3]} /* Position below the bar */
                center
                sprite
                occlude /* Enable occlusion to properly hide when objects are in front */
            >
                <div style={{
                    // Theme-aware styling with moderate visibility
                    color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.85)' : 'rgba(0,0,0,0.85)',
                    background: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.75)' : 'rgba(240,240,240,0.75)',
                    padding: '2px 6px',
                    borderRadius: '50px', // Rounded style for indices
                    fontSize: '0.65rem', // Slightly larger
                    fontWeight: 'normal', // Still less bold than values
                    textAlign: 'center',
                    textShadow: theme.palette.mode === 'dark' ? '0 0 2px #000000' : 'none',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.3)', // Lighter shadow
                    border: theme.palette.mode === 'dark' ? '1px solid rgba(100,100,100,0.5)' : '1px solid rgba(200,200,200,0.8)',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    minWidth: '16px', // Smaller width
                    opacity: 0.8, // Slightly transparent
                    zIndex: 2000
                }}>
                    {index}
                </div>
            </Html>
        </mesh>
    );
});

/**
 * Reusable 3D sorting bars component for algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.arrayData - Array of values to visualize
 * @param {Object} props.colors - Object containing color values for different states
 * @param {number} props.maxBarHeight - Maximum height of the bars
 * @param {number} props.barWidth - Width of each bar
 * @param {number} props.barSpacing - Spacing between bars
 * @param {Array} props.comparing - Indices of bars being compared
 * @param {Array} props.swapping - Indices of bars being swapped
 * @param {Array} props.sorted - Indices of bars that are sorted
 * @param {Object} props.swapAnimation - Object containing swap animation state
 * @param {boolean} props.showValues - Whether to show values on bars
 * @param {boolean} props.showIndices - Whether to show indices on bars
 * @returns {JSX.Element} - The rendered sorting bars component
 */
const SortingBars3D = ({
    arrayData = [],
    colors,
    maxBarHeight = 10,
    barWidth = 0.8,
    barSpacing = 0.2,
    comparing = [-1, -1],
    swapping = [-1, -1],
    sorted = [],
    swapAnimation = { active: false, indices: [-1, -1], progress: 0 },
    showValues = true,
    showArrows = []
}) => {
    // Get theme and default colors if not provided
    const theme = useTheme();
    const defaultColors = useMemo(() => getAlgorithmColors(theme), [theme]);

    // Use provided colors or fall back to default colors
    const barColors = colors || defaultColors;
    // Calculate the total width of the visualization
    const totalWidth = useMemo(() => {
        return arrayData.length * (barWidth + barSpacing) - barSpacing;
    }, [arrayData.length, barWidth, barSpacing]);

    // Calculate the starting X position to center the bars
    const startX = useMemo(() => {
        return -totalWidth / 2 + barWidth / 2;
    }, [totalWidth, barWidth]);

    // Find the maximum value in the array for scaling
    const maxValue = useMemo(() => {
        return Math.max(...arrayData, 1); // Ensure we don't divide by zero
    }, [arrayData]);

    // Create a separate array for indices that won't move during swaps
    const indices = useMemo(() => {
        return Array.from({ length: arrayData.length }, (_, i) => i);
    }, [arrayData.length]);

    return (
        <group>
            {/* Fixed indices layer that doesn't move during swaps - completely separate from the main group */}
            <group position={[0, 0, 0]} renderOrder={10}>
                {indices.map((index) => {
                    // Calculate the fixed position for each index
                    const xPos = startX + index * (barWidth + barSpacing);

                    return (
                        <MemoizedIndexLabel
                            key={`index-${index}`}
                            index={index}
                            xPos={xPos}
                            barWidth={barWidth}
                            theme={theme}
                        />
                    );
                })}
            </group>

            {/* Bars layer that animates during swaps */}
            {arrayData.map((value, index) => {
                // Calculate the bar's height based on its value
                const barHeight = (value / maxValue) * maxBarHeight;

                // Determine the bar's color based on its state
                let barColor = barColors.bar;
                if (comparing.includes(index)) {
                    barColor = barColors.comparing;
                } else if (swapping.includes(index)) {
                    // Use merging color if available (for merge sort), otherwise use swapping color
                    barColor = barColors.merging || barColors.swapping;
                } else if (sorted.includes(index)) {
                    barColor = barColors.sorted;
                }

                // Calculate the bar's position
                let xPos = startX + index * (barWidth + barSpacing);

                // Apply swap animation if active
                if (swapAnimation.active && swapAnimation.indices.includes(index)) {
                    const [i, j] = swapAnimation.indices;
                    const progress = swapAnimation.progress;

                    // Determine if this is the first or second bar in the swap
                    const isFirstBar = index === i;

                    // Calculate center point between the two bars
                    const firstPos = startX + i * (barWidth + barSpacing);
                    const secondPos = startX + j * (barWidth + barSpacing);
                    const centerX = (firstPos + secondPos) / 2;

                    // Calculate radius of the semicircle
                    const radius = Math.abs(secondPos - firstPos) / 2;

                    // Calculate angle based on progress (0 to π for a semicircle)
                    // First bar goes from 0 to π, second bar from π to 0
                    let angle;
                    if (isFirstBar) {
                        angle = progress * Math.PI;
                    } else {
                        angle = Math.PI - (progress * Math.PI);
                    }

                    // Calculate position along the semicircle path
                    // Determine which bar should be in front based on value
                    // Larger value bar should be in front (positive z)
                    // Smaller value bar should be behind (negative z)
                    const iValue = arrayData[i];
                    const jValue = arrayData[j];
                    const isLargerValue = index === i ? iValue > jValue : jValue > iValue;
                    const zOffset = isLargerValue ? 0.5 : -0.5;

                    // Calculate new position using parametric equation of a semicircle
                    xPos = centerX + radius * Math.cos(angle);
                    const yPos = radius * Math.sin(angle) * 0.6; // Scale height for better visibility
                    const zPos = zOffset * Math.sin(progress * Math.PI);

                    // Return the Bar3D with the animated position
                    return (
                        <Bar3D
                            key={`bar-${index}`}
                            position={[xPos, yPos, zPos]}
                            height={barHeight}
                            width={barWidth}
                            color={barColor}
                            value={value}
                            index={index}
                            showValue={showValues}
                            showIndex={false} // Don't show indices on bars - we're showing them separately
                            showArrow={showArrows.includes(index)}
                        />
                    );
                }

                return (
                    <Bar3D
                        key={`bar-${index}`}
                        position={[xPos, 0, 0]}
                        height={barHeight}
                        width={barWidth}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={showValues}
                        showIndex={false} // Don't show indices on bars - we're showing them separately
                        showArrow={showArrows.includes(index)}
                    />
                );
            })}
        </group>
    );
};

export default SortingBars3D;
