// FibonacciAlgorithm.js
// Implementation of Fibonacci sequence calculation using dynamic programming

/**
 * Generate steps for calculating Fibonacci numbers using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateFibonacciSteps = (params) => {
  console.log('generateFibonacciSteps called with params:', params);
  const { n = 10, approach = 'tabulation' } = params;
  const steps = [];

  // Validate input
  const nValue = parseInt(n, 10);
  if (isNaN(nValue) || nValue < 0) {
    throw new Error('n must be a non-negative integer');
  }

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize algorithm to calculate Fibonacci(${nValue}) using ${approach} approach`,
    n: nValue,
    approach,
    result: null,
    dp: approach === 'tabulation' ? [0, 1] : {},
    currentIndex: approach === 'tabulation' ? 1 : null,
    callStack: approach === 'memoization' ? [[nValue]] : [],
    pseudocodeLine: approach === 'tabulation' ? 1 : 8
  });

  // Calculate Fibonacci using the specified approach
  if (approach === 'tabulation') {
    // Bottom-up approach (tabulation)
    const dp = [0, 1];

    // Handle base cases
    if (nValue === 0) {
      steps.push({
        type: 'base_case',
        message: `Base case: Fibonacci(0) = 0`,
        n: nValue,
        approach,
        result: 0,
        dp: [...dp],
        currentIndex: 0,
        callStack: [],
        pseudocodeLine: 3
      });
      return { steps, result: 0 };
    } else if (nValue === 1) {
      steps.push({
        type: 'base_case',
        message: `Base case: Fibonacci(1) = 1`,
        n: nValue,
        approach,
        result: 1,
        dp: [...dp],
        currentIndex: 1,
        callStack: [],
        pseudocodeLine: 3
      });
      return { steps, result: 1 };
    }

    // Fill the dp array
    for (let i = 2; i <= nValue; i++) {
      // Add step for current calculation
      steps.push({
        type: 'calculate',
        message: `Calculate Fibonacci(${i}) = Fibonacci(${i-1}) + Fibonacci(${i-2}) = ${dp[i-1]} + ${dp[i-2]}`,
        n: nValue,
        approach,
        result: null,
        dp: [...dp],
        currentIndex: i,
        callStack: [],
        pseudocodeLine: 5
      });

      // Calculate and store the result
      dp[i] = dp[i-1] + dp[i-2];

      // Add step for storing the result
      steps.push({
        type: 'store',
        message: `Store Fibonacci(${i}) = ${dp[i]}`,
        n: nValue,
        approach,
        result: null,
        dp: [...dp],
        currentIndex: i,
        callStack: [],
        pseudocodeLine: 6
      });
    }

    // Add final step
    steps.push({
      type: 'complete',
      message: `Algorithm complete. Fibonacci(${nValue}) = ${dp[nValue]}`,
      n: nValue,
      approach,
      result: dp[nValue],
      dp: [...dp],
      currentIndex: nValue,
      callStack: [],
      pseudocodeLine: 7
    });

    return { steps, result: dp[nValue] };
  } else {
    // Top-down approach (memoization)
    const memo = {};
    const callStack = [];
    const callResults = {};

    // Define recursive function to simulate the memoization process
    const simulateFib = (num, depth = 0) => {
      // Base cases
      if (num === 0) {
        steps.push({
          type: 'base_case',
          message: `Base case: Fibonacci(0) = 0`,
          n: nValue,
          approach,
          result: null,
          dp: { ...memo },
          currentIndex: num,
          callStack: [...callStack, [num, 0]],
          pseudocodeLine: 10
        });
        return 0;
      }
      if (num === 1) {
        steps.push({
          type: 'base_case',
          message: `Base case: Fibonacci(1) = 1`,
          n: nValue,
          approach,
          result: null,
          dp: { ...memo },
          currentIndex: num,
          callStack: [...callStack, [num, 1]],
          pseudocodeLine: 10
        });
        return 1;
      }

      // Check if already memoized
      if (memo[num] !== undefined) {
        steps.push({
          type: 'memoized',
          message: `Using memoized value: Fibonacci(${num}) = ${memo[num]}`,
          n: nValue,
          approach,
          result: null,
          dp: { ...memo },
          currentIndex: num,
          callStack: [...callStack, [num, memo[num]]],
          pseudocodeLine: 12
        });
        return memo[num];
      }

      // Add step for recursive call
      callStack.push([num, null]);
      steps.push({
        type: 'recursive_call',
        message: `Recursive call: Fibonacci(${num})`,
        n: nValue,
        approach,
        result: null,
        dp: { ...memo },
        currentIndex: num,
        callStack: [...callStack],
        pseudocodeLine: 14
      });

      // Recursive calls
      steps.push({
        type: 'recursive_call_first',
        message: `Calculate Fibonacci(${num-1})`,
        n: nValue,
        approach,
        result: null,
        dp: { ...memo },
        currentIndex: num-1,
        callStack: [...callStack],
        pseudocodeLine: 14
      });
      const fib1 = simulateFib(num - 1, depth + 1);
      callResults[`${num}-1`] = fib1;

      steps.push({
        type: 'recursive_call_second',
        message: `Calculate Fibonacci(${num-2})`,
        n: nValue,
        approach,
        result: null,
        dp: { ...memo },
        currentIndex: num-2,
        callStack: [...callStack],
        pseudocodeLine: 14
      });
      const fib2 = simulateFib(num - 2, depth + 1);
      callResults[`${num}-2`] = fib2;

      // Calculate result
      const result = fib1 + fib2;
      
      // Memoize the result
      memo[num] = result;
      
      // Update call stack
      callStack[callStack.length - 1] = [num, result];
      
      // Add step for memoizing the result
      steps.push({
        type: 'memoize',
        message: `Memoize: Fibonacci(${num}) = Fibonacci(${num-1}) + Fibonacci(${num-2}) = ${fib1} + ${fib2} = ${result}`,
        n: nValue,
        approach,
        result: null,
        dp: { ...memo },
        currentIndex: num,
        callStack: [...callStack],
        pseudocodeLine: 15
      });
      
      // Pop from call stack when returning
      callStack.pop();
      
      return result;
    };

    // Simulate the recursive calls
    const result = simulateFib(nValue);

    // Add final step
    steps.push({
      type: 'complete',
      message: `Algorithm complete. Fibonacci(${nValue}) = ${result}`,
      n: nValue,
      approach,
      result: result,
      dp: { ...memo },
      currentIndex: nValue,
      callStack: [],
      pseudocodeLine: 16
    });

    return { steps, result };
  }
};

// Create the algorithm object with helper functions
const FibonacciAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateFibonacciSteps
};

export default FibonacciAlgorithm;
