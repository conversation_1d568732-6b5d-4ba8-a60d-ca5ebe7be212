import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

// Create separate memoized components for the HTML elements to prevent them from re-rendering
// when only the bar position changes
const MemoizedValueLabel = React.memo(({ value, barHeight, theme, color, yOffset = 0 }) => {
    return (
        <Html
            position={[0, barHeight + 0.5 + yOffset, 0]} // Add yOffset to move with the bar
            center
            occlude
            sprite
        >
            <div style={{
                color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,1)' : 'rgba(0,0,0,1)',
                background: theme.palette.mode === 'dark' ? 'rgba(20,20,20,0.95)' : 'rgba(220,220,220,0.95)',
                padding: '1px 3px',
                borderRadius: '4px',
                fontSize: '0.7rem',
                fontWeight: 'bold',
                textAlign: 'center',
                textShadow: theme.palette.mode === 'dark' ? '0 0 3px #000000' : '0 0 1px #000000',
                boxShadow: '0 2px 4px rgba(0,0,0,0.5)',
                border: `1px solid ${color}`,
                userSelect: 'none',
                pointerEvents: 'none',
                minWidth: '20px',
                opacity: 0.95
            }}>
                {value}
            </div>
        </Html>
    );
});

const MemoizedIndexLabel = React.memo(({ index, theme, edgeColor }) => {
    return (
        <Html
            position={[0, -0.5, 0]} // Position it lower to make it more visible
            center
            sprite // Don't use occlude to ensure it's always visible
        >
            <div style={{
                color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                background: theme.palette.mode === 'dark' ? 'rgba(30,30,30,0.95)' : 'rgba(240,240,240,0.95)',
                padding: '3px 8px',
                borderRadius: '50px', // Make it round
                fontSize: '0.75rem', // Larger font
                fontWeight: 'bold',
                textAlign: 'center',
                textShadow: theme.palette.mode === 'dark' ? '0 0 3px #000000' : '0 0 1px #000000',
                boxShadow: '0 2px 6px rgba(0,0,0,0.7)',
                border: `2px solid ${theme.palette.primary.main}`, // Use primary color for better visibility
                userSelect: 'none',
                pointerEvents: 'none',
                minWidth: '24px',
                opacity: 1, // Full opacity
                zIndex: 1000 // Higher z-index to ensure it's on top
            }}>
                {index}
            </div>
        </Html>
    );
});

const MemoizedArrow = React.memo(({ barHeight, theme, yOffset = 0 }) => {
    return (
        <Html
            position={[0, barHeight + 1.4 + yOffset, 0]} // Add yOffset to move with the bar
            center
            occlude
        >
            <div style={{
                color: theme.palette.secondary.main,
                background: 'transparent',
                fontSize: '2rem',
                fontWeight: 'bold',
                textAlign: 'center',
                textShadow: `0 0 8px ${theme.palette.secondary.main}, 0 0 16px ${theme.palette.secondary.main}`,
                userSelect: 'none',
                pointerEvents: 'none',
                transform: 'scale(1.2)',
                animation: 'pulse 1s infinite alternate'
            }}>
                ↓
            </div>
            <style>
                {`
                @keyframes pulse {
                    0% { transform: scale(1); }
                    100% { transform: scale(1.3); }
                }
                `}
            </style>
        </Html>
    );
});

// Calculate a slightly darker color for the bar edges
const getEdgeColor = (baseColor) => {
    // Convert hex to RGB
    const r = parseInt(baseColor.slice(1, 3), 16);
    const g = parseInt(baseColor.slice(3, 5), 16);
    const b = parseInt(baseColor.slice(5, 7), 16);

    // Darken the color
    const darkenFactor = 0.7;
    const newR = Math.floor(r * darkenFactor);
    const newG = Math.floor(g * darkenFactor);
    const newB = Math.floor(b * darkenFactor);

    // Convert back to hex
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
};

/**
 * Reusable 3D bar component for algorithm visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.position - [x, y, z] position of the bar
 * @param {number} props.height - Height of the bar
 * @param {number} props.width - Width of the bar
 * @param {string} props.color - Color of the bar
 * @param {number} props.value - Value represented by the bar
 * @param {number} props.index - Index of the bar in the array
 * @param {boolean} props.showValue - Whether to show the value label
 * @param {boolean} props.showIndex - Whether to show the index label
 * @param {boolean} props.showArrow - Whether to show an arrow above the bar
 * @returns {JSX.Element} - The rendered bar component
 */
const Bar3D = (props) => {
    const {
        position,
        height,
        width,
        color,
        value,
        index,
        showValue = true,
        showIndex = true,
        showArrow = false,
        yOffset = 0 // New prop for animating the bar without moving the base
    } = props;

    const theme = useTheme();
    const barHeight = Math.max(0.1, height);
    const edgeColor = getEdgeColor(color);

    return (
        <>
            {/* Bar base - completely separate from the bar, fixed at the original X position */}
            <mesh position={[position[0], 0.05, position[2]]} castShadow receiveShadow>
                <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
                <meshStandardMaterial color={edgeColor} />
            </mesh>

            {/* Animated bar group - contains everything except the base */}
            <group position={position}>
                {/* Main bar - can be animated up/down with yOffset */}
                <mesh position={[0, barHeight / 2 + 0.05 + yOffset, 0]} castShadow receiveShadow>
                    <boxGeometry args={[width, barHeight, width]} />
                    <meshStandardMaterial
                        color={color}
                        metalness={0.3}
                        roughness={0.7}
                        emissive={color}
                        emissiveIntensity={0.2}
                    />
                </mesh>

                {/* Bar top - moves with the main bar */}
                <mesh position={[0, barHeight + 0.05 + yOffset, 0]} castShadow receiveShadow>
                    <boxGeometry args={[width + 0.05, 0.1, width + 0.05]} />
                    <meshStandardMaterial color={edgeColor} />
                </mesh>

                {/* Value label - moves with the bar */}
                {showValue && (
                    <MemoizedValueLabel value={value} barHeight={barHeight} theme={theme} color={color} yOffset={yOffset} />
                )}

                {/* Arrow - moves with the bar */}
                {showArrow && (
                    <>
                        <MemoizedArrow barHeight={barHeight} theme={theme} yOffset={yOffset} />
                        {console.log(`Showing arrow for bar at index ${index}`)}
                    </>
                )}
            </group>

            {/* Index label - separate from the bar, fixed at the original position */}
            {showIndex && (
                <mesh position={[position[0], 0, position[2]]}>
                    <MemoizedIndexLabel index={index} theme={theme} edgeColor={edgeColor} />
                </mesh>
            )}
        </>
    );
};

// Wrap with memo to prevent unnecessary re-renders
export default React.memo(Bar3D, (prevProps, nextProps) => {
    // Only re-render if these specific props change
    return (
        prevProps.position[0] === nextProps.position[0] &&
        prevProps.position[1] === nextProps.position[1] &&
        prevProps.position[2] === nextProps.position[2] &&
        prevProps.height === nextProps.height &&
        prevProps.width === nextProps.width &&
        prevProps.color === nextProps.color &&
        prevProps.value === nextProps.value &&
        prevProps.index === nextProps.index &&
        prevProps.showValue === nextProps.showValue &&
        prevProps.showIndex === nextProps.showIndex &&
        prevProps.showArrow === nextProps.showArrow &&
        prevProps.yOffset === nextProps.yOffset // Include yOffset in comparison
    );
});
