import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import { ThemeProvider as MuiThemeProvider, CssBaseline } from "@mui/material";
// Remove useTheme import from here, it's not needed in this component
// import { useTheme } from '@mui/material';

// Import your actual theme definitions
import { lightTheme } from "./lightTheme"; // Adjust path if necessary
import { darkTheme } from "./darkTheme"; // Adjust path if necessary

// Define the default shape of your context
const ThemeContext = createContext({
  themeMode: "dark", // Default value
  toggleTheme: () => {
    console.warn("toggleTheme function not yet initialized");
  }, // Placeholder
});

export const ThemeProvider = ({ children }) => {
  // Initialize state, potentially reading from localStorage
  const [themeMode, setThemeMode] = useState(() => {
    // Read initial theme from localStorage or default to 'dark'
    try {
      const savedTheme = localStorage.getItem("theme");
      return savedTheme === "light" || savedTheme === "dark"
        ? savedTheme
        : "dark";
    } catch (error) {
      console.error("Could not read theme from localStorage", error);
      return "dark"; // Fallback to dark if localStorage fails
    }
  });

  // Update localStorage whenever themeMode changes and set data-theme attribute
  useEffect(() => {
    try {
      localStorage.setItem("theme", themeMode);

      // Update the data-theme attribute on the body element for CSS variables
      document.body.setAttribute('data-theme', themeMode);
    } catch (error) {
      console.error("Could not save theme to localStorage", error);
    }
  }, [themeMode]);

  // Function to toggle the theme mode state
  const toggleTheme = () => {
    setThemeMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
  };

  // *** THE IMPORTANT PART ***
  // Select the correct MUI theme object based on the current themeMode state
  // useMemo prevents recreating the theme object on every render unless themeMode changes
  const theme = useMemo(
    () => (themeMode === "light" ? lightTheme : darkTheme),
    [themeMode]
  );

  // Provide the theme mode state and toggle function via context
  return (
    <ThemeContext.Provider value={{ themeMode, toggleTheme }}>
      {/* Apply the selected MUI theme (light or dark) */}
      <MuiThemeProvider theme={theme}>
        <CssBaseline /> {/* Apply baseline styles based on the theme */}
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

// Custom hook to consume the theme context
export const useThemeContext = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useThemeContext must be used within a ThemeProvider");
  }
  return context;
};
