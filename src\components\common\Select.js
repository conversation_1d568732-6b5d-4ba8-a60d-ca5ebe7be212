// Select.js
// A reusable select component with consistent styling

import React from 'react';
import { FormControl, InputLabel, Select as MuiSelect, MenuItem, Typography, Box } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable select component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string|node} props.label - Select label (can be a string or a React node)
 * @param {any} props.value - Selected value
 * @param {function} props.onChange - Change handler
 * @param {Array} props.options - Options to display
 * @param {string} props.valueKey - Key to use for option value
 * @param {string} props.labelKey - Key to use for option label
 * @param {boolean} props.fullWidth - Whether the select should take full width
 * @param {string} props.size - Select size (small, medium)
 * @param {Object} props.sx - Additional styles
 */
const Select = ({
  label,
  value,
  onChange,
  options = [],
  valueKey = 'value',
  labelKey = 'label',
  fullWidth = true,
  size = 'medium',
  sx = {},
  ...rest
}) => {
  const handleChange = (event) => {
    onChange(event.target.value);
  };

  return (
    <FormControl fullWidth={fullWidth} size={size} sx={sx}>
      <InputLabel id={`select-label-${label}`}>{label}</InputLabel>
      <MuiSelect
        labelId={`select-label-${label}`}
        id={`select-${label}`}
        value={value}
        label={label}
        onChange={handleChange}
        {...rest}
      >
        {options.map((option) => (
          <MenuItem key={option[valueKey]} value={option[valueKey]}>
            {typeof option[labelKey] === 'object' ? (
              option[labelKey]
            ) : (
              <Typography>{option[labelKey]}</Typography>
            )}
          </MenuItem>
        ))}
      </MuiSelect>
    </FormControl>
  );
};

Select.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.any.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.array.isRequired,
  valueKey: PropTypes.string,
  labelKey: PropTypes.string,
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  sx: PropTypes.object
};

export default Select;
