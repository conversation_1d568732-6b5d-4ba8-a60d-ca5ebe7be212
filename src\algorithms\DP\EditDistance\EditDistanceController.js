// EditDistanceController.js
// This component provides the controls for Edit Distance algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, TextField } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import TextFieldsIcon from '@mui/icons-material/TextFields';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';

// Import algorithm functions
import EditDistanceAlgorithm from './EditDistanceAlgorithm';

const EditDistanceController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [string1, setString1] = useState(params?.string1 || "kitten");
    const [string2, setString2] = useState(params?.string2 || "sitting");

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { string1, string2 });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { string1, string2 });

        // Update params first
        onParamChange({
            string1,
            string2
        });

        // Set steps and movements directly
        try {
            const result = EditDistanceAlgorithm.generateEditDistanceSteps({
                string1,
                string2
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [string1, string2, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle parameter changes
    const handleString1Change = useCallback((value) => {
        console.log('handleString1Change called with value:', value);
        setString1(value);
        resetAndGenerateSteps();
    }, [resetAndGenerateSteps]);

    const handleString2Change = useCallback((value) => {
        console.log('handleString2Change called with value:', value);
        setString2(value);
        resetAndGenerateSteps();
    }, [resetAndGenerateSteps]);

    // Custom component for string input
    const StringInput = ({ value, onChange, label, disabled }) => {
        return (
            <TextField
                fullWidth
                label={label}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                variant="outlined"
                size="small"
                disabled={disabled}
                sx={{ mb: 2 }}
            />
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for Edit Distance algorithm
    const pseudocode = [
        { code: "function EditDistance(string1, string2):", lineNumber: 1, indent: 0 },
        { code: "  m = length(string1)", lineNumber: 2, indent: 1 },
        { code: "  n = length(string2)", lineNumber: 3, indent: 1 },
        { code: "  dp[0...m][0...n] = 0", lineNumber: 4, indent: 1 },
        { code: "  // Initialize first row and column", lineNumber: 5, indent: 1 },
        { code: "  for i = 0 to m: dp[i][0] = i", lineNumber: 6, indent: 1 },
        { code: "  for j = 0 to n: dp[0][j] = j", lineNumber: 7, indent: 1 },
        { code: "", lineNumber: 8, indent: 0 },
        { code: "  // Fill the DP table", lineNumber: 9, indent: 1 },
        { code: "  for i = 1 to m:", lineNumber: 10, indent: 1 },
        { code: "    for j = 1 to n:", lineNumber: 11, indent: 2 },
        { code: "      if string1[i-1] == string2[j-1]:", lineNumber: 12, indent: 3 },
        { code: "        dp[i][j] = dp[i-1][j-1]  // Match", lineNumber: 13, indent: 4 },
        { code: "      else:", lineNumber: 14, indent: 3 },
        { code: "        dp[i][j] = min(", lineNumber: 15, indent: 4 },
        { code: "          dp[i-1][j] + 1,      // Delete", lineNumber: 16, indent: 5 },
        { code: "          dp[i][j-1] + 1,      // Insert", lineNumber: 17, indent: 5 },
        { code: "          dp[i-1][j-1] + 1     // Replace", lineNumber: 18, indent: 5 },
        { code: "        )", lineNumber: 19, indent: 4 },
        { code: "", lineNumber: 20, indent: 0 },
        { code: "  // The result is in the bottom-right cell", lineNumber: 21, indent: 1 },
        { code: "  return dp[m][n]", lineNumber: 22, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Edit Distance Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Edit Distance (Levenshtein Distance) algorithm calculates the minimum number of operations required to transform one string into another.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(m*n) where m and n are the lengths of the strings
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(m*n) for the dynamic programming table
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Operations allowed: insertion, deletion, and substitution
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to build a table of minimum edit distances for substrings
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'string1',
                        type: 'component',
                        label: 'Source String',
                        component: StringInput,
                        componentProps: {
                            label: 'Source String',
                            value: string1,
                            onChange: handleString1Change
                        },
                        icon: TextFieldsIcon
                    },
                    {
                        name: 'string2',
                        type: 'component',
                        label: 'Target String',
                        component: StringInput,
                        componentProps: {
                            label: 'Target String',
                            value: string2,
                            onChange: handleString2Change
                        },
                        icon: CompareArrowsIcon
                    }
                ]}
                values={{
                    string1,
                    string2
                }}
                onChange={(newValues) => {
                    if (newValues.string1 !== undefined && newValues.string1 !== string1) {
                        handleString1Change(newValues.string1);
                    }
                    if (newValues.string2 !== undefined && newValues.string2 !== string2) {
                        handleString2Change(newValues.string2);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default EditDistanceController;
