// DFS/index.js
// Export only what's needed from this algorithm

import DFSVisualization from './DFSVisualization';
import DFSController from './DFSController';
import DFSAlgorithm from './DFSAlgorithm';

export const metadata = {
  id: 'DFS',
  name: 'Depth-First Search',
  description: 'An algorithm for traversing or searching tree or graph data structures.',
  timeComplexity: 'O(V + E)',
  spaceComplexity: 'O(V)',
  defaultParams: {
    nodes: 6,
    startNode: 0,
    targetNode: 5,
    density: 0.5,
    customEdges: [],
  },
};

export const components = {
  visualization: DFSVisualization,
  controller: DFSController,
  algorithm: DFSAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
