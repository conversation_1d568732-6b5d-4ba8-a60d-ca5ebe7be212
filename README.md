# Algorithm Visualization Project

This project provides interactive visualizations for various algorithms to help understand their inner workings.

## Implemented Algorithms

### 1. Towers of Hanoi
- **Type**: Recursive algorithm
- **Visualization**: 3D representation of disks moving between three towers
- **Implementation Files**:
  - `src/algorithms/TowersOfHanoi/TowersOfHanoiAlgorithm.js`
  - `src/algorithms/TowersOfHanoi/TowersOfHanoiVisualization.js`
  - `src/algorithms/TowersOfHanoi/TowersOfHanoiController.js`

### 2. Bubble Sort
- **Type**: Sorting algorithm
- **Time Complexity**: O(n²)
- **Space Complexity**: O(1)
- **Visualization**: 3D bars representing array elements with animated swaps
- **Implementation Files**:
  - `src/algorithms/BubbleSort/BubbleSortAlgorithm.js`
  - `src/algorithms/BubbleSort/BubbleSortVisualization.js`
  - `src/algorithms/BubbleSort/BubbleSortController.js`

### 3. Merge Sort
- **Type**: Sorting algorithm
- **Time Complexity**: O(n log n)
- **Space Complexity**: O(n)
- **Visualization**: 3D bars representing array elements with animated merges
- **Implementation Files**:
  - `src/algorithms/MergeSort/MergeSortAlgorithm.js`
  - `src/algorithms/MergeSort/MergeSortVisualization.js`
  - `src/algorithms/MergeSort/MergeSortController.js`

### 4. Quick Sort
- **Type**: Sorting algorithm
- **Time Complexity**: O(n log n) average, O(n²) worst case
- **Space Complexity**: O(log n)
- **Visualization**: 3D bars representing array elements with animated partitioning and swaps
- **Implementation Files**:
  - `src/algorithms/QuickSort/QuickSortAlgorithm.js`
  - `src/algorithms/QuickSort/QuickSortVisualization.js`
  - `src/algorithms/QuickSort/QuickSortController.js`

### 5. Heap Sort
- **Type**: Sorting algorithm
- **Time Complexity**: O(n log n)
- **Space Complexity**: O(1)
- **Visualization**: 3D bars and binary tree representation of the heap
- **Implementation Files**:
  - `src/algorithms/HeapSort/HeapSortAlgorithm.js`
  - `src/algorithms/HeapSort/HeapSortVisualization.js`
  - `src/algorithms/HeapSort/HeapSortController.js`

### 6. Dijkstra's Algorithm
- **Type**: Graph algorithm
- **Time Complexity**: O((V + E) log V)
- **Space Complexity**: O(V)
- **Visualization**: 3D graph with nodes and weighted edges
- **Implementation Files**:
  - `src/algorithms/Dijkstra/DijkstraAlgorithm.js`
  - `src/algorithms/Dijkstra/DijkstraVisualization.js`
  - `src/algorithms/Dijkstra/DijkstraController.js`

### 7. Breadth-First Search (BFS)
- **Type**: Graph algorithm
- **Time Complexity**: O(V + E)
- **Space Complexity**: O(V)
- **Visualization**: 3D graph with animated node traversal
- **Implementation Files**:
  - `src/algorithms/BFS/BFSAlgorithm.js`
  - `src/algorithms/BFS/BFSVisualization.js`
  - `src/algorithms/BFS/BFSController.js`

### 8. Depth-First Search (DFS)
- **Type**: Graph algorithm
- **Time Complexity**: O(V + E)
- **Space Complexity**: O(V)
- **Visualization**: 3D graph with animated node traversal
- **Implementation Files**:
  - `src/algorithms/DFS/DFSAlgorithm.js`
  - `src/algorithms/DFS/DFSVisualization.js`
  - `src/algorithms/DFS/DFSController.js`

### 9. Longest Common Subsequence (LCS)
- **Type**: Dynamic Programming algorithm
- **Time Complexity**: O(m*n)
- **Space Complexity**: O(m*n)
- **Visualization**: 3D grid showing the DP table and traceback path
- **Implementation Files**:
  - `src/algorithms/LCS/LCSAlgorithm.js`
  - `src/algorithms/LCS/LCSVisualization.js`
  - `src/algorithms/LCS/LCSController.js`

### 10. Floyd-Warshall Algorithm
- **Type**: Graph algorithm (All-pairs shortest path)
- **Time Complexity**: O(V³)
- **Space Complexity**: O(V²)
- **Visualization**: 3D graph and distance matrix with animated updates
- **Implementation Files**:
  - `src/algorithms/FloydWarshall/FloydWarshallAlgorithm.js`
  - `src/algorithms/FloydWarshall/FloydWarshallVisualization.js`
  - `src/algorithms/FloydWarshall/FloydWarshallController.js`

## Project Structure

Each algorithm follows a consistent structure:

1. **Algorithm Logic (`*Algorithm.js`)**
   - Contains the core algorithm implementation
   - Generates steps for visualization
   - Provides pseudocode for display

2. **Visualization Component (`*Visualization.js`)**
   - 3D visualization using Three.js/React Three Fiber
   - Animates algorithm steps
   - Handles camera and scene setup

3. **Controller Component (`*Controller.js`)**
   - UI controls for the algorithm
   - Parameter settings (array size, speed, etc.)
   - Step navigation and animation controls

## Known Issues and TODOs

### Quick Sort
- Array size changes don't properly reset the visualization
- Steps count doesn't update correctly when array size changes
- Animation and state management need improvement for array size changes

### Merge Sort
- Partially implemented, may need refinements

## Architecture Principles

1. **Modularity**: Algorithms are completely isolated from each other
2. **Centralized Array Management**: Array generation is shared across components
3. **Consistent UI**: Similar styling and behavior across all algorithm visualizations
4. **Theme-Aware**: All UI components adapt to light/dark theme

## Adding New Algorithms

To add a new algorithm:

1. Create a new folder under `src/algorithms/[AlgorithmName]/`
2. Implement the three core files:
   - `[AlgorithmName]Algorithm.js`
   - `[AlgorithmName]Visualization.js`
   - `[AlgorithmName]Controller.js`
3. Register the algorithm in `src/algorithms/AlgorithmRegistry.js`