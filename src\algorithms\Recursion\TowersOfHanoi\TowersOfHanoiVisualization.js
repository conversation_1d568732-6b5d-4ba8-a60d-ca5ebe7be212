// SimpleFixedTowersOfHanoi.js
// A completely new implementation with simplified animation logic

import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useThree } from "@react-three/fiber";
import { Html } from "@react-three/drei";
import * as THREE from "three";
import { useSpring, a } from "@react-spring/three";
import { useSpeed } from "../../../context/SpeedContext";
import { useAlgorithm } from "../../../context/AlgorithmContext";

// Constants for the tower
const POLE_HEIGHT = 2.5;       // Further increased to 2.5
const POLE_RADIUS = 0.07;      // Kept at 0.07
const BASE_WIDTH = 12.0;       // Further increased to 12.0 to accommodate larger discs
const BASE_DEPTH = 3.0;        // Further increased to 3.0 (this is the width from user's perspective)
const BASE_HEIGHT = 0.35;      // Further increased to 0.35
const DISC_HEIGHT = 0.2;       // Kept at 0.2
const DISC_BASE_Y = BASE_HEIGHT + 0.1; // Start discs much further above the base to prevent clipping
const BASE_Y_OFFSET = -1;      // Kept at -1
const POLE_SPACING = 4.0;      // Further increased to 4.0 to accommodate larger discs
const POLE_X_POSITIONS = [-POLE_SPACING, 0, POLE_SPACING]; // Left, Middle, Right
const MIN_DISCS = 1;
const MAX_DISCS = 8;
const DEFAULT_NUM_DISCS = 3;

// CRITICAL FIX: Define a constant for the lift height
// This ensures discs are lifted to a consistent height
const LIFT_HEIGHT_ABOVE_POLES = 0.3; // Increased from 0.2 to 0.3

// Camera settings
const CAMERA_POSITION = [0, 2.8, 7.0]; // Brought even closer to the user while still showing the entire tower
const CAMERA_LOOKAT = [0, 0.6, 0];     // Slightly lowered look point
const CAMERA_FOV = 75;                  // Widened FOV to ensure everything is visible even when closer
const CAMERA_NEAR = 0.5;
const CAMERA_FAR = 500;

// Function to calculate animation delay based on speed (1-10)
const getAnimationDelay = (speed) => {
  // Convert speed (1-10) to delay (2000ms - 200ms)
  // Speed 1 (slowest) = 2000ms
  // Speed 10 (fastest) = 200ms
  const validSpeed = Math.max(1, Math.min(10, speed));
  const delay = 2200 - (validSpeed * 200);
  // console.log(`Calculated delay: ${delay}ms for speed ${validSpeed}`);
  return delay;
};

// Theme-aware colors for the tower
const getColors = (theme) => {
  // Check if theme is provided and determine if it's dark mode
  const isDarkMode = theme?.palette?.mode === 'dark';
  // console.log('getColors called with isDarkMode:', isDarkMode);

  return {
    // Keep the original pole and disc colors
    POLE: isDarkMode ? "#8D6E63" : "#8B4513", // Brown for poles
    POLE_EMISSIVE: isDarkMode ? "#6D4C41" : "#3D2314", // Emissive for poles

    // Keep the original base color
    BASE: isDarkMode ? "#795548" : "#A0522D", // Brown for base

    // Theme-aware ground color
    GROUND: isDarkMode ? "#121212" : "#eeeeee", // Match Bubble Sort ground color

    // Theme-aware world color
    SURFACE: isDarkMode ? "#212121" : "#ffffff", // Match Bubble Sort surface color

    // Theme-aware text colors
    TEXT: isDarkMode ? "#ffffff" : "#000000", // White text on dark, black on light
    TEXT_BACKGROUND: isDarkMode ? "rgba(0,0,0,0.7)" : "rgba(255,255,255,0.7)", // Semi-transparent background
    TEXT_SHADOW: isDarkMode ? "rgba(0,0,0,0.7)" : "rgba(0,0,0,0.3)", // Shadow for text

    // Keep the original disc colors
    DISC_PALETTE: isDarkMode ? [
      "#FF7043", // Deep Orange 400
      "#42A5F5", // Blue 400
      "#66BB6A", // Green 400
      "#FFCA28", // Amber 400
      "#AB47BC", // Purple 400
      "#26C6DA", // Cyan 400
      "#EC407A", // Pink 400
      "#5C6BC0", // Indigo 400
    ] : [
      "#FF8A65", // Deep Orange 300
      "#64B5F6", // Blue 300
      "#81C784", // Green 300
      "#FFD54F", // Amber 300
      "#BA68C8", // Purple 300
      "#4DD0E1", // Cyan 300
      "#F06292", // Pink 300
      "#7986CB", // Indigo 300
    ],
  };
};

// Generate disc data for the tower
const generateDiscData = (numDiscs) => {
  const discs = [];
  const initialStacks = [[], [], []]; // Three poles

  // Create discs from smallest to largest
  for (let i = 1; i <= numDiscs; i++) {
    // IMPROVED: Better disc size calculation to prevent overlap
    // Minimum size is 0.4, maximum size is 1.4
    // This ensures discs are properly sized and don't overlap
    const minSize = 0.4;
    const maxSize = 1.4;
    const sizeRange = maxSize - minSize;

    const disc = {
      id: `disc-${i}`,
      // Size based on disc number (larger number = larger disc)
      // Linear interpolation between minSize and maxSize
      size: minSize + (i / numDiscs) * sizeRange,
    };
    discs.push(disc);
  }

  // Add discs to the first pole in the correct order (largest at bottom)
  for (let i = numDiscs; i >= 1; i--) {
    initialStacks[0].push(`disc-${i}`);
  }

  return { discs, initialStacks };
};

// Generate moves for the Tower of Hanoi algorithm
const generateMoves = (n, source, target, auxiliary) => {
  const moves = [];

  const hanoi = (n, source, target, auxiliary) => {
    if (n === 1) {
      // Base case: move the smallest disc directly
      moves.push({ disc: 1, from: source, to: target });
      return;
    }

    // Move n-1 discs from source to auxiliary using target as temporary
    hanoi(n - 1, source, auxiliary, target);

    // Move the nth disc from source to target
    moves.push({ disc: n, from: source, to: target });

    // Move n-1 discs from auxiliary to target using source as temporary
    hanoi(n - 1, auxiliary, target, source);
  };

  hanoi(n, source, target, auxiliary);
  return moves;
};

// Animated disc component
// Removed React.memo to ensure the component re-renders when theme changes
const AnimatedDisc = ({ discData, initialPosition, springApiRef, color, theme, colors }) => {
  // CRITICAL FIX: Create a spring for the disc position with immediate positioning
  // This ensures the disc starts at the exact position we want
  const [spring, api] = useSpring(() => ({
    position: [initialPosition.x, initialPosition.y, initialPosition.z],
    config: { tension: 170, friction: 26, precision: 0.01 },
    immediate: true, // Apply the initial position immediately without animation
  }));

  // CRITICAL FIX: Update position when initialPosition changes
  useEffect(() => {
    // Ensure the disc is at the correct position whenever initialPosition changes
    api.set({
      position: [initialPosition.x, initialPosition.y, initialPosition.z]
    });
  }, [api, initialPosition]);

  // Store the API reference for external control
  useEffect(() => {
    if (springApiRef) {
      springApiRef.current = api;

      // CRITICAL FIX: Ensure the disc is at the correct position
      // This is a backup to make sure the disc is where it should be
      api.set({
        position: [initialPosition.x, initialPosition.y, initialPosition.z]
      });
    }
  }, [api, springApiRef, initialPosition]);

  return (
    <a.mesh
      position={spring.position}
      castShadow
      receiveShadow
    >
      <cylinderGeometry
        attach="geometry"
        args={[discData.size, discData.size, DISC_HEIGHT, 64]}
      />
      <meshPhysicalMaterial
        color={color}
        roughness={0.3}
        metalness={0.4}
        clearcoat={0.5}
        clearcoatRoughness={0.2}
        reflectivity={0.5}
        emissive={color}
        emissiveIntensity={0.15}
        envMapIntensity={1.0}
      />

      {/* Add a label to show the disc ID */}
      <Html position={[0, 0, discData.size]}>
        <div
          style={{
            color: colors.TEXT,
            background: colors.TEXT_BACKGROUND,
            padding: "2px 5px",
            borderRadius: "3px",
            fontSize: "10px",
            fontWeight: "bold",
            userSelect: "none",
            boxShadow: `0 0 3px ${colors.TEXT_SHADOW}`,
          }}
        >
          {discData.id.replace('disc-', '')}
        </div>
      </Html>
    </a.mesh>
  );
};

// --- SimpleFixedTowersOfHanoi Component ---
const SimpleFixedTowersOfHanoi = ({ params, state, step, setStep, theme }) => {
  // Get totalSteps from context
  const { totalSteps } = useAlgorithm();
  // Get theme-aware colors
  const colors = useMemo(() => {
    // console.log('Theme in SimpleFixedTowersOfHanoi:', theme?.palette?.mode);
    // console.log('Theme object:', theme);
    return getColors(theme);
  }, [theme?.palette?.mode]); // Explicitly depend on theme.palette.mode to ensure re-render
  const { camera } = useThree();
  const { speed } = useSpeed(); // Get speed from context
  const [discsData, setDiscsData] = useState([]);
  const [poleStacks, setPoleStacks] = useState([[], [], []]);
  const poleStacksRef = useRef([[], [], []]); // Reference to current pole stacks
  const discSpringApis = useRef({}); // Refs to hold APIs of AnimatedDisc instances
  const isAnimatingRef = useRef(false); // Flag to track if animation is in progress
  const lastAppliedStepRef = useRef(0); // Track the last applied step
  const timeoutIdRef = useRef(null); // Reference to timeout ID for cleanup
  const speedRef = useRef(speed); // Reference to current speed to avoid re-renders

  // Update speedRef when speed changes
  useEffect(() => {
    // console.log(`SimpleFixedTowersOfHanoi: Speed changed to ${speed}`);
    speedRef.current = speed;
  }, [speed]);

  // Set camera position and FOV
  useEffect(() => {
    camera.position.set(CAMERA_POSITION[0], CAMERA_POSITION[1], CAMERA_POSITION[2]);
    camera.lookAt(CAMERA_LOOKAT[0], CAMERA_LOOKAT[1], CAMERA_LOOKAT[2]);
    camera.fov = CAMERA_FOV;
    camera.near = CAMERA_NEAR;
    camera.far = CAMERA_FAR;
    camera.updateProjectionMatrix();
  }, [camera]);

  // We no longer need to set scene background color here
  // It's now handled centrally in AlgorithmVisualizer.js

  // Create the scene objects
  const sceneObjects = useMemo(() => {
    // Create poles
    const poles = POLE_X_POSITIONS.map((x, index) => (
      <mesh
        key={`pole-${index}`}
        position={[x, BASE_Y_OFFSET + POLE_HEIGHT / 2 + BASE_HEIGHT, 0]}
        castShadow
        receiveShadow
      >
        <cylinderGeometry
          attach="geometry"
          args={[POLE_RADIUS, POLE_RADIUS, POLE_HEIGHT, 32]}
        />
        <meshPhysicalMaterial
          attach="material"
          color={colors.POLE}
          roughness={0.5}
          metalness={0.3}
          clearcoat={0.3}
          clearcoatRoughness={0.2}
          reflectivity={0.3}
          emissive={colors.POLE_EMISSIVE}
          emissiveIntensity={0.1}
          envMapIntensity={0.8}
        />

        {/* Add pole labels */}
        <Html position={[0, -POLE_HEIGHT/2 - 0.2, 0]}>
          <div style={{
            color: colors.TEXT,
            background: colors.TEXT_BACKGROUND,
            padding: '3px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
            userSelect: 'none',
            whiteSpace: 'nowrap',
            boxShadow: `0 0 3px ${colors.TEXT_SHADOW}`
          }}>
            {index === 0 ? 'Source' : index === 1 ? 'Auxiliary' : 'Destination'}
          </div>
        </Html>
      </mesh>
    ));

    // Create base
    const base = (
      <mesh
        position={[0, BASE_Y_OFFSET + BASE_HEIGHT / 2, 0]}
        castShadow
        receiveShadow
      >
        <boxGeometry
          attach="geometry"
          args={[BASE_WIDTH, BASE_HEIGHT, BASE_DEPTH, 4, 4, 4]}
        />
        <meshStandardMaterial
          attach="material"
          color={colors.BASE}
          roughness={0.5}
          metalness={0.2}
          receiveShadow
        />
      </mesh>
    );

    // Create ground plane
    const ground = (
      <mesh
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, BASE_Y_OFFSET - 0.01, 0]}
        receiveShadow
      >
        <planeGeometry attach="geometry" args={[20, 20]} />
        <meshStandardMaterial
          attach="material"
          color={colors.GROUND}
          side={THREE.DoubleSide}
          roughness={0.8}
          metalness={0.2}
          receiveShadow
        />
      </mesh>
    );

    return { poles, base, ground };
  }, [colors]); // Add colors as a dependency to update when theme changes

  // Initialize disc data when numDiscs changes
  useEffect(() => {
    // CRITICAL FIX: Ensure we're not in the middle of an animation
    const initializeDiscs = async () => {
      // console.log('Starting disc initialization...');

      // CRITICAL FIX: Always wait a moment before initializing
      // This ensures any previous animations have completed
      await new Promise(resolve => setTimeout(resolve, 200));

      // If animation is in progress, wait for it to complete
      if (isAnimatingRef.current) {
        // console.log('Animation in progress, waiting before initializing discs...');
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Set flag to prevent other animations during initialization
      isAnimatingRef.current = true;

      try {
        // CRITICAL FIX: Cancel any ongoing animations
        // This ensures we start with a clean slate
        Object.values(discSpringApis.current).forEach(api => {
          if (api.current) {
            api.current.stop();
          }
        });

        const numDiscs = params?.numDiscs
          ? Math.max(MIN_DISCS, Math.min(MAX_DISCS, params.numDiscs))
          : DEFAULT_NUM_DISCS;

        // console.log(`Initializing ${numDiscs} discs`);

        // Generate fresh disc data
        const { discs, initialStacks } = generateDiscData(numDiscs);

        // Update state and refs
        setDiscsData(discs);
        setPoleStacks(initialStacks);
        poleStacksRef.current = initialStacks;

        // Initialize spring APIs for each disc
        discSpringApis.current = {};
        discs.forEach((disc) => {
          discSpringApis.current[disc.id] = { current: null };
        });

        // Generate all moves
        const moves = generateMoves(numDiscs, 0, 2, 1);

        // Reset step
        if (typeof setStep === 'function') {
          setStep(0);
        }
        lastAppliedStepRef.current = 0;

        // CRITICAL FIX: Wait longer for the initialization to complete
        // This ensures all discs are properly positioned
        await new Promise(resolve => setTimeout(resolve, 200));

        // console.log('Disc initialization complete');
      } catch (error) {
        console.error('Error initializing discs:', error);
      } finally {
        // Clear the animation flag
        isAnimatingRef.current = false;
      }
    };

    // Start the initialization process
    initializeDiscs();

  }, [params?.numDiscs, setStep]);

  // Helper to get the logical position of a disc based on its current position in the stacks
  const getDiscLogicalPosition = useCallback((discId) => {
    // Find which pole the disc is on and its position in the stack
    for (let poleIndex = 0; poleIndex < poleStacksRef.current.length; poleIndex++) {
      const stack = poleStacksRef.current[poleIndex];
      const discIndex = stack.indexOf(discId);
      if (discIndex !== -1) {
        // Found the disc on this pole
        // CRITICAL FIX: Calculate the correct Y position
        // The Y position is the base Y offset + disc base Y + (disc index * disc height)
        // This ensures discs are stacked properly with the largest at the bottom
        return {
          x: POLE_X_POSITIONS[poleIndex],
          y: BASE_Y_OFFSET + DISC_BASE_Y + discIndex * DISC_HEIGHT,
          z: 0,
        };
      }
    }
    // Disc not found in any stack, return a default position
    console.warn(`Disc ${discId} not found in any stack`);
    return { x: 0, y: 0, z: 0 };
  }, []);

  // Reset the tower to initial state
  const resetTower = useCallback(async () => {
    // console.log('Resetting tower...');

    // If animation is in progress, wait for it to complete
    if (isAnimatingRef.current) {
      // console.log('Animation in progress, waiting before reset...');
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    isAnimatingRef.current = true;

    try {
      const numDiscs = discsData.length;
      if (numDiscs > 0) {
        // Generate fresh initial data
        const { initialStacks: resetStacks } = generateDiscData(numDiscs);

        // Update both the state and the ref
        setPoleStacks(resetStacks);
        poleStacksRef.current = resetStacks;

        // CRITICAL FIX: Reset spring positions imperatively to match initialStacks
        // We need to ensure discs are positioned correctly when the tower is reset
        for (const disc of discsData) {
          const apiRef = discSpringApis.current[disc.id];
          if (!apiRef?.current) continue;

          // Calculate the initial position based on the disc's position in the initial stack
          const discIndex = resetStacks[0].indexOf(disc.id);
          if (discIndex === -1) {
            console.error(`Disc ${disc.id} not found in reset stacks`);
            continue;
          }

          const initialPos = {
            x: POLE_X_POSITIONS[0], // All discs start on the first pole
            y: BASE_Y_OFFSET + DISC_BASE_Y + discIndex * DISC_HEIGHT,
            z: 0
          };

          // console.log(`Resetting disc ${disc.id} to position [${initialPos.x}, ${initialPos.y}, ${initialPos.z}]`);

          // First stop any ongoing animations
          apiRef.current.stop();

          // Then set the position immediately (no animation)
          apiRef.current.set({
            position: [initialPos.x, initialPos.y, initialPos.z]
          });
        }

        // Wait a moment for the reset to complete
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      isAnimatingRef.current = false;
    }
  }, [discsData]);

  // COMPLETELY REWRITTEN: Move a disc from one pole to another with a simplified animation approach
  const moveDisc = useCallback(async (sourcePoleIndex, targetPoleIndex, discId) => {
    // console.log(`Moving disc ${discId} from pole ${sourcePoleIndex} to pole ${targetPoleIndex}`);

    // If animation is already in progress, wait
    if (isAnimatingRef.current) {
      // console.log('Animation already in progress, waiting...');
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    isAnimatingRef.current = true;

    try {
      // Get the current state of the stacks
      const currentStacks = JSON.parse(JSON.stringify(poleStacksRef.current));

      // Validate the move
      const sourceStack = currentStacks[sourcePoleIndex];
      if (sourceStack.length === 0) {
        console.error(`Source pole ${sourcePoleIndex} is empty`);
        return;
      }

      // Get the top disc from the source pole
      const topDiscId = sourceStack[sourceStack.length - 1];
      if (topDiscId !== discId) {
        console.error(`Disc ${discId} is not at the top of source pole ${sourcePoleIndex}`);
        return;
      }

      // Get the spring API for this disc
      const apiRef = discSpringApis.current[discId];
      if (!apiRef?.current) {
        console.error(`No spring API found for disc ${discId}`);
        return;
      }

      // Update the stacks (remove from source, add to target)
      const newStacks = JSON.parse(JSON.stringify(currentStacks));
      newStacks[sourcePoleIndex].pop();
      newStacks[targetPoleIndex].push(discId);

      // CRITICAL FIX: Calculate positions with exact values
      // Get the X positions of the source and target poles
      const sourceX = POLE_X_POSITIONS[sourcePoleIndex];
      const targetX = POLE_X_POSITIONS[targetPoleIndex];

      // Calculate the current Y position (where the disc is now)
      // This is the height of the disc on the source pole
      // sourceStack.length - 1 gives us the index of the top disc
      const discIndexOnSource = sourceStack.length - 1;
      const currentY = BASE_Y_OFFSET + DISC_BASE_Y + (discIndexOnSource * DISC_HEIGHT);

      // Calculate the lift height (how high to lift the disc)
      // We'll lift it just above the poles
      const liftY = BASE_Y_OFFSET + POLE_HEIGHT + LIFT_HEIGHT_ABOVE_POLES; // Just above the poles

      // Calculate the target Y position (where the disc will end up)
      // We need to find how many discs are already on the target pole
      // We've already added our disc to newStacks, so we need to subtract 1
      const targetStack = currentStacks[targetPoleIndex]; // Use the original stack before our changes
      const discIndexOnTarget = targetStack.length; // This will be the index of our disc after the move
      const targetY = BASE_Y_OFFSET + DISC_BASE_Y + (discIndexOnTarget * DISC_HEIGHT);

      // console.log(`Moving disc from Y=${currentY} to Y=${targetY} via liftY=${liftY}`);

      const api = apiRef.current;

      // Get current speed from ref
      const currentSpeed = speedRef.current;
      // console.log(`Using speed ${currentSpeed} for disc animation`);

      // Calculate animation parameters based on speed (1-10)
      // Higher speed = higher tension, lower friction for faster animations
      const tensionBase = 120;
      const frictionBase = 26;
      const tensionFactor = 8; // Adjusted for speed range 1-10
      const frictionFactor = 1.5; // Adjusted for speed range 1-10

      // Ensure speed is within valid range
      const validSpeed = Math.max(1, Math.min(10, currentSpeed));

      const tension = tensionBase + (validSpeed * tensionFactor);
      const friction = Math.max(5, frictionBase - (validSpeed * frictionFactor));

      // console.log(`Animation parameters: tension=${tension}, friction=${friction}`);

      // CRITICAL FIX: Force the disc to its exact current position before animation
      // This is crucial to prevent the disc from appearing to move down first
      // console.log(`Forcing disc ${discId} to exact position [${sourceX}, ${currentY}, 0]`);

      // First, stop any ongoing animations
      api.stop();

      // Then set the position immediately (no animation)
      api.set({
        position: [sourceX, currentY, 0]
      });

      // Wait a moment to ensure the disc is at its starting position
      await new Promise(resolve => setTimeout(resolve, 100));

      // TRADITIONAL ANIMATION: Use a three-step animation (up, across, down)
      // This follows the classic Tower of Hanoi animation style
      // console.log(`Starting three-step animation for disc ${discId}`);

      // Calculate the lift height (how high to lift the disc)
      // We'll lift it well above the poles
      // Use the existing liftY value

      // Calculate animation duration based on speed (1-10)
      // Higher speed = shorter duration
      const baseDuration = 1000; // 1 second at speed 1
      const minDuration = 100;  // 0.1 seconds at speed 10
      const stepDuration = Math.max(minDuration, baseDuration - ((validSpeed - 1) * (baseDuration - minDuration) / 9));

      // Total duration is divided among the three steps
      const upDuration = stepDuration * 0.25;    // 25% of time for up movement
      const acrossDuration = stepDuration * 0.5; // 50% of time for across movement
      const downDuration = stepDuration * 0.25;   // 25% of time for down movement

      // console.log(`Animation durations: up=${upDuration}ms, across=${acrossDuration}ms, down=${downDuration}ms for speed ${validSpeed}`);

      // Use requestAnimationFrame for smooth animation
      let startTime = null;
      let animationFrameId = null;

      await new Promise(resolve => {
        const animate = (timestamp) => {
          if (!startTime) startTime = timestamp;
          const elapsed = timestamp - startTime;

          // Determine which step we're in and the progress within that step
          let stepProgress;
          let x, y;

          if (elapsed < upDuration) {
            // STEP 1: Moving UP
            stepProgress = elapsed / upDuration;

            // Use easing for smoother animation
            const easedProgress = 1 - Math.pow(1 - stepProgress, 2); // Ease out quad

            // Interpolate between current position and lifted position
            x = sourceX;
            y = currentY + (liftY - currentY) * easedProgress;
          }
          else if (elapsed < upDuration + acrossDuration) {
            // STEP 2: Moving ACROSS
            stepProgress = (elapsed - upDuration) / acrossDuration;

            // Use easing for smoother animation
            const easedProgress = stepProgress < 0.5
              ? 2 * stepProgress * stepProgress
              : 1 - Math.pow(-2 * stepProgress + 2, 2) / 2; // Ease in-out quad

            // Interpolate between lifted positions above source and target
            x = sourceX + (targetX - sourceX) * easedProgress;
            y = liftY;
          }
          else if (elapsed < upDuration + acrossDuration + downDuration) {
            // STEP 3: Moving DOWN
            stepProgress = (elapsed - upDuration - acrossDuration) / downDuration;

            // Use easing for smoother animation
            const easedProgress = stepProgress * stepProgress; // Ease in quad

            // Interpolate between lifted position and target position
            x = targetX;
            y = liftY + (targetY - liftY) * easedProgress;
          }
          else {
            // Animation complete
            x = targetX;
            y = targetY;

            // End the animation
            // console.log(`Animation complete for disc ${discId}`);
            resolve();
            return;
          }

          // Update the disc position
          api.set({ position: [x, y, 0] });

          // Continue the animation
          animationFrameId = requestAnimationFrame(animate);
        };

        // Start the animation
        animationFrameId = requestAnimationFrame(animate);

        // Add cleanup function in case we need to cancel
        return () => {
          if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            // console.log(`Animation cancelled for disc ${discId}`);
          }
        };
      });

      // Ensure the disc is at the exact final position
      api.set({ position: [targetX, targetY, 0] });

      // Update the state AFTER animation completes
      poleStacksRef.current = newStacks;
      setPoleStacks(newStacks);

      // console.log(`Move complete for disc ${discId}`);
    } finally {
      isAnimatingRef.current = false;
    }
  }, []);

  // Apply a single move from the algorithm
  const applyMove = useCallback(async (moveIndex, allMoves) => {
    if (moveIndex < 0 || moveIndex >= allMoves.length) {
      console.error(`Invalid move index: ${moveIndex}`);
      return;
    }

    const move = allMoves[moveIndex];
    // console.log(`Applying move ${moveIndex}:`, move);

    const discId = `disc-${move.disc}`;
    await moveDisc(move.from, move.to, discId);
  }, [moveDisc]);

  // Store the history of moves for efficient backward stepping
  const moveHistoryRef = useRef([]);

  // Apply a move and store it in history
  const applyMoveWithHistory = useCallback(async (moveIndex, allMoves) => {
    if (moveIndex < 0 || moveIndex >= allMoves.length) {
      console.error(`Invalid move index: ${moveIndex}`);
      return;
    }

    const move = allMoves[moveIndex];
    // Store the reverse move in history for backward stepping
    const reverseMove = {
      disc: move.disc,
      from: move.to,
      to: move.from
    };
    moveHistoryRef.current.push(reverseMove);

    // Apply the move
    const discId = `disc-${move.disc}`;
    await moveDisc(move.from, move.to, discId);
  }, [moveDisc]);

  // Undo the last move using the stored history
  const undoLastMove = useCallback(async () => {
    if (moveHistoryRef.current.length === 0) {
      console.error('No moves to undo');
      return;
    }

    // Get the last move from history
    const lastMove = moveHistoryRef.current.pop();
    const discId = `disc-${lastMove.disc}`;

    // Apply the reverse move
    await moveDisc(lastMove.from, lastMove.to, discId);
  }, [moveDisc]);

  // Handle step changes
  useEffect(() => {
    // Generate all moves for the current number of discs
    const numDiscs = discsData.length;
    if (numDiscs === 0) return;

    const allMoves = generateMoves(numDiscs, 0, 2, 1);

    const handleStepChange = async () => {
      // console.log(`Step changed from ${lastAppliedStepRef.current} to ${step}`);

      // Clear any existing timeout
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      if (step === 0) {
        // Reset to initial state
        await resetTower();
        lastAppliedStepRef.current = 0;
        moveHistoryRef.current = [];
      } else if (step > 0 && step <= allMoves.length) {
        if (step > lastAppliedStepRef.current) {
          // Moving forward - apply only the new moves
          for (let i = lastAppliedStepRef.current; i < step; i++) {
            await applyMoveWithHistory(i, allMoves);
          }
        } else if (step < lastAppliedStepRef.current) {
          // Moving backward - undo only the last move
          await undoLastMove();
        }
        lastAppliedStepRef.current = step;
      }
    };

    handleStepChange();
  }, [step, discsData.length, resetTower, applyMoveWithHistory, undoLastMove]);

  // Handle state changes (running, paused, idle)
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    // Generate all moves for the current number of discs
    const numDiscs = discsData.length;
    if (numDiscs === 0) return;

    const allMoves = generateMoves(numDiscs, 0, 2, 1);

    const runAutomaticSolver = () => {
      if (state === "running" && step < allMoves.length) {
        // If animation is in progress, wait before incrementing step
        if (isAnimatingRef.current) {
          timeoutIdRef.current = setTimeout(runAutomaticSolver, 50); // Reduced from 100ms to 50ms
          return;
        }

        // Get current speed from ref
        const currentSpeed = speedRef.current;
        // console.log(`Using speed ${currentSpeed} for automatic solver`);

        // Calculate delay based on speed
        const delay = getAnimationDelay(currentSpeed);

        // Increment step with a delay
        timeoutIdRef.current = setTimeout(() => {
          if (state === "running") {
            if (typeof setStep === 'function') {
              // console.log(`Auto-incrementing step from ${step} to ${step + 1}`);
              setStep(step + 1);
            }
            runAutomaticSolver(); // Continue with the next step
          }
        }, delay);
      }
    };

    if (state === "running") {
      // console.log('Starting automatic solver');
      runAutomaticSolver();
    } else if (state === "idle") {
      // Reset to initial state
      // console.log('Resetting tower to initial state');
      resetTower();
      // Reset step in parent component
      if (typeof setStep === 'function') {
        setStep(0);
      }
      lastAppliedStepRef.current = 0;
    }

    // Cleanup function
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }
    };
  }, [state, step, discsData.length, resetTower, setStep]);

  // --- Render ---
  return (
    <group position={[0, -1, 0]}> {/* Position the entire visualization lower to match Merge Sort */}
      {/* Static scene objects */}
      {sceneObjects.base}
      {sceneObjects.ground}
      {sceneObjects.poles}

      {/* Dynamic discs */}
      {discsData.map((disc) => {
        // Calculate a color based on disc ID
        const discNumber = parseInt(disc.id.replace('disc-', ''), 10);
        const colorIndex = (discNumber - 1) % colors.DISC_PALETTE.length;
        const discColor = colors.DISC_PALETTE[colorIndex];

        // Get the current logical position of this disc
        const currentLogicalPos = getDiscLogicalPosition(disc.id);

        return (
          <AnimatedDisc
            key={disc.id}
            discData={disc}
            initialPosition={currentLogicalPos}
            springApiRef={discSpringApis.current[disc.id]}
            color={discColor}
            theme={theme}
            colors={colors}
          />
        );
      })}
    </group>
  );
};

export default SimpleFixedTowersOfHanoi;
