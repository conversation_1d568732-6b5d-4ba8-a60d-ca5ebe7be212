import React from 'react';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { ReactComponent as LogoDark } from '../../assets/logo.svg';
import { ReactComponent as LogoLight } from '../../assets/logo-light.svg';
import { ReactComponent as LogoIcon } from '../../assets/logo-icon.svg';

/**
 * A theme-aware logo component that switches between light and dark versions
 * based on the current theme mode.
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Logo variant: 'full' or 'icon' (default: 'full')
 * @param {Object} props.sx - Additional styling via MUI's sx prop
 * @returns {JSX.Element} The theme-aware logo component
 */
const ThemeAwareLogo = ({ variant = 'full', sx = {} }) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  
  // Select the appropriate logo based on theme and variant
  const Logo = variant === 'icon' 
    ? LogoIcon 
    : (isDarkMode ? LogoDark : LogoLight);
  
  return (
    <Box
      component={Logo}
      sx={{
        width: variant === 'icon' ? '2.5rem' : '3.5rem',
        height: variant === 'icon' ? '2.5rem' : '3.5rem',
        ...sx
      }}
    />
  );
};

export default ThemeAwareLogo;
