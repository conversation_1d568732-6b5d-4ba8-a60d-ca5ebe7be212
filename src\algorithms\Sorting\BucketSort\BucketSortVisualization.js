// BucketSortVisualization.js
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateBucketSortSteps } from './BucketSortAlgorithm';

// Import reusable visualization components
import { Bar, Bucket, ColorLegend, StepBoard, GroundPlane } from '../../../components/visualization';

// Constants for visualization
const BAR_WIDTH = 0.5;
const BAR_SPACING = 0.5;
const MAX_BAR_HEIGHT = 3.8;
const CAMERA_POSITION = [0, 5, 8];
const CAMERA_LOOKAT = [0, 0, -2];

// Main visualization component
const BucketSortVisualization = ({ params = {} }) => {
    const theme = useTheme();
    const { camera } = useThree();
    const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
    const { speed } = useSpeed();

    // Refs for animation control
    const speedRef = useRef(speed);
    const stepsRef = useRef([]);
    const initialArrayRef = useRef([]);
    const lastAppliedStepRef = useRef(-1);
    const animatingRef = useRef(false);
    const timeoutIdRef = useRef(null);
    const stateRef = useRef(state);
    const currentStepRef = useRef(step);

    // State for array data
    const [arrayData, setArrayData] = useState([]);
    const [buckets, setBuckets] = useState([]);
    const [sortedArray, setSortedArray] = useState([]);

    // State for visualization
    const [currentIndex, setCurrentIndex] = useState(-1);
    const [currentBucket, setCurrentBucket] = useState(-1);
    const [currentValue, setCurrentValue] = useState(null);
    const [compareIndices, setCompareIndices] = useState([]);
    const [sorted, setSorted] = useState([]);

    // Track which elements have been distributed to buckets
    const [distributedElements, setDistributedElements] = useState([]);

    // Animation state
    const [moveAnimation, setMoveAnimation] = useState({
        active: false,
        fromIndex: -1,
        toBucket: -1,
        fromBucket: -1,
        toSortedIndex: -1,
        progress: 0,
        startTime: 0,
        type: '' // 'toBucket', 'withinBucket', 'toSortedArray'
    });

    // Colors based on theme
    const colors = useMemo(() => {
        const isDark = theme.palette.mode === 'dark';
        return {
            bar: isDark ? '#42a5f5' : '#1976d2',
            current: isDark ? '#ab47bc' : '#7b1fa2',
            bucket: isDark ? '#ff9800' : '#f57c00',
            sorting: isDark ? '#4caf50' : '#388e3c',
            sorted: isDark ? '#66bb6a' : '#388e3c',
            base: isDark ? '#1a1a1a' : '#f5f5f5',
            ground: isDark ? '#121212' : '#eeeeee'
        };
    }, [theme.palette.mode]);

    // Update refs when state changes
    useEffect(() => {
        stateRef.current = state;
    }, [state]);

    useEffect(() => {
        currentStepRef.current = step;
    }, [step]);

    useEffect(() => {
        speedRef.current = speed;
    }, [speed]);

    // Initialize array when params change
    useEffect(() => {
        // Clear any existing animation timeouts
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state
        animatingRef.current = false;
        setMoveAnimation({
            active: false,
            fromIndex: -1,
            toBucket: -1,
            fromBucket: -1,
            toSortedIndex: -1,
            progress: 0,
            startTime: 0,
            type: ''
        });

        // Extract parameters with safety checks
        const { arraySize = 10, randomize = true, customArray = [], bucketCount = 5 } = params;

        // Generate array data
        let newArray = [];

        // Use custom array if provided
        if (customArray && customArray.length > 0) {
            newArray = [...customArray];
        } else if (randomize) {
            // Generate random array - for bucket sort, ensure all numbers are positive integers
            newArray = Array.from({ length: arraySize }, () =>
                Math.floor(Math.random() * 99) + 1
            );
        } else {
            // Generate reverse sorted array
            newArray = Array.from({ length: arraySize }, (_, i) => arraySize - i);
        }

        // Store the initial array
        initialArrayRef.current = [...newArray];
        setArrayData(newArray);

        // Generate steps
        const { steps } = generateBucketSortSteps(newArray, bucketCount);
        stepsRef.current = steps;
        setSteps(steps);
        setTotalSteps(steps.length);
        setAlgorithmSteps(steps.map(step => step.movement));

        // Reset visualization state
        setCurrentIndex(-1);
        setCurrentBucket(-1);
        setCurrentValue(null);
        setCompareIndices([]);
        setBuckets([]);
        setSortedArray([]);
        setSorted([]);
        setDistributedElements([]);

        // Reset step if needed
        setStep(0);
        lastAppliedStepRef.current = -1;

        // Position camera
        if (camera) {
            camera.position.set(...CAMERA_POSITION);
            camera.lookAt(...CAMERA_LOOKAT);
        }

        // Force a small delay to ensure everything is reset
        setTimeout(() => {
            // Apply the initial step
            if (steps.length > 0) {
                applyStep(0);
            }
        }, 50);
    }, [params, setStep, setTotalSteps, setSteps, setAlgorithmSteps, camera]);

    // Apply a single step of the bucket sort algorithm
    const applyStep = (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
            return;
        }

        const currentStep = stepsRef.current[stepIndex];
        lastAppliedStepRef.current = stepIndex;

        // Update array data if needed
        if (currentStep.array) {
            setArrayData(currentStep.array);
        }

        // Update buckets if needed
        if (currentStep.buckets) {
            setBuckets(currentStep.buckets);
        }

        // Update sorted array if needed
        if (currentStep.sortedArray) {
            setSortedArray(currentStep.sortedArray);
        }

        // Update visualization state based on step type
        switch (currentStep.type) {
            case 'init':
                setCurrentIndex(-1);
                setCurrentBucket(-1);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                setDistributedElements([]);
                break;

            case 'findMinMax':
            case 'createBuckets':
            case 'calculateRange':
                setCurrentIndex(-1);
                setCurrentBucket(-1);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'distribute':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentBucket(currentStep.bucketIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndices([]);
                setSorted([]);

                // Start animation to move item to bucket
                if (!animatingRef.current) {
                    animatingRef.current = true;
                    setMoveAnimation({
                        active: true,
                        fromIndex: currentStep.currentIndex,
                        toBucket: currentStep.bucketIndex,
                        fromBucket: -1,
                        toSortedIndex: -1,
                        progress: 0,
                        startTime: performance.now(),
                        type: 'toBucket'
                    });
                }
                break;

            case 'placed':
                setCurrentIndex(currentStep.currentIndex);
                setCurrentBucket(currentStep.bucketIndex);
                setCurrentValue(currentStep.currentValue);
                setCompareIndices([]);
                setSorted([]);

                // Add the element to distributed elements
                if (!distributedElements.includes(currentStep.currentIndex)) {
                    setDistributedElements(prev => [...prev, currentStep.currentIndex]);
                }
                break;

            case 'distributed':
                setCurrentIndex(-1);
                setCurrentBucket(-1);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'sortBucket':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'insertionStart':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(currentStep.currentValue);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'compare':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices(currentStep.compareIndices);
                setSorted([]);
                break;

            case 'shift':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'insert':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(currentStep.insertValue);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'bucketSorted':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'allBucketsSorted':
                setCurrentIndex(-1);
                setCurrentBucket(-1);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'concatenateBucket':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'concatenateElement':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(currentStep.currentValue);
                setCompareIndices([]);
                setSorted([]);

                // Start animation to move item from bucket to sorted array
                if (!animatingRef.current) {
                    animatingRef.current = true;
                    setMoveAnimation({
                        active: true,
                        fromIndex: -1,
                        toBucket: -1,
                        fromBucket: currentStep.currentBucket,
                        toSortedIndex: currentStep.outputIndex,
                        progress: 0,
                        startTime: performance.now(),
                        type: 'toSortedArray'
                    });
                }
                break;

            case 'elementAdded':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(currentStep.currentValue);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'bucketConcatenated':
                setCurrentIndex(-1);
                setCurrentBucket(currentStep.currentBucket);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted([]);
                break;

            case 'complete':
                setCurrentIndex(-1);
                setCurrentBucket(-1);
                setCurrentValue(null);
                setCompareIndices([]);
                setSorted(currentStep.sorted || []);
                // Reset distributed elements in the final step
                setDistributedElements([]);
                break;

            default:
                break;
        }
    };

    // Handle step changes
    useEffect(() => {
        // Clear any existing timeout
        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
        }

        // Reset animation state if step changes manually
        if (animatingRef.current && lastAppliedStepRef.current !== step - 1) {
            animatingRef.current = false;
            setMoveAnimation(prev => ({
                ...prev,
                active: false
            }));
        }

        // Don't apply steps if we're still animating and the step is sequential
        if (animatingRef.current && lastAppliedStepRef.current === step - 1) {
            return;
        }

        // Apply the current step
        applyStep(step);
    }, [step]);

    // Handle automatic stepping when state is 'running'
    useEffect(() => {
        // Reset animation state when state changes to idle
        if (state === 'idle') {
            // Clear any existing timeouts
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }

            // Reset animation state
            animatingRef.current = false;
            setMoveAnimation({
                active: false,
                fromIndex: -1,
                toBucket: -1,
                fromBucket: -1,
                toSortedIndex: -1,
                progress: 0,
                startTime: 0,
                type: ''
            });

            // Reset visualization state
            setCurrentIndex(-1);
            setCurrentBucket(-1);
            setCurrentValue(null);
            setCompareIndices([]);
            setSorted([]);
            setDistributedElements([]);

            // Apply the initial step
            if (stepsRef.current && stepsRef.current.length > 0) {
                applyStep(0);
            }

            return;
        }

        // Only proceed if state is 'running'
        if (state !== 'running') {
            return;
        }

        const steps = stepsRef.current || [];

        // Stop if we've reached the end
        if (step >= steps.length) {
            setState('completed');
            return;
        }

        // Don't schedule next step if we're animating
        if (animatingRef.current) {
            return;
        }

        // Schedule the next step with a delay
        const delay = Math.max(200, 800 - (speedRef.current * 80));
        const timeoutId = setTimeout(() => {
            // Only increment if still in running state
            if (stateRef.current === 'running' && !animatingRef.current) {
                setStep(prevStep => prevStep + 1);
            }
        }, delay);

        // Store the timeout ID for cleanup
        timeoutIdRef.current = timeoutId;

        // Cleanup function
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
                timeoutIdRef.current = null;
            }
        };
    }, [state, step, setStep, setState]);

    // Animation frame for move animation
    useFrame(() => {
        if (moveAnimation.active) {
            // Use performance.now() for more accurate timing
            const now = performance.now();
            const elapsed = now - moveAnimation.startTime;

            // Adjust duration based on speed - slower for better visibility
            const baseDuration = 1000; // Shorter base duration for faster animation
            const duration = Math.max(500, baseDuration - (speedRef.current * 100));

            const progress = Math.min(elapsed / duration, 1);

            // Only update if there's a meaningful change to reduce unnecessary renders
            if (Math.abs(progress - moveAnimation.progress) > 0.01) {
                setMoveAnimation(prev => ({
                    ...prev,
                    progress
                }));
            }

            // If animation is complete, mark it as inactive
            if (progress >= 1) {
                // Small delay to ensure the animation completes visually
                setTimeout(() => {
                    setMoveAnimation(prev => ({
                        ...prev,
                        active: false
                    }));
                    animatingRef.current = false;

                    // Add the element to distributed elements if moving to bucket
                    if (moveAnimation.type === 'toBucket' && moveAnimation.fromIndex >= 0) {
                        setDistributedElements(prev => {
                            if (!prev.includes(moveAnimation.fromIndex)) {
                                return [...prev, moveAnimation.fromIndex];
                            }
                            return prev;
                        });
                    }

                    // If we're in running state, schedule the next step
                    if (stateRef.current === 'running') {
                        setTimeout(() => {
                            if (stateRef.current === 'running') {
                                setStep(prevStep => prevStep + 1);
                            }
                        }, 50); // Shorter delay before next step
                    }
                }, 50); // Shorter delay for faster transitions
            }
        }
    });

    // Calculate maximum value for scaling
    const maxValue = useMemo(() => {
        if (!arrayData || arrayData.length === 0) return 1;
        return Math.max(...arrayData);
    }, [arrayData]);

    // Calculate adaptive width and spacing based on array size
    const adaptiveWidth = Math.max(0.2, BAR_WIDTH - (arrayData.length * 0.01));
    const adaptiveSpacing = Math.max(0.1, BAR_SPACING - (arrayData.length * 0.01));

    // Calculate total width of all bars
    const totalWidth = (adaptiveWidth + adaptiveSpacing) * arrayData.length;

    // Calculate starting X position to center the array
    const startX = -(totalWidth / 2) + (adaptiveWidth / 2);

    // Calculate bucket positions in a half-circle arrangement
    const bucketPositions = useMemo(() => {
        if (!buckets || buckets.length === 0) return [];

        const bucketCount = buckets.length;

        // Increase radius for better spacing and visibility
        const radius = Math.max(10, bucketCount * 2.0);

        // Wider arc angle for better distribution
        const arcAngle = Math.min(Math.PI * 0.9, Math.PI * (bucketCount > 5 ? 0.9 : 0.8));
        const angleStep = arcAngle / (bucketCount > 1 ? bucketCount - 1 : 1);

        return Array.from({ length: bucketCount }, (_, i) => {
            const startAngle = Math.PI / 2 - arcAngle / 2;
            const angle = startAngle + angleStep * i;

            // Calculate position with improved spacing
            const x = radius * Math.cos(angle);

            // Alternate bucket heights for better visibility with larger buckets
            const y = bucketCount > 3 ? -0.5 + (i % 2) * 1.0 : 0;

            // Deeper z position for better 3D effect
            const z = radius * Math.sin(angle) * -1;

            return [x, y, z];
        });
    }, [buckets]);

    // Get the current step data for the step board
    const currentStepData = useMemo(() => {
        if (step >= 0 && step < stepsRef.current?.length) {
            return stepsRef.current[step];
        }
        return null;
    }, [step]);

    // We've simplified the logic to directly check currentStepData?.type in the render conditions

    // Render the visualization
    return (
        <group position={[0, -2, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <directionalLight position={[-10, 10, -10]} intensity={0.4} />
            <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

            {/* Ground plane */}
            <GroundPlane
                position={[0, -0.3, 0]}
                width={100}
                depth={100}
                color={colors.ground}
                receiveShadow={true}
            />

            {/* Step Board */}
            <StepBoard
                position={[0, 6, 0.5]}
                description={currentStepData?.movement || ''}
                stepData={currentStepData || {}}
                currentStep={step + 1}
                totalSteps={stepsRef.current.length}
                showBackground={false}
            />

            {/* Color Legend */}
            <ColorLegend
                position={[0, -3, 0.5]}
                colors={colors}
                items={['Default', 'Current', 'Bucket', 'Sorting', 'Sorted']}
            />

            {/* Buckets */}
            {buckets.map((bucket, index) => {
                const position = bucketPositions[index] || [0, 0, 0];
                const isHighlighted = index === currentBucket;

                // Larger bucket sizes for better visibility
                const bucketSize = Math.max(2.5, Math.min(4.0, 2.5 + bucket.length * 0.15));
                const bucketHeight = Math.max(2.0, Math.min(3.5, 2.0 + bucket.length * 0.15));
                const bucketDepth = Math.max(1.5, Math.min(2.5, 1.5 + bucket.length * 0.1));

                return (
                    <Bucket
                        key={`bucket-${index}`}
                        position={position}
                        width={bucketSize}
                        height={bucketHeight}
                        depth={bucketDepth}
                        color={colors.bucket}
                        bucketIndex={index}
                        elements={bucket}
                        isHighlighted={isHighlighted}
                        showLabel={true}
                        showElements={true}
                        animateLid={true}
                    />
                );
            })}

            {/* Sorted Array - show whenever we have sorted elements */}
            {sortedArray.length > 0 && (
                <group position={[0, -1.5, 0]}>
                    {sortedArray.map((value, index) => {
                        const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;
                        const totalSortedWidth = (adaptiveWidth + adaptiveSpacing) * sortedArray.length;
                        const sortedStartX = -(totalSortedWidth / 2) + (adaptiveWidth / 2);
                        const x = sortedStartX + index * (adaptiveWidth + adaptiveSpacing);

                        // Skip rendering if this element is being animated from a bucket
                        if (moveAnimation.active &&
                            moveAnimation.type === 'toSortedArray' &&
                            moveAnimation.toSortedIndex === index) {
                            return null;
                        }

                        return (
                            <Bar
                                key={`sorted-${index}`}
                                position={[x, 0, 0]}
                                height={normalizedHeight}
                                width={adaptiveWidth}
                                color={colors.sorted}
                                value={value}
                                index={index}
                                showValue={arrayData.length <= 20}
                                showIndex={false}
                                showArrow={false}
                                depth={adaptiveWidth}
                            />
                        );
                    })}
                </group>
            )}

            {/* Original Array Bars - hide in final step */}
            {currentStepData?.type !== 'complete' && arrayData.map((value, index) => {
                // Skip rendering bars that have been moved to buckets
                const isBeingAnimated = moveAnimation.active &&
                                       moveAnimation.type === 'toBucket' &&
                                       index === moveAnimation.fromIndex;

                // Only show bars in the initial state or during animation
                const isInitialStep = currentStepData?.type === 'init';

                // Skip rendering if the element has been distributed and is not being animated
                // unless we're in the initial step
                if (distributedElements.includes(index) &&
                    !isBeingAnimated &&
                    !isInitialStep) {
                    return null;
                }

                // Calculate bar properties
                const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

                // Calculate position with animation
                let x = startX + index * (adaptiveWidth + adaptiveSpacing);
                let y = 0;
                let z = 0;

                // Apply animation for items being moved to buckets
                if (isBeingAnimated) {
                    const { progress } = moveAnimation;

                    // Use a smoother easing function for more natural movement
                    const easedProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);

                    // Calculate target position in bucket
                    const targetPosition = bucketPositions[moveAnimation.toBucket] || [0, 0, 0];
                    const [targetX, targetY, targetZ] = targetPosition;

                    // Calculate position along the path
                    x = x + (targetX - x) * easedProgress;
                    y = y + (targetY - y) * easedProgress;
                    z = z + (targetZ - z) * easedProgress;

                    // Add arc motion with higher peak for more dramatic effect
                    const arcHeight = 3.0; // Increased arc height
                    const midPoint = Math.sin(easedProgress * Math.PI);
                    y += arcHeight * midPoint; // Arc height with smoother curve
                }

                // Determine bar color based on current state
                let barColor = colors.bar; // Default color
                let showArrow = false; // Flag to show arrow indicator

                // Use a priority system for coloring and arrow indicators
                if (sorted.includes(index)) {
                    barColor = colors.sorted;
                } else if (isBeingAnimated) {
                    barColor = colors.bucket;
                    showArrow = true;
                } else if (index === currentIndex) {
                    barColor = colors.current;
                    showArrow = true;
                } else if (currentValue !== null && value === currentValue) {
                    barColor = colors.sorting;
                    showArrow = true;
                } else if (compareIndices.includes(index)) {
                    barColor = colors.current;
                }

                // Return the bar component
                return (
                    <Bar
                        key={`bar-${index}`}
                        position={[x, y, z]}
                        height={normalizedHeight}
                        width={adaptiveWidth}
                        color={barColor}
                        value={value}
                        index={index}
                        showValue={arrayData.length <= 20}
                        showIndex={true}
                        showArrow={showArrow}
                        depth={adaptiveWidth}
                    />
                );
            })}

            {/* Animated elements from buckets to sorted array */}
            {moveAnimation.active && moveAnimation.type === 'toSortedArray' && (
                (() => {
                    // Get the value being moved
                    const bucketIndex = moveAnimation.fromBucket;
                    // We don't need the bucket contents, just the position
                    const value = currentValue;

                    if (value === null) return null;

                    const normalizedHeight = (value / maxValue) * MAX_BAR_HEIGHT;

                    // Calculate source position (bucket)
                    const sourcePosition = bucketPositions[bucketIndex] || [0, 0, 0];

                    // Calculate target position (sorted array)
                    const totalSortedWidth = (adaptiveWidth + adaptiveSpacing) * sortedArray.length;
                    const sortedStartX = -(totalSortedWidth / 2) + (adaptiveWidth / 2);
                    const targetX = sortedStartX + moveAnimation.toSortedIndex * (adaptiveWidth + adaptiveSpacing);
                    const targetY = -1.5; // Sorted array y position
                    const targetZ = 0;

                    // Use a smoother easing function for more natural movement
                    const { progress } = moveAnimation;
                    const easedProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);

                    // Calculate current position
                    const x = sourcePosition[0] + (targetX - sourcePosition[0]) * easedProgress;
                    const y = sourcePosition[1] + (targetY - sourcePosition[1]) * easedProgress;
                    const z = sourcePosition[2] + (targetZ - sourcePosition[2]) * easedProgress;

                    // Add arc motion
                    const arcHeight = 3.0;
                    const midPoint = Math.sin(easedProgress * Math.PI);
                    const yOffset = arcHeight * midPoint;

                    return (
                        <Bar
                            key="animated-to-sorted"
                            position={[x, y + yOffset, z]}
                            height={normalizedHeight}
                            width={adaptiveWidth}
                            color={colors.sorting}
                            value={value}
                            index={moveAnimation.toSortedIndex}
                            showValue={arrayData.length <= 20}
                            showIndex={false}
                            showArrow={true}
                            depth={adaptiveWidth}
                        />
                    );
                })()
            )}
        </group>
    );
};

export default BucketSortVisualization;
