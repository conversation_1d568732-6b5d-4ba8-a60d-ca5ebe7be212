// LCSAlgorithm.js
// This file contains the core logic for the Longest Common Subsequence algorithm

/**
 * Generate steps for the Longest Common Subsequence algorithm
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {Object} - Object containing steps and result
 */
export const generateLCSSteps = (str1, str2) => {
  const steps = [];

  // Add initial step
  steps.push({
    type: 'initialize',
    str1,
    str2,
    dp: null,
    current: null,
    movement: 'Initialize LCS algorithm with strings: "' + str1 + '" and "' + str2 + '"'
  });

  // Create DP table
  const m = str1.length;
  const n = str2.length;
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // Add DP table creation step
  steps.push({
    type: 'create_table',
    str1,
    str2,
    dp: JSON.parse(JSON.stringify(dp)),
    current: null,
    movement: 'Create a table of size ' + (m + 1) + 'x' + (n + 1) + ' initialized with zeros'
  });

  // Fill the DP table
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      // Check if characters match
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;

        // Add step for character match
        steps.push({
          type: 'match',
          str1,
          str2,
          dp: JSON.parse(JSON.stringify(dp)),
          current: { i, j },
          match: true,
          movement: `Characters match: ${str1[i - 1]} at positions (${i}, ${j}). Set dp[${i}][${j}] = dp[${i-1}][${j-1}] + 1 = ${dp[i][j]}`
        });
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);

        // Add step for character mismatch
        steps.push({
          type: 'mismatch',
          str1,
          str2,
          dp: JSON.parse(JSON.stringify(dp)),
          current: { i, j },
          match: false,
          movement: `Characters don't match: ${str1[i - 1]} ≠ ${str2[j - 1]}. Set dp[${i}][${j}] = max(dp[${i-1}][${j}], dp[${i}][${j-1}]) = ${dp[i][j]}`
        });
      }
    }
  }

  // Trace back to find the LCS
  let i = m, j = n;
  let lcs = '';
  const path = [];

  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    str1,
    str2,
    dp: JSON.parse(JSON.stringify(dp)),
    current: { i, j },
    path: [...path],
    lcs,
    movement: 'Start tracing back from the bottom-right cell to find the LCS'
  });

  while (i > 0 && j > 0) {
    if (str1[i - 1] === str2[j - 1]) {
      lcs = str1[i - 1] + lcs;
      path.push({ i, j });
      i--;
      j--;

      // Add step for traceback match
      steps.push({
        type: 'traceback_match',
        str1,
        str2,
        dp: JSON.parse(JSON.stringify(dp)),
        current: { i, j },
        path: [...path],
        lcs,
        movement: `Found matching character "${str1[i - 1]}" in the LCS. Move diagonally to (${i}, ${j})`
      });
    } else if (dp[i - 1][j] > dp[i][j - 1]) {
      i--;

      // Add step for traceback up
      steps.push({
        type: 'traceback_up',
        str1,
        str2,
        dp: JSON.parse(JSON.stringify(dp)),
        current: { i, j },
        path: [...path],
        lcs,
        movement: `Move up to (${i}, ${j})`
      });
    } else {
      j--;

      // Add step for traceback left
      steps.push({
        type: 'traceback_left',
        str1,
        str2,
        dp: JSON.parse(JSON.stringify(dp)),
        current: { i, j },
        path: [...path],
        lcs,
        movement: `Move left to (${i}, ${j})`
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    str1,
    str2,
    dp: JSON.parse(JSON.stringify(dp)),
    current: null,
    path: [...path],
    lcs,
    movement: `LCS algorithm complete. The longest common subsequence is "${lcs}" with length ${lcs.length}`
  });

  return {
    steps,
    lcs,
    lcsLength: lcs.length
  };
};

// Export the algorithm function
export default {
  generateLCSSteps
};
