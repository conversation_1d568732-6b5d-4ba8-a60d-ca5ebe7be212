// MergeSortConfig.js - Centralized configuration for Merge Sort visualization

const MergeSortConfig = {
  // Bar dimensions
  bars: {
    width: 0.6,           // Width of each bar
    spacing: 0.5,         // Spacing between bars (increased from 0.3 to prevent label overlap)
    maxHeight: 3.2,       // Maximum height of bars
  },

  // Base/stage platform
  stage: {
    height: 0.2,          // Height (Y-axis) of the base platform

    // Platform length (X-axis)
    length: 10,           // Minimum length of the stage (will expand for larger arrays)
    lengthPadding: {
      left: 1,            // Padding on the left side
      right: 1,           // Padding on the right side
    },
    useFixedLength: false, // When true, always use the exact length specified above
    lengthMultiplier: 1.0, // Multiplier for the calculated length (smaller value = shorter stage)

    // Platform width (Z-axis)
    width: 4.0,           // Fixed width of the stage platform (Z-axis)

    color: null,          // Use theme colors if null, or specify a color
  },

  // Camera settings
  camera: {
    position: [0, 2, 10], // Camera position [x, y, z]
    lookAt: [0, -1, 0],   // Point camera looks at [x, y, z]
    fov: 50,              // Field of view
    near: 0.1,            // Near clipping plane
    far: 1000,            // Far clipping plane
  },

  // Group positioning
  group: {
    position: [0, -4, 0], // Position of the entire visualization group [x, y, z]
  },

  // Levitation animation
  levitation: {
    enabled: true,        // Enable/disable levitation effect
    baseY: -4,            // Base Y position for levitation (should match group.position[1])
    amplitude: 0.2,       // Amplitude of the levitation movement
    xAmplitude: 0.1,      // X-axis movement amplitude
    zAmplitude: 0.1,      // Z-axis movement amplitude
    xFrequency: 0.2,      // X-axis movement frequency
    yFrequency: 0.3,      // Y-axis movement frequency
    zFrequency: 0.15,     // Z-axis movement frequency
  },

  // Explanatory elements configuration
  explanatoryElements: {
    // Common configuration for all explanatory elements
    common: {
      yPosition: 6,           // Y position for all explanatory elements
      barScale: 0.3,          // Scale factor for bars in explanatory elements
      labelScale: 0.5,        // Scale factor for labels in explanatory elements
      backgroundColor: null,  // Background color (null = use theme)
      borderColor: null,      // Border color (null = use theme)
    },

    // Container configuration
    container: {
      padding: 1.0,           // Padding around the container
      spacing: 2.0,           // Spacing between elements in the container
    },

    // Left side configuration (temporary arrays)
    leftSide: {
      position: -5.0,         // X position of the left side container
      spacing: 2.0,           // Spacing between left and right subarrays

      // Left subarray configuration
      leftSubarray: {
        labelPosition: [-1, 2, 0], // Position of the left subarray label relative to container
        barsOffset: [0, 0, 0],         // Offset for the bars relative to the label
      },

      // Right subarray configuration
      rightSubarray: {
        labelPosition: [2.5, 2, 0],  // Position of the right subarray label relative to container

        barsOffset: [0, 0, 0],         // Offset for the bars relative to the label
      }
    },

    // Right side configuration (result array)
    rightSide: {
      position: 8.0,          // X position of the right side container

      // Result array configuration
      resultArray: {
        labelPosition: [-4, 2, 0],    // Position of the result array label relative to container
        barsOffset: [0, 0, 0],         // Offset for the bars relative to the label
      }
    }
  },

  // Animation timing
  animation: {
    duration: 500,            // Base animation duration in ms
    speedFactor: 1.0,         // Speed multiplier (higher = faster)
  },

  // Visual elements
  visual: {
    showValues: true,         // Show value labels on bars
    showIndices: true,        // Show index labels on bars
    showArrows: true,         // Show arrows for comparisons
    adaptiveLabels: true,     // Hide labels for small bars
    valueVisibilityThreshold: 0.0, // Hide values when scale factor is below this
    indexVisibilityThreshold: 0.0, // Hide indices when scale factor is below this
  },

  // Comparison arrow configuration
  comparisonArrow: {
    position: [0, -0.9, 0],   // Position of the comparison arrow relative to the bar
    fontSize: '1.4rem',         // Font size of the arrow
    scale: 1,               // Scale of the arrow
    rotation: 180,            // Rotation of the arrow in degrees (180 = pointing up)
    animation: {
      enabled: true,          // Enable/disable animation
      duration: '1s',         // Animation duration
      minScale: 1.0,          // Minimum scale during animation
      maxScale: 1.3,          // Maximum scale during animation
    },
  },

  // Color configuration for merge sort
  colors: {
    // Step-specific color assignments
    steps: {
      // For split steps, use the merging color
      split: 'merging',

      // For create_temp_arrays steps, use left/right subarray colors
      create_temp_arrays: {
        useSubarrayColors: true // Use left/right colors based on position relative to mid
      },

      // For merge steps, use the merging color
      merge: 'merging',

      // For compare steps, use the comparing color
      compare: 'comparing',

      // For place steps, use the subarray colors
      place_from_left_subarray: 'tempStructure.left',
      place_from_right_subarray: 'tempStructure.right',

      // For result array steps, use the result color
      create_result_array: 'tempStructure.result',
      result_array_complete: 'tempStructure.result',

      // For copy_back steps, use the merging color
      copy_back: 'merging',

      // Default color for other steps
      default: 'merging'
    },

    // Subarray color assignments
    subarrays: {
      // Left subarray color (blue)
      left: 'tempStructure.left',

      // Right subarray color (orange)
      right: 'tempStructure.right',

      // Result array color (green)
      result: 'tempStructure.result'
    }
  }
};

export default MergeSortConfig;
