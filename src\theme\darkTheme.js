import { createTheme } from "@mui/material/styles";

// Revised dark color scheme for a more distinct dark mode feel
export const darkTheme = createTheme({
  palette: {
    mode: "dark",
    primary: {
      // Using a brighter, more vivid light blue (Light Blue 300)
      main: "#4fc3f7",
      light: "#81d4fa", // Light Blue 200
      dark: "#29b6f6", // Light Blue 400
    },
    secondary: {
      // Using a distinct and reasonably bright pink (Pink 300)
      main: "#f06292",
      light: "#f48fb1", // Pink 200
      dark: "#ec407a", // Pink 400
    },
    error: {
      // Standard Material dark error colors (Red 200-400 range) - kept bright
      main: "#ef9a9a",
      light: "#ffcdd2",
      dark: "#e57373",
    },
    warning: {
      // Standard Material dark warning colors (Orange 300-500 range) - kept bright
      main: "#ffb74d",
      light: "#ffcc80",
      dark: "#ffa726",
    },
    info: {
      // Aligning info with the primary blue shades for consistency
      main: "#4fc3f7",
      light: "#81d4fa",
      dark: "#29b6f6",
    },
    success: {
      // Standard Material dark success colors (Green 200-400 range) - kept bright
      main: "#81c784", // Green 300
      light: "#a5d6a7", // Green 200
      dark: "#66bb6a", // Green 400
    },
    background: {
      // Standard dark theme background colors for good contrast
      default: "#121212", // Very dark grey, almost black
      paper: "#1e1e1e", // Slightly lighter grey for surfaces
    },
    text: {
      // Pure white for maximum contrast against dark backgrounds
      primary: "#ffffff",
      secondary: "rgba(255, 255, 255, 0.7)",
      disabled: "rgba(255, 255, 255, 0.5)",
    },
    action: {
      // Action colors based on pure white
      active: "rgba(255, 255, 255, 0.54)",
      hover: "rgba(255, 255, 255, 0.08)", // Subtle hover
      selected: "rgba(255, 255, 255, 0.16)", // More prominent selection
      disabled: "rgba(255, 255, 255, 0.3)",
      disabledBackground: "rgba(255, 255, 255, 0.12)",
    },
    grey: {
      // Material Design standard grey scale (inverted brightness for dark mode intuition)
      50: "#fafafa",
      100: "#f5f5f5",
      200: "#eeeeee",
      300: "#e0e0e0",
      400: "#bdbdbd",
      500: "#9e9e9e",
      600: "#757575",
      700: "#616161",
      800: "#424242", // Often used for subtle borders/backgrounds in dark mode
      900: "#212121", // Closer to paper background
      A100: "#ffffff",
      A200: "#eeeeee",
      A400: "#bdbdbd",
      A700: "#616161",
    },
    divider: "rgba(255, 255, 255, 0.12)", // Divider based on white text
  },
  // --- NO CHANGES BELOW THIS LINE ---
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: "2.5rem",
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: "2rem",
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: "1.75rem",
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: "1.5rem",
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h5: {
      fontSize: "1.25rem",
      fontWeight: 500,
      lineHeight: 1.6,
    },
    h6: {
      fontSize: "1rem",
      fontWeight: 500,
      lineHeight: 1.7,
    },
    subtitle1: {
      fontSize: "1rem",
      fontWeight: 400,
      lineHeight: 1.75,
    },
    subtitle2: {
      fontSize: "0.875rem",
      fontWeight: 500,
      lineHeight: 1.57,
    },
    body1: {
      fontSize: "1rem",
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: "0.875rem",
      fontWeight: 400,
      lineHeight: 1.43,
    },
  },
  shape: {
    borderRadius: 4,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          borderRadius: 4,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
      },
    },
  },
});
