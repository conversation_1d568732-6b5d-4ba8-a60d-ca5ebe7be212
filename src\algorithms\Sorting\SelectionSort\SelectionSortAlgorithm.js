// SelectionSortAlgorithm.js
// Implementation of the Selection Sort algorithm with step generation
// Updated to use detailed steps for better visualization

import { generateSelectionSortDetailedSteps } from './SelectionSortDetailedSteps';

/**
 * Generates steps for the Selection Sort algorithm
 * @param {Array} arr - The array to sort
 * @returns {Object} - Object containing steps and the sorted array
 */
export const generateSelectionSortSteps = (arr) => {
  // Use the detailed steps generator for comprehensive visualization
  const detailedSteps = generateSelectionSortDetailedSteps(arr);

  // Convert detailed steps to the format expected by the old system
  const steps = detailedSteps.map(step => ({
    type: step.type,
    array: step.visualizationData?.mainArray?.values || [...arr],
    comparing: step.visualizationData?.mainArray?.comparingIndices || [],
    swapping: step.visualizationData?.mainArray?.swappingIndices || [],
    sorted: step.visualizationData?.mainArray?.sortedIndices || [],
    current: step.visualizationData?.mainArray?.currentIndex,
    minIndex: step.visualizationData?.mainArray?.minIndex,
    movement: step.statement || ''
  }));

  // Get the final sorted array from the last step
  const finalStep = detailedSteps[detailedSteps.length - 1];
  const sortedArray = finalStep?.visualizationData?.mainArray?.values || [...arr].sort((a, b) => a - b);

  return { steps, sortedArray };
};

// Legacy function for backward compatibility
export const generateLegacySelectionSortSteps = (arr) => {
  // Create a copy of the array to avoid modifying the original
  arr = [...arr];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'init',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: [], // Start with no elements sorted
    current: null,
    movement: 'Initialize Selection Sort'
  });

  // Selection sort algorithm
  const n = arr.length;

  for (let i = 0; i < n - 1; i++) {
    // Add step to mark the current position
    steps.push({
      type: 'select',
      array: [...arr],
      comparing: [],
      swapping: [],
      sorted: Array.from({ length: i }, (_, idx) => idx), // Elements before i are sorted
      current: i,
      movement: `Start iteration ${i + 1}: Find minimum element from index ${i} to ${n - 1}`
    });

    let minIndex = i;

    // Find the minimum element in the unsorted part of the array
    for (let j = i + 1; j < n; j++) {
      // Add comparison step
      steps.push({
        type: 'compare',
        array: [...arr],
        comparing: [minIndex, j],
        swapping: [],
        sorted: Array.from({ length: i }, (_, idx) => idx),
        current: i,
        movement: `Compare minimum (${arr[minIndex]}) with element at index ${j} (${arr[j]})`
      });

      // If current element is smaller than the minimum, update minIndex
      if (arr[j] < arr[minIndex]) {
        // Add step to update minimum
        steps.push({
          type: 'newMin',
          array: [...arr],
          comparing: [],
          minIndex: j,
          swapping: [],
          sorted: Array.from({ length: i }, (_, idx) => idx),
          current: i,
          movement: `Found new minimum ${arr[j]} at index ${j}`
        });

        minIndex = j;
      }
    }

    // Swap the minimum element with the first element of the unsorted part
    if (minIndex !== i) {
      // Add step before swapping
      steps.push({
        type: 'beforeSwap',
        array: [...arr],
        comparing: [],
        swapping: [i, minIndex],
        sorted: Array.from({ length: i }, (_, idx) => idx),
        current: i,
        movement: `Swap element at index ${i} (${arr[i]}) with minimum at index ${minIndex} (${arr[minIndex]})`
      });

      // Perform the swap
      [arr[i], arr[minIndex]] = [arr[minIndex], arr[i]];

      // Add step after swapping
      steps.push({
        type: 'afterSwap',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: Array.from({ length: i + 1 }, (_, idx) => idx), // Mark current position as sorted
        current: i,
        movement: `Swapped elements: placed ${arr[i]} at index ${i} and ${arr[minIndex]} at index ${minIndex}`
      });
    } else {
      // If no swap needed, still mark the element as sorted
      steps.push({
        type: 'noSwap',
        array: [...arr],
        comparing: [],
        swapping: [],
        sorted: Array.from({ length: i + 1 }, (_, idx) => idx), // Mark current position as sorted
        current: i,
        movement: `Element ${arr[i]} at index ${i} is already in the correct position`
      });
    }
  }

  // Mark the last element as sorted
  steps.push({
    type: 'complete',
    array: [...arr],
    comparing: [],
    swapping: [],
    sorted: Array.from({ length: n }, (_, idx) => idx), // All elements are sorted
    current: null,
    movement: 'Selection Sort complete'
  });

  return { steps, sortedArray: arr };
};

// Default export
const SelectionSortAlgorithm = {
  generateSteps: generateSelectionSortSteps
};

export default SelectionSortAlgorithm;
