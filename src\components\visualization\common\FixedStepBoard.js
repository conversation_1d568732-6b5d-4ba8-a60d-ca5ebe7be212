import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';

/**
 * Fixed step board component that stays at the top of the screen regardless of camera rotation
 *
 * @param {Object} props - Component props
 * @param {number} props.currentStep - Current step number
 * @param {number} props.totalSteps - Total number of steps
 * @param {string} props.description - Description of the current step
 * @param {Object} props.stepData - Additional step data
 * @param {Object} props.theme - MUI theme object
 * @param {boolean} props.showStepNumber - Whether to show the step number (default: true)
 * @param {Object} props.config - Configuration options for the step board
 * @param {string} props.config.minWidth - Minimum width of the step board (default: '600px')
 * @param {string} props.config.maxWidth - Maximum width of the step board (default: '80%')
 * @param {string} props.config.fontSize - Font size for the description (default: '17px')
 * @param {string} props.config.fontFamily - Font family for the description (default: engaging font stack)
 * @param {string} props.config.lineHeight - Line height for the description (default: '1.6')
 * @param {string} props.config.letterSpacing - Letter spacing for the description (default: '0.01em')
 * @param {string} props.config.fontWeight - Font weight for the description (default: 'bold')
 * @param {string} props.config.textAlign - Text alignment for the description (default: 'center')
 * @param {string} props.config.padding - Padding for the container (default: '8px 12px')
 * @param {string} props.config.top - Top position of the container (default: '10px')
 * @param {string} props.config.stepNumberFormat - Format for the step number (default: 'Step {current} of {total}: ')
 */
const FixedStepBoard = ({
  currentStep = 0,
  totalSteps = 0,
  description = '',
  stepData = {},
  theme,
  showStepNumber = true,
  config = {}
}) => {
  // Always call useTheme hook first to follow React rules
  const themeFromContext = useTheme();
  // Then use provided theme or the one from context
  const muiTheme = theme || themeFromContext;
  const isDark = muiTheme?.palette?.mode === 'dark';

  // Default configuration values - MODIFY THESE VALUES TO CHANGE THE DEFAULT APPEARANCE
  const defaultConfig = {
    // Size and positioning
    padding: '8px 12px',    // Padding inside the container (top/bottom left/right)
    top: '15px',            // Distance from the top of the screen
    zIndex: 1000,           // Z-index for stacking order

    // Typography
    fontFamily: '"Poppins", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif', // Font stack
    fontWeight: '500',      // Font weight (normal, bold, 600, etc.)

    // Step number format
    stepNumberFormat: 'Step {current}: ', // Format for the step number

    // Visual effects
    backgroundColor: 'rgba(0, 0, 0, 0.2)', // Semi-transparent background for better readability
    borderRadius: '8px',    // Rounded corners for better appearance
  };

  // Merge default config with provided config
  const mergedConfig = { ...defaultConfig, ...config };

  // Format the step number if showStepNumber is true
  const formattedDescription = () => {
    let desc = description || 'No description available for this step.';

    // Break long descriptions at logical points for better readability
    // But don't break inside array brackets to avoid splitting arrays across lines
    if (desc.length > 80 && !desc.includes('[')) {
      // Add line breaks at logical points like commas and periods followed by spaces
      // But only if not inside square brackets
      desc = desc.replace(/([,.]) (?![^\[]*\])/g, '$1\n');
    }

    if (!showStepNumber) {
      return desc;
    }

    // Replace placeholders in the step number format
    // The component should display exactly what it's given without manipulation
    // Parent components are responsible for providing the correct step number format
    const stepNumberText = mergedConfig.stepNumberFormat
      .replace('{current}', currentStep) // Use the exact step number provided by the parent
      .replace('{total}', totalSteps);

    return `${stepNumberText}${desc}`;
  };

  return (
    <Html
      prepend // This ensures the HTML is rendered in front of the scene
      fullscreen // This makes it cover the entire canvas
      portal={null} // This ensures it's rendered directly in the canvas
      style={{ pointerEvents: 'none' }} // This ensures it doesn't block interactions with the scene
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: mergedConfig.padding,
          backgroundColor: mergedConfig.backgroundColor,
          width: '90%', // Fixed width percentage of viewport
          maxWidth: '1200px', // Maximum width to prevent stretching on large screens
          margin: '0 auto',
          userSelect: 'none',
          pointerEvents: 'none',
          position: 'absolute',
          top: mergedConfig.top,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: mergedConfig.zIndex,
          overflow: 'hidden', // Prevent content from overflowing
          ...(mergedConfig.borderColor && {
            border: `${mergedConfig.borderWidth || '1px'} solid ${mergedConfig.borderColor}`,
          }),
          ...(mergedConfig.borderRadius && {
            borderRadius: mergedConfig.borderRadius,
          }),
        }}
      >
        <Box sx={{ maxHeight: '80vh', overflowY: 'auto', width: '100%', padding: '0 5px' }}>
          <Typography
            variant="body1"
            sx={{
              margin: 0,
              fontSize: { xs: '14px', sm: '15px', md: '16px' },
              fontFamily: mergedConfig.fontFamily,
              fontWeight: mergedConfig.fontWeight,
              lineHeight: 1.4,
              letterSpacing: '0.01em',
              color: mergedConfig.textColor || (isDark ? '#ffffff' : '#000000'),
              textAlign: 'center',
              width: '100%',
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'pre-wrap', // Preserve whitespace but allow wrapping
              padding: '5px',
              textShadow: isDark
                ? '0 0 3px rgba(0,0,0,0.7)'
                : '0 0 3px rgba(255,255,255,0.7)',
            }}
          >
            {formattedDescription()}
          </Typography>
        </Box>
      </Box>
    </Html>
  );
};

export default FixedStepBoard;
