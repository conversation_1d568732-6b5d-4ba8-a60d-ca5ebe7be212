// BucketSortController.js
// This component provides the controls for the Bucket Sort algorithm.

import React, { useEffect, useState } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography, Slider } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import GridViewIcon from '@mui/icons-material/GridView';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

const BucketSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks
    const arraySize = params?.arraySize || 10;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // Calculate default bucket count based on array size
    // Use the square root of array size as a reasonable default
    const defaultBucketCount = Math.max(2, Math.min(10, Math.ceil(Math.sqrt(arraySize))));
    const bucketCount = params?.bucketCount || defaultBucketCount;

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            // Calculate new bucket count based on array size
            const newBucketCount = Math.max(2, Math.min(10, Math.ceil(Math.sqrt(newSize))));

            // Only update bucket count if it hasn't been manually set
            // or if it's outside the valid range for the new array size
            const currentBucketCount = params?.bucketCount || 5;
            const shouldUpdateBucketCount =
                !params.hasOwnProperty('bucketCount') ||
                currentBucketCount < 2 ||
                currentBucketCount > 10;

            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: [],
                    // Update bucket count if needed
                    ...(shouldUpdateBucketCount ? { bucketCount: newBucketCount } : {})
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle bucket count change
    const handleBucketCountChange = (newCount) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                bucketCount: newCount
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));

                // Calculate appropriate bucket count for the default array
                const newBucketCount = Math.max(2, Math.min(10, Math.ceil(Math.sqrt(defaultArray.length))));

                // Only update bucket count if it hasn't been manually set
                const currentBucketCount = params?.bucketCount || 5;
                const shouldUpdateBucketCount =
                    !params.hasOwnProperty('bucketCount') ||
                    currentBucketCount < 2 ||
                    currentBucketCount > 10;

                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false,
                        // Update bucket count if needed
                        ...(shouldUpdateBucketCount ? { bucketCount: newBucketCount } : {})
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));

                // Calculate appropriate bucket count for the default array
                const newBucketCount = Math.max(2, Math.min(10, Math.ceil(Math.sqrt(defaultArray.length))));

                // Only update bucket count if it hasn't been manually set
                const currentBucketCount = params?.bucketCount || 5;
                const shouldUpdateBucketCount =
                    !params.hasOwnProperty('bucketCount') ||
                    currentBucketCount < 2 ||
                    currentBucketCount > 10;

                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false,
                        // Update bucket count if needed
                        ...(shouldUpdateBucketCount ? { bucketCount: newBucketCount } : {})
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    if (num < 0) {
                        throw new Error(`Bucket Sort requires positive numbers: ${item}`);
                    }
                    if (num > 99) {
                        throw new Error(`For better visualization, please use numbers between 0-99: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < 3) {
                setCustomArrayError('Please provide at least 3 numbers');
                return;
            }

            if (parsedArray.length > 20) {
                setCustomArrayError('Please provide at most 20 numbers');
                return;
            }

            // Calculate appropriate bucket count for the custom array
            const newBucketCount = Math.max(2, Math.min(10, Math.ceil(Math.sqrt(parsedArray.length))));

            // Only update bucket count if it hasn't been manually set
            // or if it's outside the valid range for the custom array size
            const currentBucketCount = params?.bucketCount || 5;
            const shouldUpdateBucketCount =
                !params.hasOwnProperty('bucketCount') ||
                currentBucketCount < 2 ||
                currentBucketCount > 10;

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false,
                    // Update bucket count if needed
                    ...(shouldUpdateBucketCount ? { bucketCount: newBucketCount } : {})
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box sx={{ p: 1, height: '100%', overflowY: 'auto' }}>
            {/* Algorithm Title */}
            <Typography variant="h5" gutterBottom>
                Bucket Sort
            </Typography>

            {/* Information Section */}
            <InformationSection defaultExpanded={false}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Bucket Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Bucket Sort is a distribution sort that works by distributing the elements into a number of buckets, then sorting each bucket individually (usually with another sorting algorithm), and finally concatenating the sorted buckets.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n+k) where n is the number of elements and k is the number of buckets
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n+k)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n²) when all elements are placed in a single bucket
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(n+k) where n is the number of elements and k is the number of buckets
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: 3,
                        max: 20,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'bucketCount',
                        type: 'slider',
                        label: `Number of Buckets (Recommended: ${defaultBucketCount})`,
                        min: 2,
                        max: 10,
                        step: 1,
                        defaultValue: bucketCount,
                        icon: GridViewIcon
                    },

                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: "Enter comma-separated numbers between 0-99 (e.g., 5, 3, 8, 1). Maximum 20 numbers allowed.",
                        placeholder: "e.g., 5, 3, 8, 4, 2 (min 3, max 20 numbers)",
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    bucketCount,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.bucketCount !== undefined && newValues.bucketCount !== bucketCount) {
                        handleBucketCountChange(newValues.bucketCount);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.movement || ''
                }))}
                currentStep={step}
                defaultExpanded
                renderStep={(_, index) => {
                    const currentStep = steps && steps[index];
                    const isCurrentStep = index === step - 1;

                    if (!currentStep) return null;

                    return (
                        <Typography
                            variant="body2"
                            component="div"
                            sx={{
                                fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
                                fontSize: '0.85rem',
                                fontWeight: isCurrentStep ? 'bold' : 'normal',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                mb: 0.75,
                                pb: 0.75,
                                borderBottom: index < steps.length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none',
                                color: isCurrentStep ? 'text.primary' : 'text.secondary',
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                    bgcolor: 'action.hover',
                                    borderRadius: '4px',
                                }
                            }}
                        >
                            <Box
                                component="span"
                                sx={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: '24px',
                                    height: '24px',
                                    borderRadius: '12px',
                                    bgcolor: isCurrentStep ? 'success.main' : 'rgba(0, 0, 0, 0.1)',
                                    color: isCurrentStep ? 'success.contrastText' : 'text.secondary',
                                    mr: 1.5,
                                    fontSize: '0.75rem',
                                    fontWeight: 'bold',
                                    flexShrink: 0,
                                    boxShadow: isCurrentStep ? '0 0 0 2px rgba(76, 175, 80, 0.2)' : 'none',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {index + 1}
                            </Box>
                            {currentStep.movement}
                        </Typography>
                    );
                }}
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Bucket Sort Algorithm"
                defaultExpanded
                currentStep={step > 0 && steps && steps.length > 0 ?
                    // Map the current step to the corresponding line number
                    steps[step - 1]?.type === 'init' ? 0 :
                        steps[step - 1]?.type === 'findMinMax' ? 1 :
                            steps[step - 1]?.type === 'createBuckets' ? 2 :
                                steps[step - 1]?.type === 'calculateRange' ? 3 :
                                    steps[step - 1]?.type === 'distribute' || steps[step - 1]?.type === 'placed' ? 4 :
                                        steps[step - 1]?.type === 'distributed' ? 5 :
                                            steps[step - 1]?.type === 'sortBucket' || steps[step - 1]?.type === 'insertionStart' ||
                                            steps[step - 1]?.type === 'compare' || steps[step - 1]?.type === 'shift' ||
                                            steps[step - 1]?.type === 'insert' || steps[step - 1]?.type === 'bucketSorted' ? 6 :
                                                steps[step - 1]?.type === 'allBucketsSorted' ? 7 :
                                                    steps[step - 1]?.type === 'concatenateBucket' || steps[step - 1]?.type === 'concatenateElement' ||
                                                    steps[step - 1]?.type === 'elementAdded' || steps[step - 1]?.type === 'bucketConcatenated' ? 8 :
                                                        steps[step - 1]?.type === 'complete' ? 9 : 0
                    : 0
                }
                algorithm={[
                    { code: "function bucketSort(arr, bucketCount):", lineNumber: 0, indent: 0 },
                    { code: "min = findMinimum(arr), max = findMaximum(arr)", lineNumber: 1, indent: 1 },
                    { code: "buckets = new Array(bucketCount).fill([])", lineNumber: 2, indent: 1 },
                    { code: "range = (max - min) / bucketCount + 1", lineNumber: 3, indent: 1 },
                    { code: "for i = 0 to length(arr) - 1:", lineNumber: 4, indent: 1 },
                    { code: "    bucketIndex = Math.floor((arr[i] - min) / range)", lineNumber: 5, indent: 2 },
                    { code: "    buckets[bucketIndex].push(arr[i])", lineNumber: 6, indent: 2 },
                    { code: "for i = 0 to bucketCount - 1:", lineNumber: 7, indent: 1 },
                    { code: "    sort(buckets[i]) // Using insertion sort", lineNumber: 8, indent: 2 },
                    { code: "result = concatenate all buckets", lineNumber: 9, indent: 1 },
                    { code: "return result", lineNumber: 10, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default BucketSortController;
