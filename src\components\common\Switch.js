// Switch.js
// A reusable switch component with consistent styling

import React from 'react';
import { FormControlLabel, Switch as MuiSwitch } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable switch component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {string|node} props.label - Switch label (can be a string or a React node)
 * @param {boolean} props.checked - Whether the switch is checked
 * @param {function} props.onChange - Change handler
 * @param {string} props.color - Switch color (primary, secondary, error, etc.)
 * @param {string} props.size - Switch size (small, medium)
 * @param {boolean} props.disabled - Whether the switch is disabled
 * @param {Object} props.sx - Additional styles
 */
const Switch = ({
  label,
  checked,
  onChange,
  color = 'primary',
  size = 'medium',
  disabled = false,
  sx = {},
  ...rest
}) => {
  const handleChange = (event) => {
    onChange(event.target.checked);
  };

  return (
    <FormControlLabel
      control={
        <MuiSwitch
          checked={checked}
          onChange={handleChange}
          color={color}
          size={size}
          disabled={disabled}
          {...rest}
        />
      }
      label={label}
      sx={{
        ...sx
      }}
    />
  );
};

Switch.propTypes = {
  label: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node
  ]).isRequired,
  checked: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  color: PropTypes.string,
  size: PropTypes.oneOf(['small', 'medium']),
  disabled: PropTypes.bool,
  sx: PropTypes.object
};

export default Switch;
