// syntaxHighlighting.js
// Utility for theme-aware syntax highlighting colors

/**
 * Get theme-aware syntax highlighting colors
 * @param {Object} theme - MUI theme object
 * @returns {Object} - Object with color values for syntax highlighting
 */
export const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';

  return {
    // Syntax highlighting colors
    keyword: isDark ? "#569cd6" : "#0000ff", // Blue for keywords (function, if, else)
    function: isDark ? "#dcdcaa" : "#795e26", // Yellow/brown for function name
    parameter: isDark ? "#9cdcfe" : "#001080", // Blue for parameters/variables
    comment: isDark ? "#6a9955" : "#008000", // Green for comments/actions
    operator: isDark ? "#d4d4d4" : "#000000", // Default/operator color
    number: isDark ? "#b5cea8" : "#098658", // Green for numbers
    string: isDark ? "#ce9178" : "#a31515", // Red for strings
    variable: isDark ? "#9cdcfe" : "#001080", // Blue for variables
    property: isDark ? "#4fc1ff" : "#0070c1", // Light blue for properties
    punctuation: isDark ? "#d4d4d4" : "#000000", // Default for punctuation

    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    lineHighlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    highlightedText: isDark ? "#ffffff" : "#000000",
    border: theme.palette.divider,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    activeHighlight: isDark ? "rgba(14, 99, 156, 0.8)" : "rgba(0, 120, 215, 0.4)",
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

export default { getSyntaxColors };
