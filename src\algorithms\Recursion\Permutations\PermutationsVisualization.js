// PermutationsVisualization.js
// 3D visualization component for the Permutations algorithm

import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { Html } from '@react-three/drei';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import * as THREE from 'three';
import { FixedColorLegend, FixedStepBoard } from '../../../components/visualization';

// Constants for visualization
const ELEMENT_SIZE = 0.8;
const ELEMENT_SPACING = 1.5;
const LEVEL_HEIGHT = 2.5;
const ORBIT_RADIUS = 6;

const PermutationsVisualization = ({
  algorithm,
  params,
  state,
  setState,
  step,
  setStep,
  totalSteps,
  theme,
  steps,
  setMovements,
}) => {
  // Get Three.js objects
  const { camera } = useThree();

  // Get theme-aware colors
  const muiTheme = theme; // Store the theme for use in fixed components
  const isDark = theme?.palette?.mode === 'dark';

  // State for current step data
  const [currentStepData, setCurrentStepData] = useState(null);

  // Refs for animation
  const stepsRef = useRef([]);
  const lastAppliedStepRef = useRef(-1);
  const stateRef = useRef(state);
  const speedRef = useRef(1);
  const lastUpdateTimeRef = useRef(0);
  const rotationRef = useRef(0);

  // Set up camera position for better 3D view
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 15, 15);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Update refs when props change
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Get speed from context
  const { speed } = useSpeed();

  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Update steps ref when steps change
  useEffect(() => {
    if (steps && steps.length > 0) {
      stepsRef.current = steps;
    }
  }, [steps]);

  // Update current step data when step changes
  useEffect(() => {
    if (step > 0 && step <= stepsRef.current.length) {
      setCurrentStepData(stepsRef.current[step - 1]);

      // Update movements
      if (setMovements && stepsRef.current[step - 1]) {
        setMovements([stepsRef.current[step - 1].message]);
      }
    } else if (stepsRef.current.length > 0) {
      // If step is 0 but we have steps, use the first step data to show initial state
      setCurrentStepData(stepsRef.current[0]);
    } else {
      // Create a default empty data to show when no steps are available
      setCurrentStepData({
        elements: ['A', 'B', 'C'],
        currentPermutation: [],
        permutations: [],
        currentIndex: -1,
        depth: 0
      });
    }

    lastAppliedStepRef.current = step;
  }, [step, setMovements]);

  // Auto-advance steps based on state and speed
  useFrame(({ clock }) => {
    // Only proceed if we're in running state and have more steps
    if (stateRef.current === 'running' && lastAppliedStepRef.current < stepsRef.current.length) {
      // Calculate time to wait based on speed (in seconds)
      const timeToWait = 1 / speedRef.current;

      // Get current time
      const currentTime = clock.getElapsedTime();

      // Check if enough time has passed since the last update
      if (currentTime - lastUpdateTimeRef.current >= timeToWait) {
        // Update the step
        setStep(lastAppliedStepRef.current + 1);

        // Update the last update time
        lastUpdateTimeRef.current = currentTime;
      }
    }

    // Rotate the visualization slowly
    rotationRef.current += 0.001;
  });

  // Define colors based on theme
  const colors = useMemo(() => ({
    // Element colors
    element: isDark ? '#2d3436' : '#636e72',
    elementText: isDark ? '#ffffff' : '#ffffff',
    currentElement: isDark ? '#e84118' : '#ff7675',
    usedElement: isDark ? '#0984e3' : '#74b9ff',

    // Permutation colors
    permutation: isDark ? '#00b894' : '#55efc4',
    permutationText: isDark ? '#2d3436' : '#2d3436',
    currentPermutation: isDark ? '#fdcb6e' : '#ffeaa7',

    // Connection colors
    connection: isDark ? '#b2bec3' : '#dfe6e9',

    // Background colors
    background: isDark ? '#1e272e' : '#f5f6fa',

    // Text colors
    textDark: isDark ? '#ffffff' : '#2d3436',
    textLight: isDark ? '#dfe6e9' : '#636e72',
  }), [isDark]);

  // Generate color legend items
  const legendItems = useMemo(() => [
    { color: colors.element, label: 'Element' },
    { color: colors.currentElement, label: 'Current Element' },
    { color: colors.usedElement, label: 'Used Element' },
    { color: colors.permutation, label: 'Permutation' },
    { color: colors.currentPermutation, label: 'Current Permutation' },
  ], [colors]);

  // Use fixed position and rotation for stability
  const position = [0, 0, 0];
  const rotation = [0, rotationRef.current, 0];

  // Create an element mesh
  const Element = ({ position, text, color, isHighlighted }) => {
    return (
      <group position={position}>
        {/* Element cube */}
        <mesh castShadow>
          <boxGeometry args={[ELEMENT_SIZE, ELEMENT_SIZE, ELEMENT_SIZE]} />
          <meshStandardMaterial color={color} />
        </mesh>

        {/* Element text */}
        <Html
          position={[0, 0, ELEMENT_SIZE/2 + 0.01]}
          center
          occlude
        >
          <div style={{
            color: colors.elementText,
            fontSize: `${ELEMENT_SIZE * 20}px`,
            fontWeight: 'bold',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            userSelect: 'none',
            pointerEvents: 'none'
          }}>
            {text}
          </div>
        </Html>

        {/* Highlight effect */}
        {isHighlighted && (
          <mesh>
            <boxGeometry args={[ELEMENT_SIZE + 0.1, ELEMENT_SIZE + 0.1, ELEMENT_SIZE + 0.1]} />
            <meshStandardMaterial color={colors.currentElement} wireframe={true} />
          </mesh>
        )}
      </group>
    );
  };

  // Create a permutation mesh
  const Permutation = ({ position, elements, isCurrent }) => {
    return (
      <group position={position}>
        {/* Permutation background */}
        <mesh castShadow>
          <boxGeometry args={[elements.length * ELEMENT_SPACING * 0.8, ELEMENT_SIZE, ELEMENT_SIZE]} />
          <meshStandardMaterial color={isCurrent ? colors.currentPermutation : colors.permutation} />
        </mesh>

        {/* Permutation elements */}
        {elements.map((element, index) => {
          const offset = (index - (elements.length - 1) / 2) * ELEMENT_SPACING * 0.7;
          return (
            <Html
              key={index}
              position={[offset, 0, ELEMENT_SIZE/2 + 0.01]}
              center
              occlude
            >
              <div style={{
                color: colors.permutationText,
                fontSize: `${ELEMENT_SIZE * 16}px`,
                fontWeight: 'bold',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                pointerEvents: 'none'
              }}>
                {element}
              </div>
            </Html>
          );
        })}
      </group>
    );
  };

  // Render the Permutations visualization
  const renderPermutationsVisualization = () => {
    if (!currentStepData) return null;

    const { elements, currentPermutation, permutations, currentIndex, depth } = currentStepData;

    // Calculate positions based on the number of elements
    const numElements = elements.length;
    const angleStep = (2 * Math.PI) / numElements;

    return (
      <group position={position} rotation={rotation}>
        {/* Original elements in a circle */}
        {elements.map((element, index) => {
          const angle = index * angleStep;
          const x = Math.sin(angle) * ORBIT_RADIUS;
          const z = Math.cos(angle) * ORBIT_RADIUS;

          // Determine if this element is being used or is the current one
          const isUsed = currentPermutation.includes(element);
          const isCurrent = index === currentIndex;

          return (
            <Element
              key={`element-${index}`}
              position={[x, 0, z]}
              text={element}
              color={isCurrent ? colors.currentElement : isUsed ? colors.usedElement : colors.element}
              isHighlighted={isCurrent}
            />
          );
        })}

        {/* Current permutation being built */}
        {currentPermutation.length > 0 && (
          <Permutation
            position={[0, LEVEL_HEIGHT, 0]}
            elements={currentPermutation}
            isCurrent={true}
          />
        )}

        {/* Completed permutations */}
        {permutations.slice(0, 10).map((perm, index) => {
          // Calculate position in a spiral pattern
          const spiralAngle = index * 0.6;
          const spiralRadius = 3 + index * 0.3;
          const x = Math.sin(spiralAngle) * spiralRadius;
          const z = Math.cos(spiralAngle) * spiralRadius;

          return (
            <Permutation
              key={`perm-${index}`}
              position={[x, LEVEL_HEIGHT * 2, z]}
              elements={perm}
              isCurrent={false}
            />
          );
        })}

        {/* Connection lines */}
        {currentPermutation.length > 0 && currentIndex >= 0 && (
          <group>
            {/* Line from current element to current permutation */}
            <mesh>
              <cylinderGeometry args={[0.05, 0.05, LEVEL_HEIGHT, 8]} />
              <meshStandardMaterial color={colors.connection} />
              <group position={[0, -LEVEL_HEIGHT/2, 0]}>
                {/* Position and rotate to connect the points */}
                {/* This is a simplified version - in a real implementation, you'd calculate the exact position and rotation */}
              </group>
            </mesh>
          </group>
        )}

        {/* Permutation count */}
        {permutations.length > 0 && (
          <Html position={[0, LEVEL_HEIGHT * 3, 0]}>
            <div style={{
              background: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              color: isDark ? 'white' : 'black',
              padding: '8px 16px',
              borderRadius: '4px',
              fontWeight: 'bold',
              textAlign: 'center',
              whiteSpace: 'nowrap'
            }}>
              Total Permutations: {permutations.length}
            </div>
          </Html>
        )}
      </group>
    );
  };

  return (
    <>
      <group>
        {/* Fixed Step board - stays at the top of the screen */}
        <FixedStepBoard
          description={currentStepData?.message || 'Permutations Algorithm'}
          currentStep={step}
          totalSteps={stepsRef.current.length || 1}
          stepData={currentStepData || {}}
        />

        {/* Fixed Color legend - stays at the bottom of the screen */}
        <FixedColorLegend
          items={legendItems}
          theme={muiTheme}
        />

        {/* Theme-aware fog for depth perception */}
        <fog attach="fog" args={[isDark ? '#0c1014' : '#f8f9fa', 70, 250]} />

        {/* Ambient light for overall scene illumination */}
        <ambientLight intensity={isDark ? 0.2 : 0.3} />

        {/* Main directional light with shadows */}
        <directionalLight
          position={[5, 15, 8]}
          intensity={isDark ? 0.7 : 0.8}
          color="#ffffff"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Fill light from opposite direction */}
        <directionalLight
          position={[-8, 10, -5]}
          intensity={0.4}
          color={isDark ? '#a0a0ff' : '#a0d0ff'}
        />

        {/* Rim light for edge definition */}
        <directionalLight
          position={[0, 5, -10]}
          intensity={0.3}
          color={isDark ? '#ffb0b0' : '#ffe0c0'}
        />

        {/* Spotlight to highlight the main visualization */}
        <spotLight
          position={[0, 15, 0]}
          angle={0.5}
          penumbra={0.8}
          intensity={isDark ? 0.5 : 0.6}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          color={isDark ? '#ffffff' : '#ffffff'}
        />

        {/* Visualization */}
        <group position={position} rotation={rotation}>
          {renderPermutationsVisualization()}
        </group>

        {/* Add a grid helper for better spatial reference */}
        <gridHelper
          args={[80, 80, isDark ? '#2d3436' : '#dfe6e9', isDark ? '#1e272e' : '#f5f6fa']}
          position={[0, -1, 0]}
          rotation={[0, 0, 0]}
        />
      </group>
    </>
  );
};

export default PermutationsVisualization;
