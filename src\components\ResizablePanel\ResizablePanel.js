// ResizablePanel.js
// A component that provides a resizable panel

import React, { useState, useRef, useEffect } from 'react';
import { Box, useTheme } from '@mui/material';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { savePanelWidth, getPanelWidth } from '../../utils/layoutStorage';

const ResizablePanel = ({
  children,
  initialWidth = 400,
  minWidth = 300,
  maxWidth = 500,
  position = 'left', // 'left' or 'right'
  bgcolor,
  borderSide = position === 'left' ? 'right' : 'left',
  id, // Optional unique identifier
  ...otherProps
}) => {
  const theme = useTheme();
  const resizerRef = useRef(null);

  // Initialize width from localStorage or use initialWidth as fallback
  const [width, setWidth] = useState(() => {
    return getPanelWidth(initialWidth);
  });

  // Determine border side
  const borderStyle = {
    [`border${borderSide.charAt(0).toUpperCase() + borderSide.slice(1)}`]: 1,
    borderColor: "divider",
  };

  // Handle mouse down event
  const handleMouseDown = (e) => {
    // Prevent default behavior
    e.preventDefault();

    // Get initial mouse position
    const startX = e.clientX;
    const startWidth = width;
    let lastSavedWidth = width;

    // Handle mouse move
    function handleMouseMove(e) {
      // Calculate new width
      let newWidth;
      if (position === 'left') {
        newWidth = startWidth + (e.clientX - startX);
      } else {
        newWidth = startWidth - (e.clientX - startX);
      }

      // Clamp width between min and max
      newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));

      // Update width
      setWidth(newWidth);

      // Save width periodically during drag to avoid losing state
      // Only save if width has changed by at least 5px to reduce storage operations
      if (Math.abs(newWidth - lastSavedWidth) > 5) {
        savePanelWidth(newWidth);
        lastSavedWidth = newWidth;
      }
    }

    // Handle mouse up
    function handleMouseUp() {
      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Reset cursor
      document.body.style.cursor = '';

      // Always save the final width to localStorage
      savePanelWidth(width);
    }

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Set cursor
    document.body.style.cursor = 'col-resize';
  };

  // Force save width to localStorage on component mount and unmount
  useEffect(() => {
    // On mount, ensure we have the latest width from storage
    const savedWidth = getPanelWidth();
    if (savedWidth !== width) {
      setWidth(savedWidth);
    }

    // On unmount, save the current width
    return () => {
      savePanelWidth(width);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount/unmount

  return (
    <Box
      sx={{
        width: `${width}px`,
        bgcolor: bgcolor || theme.palette.background.paper,
        display: "flex",
        flexDirection: "column",
        position: "relative",
        overflow: "hidden",
        flexShrink: 0, // Prevent the panel from shrinking
        flexGrow: 0, // Prevent the panel from growing
        zIndex: 10, // Ensure panel is above other elements
        ...borderStyle,
        ...otherProps
      }}
    >
      {/* Resizer */}
      <Box
        ref={resizerRef}
        onMouseDown={handleMouseDown}
        sx={{
          position: 'absolute',
          [position === 'left' ? 'right' : 'left']: -5,
          top: 0,
          bottom: 0,
          width: 10,
          cursor: 'col-resize',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover': {
            '&::after': {
              backgroundColor: theme.palette.primary.main,
              width: 4,
              opacity: 1,
            },
            '& .resizer-icon': {
              opacity: 1,
              color: theme.palette.primary.main,
            }
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: '15%',
            bottom: '15%',
            left: '50%',
            transform: 'translateX(-50%)',
            width: 2,
            backgroundColor: theme.palette.divider,
            opacity: 0.7,
            borderRadius: 4,
          }
        }}
      >
        <DragIndicatorIcon
          className="resizer-icon"
          sx={{
            transform: 'rotate(90deg)',
            fontSize: '1.2rem',
            opacity: 0.5,
            color: theme.palette.text.secondary,
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%) rotate(90deg)',
          }}
        />
      </Box>

      {/* Content */}
      <Box
        sx={{
          flex: 1,
          overflow: 'auto',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ResizablePanel;
