// LISController.js
// This component provides the controls for Longest Increasing Subsequence algorithm visualization

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, TextField, IconButton } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// Import algorithm functions
import LISAlgorithm from './LISAlgorithm';

const LISController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => {} } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        steps,
        setSteps,
        setTotalSteps,
        setMovements
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Algorithm parameters
    const [sequence, setSequence] = useState(params?.sequence || [10, 22, 9, 33, 21, 50, 41, 60, 80]);
    const [customSequence, setCustomSequence] = useState(sequence.join(', '));
    const [useCustomSequence, setUseCustomSequence] = useState(false);

    // Reset function to properly reset the state and trigger a re-render
    const resetAndGenerateSteps = useCallback(() => {
        console.log('resetAndGenerateSteps called with:', { sequence });

        // Reset state and step
        setState('idle');
        setStep(0);

        // Generate new steps with current parameters
        console.log('Generating steps with parameters:', { sequence });

        // Update params first
        onParamChange({
            sequence
        });

        // Set steps and movements directly
        try {
            const result = LISAlgorithm.generateLISSteps({
                sequence
            });
            
            console.log('Generated steps:', result);

            if (setSteps && typeof setSteps === 'function') {
                setSteps(result.steps);
            }

            if (setTotalSteps && typeof setTotalSteps === 'function') {
                setTotalSteps(result.steps.length);
            }

            if (setMovements && typeof setMovements === 'function') {
                setMovements(result.steps.map(step => step.message));
            }
        } catch (error) {
            console.error('Error setting steps:', error);
        }
    }, [sequence, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

    // Generate steps when component mounts
    useEffect(() => {
        console.log('Component mounted, generating steps...');
        resetAndGenerateSteps();
        console.log('Steps generated after component mount');
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle sequence element change
    const handleElementChange = useCallback((index, value) => {
        const newValue = parseInt(value, 10);
        if (isNaN(newValue)) return;

        const newSequence = [...sequence];
        newSequence[index] = newValue;
        setSequence(newSequence);
        setCustomSequence(newSequence.join(', '));
        resetAndGenerateSteps();
    }, [sequence, resetAndGenerateSteps]);

    // Add a new element
    const addElement = useCallback(() => {
        if (sequence.length < 15) { // Limit to 15 elements for visualization clarity
            const newSequence = [...sequence, 0]; // Add a new element with default value 0
            setSequence(newSequence);
            setCustomSequence(newSequence.join(', '));
            resetAndGenerateSteps();
        }
    }, [sequence, resetAndGenerateSteps]);

    // Remove the last element
    const removeElement = useCallback(() => {
        if (sequence.length > 2) { // Keep at least 2 elements
            const newSequence = sequence.slice(0, -1);
            setSequence(newSequence);
            setCustomSequence(newSequence.join(', '));
            resetAndGenerateSteps();
        }
    }, [sequence, resetAndGenerateSteps]);

    // Generate random sequence
    const generateRandomSequence = useCallback(() => {
        const length = sequence.length;
        const newSequence = Array(length).fill(0).map(() => Math.floor(Math.random() * 100));
        setSequence(newSequence);
        setCustomSequence(newSequence.join(', '));
        resetAndGenerateSteps();
    }, [sequence.length, resetAndGenerateSteps]);

    // Handle custom sequence input
    const handleCustomSequenceChange = useCallback((value) => {
        setCustomSequence(value);
    }, []);

    // Apply custom sequence
    const applyCustomSequence = useCallback(() => {
        try {
            const newSequence = customSequence.split(',').map(item => {
                const num = parseInt(item.trim(), 10);
                if (isNaN(num)) {
                    throw new Error('Invalid sequence');
                }
                return num;
            });
            
            if (newSequence.length < 2) {
                throw new Error('Sequence must have at least 2 elements');
            }
            
            if (newSequence.length > 15) {
                throw new Error('Sequence must have at most 15 elements');
            }
            
            setSequence(newSequence);
            resetAndGenerateSteps();
        } catch (error) {
            console.error('Error applying custom sequence:', error);
            // Reset custom sequence to current sequence
            setCustomSequence(sequence.join(', '));
        }
    }, [customSequence, sequence, resetAndGenerateSteps]);

    // Custom component for sequence input
    const SequenceInput = ({ disabled }) => {
        return (
            <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>Sequence</Typography>
                    <Button 
                        startIcon={<ShuffleIcon />} 
                        onClick={generateRandomSequence}
                        disabled={disabled}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                    >
                        Random
                    </Button>
                    <Button 
                        startIcon={<AddIcon />} 
                        onClick={addElement}
                        disabled={sequence.length >= 15 || disabled}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1 }}
                    >
                        Add
                    </Button>
                    <Button 
                        startIcon={<RemoveIcon />} 
                        onClick={removeElement}
                        disabled={sequence.length <= 2 || disabled}
                        variant="outlined"
                        size="small"
                    >
                        Remove
                    </Button>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <TextField
                        fullWidth
                        label="Custom Sequence (comma-separated)"
                        value={customSequence}
                        onChange={(e) => handleCustomSequenceChange(e.target.value)}
                        disabled={disabled}
                        sx={{ mb: 1 }}
                    />
                    <Button
                        variant="contained"
                        onClick={applyCustomSequence}
                        disabled={disabled}
                        size="small"
                        sx={{ mb: 2 }}
                    >
                        Apply Custom Sequence
                    </Button>
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                    {sequence.map((value, index) => (
                        <TextField
                            key={index}
                            label={`Element ${index + 1}`}
                            type="number"
                            size="small"
                            value={value}
                            onChange={(e) => handleElementChange(index, e.target.value)}
                            slotProps={{ input: { min: -100, max: 100 } }}
                            sx={{ width: 100 }}
                            disabled={disabled}
                        />
                    ))}
                </Box>
                
                <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Current Sequence:</Typography>
                    <Box sx={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: 1,
                        p: 1,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1
                    }}>
                        {sequence.map((value, i) => (
                            <Box key={i} sx={{ 
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: 1,
                                p: 1,
                                minWidth: 30,
                                textAlign: 'center',
                                bgcolor: 'background.paper'
                            }}>
                                <Typography variant="body2">
                                    {value}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Box>
        );
    };

    // Handle control button clicks
    const handleStart = useCallback(() => {
        console.log('handleStart called, setting state to running');
        setState('running');
    }, [setState]);

    const handlePause = useCallback(() => {
        setState('paused');
    }, [setState]);

    const handleReset = useCallback(() => {
        setStep(0);
        setState('idle');
    }, [setStep, setState]);

    const handleStepForward = useCallback(() => {
        if (step < totalSteps - 1) {
            setStep(step + 1);
        }
    }, [step, totalSteps, setStep]);

    const handleStepBackward = useCallback(() => {
        if (step > 0) {
            setStep(step - 1);
        }
    }, [step, setStep]);

    // Pseudocode for LIS algorithm
    const pseudocode = [
        { code: "function LIS(sequence):", lineNumber: 1, indent: 0 },
        { code: "  n = sequence.length", lineNumber: 2, indent: 1 },
        { code: "  // Create DP table", lineNumber: 3, indent: 1 },
        { code: "  dp = [1, 1, ..., 1] // Length n, each element is at least a subsequence of length 1", lineNumber: 4, indent: 1 },
        { code: "  prev = [-1, -1, ..., -1] // Length n, to track previous element in LIS", lineNumber: 5, indent: 1 },
        { code: "  for i = 1 to n-1:", lineNumber: 6, indent: 1 },
        { code: "    for j = 0 to i-1:", lineNumber: 7, indent: 2 },
        { code: "      if sequence[i] > sequence[j]:", lineNumber: 8, indent: 3 },
        { code: "        // If extending the LIS ending at j gives a longer LIS ending at i", lineNumber: 9, indent: 3 },
        { code: "        if dp[j] + 1 > dp[i]:", lineNumber: 10, indent: 3 },
        { code: "          dp[i] = dp[j] + 1", lineNumber: 11, indent: 4 },
        { code: "          prev[i] = j", lineNumber: 12, indent: 4 },
        { code: "        // else: keep current LIS length", lineNumber: 13, indent: 3 },
        { code: "      // else: cannot extend LIS from j to i", lineNumber: 14, indent: 3 },
        { code: "", lineNumber: 15, indent: 0 },
        { code: "  // Find the maximum LIS length and its ending index", lineNumber: 16, indent: 1 },
        { code: "  maxLength = max(dp), maxIndex = argmax(dp)", lineNumber: 17, indent: 1 },
        { code: "", lineNumber: 18, indent: 0 },
        { code: "  // Trace back to find the LIS", lineNumber: 19, indent: 1 },
        { code: "  lis = []", lineNumber: 20, indent: 1 },
        { code: "  while maxIndex != -1:", lineNumber: 21, indent: 1 },
        { code: "    lis.prepend(sequence[maxIndex])", lineNumber: 22, indent: 2 },
        { code: "    maxIndex = prev[maxIndex]", lineNumber: 23, indent: 2 },
        { code: "", lineNumber: 24, indent: 0 },
        { code: "  return maxLength, lis", lineNumber: 25, indent: 1 },
    ];

    // Get current line number for pseudocode highlighting
    const currentLine = step < steps?.length ? (
        steps[step]?.pseudocodeLine || 1
    ) : 1;

    return (
        <Box>
            {/* Information Section */}
            <InformationSection title="Longest Increasing Subsequence Algorithm">
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        The Longest Increasing Subsequence (LIS) algorithm finds the length of the longest subsequence of a given sequence such that all elements of the subsequence are sorted in increasing order.
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Time Complexity: O(n²) where n is the length of the sequence
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • Space Complexity: O(n) for the dynamic programming table
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • A subsequence is a sequence that can be derived from another sequence by deleting some or no elements without changing the order of the remaining elements
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        • The algorithm uses dynamic programming to build a table of LIS lengths ending at each position
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'sequence',
                        type: 'component',
                        label: 'Sequence',
                        component: SequenceInput,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    sequence
                }}
                onChange={() => {
                    // Sequence is handled by the SequenceInput component
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={handleStart}
                onPause={handlePause}
                onReset={handleReset}
                onStepForward={handleStepForward}
                onStepBackward={handleStepBackward}
            />

            {/* Progress Section */}
            <ProgressSection
                step={(() => {
                    // Handle special cases for the last step
                    if (step >= totalSteps) {
                        // If we've gone beyond the last step, use totalSteps
                        return totalSteps;
                    } else if (step === totalSteps - 1) {
                        // If we're at the last step, use totalSteps
                        return totalSteps;
                    } else if (step === 0) {
                        // If we're at the first step, use 0
                        return 0;
                    } else {
                        // Otherwise, use the step as is (ProgressSection doesn't need 1-indexing)
                        return step;
                    }
                })()}
                totalSteps={totalSteps}
                state={state}
            />

            {/* Debug information - hidden */}
            <div style={{ display: 'none' }}>
                Steps: {steps ? steps.length : 0}, Total Steps: {totalSteps}, Current Step: {step}
            </div>

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.message || ''
                }))}
                currentStep={(() => {
                    // Adjust the currentStep value to match what StepsSequenceSection expects
                    // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
                    // Handle special cases for the last step
                    if (step >= steps?.length) {
                        // If we've gone beyond the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === (steps?.length || 0) - 1) {
                        // If we're at the last step, use steps.length
                        return steps?.length || 0;
                    } else if (step === 0) {
                        // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
                        return 1;
                    } else {
                        // Otherwise, add 1 to convert from 0-indexed to 1-indexed
                        return step + 1;
                    }
                })()}
                title="Steps Sequence"
                defaultExpanded={true}
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                algorithm={pseudocode}
                currentStep={currentLine || 0}
                title="Algorithm"
                defaultExpanded={true}
            />
        </Box>
    );
};

export default LISController;
