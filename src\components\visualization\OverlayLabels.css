/* OverlayLabels.css */
/* CSS for stable overlay labels */

.overlay-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.overlay-label {
  position: absolute;
  transform: translate(-50%, -50%);
  pointer-events: none;
  user-select: none;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: transform;
  transform-style: preserve-3d;
  transition: none !important;
  animation: none !important;
}

.node-label {
  color: white;
  font-size: 28px;
  font-weight: bold;
  text-shadow: 0 0 4px black, 0 0 8px black, 0 0 12px black;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edge-label {
  color: white;
  background-color: rgba(0,0,0,0.7);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid rgba(255,255,255,0.3);
  box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  text-shadow: 0 1px 2px rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
