// CountingSortConfig.js
// Configuration file for CountingSort algorithm visualization

const CONFIG = {
  // Main array configuration
  mainArray: {
    // Bar configuration (consistent with other sorting algorithms)
    bars: {
      width: 0.6,                    // Width of each bar (same as other sorting algorithms)
      spacing: 0.3,                  // Spacing between bars (same as other sorting algorithms)
      maxHeight: 3.8,                // Maximum height for bars (same as other sorting algorithms)
      baseOffset: [0, 0, 0],         // Offset from base platform
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance
      defaultCountForSizing: 10,     // Default count for sizing calculations

      // Bar geometry (consistent with other sorting algorithms)
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties (consistent with other sorting algorithms)
      material: {
        roughness: 0.6,              // Material roughness (0 = mirror, 1 = rough)
        metalness: 0.2,              // Material metalness (0 = non-metal, 1 = metal)
        opacity: 1.0,                // Material opacity
        transparent: false,          // Enable transparency
      },

      // Base platform under each bar (consistent with other sorting algorithms)
      base: {
        enabled: true,               // Enable/disable base platform
        height: 0.05,                // Height of base platform
        widthScale: 1.0,             // Width scale factor for base
        depthScale: 1.0,             // Depth scale factor for base
        material: {
          roughness: 0.8,            // Base material roughness
          metalness: 0.2,            // Base material metalness
          opacity: 0.7,              // Base opacity
          transparent: false,        // Enable transparency
        },
      },
    },

    // Value labels configuration (consistent with other sorting algorithms)
    valueLabels: {
      enabled: true,                 // Enable/disable value labels
      offset: [0, 0.3, 0],          // Offset from bar top [x, y, z]
      fontSize: '0.6rem',            // Font size for value labels (same as other sorting algorithms)
      fontWeight: 'bold',            // Font weight (same as other sorting algorithms)
      padding: {
        horizontal: 0.5,             // Horizontal padding inside label
        vertical: 0.1,               // Vertical padding inside label
      },
      borderRadius: 1.5,             // Border radius factor (boxy style like other sorting algorithms)
      minWidth: '16px',              // Minimum width of label container
      elevation: 1,                  // Material-UI elevation
    },

    // Index labels configuration (consistent with other sorting algorithms)
    indexLabels: {
      enabled: true,                 // Enable/disable index labels
      offset: [0, 0.1, 0.4],         // Offset from bar base [x, y, z] (same as other sorting algorithms)
      fontSize: '10px',              // Font size for index labels (same as other sorting algorithms)
      fontWeight: 'normal',          // Font weight (same as other sorting algorithms)
      size: {
        width: '20px',               // Width of circular index label (same as other sorting algorithms)
        height: '20px',              // Height of circular index label (same as other sorting algorithms)
      },
      elevation: 1,                  // Material-UI elevation
    },

    // Animation configuration
    animation: {
      enabled: true,                 // Enable/disable animations
      duration: 800,                 // Base animation duration (ms)
      easing: 'easeInOutCubic',      // Animation easing function
      stagger: 100,                  // Stagger delay between elements (ms)
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation
      amplitude: 0.1,                // Levitation amplitude
      frequency: 0.5,                // Levitation frequency
      offset: 0,                     // Phase offset
      disableDuringSimulation: true, // Disable levitation when algorithm is running
    },
  },

  // Base platform configuration (consistent with other sorting algorithms)
  basePlatform: {
    dimensions: {
      height: 0.2,                   // Height of the base platform (same as other sorting algorithms)
      lengthPadding: {
        left: 1,                     // Padding on the left side (same as other sorting algorithms)
        right: 1,                    // Padding on the right side (same as other sorting algorithms)
      },
      depth: 3,                      // Depth of the base platform (same as other sorting algorithms)
      minWidth: 8,                   // Minimum width for the platform
    },
    position: [0, -5, 0],            // MASTER position - controls entire main array group [x, y, z] (same as other sorting algorithms)
    material: {
      roughness: 0.8,                // Material roughness (same as other sorting algorithms)
      metalness: 0.1,                // Material metalness (same as other sorting algorithms)
    },
  },

  // ==================== CAMERA CONFIGURATION ====================
  camera: {
    position: [0, 4, 14],            // Default camera position [x, y, z] - slightly higher and further for count/output arrays
    lookAt: [0, -1, -2],             // Camera look-at point [x, y, z] - adjusted to center on main array
    fov: 55,                         // Field of view in degrees (same as other sorting algorithms)
    near: 0.1,                       // Near clipping plane (same as other sorting algorithms)
    far: 1000,                       // Far clipping plane (same as other sorting algorithms)

    // Dynamic positioning based on array size
    dynamicPositioning: {
      enabled: true,                 // Enable dynamic camera positioning
      minDistance: 10,               // Minimum camera distance
      paddingFactor: 2.2,            // Padding factor for camera distance calculation
      heightOffset: 4,               // Additional height offset
    },
  },

  // Count array configuration for CountingSort
  countArray: {
    // Array arrangement
    arrangement: {
      position: [0, 3, 0],           // Position for count array [x, y, z]
      maxWidth: 12,                  // Maximum width for count array
    },

    // Individual cell configuration
    cell: {
      width: 0.8,                    // Width of each count cell
      height: 0.8,                   // Height of each count cell
      depth: 0.8,                    // Depth of each count cell
      spacing: 0.2,                  // Spacing between cells
    },

    // Cell labels
    labels: {
      enabled: true,                 // Enable/disable cell labels
      indexOffset: [0, -0.5, 0],    // Offset for index labels [x, y, z]
      valueOffset: [0, 0, 0.5],     // Offset for value labels [x, y, z]
      fontSize: '0.6rem',           // Font size for cell labels
      fontWeight: 'bold',           // Font weight
    },
  },

  // Output array configuration for CountingSort
  outputArray: {
    // Array arrangement
    arrangement: {
      position: [0, -1.5, 0],        // Position for output array [x, y, z]
      maxWidth: 12,                  // Maximum width for output array
    },

    // Individual cell configuration
    cell: {
      width: 0.8,                    // Width of each output cell
      height: 0.8,                   // Height of each output cell
      depth: 0.8,                    // Depth of each output cell
      spacing: 0.2,                  // Spacing between cells
    },

    // Cell labels
    labels: {
      enabled: true,                 // Enable/disable cell labels
      indexOffset: [0, -0.5, 0],    // Offset for index labels [x, y, z]
      valueOffset: [0, 0, 0.5],     // Offset for value labels [x, y, z]
      fontSize: '0.6rem',           // Font size for cell labels
      fontWeight: 'bold',           // Font weight
    },
  },

  // Step board configuration (consistent with other sorting algorithms)
  stepBoard: {
    enabled: true,                   // Enable/disable step board
    position: [0, 5, 0.5],          // Position of the step board [x, y, z] (same as other sorting algorithms)
    dimensions: {
      width: 12,                     // Width of the step board (same as other sorting algorithms)
      height: 1.5,                   // Height of the step board (same as other sorting algorithms)
      depth: 0.1,                    // Depth of the step board (same as other sorting algorithms)
    },
    material: {
      opacity: 0.9,                  // Material opacity (same as other sorting algorithms)
      transparent: true,             // Enable transparency (same as other sorting algorithms)
    },
    text: {
      fontSize: 'h6',                // Material-UI typography variant (same as other sorting algorithms)
      fontWeight: 'bold',            // Font weight (same as other sorting algorithms)
      align: 'center',               // Text alignment (same as other sorting algorithms)
      padding: 2,                    // Padding around text (same as other sorting algorithms)
    },
    border: {
      enabled: true,                 // Enable border (same as other sorting algorithms)
      width: 2,                      // Border width (same as other sorting algorithms)
      radius: 2,                     // Border radius (same as other sorting algorithms)
    },
    elevation: 3,                    // Material-UI elevation (same as other sorting algorithms)
  },

  // Color legend configuration
  colorLegend: {
    enabled: true,                   // Enable/disable color legend
    position: [0, -3.5, 0.5],      // Position of color legend [x, y, z]
    itemSpacing: 0.3,                // Spacing between legend items
    fontSize: '0.85rem',             // Font size for legend text
    fontWeight: 500,                 // Font weight for legend text
    padding: 0.8,                    // Padding inside legend
    borderRadius: 2,                 // Border radius divisor
    elevation: 4,                    // Material-UI elevation
    maxItemsPerRow: 6,               // Maximum items per row

    // Legend items definition - Only colors actually used in CountingSort
    legendItems: [
      { colorKey: 'bar', label: 'Default' },
      { colorKey: 'current', label: 'Current Element' },
      { colorKey: 'counting', label: 'Counting' },
      { colorKey: 'placing', label: 'Placing' },
      { colorKey: 'sorted', label: 'Sorted' }
    ],
  },

  // Colors configuration
  colors: {
    // Theme-based colors
    themes: {
      light: {
        // Bar colors
        bar: '#42a5f5',              // Default bar color
        current: '#9c27b0',          // Current element being processed
        counting: '#ff9800',         // Element being counted
        placing: '#4caf50',          // Element being placed
        sorted: '#66bb6a',           // Sorted elements

        // Array colors
        countArray: '#e3f2fd',       // Count array background
        outputArray: '#f3e5f5',      // Output array background
        activeCell: '#ffeb3b',       // Active cell highlight

        // Platform and base colors
        platform: '#616161',         // Base platform color
        base: '#757575',             // Base color for bars

        // UI colors
        stepBoard: '#ffffff',        // Step board background
        legend: '#ffffff',           // Legend background
        text: '#000000',             // Text color
        border: '#e0e0e0',           // Border color
      },
      dark: {
        // Bar colors
        bar: '#64b5f6',              // Default bar color
        current: '#ba68c8',          // Current element being processed
        counting: '#ffb74d',         // Element being counted
        placing: '#66bb6a',          // Element being placed
        sorted: '#81c784',           // Sorted elements

        // Array colors
        countArray: '#1e3a8a',       // Count array background
        outputArray: '#4a148c',      // Output array background
        activeCell: '#fbc02d',       // Active cell highlight

        // Platform and base colors
        platform: '#424242',         // Base platform color
        base: '#616161',             // Base color for bars

        // UI colors
        stepBoard: '#2d2d2d',        // Step board background
        legend: '#2d2d2d',           // Legend background
        text: '#ffffff',             // Text color
        border: '#424242',           // Border color
      },
    },
  },

  // Speed and timing configuration
  speed: {
    baseDelay: 1000,                 // Base delay in milliseconds
    minDelay: 100,                   // Minimum delay
    maxDelay: 3000,                  // Maximum delay
    animationDuration: 800,          // Animation duration
    enhancedCurve: true,             // Use enhanced exponential curve
  },
};

export default CONFIG;