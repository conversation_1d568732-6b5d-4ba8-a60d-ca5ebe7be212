// CountingSortConfig.js
// Configuration file for CountingSort algorithm visualization

const CountingSortConfig = {
  // ==================== MAIN ARRAY CONFIGURATION ====================
  mainArray: {
    // Bar dimensions and positioning
    bars: {
      width: 0.6,                    // Width of each main array bar
      spacing: 0.3,                  // Spacing between main array bars
      maxHeight: 3.8,                // Maximum height of main array bars
      baseOffset: [0, 0, 0],         // Offset from base platform position [x, y, z]
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance

      // Bar geometry
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties
      material: {
        roughness: 0.3,              // Material roughness
        metalness: 0.1,              // Material metalness
        transparent: false,          // Transparency
        opacity: 1.0,                // Opacity level
      },
    },

    // Platform configuration
    platform: {
      enabled: true,                 // Enable/disable platform
      width: 'auto',                 // Platform width ('auto' = calculated from array)
      depth: 2.0,                    // Platform depth
      height: 0.1,                   // Platform height
      color: 'base',                 // Platform color (from color palette)
      position: [0, -0.05, 0],       // Platform position offset [x, y, z]

      // Platform material
      material: {
        roughness: 0.8,              // Material roughness
        metalness: 0.2,              // Material metalness
      },
    },

    // Animation configuration
    animation: {
      enabled: true,                 // Enable/disable animations
      duration: 800,                 // Base animation duration (ms)
      easing: 'easeInOutCubic',      // Animation easing function
      stagger: 100,                  // Stagger delay between elements (ms)
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation
      amplitude: 0.1,                // Levitation amplitude
      frequency: 0.5,                // Levitation frequency
      offset: 0,                     // Phase offset
    },
  },

  // ==================== COUNT ARRAY CONFIGURATION ====================
  countArray: {
    // Position and layout
    position: {
      x: 0,                          // X position relative to main array
      y: 4.5,                        // Y position (height above main array)
      z: 0,                          // Z position (forward/backward offset)
    },

    // Bar dimensions
    bars: {
      width: 0.5,                    // Width of count array bars
      spacing: 0.2,                  // Spacing between count array bars
      maxHeight: 2.0,                // Maximum height of count array bars
      minHeight: 0.1,                // Minimum height for zero counts

      // Bar geometry
      geometry: {
        widthScale: 0.9,             // Width scale factor
        depthScale: 0.9,             // Depth scale factor
      },

      // Bar material
      material: {
        roughness: 0.4,              // Material roughness
        metalness: 0.2,              // Material metalness
      },
    },

    // Platform for count array
    platform: {
      enabled: true,                 // Enable count array platform
      width: 'auto',                 // Platform width
      depth: 1.5,                    // Platform depth
      height: 0.08,                  // Platform height
      color: 'countPlatform',        // Platform color
      position: [0, -0.04, 0],       // Platform position offset
    },

    // Labels configuration
    labels: {
      indices: {
        enabled: true,               // Show index labels
        fontSize: '0.8rem',          // Font size
        fontWeight: 'normal',        // Font weight
        color: 'text.primary',       // Text color
        offset: [0, -0.5, 0],       // Label position offset [x, y, z]
      },
      values: {
        enabled: true,               // Show value labels
        fontSize: '0.7rem',          // Font size
        fontWeight: 'bold',          // Font weight
        color: 'text.secondary',     // Text color
        offset: [0, 0.3, 0],        // Label position offset [x, y, z]
      },
    },

    // Animation
    animation: {
      enabled: true,                 // Enable count array animations
      duration: 600,                 // Animation duration
      highlightDuration: 300,        // Highlight animation duration
      easing: 'easeInOutQuad',       // Animation easing
    },
  },

  // ==================== OUTPUT ARRAY CONFIGURATION ====================
  outputArray: {
    // Position and layout
    position: {
      x: 0,                          // X position relative to main array
      y: -2.5,                       // Y position (below main array)
      z: 0,                          // Z position
    },

    // Bar dimensions (same as main array)
    bars: {
      width: 0.6,                    // Width of output array bars
      spacing: 0.3,                  // Spacing between output array bars
      maxHeight: 3.8,                // Maximum height of output array bars

      // Bar geometry
      geometry: {
        widthScale: 0.8,             // Width scale factor
        depthScale: 0.8,             // Depth scale factor
      },

      // Bar material
      material: {
        roughness: 0.3,              // Material roughness
        metalness: 0.1,              // Material metalness
      },
    },

    // Platform for output array
    platform: {
      enabled: true,                 // Enable output array platform
      width: 'auto',                 // Platform width
      depth: 2.0,                    // Platform depth
      height: 0.1,                   // Platform height
      color: 'outputPlatform',       // Platform color
      position: [0, -0.05, 0],       // Platform position offset
    },

    // Animation
    animation: {
      enabled: true,                 // Enable output array animations
      duration: 800,                 // Animation duration
      placementDelay: 200,           // Delay between placements
      easing: 'easeInOutCubic',      // Animation easing
    },
  },

  // ==================== VISUAL LABELS CONFIGURATION ====================
  visual: {
    // Value labels configuration
    labels: {
      values: {
        enabled: true,               // Enable value labels
        adaptiveVisibility: true,    // Hide labels when bars are too small
        visibilityThreshold: 0.7,    // Scale threshold for hiding labels
        fontSize: '1rem',            // Font size
        fontWeight: 'bold',          // Font weight
        color: 'text.primary',       // Text color (theme palette reference)

        // Label positioning
        offset: [0, 0.3, 0],        // Position offset from bar top [x, y, z]
        background: {
          enabled: true,             // Enable label background
          color: 'background.paper', // Background color
          padding: 0.1,              // Background padding
          borderRadius: 0.05,        // Background border radius
          opacity: 0.9,              // Background opacity
        },
      },

      indices: {
        enabled: true,               // Enable index labels
        adaptiveVisibility: true,    // Hide labels when bars are too small
        visibilityThreshold: 0.7,    // Scale threshold for hiding labels
        fontSize: '0.8rem',          // Font size
        fontWeight: 'normal',        // Font weight
        color: 'text.secondary',     // Text color

        // Label positioning
        offset: [0, -0.8, 0],       // Position offset from bar base [x, y, z]
        background: {
          enabled: true,             // Enable label background
          color: 'background.default', // Background color
          padding: 0.08,             // Background padding
          borderRadius: 0.1,         // Background border radius
          opacity: 0.8,              // Background opacity
        },
      },
    },

    // Step board configuration
    stepBoard: {
      enabled: true,                 // Enable step board
      position: [0, 6.5, 0.5],      // Position of step board [x, y, z]
      width: 12,                     // Step board width
      height: 2,                     // Step board height

      // Text configuration
      text: {
        fontSize: '1.2rem',          // Font size
        fontWeight: 'normal',        // Font weight
        color: 'text.primary',       // Text color
        lineHeight: 1.4,             // Line height
        maxLines: 3,                 // Maximum number of lines
      },

      // Background
      background: {
        enabled: true,               // Enable background
        color: 'background.paper',   // Background color
        opacity: 0.95,               // Background opacity
        borderRadius: 0.2,           // Border radius
        padding: 0.3,                // Padding
      },
    },

    // Color legend configuration
    legend: {
      enabled: true,                 // Enable color legend
      position: [0, -4.5, 0.5],     // Position of legend [x, y, z]
      layout: 'horizontal',          // Layout: 'horizontal' or 'vertical'
      spacing: 2.0,                  // Spacing between legend items

      // Title configuration
      title: {
        text: 'Color Legend',        // Legend title
        fontSize: '1rem',            // Title font size
        fontWeight: 'bold',          // Title font weight
        color: 'text.primary',       // Title color
        offset: [0, 0.8, 0],        // Title position offset [x, y, z]
      },
    },

    // Individual legend item configuration
    items: {
      dimensions: {
        width: 0.8,                  // Width of color sample
        height: 0.4,                 // Height of color sample
        depth: 0.2,                  // Depth of color sample
      },
      material: {
        roughness: 0.5,              // Material roughness
        metalness: 0.3,              // Material metalness
      },
      label: {
        fontSize: 'caption',         // Material-UI typography variant
        fontWeight: 'normal',        // Font weight
        offset: [0, -0.8, 0],       // Offset from color sample [x, y, z]
        padding: {
          horizontal: 0.5,           // Horizontal padding
          vertical: 0.2,             // Vertical padding
        },
        borderRadius: 1,             // Border radius factor
        elevation: 1,                // Material-UI elevation
      },
    },
  },

  // ==================== ANIMATION CONFIGURATION ====================
  animation: {
    // Timing configuration
    timing: {
      baseDuration: 500,             // Base animation duration in ms
      speedFactor: 1.0,              // Speed multiplier (higher = faster)

      // Speed-based delays (calculated dynamically)
      delays: {
        slow: 2000,                  // Delay for slow speed (1-3)
        medium: 1000,                // Delay for medium speed (4-7)
        fast: 400,                   // Delay for fast speed (8-10)
      },
    },

    // Easing functions
    easing: {
      default: 'easeInOutCubic',     // Default easing function
      bounce: 'easeOutBounce',       // Bounce easing
      elastic: 'easeOutElastic',     // Elastic easing
      back: 'easeOutBack',           // Back easing
    },

    // Animation types
    types: {
      move: {
        duration: 800,               // Move animation duration
        easing: 'easeInOutCubic',    // Move easing
        arcHeight: 1.5,              // Arc height for move animations
      },
      highlight: {
        duration: 300,               // Highlight animation duration
        easing: 'easeInOutQuad',     // Highlight easing
        intensity: 1.2,              // Highlight intensity multiplier
      },
      scale: {
        duration: 400,               // Scale animation duration
        easing: 'easeOutBack',       // Scale easing
        factor: 1.1,                 // Scale factor
      },
    },

    // Performance settings
    performance: {
      enableGPUAcceleration: true,   // Enable GPU acceleration
      reducedMotion: false,          // Respect user's reduced motion preference
      maxConcurrentAnimations: 10,   // Maximum concurrent animations
    },
  },

  // ==================== COLOR CONFIGURATION ====================
  colors: {
    // Main color palette
    palette: {
      // Bar colors for different states
      default: '#42a5f5',            // Default bar color (blue)
      current: '#ff9800',            // Current element color (orange)
      counting: '#9c27b0',           // Counting state color (purple)
      sorted: '#4caf50',             // Sorted elements color (green)

      // Count array colors
      countBar: '#e91e63',           // Count bar color (pink)
      countHighlight: '#f06292',     // Count highlight color (light pink)

      // Output array colors
      outputBar: '#4caf50',          // Output bar color (green)
      outputHighlight: '#66bb6a',    // Output highlight color (light green)

      // Platform and environment colors
      base: '#424242',               // Base platform color
      countPlatform: '#616161',      // Count array platform color
      outputPlatform: '#388e3c',     // Output array platform color
      ground: '#303030',             // Ground color
      background: '#121212',         // Background color
    },

    // State-specific color mappings
    states: {
      initial: 'default',            // Initial state color
      findMax: 'current',            // Finding max color
      initCount: 'countBar',         // Initialize count array color
      counting: 'counting',          // Counting elements color
      incrementCount: 'countHighlight', // Increment count color
      calculatePosition: 'countBar', // Calculate position color
      updatePosition: 'countHighlight', // Update position color
      initOutput: 'outputBar',       // Initialize output color
      placing: 'current',            // Placing element color
      placed: 'outputHighlight',     // Placed element color
      decrementCount: 'countBar',    // Decrement count color
      complete: 'sorted',            // Completion color
    },

    // Theme-specific overrides
    themes: {
      light: {
        // Light theme color overrides
        base: '#f0f0f0',
        countPlatform: '#e0e0e0',
        outputPlatform: '#c8e6c9',
        ground: '#fafafa',
        background: '#ffffff',
      },
      dark: {
        // Dark theme color overrides
        base: '#424242',
        countPlatform: '#616161',
        outputPlatform: '#388e3c',
        ground: '#303030',
        background: '#121212',
      },
    },
  },

  // ==================== ALGORITHM-SPECIFIC CONFIGURATION ====================
  algorithm: {
    // Array generation
    array: {
      generation: {
        defaultSize: 8,              // Default array size (smaller for counting sort)
        minSize: 3,                  // Minimum array size
        maxSize: 15,                 // Maximum array size (smaller due to count array)
        valueRange: {
          min: 1,                    // Minimum value in array
          max: 20,                   // Maximum value (smaller for better visualization)
        },
      },

      // Custom array validation
      validation: {
        enforceRange: true,          // Enforce min/max range for custom arrays
        warnOnLargeValues: true,     // Warn when values are very large
        largeValueThreshold: 50,     // Threshold for large value warning
        maxCountArraySize: 100,      // Maximum count array size
      },
    },

    // Counting sort specific settings
    countingSort: {
      maxValueLimit: 100,            // Maximum value limit for count array
      showCountArray: true,          // Show count array visualization
      showOutputArray: true,         // Show output array visualization
      animateCountIncrement: true,   // Animate count increments
      animatePlacement: true,        // Animate element placement
    },

    // Performance settings
    performance: {
      maxArraySize: 20,              // Maximum array size for performance
      enableOptimizations: true,     // Enable performance optimizations
      reducedAnimations: false,      // Reduce animations for large arrays
    },
  },
};

export default CountingSortConfig;
