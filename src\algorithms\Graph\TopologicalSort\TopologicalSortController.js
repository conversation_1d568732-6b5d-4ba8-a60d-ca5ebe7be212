// TopologicalSortController.js
// This component provides the controls for Topological Sort algorithm visualization

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { ControlsSection, ParametersSection, InformationSection, AlgorithmSection, ProgressSection, StepsSequenceSection } from '../../../components/common';

// Import icons
import GraphIcon from '@mui/icons-material/AccountTree';
import TuneIcon from '@mui/icons-material/Tune';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'; // Used in customEdges parameter

// Import algorithm functions
import { generateTopologicalSortSteps } from './TopologicalSortAlgorithm';

const TopologicalSortController = (props) => {
  // Destructure props
  const { params = {}, onParamChange = () => {} } = props;

  // Get algorithm state from context
  const {
    state,
    setState,
    step,
    setStep,
    totalSteps,
    steps,
    setSteps,
    setTotalSteps,
    setMovements
  } = useAlgorithm();

  // Get speed from context
  const { speed, setSpeed } = useSpeed();

  // Extract default parameters
  const {
    nodes: defaultNodes = 6,
    density: defaultDensity = 0.5,
    customEdges: defaultCustomEdges = []
  } = params;

  // State for algorithm parameters
  const [numNodes, setNumNodes] = useState(defaultNodes);
  const [density, setDensity] = useState(defaultDensity);
  const [useCustomGraph, setUseCustomGraph] = useState(false);
  const [customEdges, setCustomEdges] = useState(defaultCustomEdges.map(edge => edge.join(', ')).join('\n'));
  const [error, setError] = useState('');

  // Parse custom edges from text input
  const parseCustomEdges = useCallback((text) => {
    try {
      return text.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(line => {
          const [source, target] = line.split(',').map(num => parseInt(num.trim()));
          if (isNaN(source) || isNaN(target)) {
            throw new Error(`Invalid edge format: ${line}. Expected format: "source, target"`);
          }
          return [source, target];
        });
    } catch (err) {
      setError(err.message);
      return [];
    }
  }, []);

  // Reset function to properly reset the state and trigger a re-render
  const resetAndGenerateSteps = useCallback(() => {
    console.log('resetAndGenerateSteps called with:', { numNodes, density });

    // Reset state and step
    setState('idle');
    setStep(0);
    setError('');

    // Generate new steps with current parameters
    console.log('Generating steps with parameters:', { numNodes, density, useCustomGraph });

    // Parse custom edges if needed
    const parsedCustomEdges = useCustomGraph ? parseCustomEdges(customEdges) : [];

    // Update params first
    onParamChange({
      nodes: numNodes,
      density,
      customEdges: parsedCustomEdges
    });

    // Set steps and movements directly
    try {
      // Generate steps
      const result = generateTopologicalSortSteps({
        nodes: numNodes,
        density,
        customEdges: parsedCustomEdges
      });
      console.log('Generated steps:', JSON.stringify(result, null, 2));

      // Debug: Check if the first step has graph data
      if (result.steps && result.steps.length > 0) {
        const firstStep = result.steps[0];
        console.log('First step graph data:', JSON.stringify(firstStep.graph, null, 2));
        console.log('First step graph nodes count:', firstStep.graph?.nodes?.length || 0);
        console.log('First step graph edges count:', firstStep.graph?.edges?.length || 0);
      } else {
        console.error('No steps generated!');
      }

      // Check for errors
      if (result.steps.length > 0 && result.steps[0].type === 'error') {
        setError(result.steps[0].message);
        return;
      }

      // Set steps
      if (setSteps && typeof setSteps === 'function') {
        setSteps(result.steps);
      }

      // Set total steps
      if (setTotalSteps && typeof setTotalSteps === 'function') {
        setTotalSteps(result.steps.length);
      }

      // Set movements
      if (setMovements && typeof setMovements === 'function') {
        setMovements(result.steps.map(step => step.message));
      }

      console.log('Steps set successfully:', result.steps.length, 'steps');
    } catch (error) {
      console.error('Error setting steps:', error);
      setError(`Error generating steps: ${error.message}`);
    }
  }, [numNodes, density, useCustomGraph, customEdges, parseCustomEdges, setState, setStep, setSteps, setTotalSteps, setMovements, onParamChange]);

  // Generate steps when component mounts or when parameters change
  useEffect(() => {
    console.log('Component mounted or parameters changed, generating steps...');
    // Small delay to ensure all state is properly initialized
    const timer = setTimeout(() => {
      resetAndGenerateSteps();
      console.log('Steps generated after component mount or parameter change');
    }, 100); // Slightly longer delay to ensure context is fully initialized

    return () => clearTimeout(timer);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle parameter changes
  const handleNumNodesChange = useCallback((value) => {
    if (value >= 2 && value <= 10) {
      console.log('handleNumNodesChange called with value:', value);
      setNumNodes(value);
      onParamChange({ ...params, nodes: value });
      // Use the resetAndGenerateSteps function to properly reset and update
      console.log('Calling resetAndGenerateSteps from handleNumNodesChange');
      resetAndGenerateSteps();
      console.log('After resetAndGenerateSteps from handleNumNodesChange');
    }
  }, [params, onParamChange, resetAndGenerateSteps]);

  const handleDensityChange = useCallback((value) => {
    if (value >= 0.1 && value <= 0.9) {
      console.log('handleDensityChange called with value:', value);
      setDensity(value);
      onParamChange({ ...params, density: value });
      // Use the resetAndGenerateSteps function to properly reset and update
      console.log('Calling resetAndGenerateSteps from handleDensityChange');
      resetAndGenerateSteps();
      console.log('After resetAndGenerateSteps from handleDensityChange');
    }
  }, [params, onParamChange, resetAndGenerateSteps]);

  const handleCustomGraphToggle = useCallback((checked) => {
    console.log('handleCustomGraphToggle called with value:', checked);
    setUseCustomGraph(checked);
    // Use the resetAndGenerateSteps function to properly reset and update
    console.log('Calling resetAndGenerateSteps from handleCustomGraphToggle');
    resetAndGenerateSteps();
    console.log('After resetAndGenerateSteps from handleCustomGraphToggle');
  }, [resetAndGenerateSteps]);

  const handleCustomEdgesChange = useCallback((value) => {
    console.log('handleCustomEdgesChange called with value:', value);
    setCustomEdges(value);
    setError('');
    // Don't regenerate steps here, wait for user to click generate
  }, []);

  // Handle algorithm controls
  const handleStart = useCallback(() => {
    setState('running');
  }, [setState]);

  const handlePause = useCallback(() => {
    setState('paused');
  }, [setState]);

  const handleReset = useCallback(() => {
    setStep(0);
    setState('idle');
  }, [setStep, setState]);

  const handleStepForward = useCallback(() => {
    if (step < totalSteps - 1) {
      setStep(step + 1);
    }
  }, [step, totalSteps, setStep]);

  const handleStepBackward = useCallback(() => {
    if (step > 0) {
      setStep(step - 1);
    }
  }, [step, setStep]);

  // Pseudocode for Topological Sort algorithm
  const pseudocode = [
    { code: "function TopologicalSort(G):", lineNumber: 1, indent: 0 },
    { code: "// Initialize data structures", lineNumber: 2, indent: 1 },
    { code: "visited = {}", lineNumber: 3, indent: 1 },
    { code: "stack = []", lineNumber: 4, indent: 1 },
    { code: "// For each node in the graph", lineNumber: 5, indent: 1 },
    { code: "for each node in G:", lineNumber: 6, indent: 1 },
    { code: "  if node not in visited:", lineNumber: 7, indent: 1 },
    { code: "    DFS(node, visited, stack)", lineNumber: 8, indent: 1 },
    { code: "", lineNumber: 9, indent: 0 },
    { code: "function DFS(node, visited, stack):", lineNumber: 10, indent: 0 },
    { code: "// Mark node as visited", lineNumber: 11, indent: 1 },
    { code: "visited.add(node)", lineNumber: 12, indent: 1 },
    { code: "// For each neighbor of the node", lineNumber: 13, indent: 1 },
    { code: "for each neighbor of node:", lineNumber: 14, indent: 1 },
    { code: "  if neighbor not in visited:", lineNumber: 15, indent: 1 },
    { code: "    DFS(neighbor, visited, stack)", lineNumber: 16, indent: 1 },
    { code: "// Add node to stack", lineNumber: 17, indent: 1 },
    { code: "stack.push(node)", lineNumber: 18, indent: 1 },
    { code: "", lineNumber: 19, indent: 0 },
    { code: "// Return reversed stack", lineNumber: 20, indent: 0 },
    { code: "return stack.reverse()", lineNumber: 21, indent: 0 },
  ];

  // Get current pseudocode line
  const currentLine = useMemo(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) return 1;
    return steps[step].pseudocodeLine || 1;
  }, [steps, step]);

  return (
    <Box>
      {/* Information Section */}
      <InformationSection title="Topological Sort">
        <Box>
          <Typography variant="body2" paragraph>
            An algorithm for ordering the vertices of a directed acyclic graph (DAG) such that for every directed edge (u, v), vertex u comes before vertex v in the ordering.
          </Typography>
          <Typography variant="body2">
            <strong>Time Complexity:</strong> O(V + E)
          </Typography>
          <Typography variant="body2">
            <strong>Space Complexity:</strong> O(V)
          </Typography>
        </Box>
      </InformationSection>

      {/* Parameters Section */}
      <ParametersSection
        parameters={[
          {
            name: 'Number of Nodes',
            type: 'slider',
            label: 'Number of Nodes',
            value: numNodes,
            onChange: handleNumNodesChange,
            min: 2,
            max: 10,
            step: 1,
            disabled: useCustomGraph || state === 'running',
            icon: GraphIcon
          },
          {
            name: 'Edge Density',
            type: 'slider',
            label: 'Edge Density',
            value: density,
            onChange: handleDensityChange,
            min: 0.1,
            max: 0.9,
            step: 0.1,
            disabled: useCustomGraph || state === 'running',
            icon: ShuffleIcon
          },
          {
            name: 'Use Custom Graph',
            type: 'switch',
            label: 'Use Custom Graph',
            value: useCustomGraph,
            onChange: handleCustomGraphToggle,
            disabled: state === 'running'
          },
          useCustomGraph && {
            name: 'Custom Edges',
            type: 'text',
            label: 'Custom Edges',
            value: customEdges,
            onChange: handleCustomEdgesChange,
            placeholder: "0, 1\n1, 2\n0, 2",
            helperText: "Enter one edge per line in the format: source, target",
            multiline: true,
            rows: 4,
            disabled: state === 'running',
            icon: FormatListNumberedIcon
          },
        ].filter(Boolean)}
        values={{
          'Number of Nodes': numNodes,
          'Edge Density': density,
          'Use Custom Graph': useCustomGraph,
          'Custom Edges': customEdges
        }}
        onChange={(newValues) => {
          if (newValues['Number of Nodes'] !== numNodes) {
            handleNumNodesChange(newValues['Number of Nodes']);
          }
          if (newValues['Edge Density'] !== density) {
            handleDensityChange(newValues['Edge Density']);
          }
          if (newValues['Use Custom Graph'] !== useCustomGraph) {
            handleCustomGraphToggle(newValues['Use Custom Graph']);
          }
          if (newValues['Custom Edges'] !== customEdges) {
            handleCustomEdgesChange(newValues['Custom Edges']);
          }
        }}
        disabled={state === 'running'}
      />

      {/* Controls Section */}
      <ControlsSection
        state={state}
        step={step}
        totalSteps={totalSteps}
        speed={speed}
        setSpeed={setSpeed}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onStepForward={handleStepForward}
        onStepBackward={handleStepBackward}
      />

      {/* Progress Section */}
      <ProgressSection
        step={step}
        totalSteps={totalSteps}
        message={steps && steps[step] ? steps[step].message : ''}
      />

      {/* Steps Sequence Section */}
      <StepsSequenceSection
        steps={(steps || []).map(step => ({
          description: step.message || ''
        }))}
        currentStep={(() => {
          // Adjust the currentStep value to match what StepsSequenceSection expects
          // StepsSequenceSection expects a 1-indexed step value, but our step state is 0-indexed
          // Handle special cases for the last step
          if (step >= steps?.length) {
            // If we've gone beyond the last step, use steps.length
            return steps?.length || 0;
          } else if (step === (steps?.length || 0) - 1) {
            // If we're at the last step, use steps.length
            return steps?.length || 0;
          } else if (step === 0) {
            // If we're at the first step, use 1 (StepsSequenceSection expects 1-indexed)
            return 1;
          } else {
            // Otherwise, add 1 to convert from 0-indexed to 1-indexed
            return step + 1;
          }
        })()}
        onStepClick={setStep}
      />

      {/* Algorithm Section */}
      <AlgorithmSection
        algorithm={pseudocode}
        currentStep={currentLine || 0}
        title="Algorithm"
        defaultExpanded={true}
      />
    </Box>
  );
};

export default TopologicalSortController;
