// HeapSortConfig.js - Comprehensive configuration for Heap Sort visualization
// This file contains ALL configurable aspects of the HeapSort visualization

const HeapSortConfig = {
  // ==================== MAIN ARRAY CONFIGURATION ====================
  mainArray: {
    // Bar dimensions and positioning
    bars: {
      width: 0.6,                    // Width of each main array bar
      spacing: 0.3,                  // Spacing between main array bars
      maxHeight: 3.8,                // Maximum height of main array bars
      baseOffset: [0, 0, 0],         // Offset from base platform position [x, y, z]
      centerAlignment: true,         // Whether to center the array horizontally
      visualOffset: 0.2,             // Visual offset for better balance

      // Bar geometry
      geometry: {
        widthScale: 0.8,             // Width scale factor for main bar
        depthScale: 0.8,             // Depth scale factor for main bar
      },

      // Bar material properties
      material: {
        roughness: 0.7,              // Material roughness
        metalness: 0.2,              // Material metalness
        opacity: 0.9,                // Bar opacity
        transparent: false,          // Enable transparency
      },

      // Base platform under each bar
      base: {
        enabled: true,               // Enable/disable base platform under bars
        height: 0.2,                 // Height of base platform
        widthScale: 1.0,             // Width scale factor for base
        depthScale: 1.0,             // Depth scale factor for base
        material: {
          roughness: 0.8,            // Base material roughness
          metalness: 0.1,            // Base material metalness
          opacity: 0.7,              // Base opacity
          transparent: false,        // Enable transparency
        },
      },
    },

    // Value labels (numbers on top of bars)
    valueLabels: {
      enabled: true,                 // Show/hide value labels
      offset: [0, 0.3, 0],          // Offset from bar top [x, y, z]
      fontSize: '0.6rem',           // Font size for value labels
      fontWeight: 'bold',           // Font weight for value labels
      padding: {
        horizontal: 0.5,             // Horizontal padding inside label
        vertical: 0.1,               // Vertical padding inside label
      },
      borderRadius: 1.5,             // Border radius factor - BOXY STYLE
      minWidth: '16px',              // Minimum width of label container
      elevation: 1,                  // Material-UI elevation
    },

    // Index labels (numbers below bars)
    indexLabels: {
      enabled: true,                 // Show/hide index labels
      offset: [0, 0.1, 0.4],         // Offset from bar base [x, y, z]
      fontSize: '10px',             // Font size for index labels
      fontWeight: 'normal',         // Font weight for index labels
      size: {
        width: '20px',               // Width of circular index label
        height: '20px',              // Height of circular index label
      },
      elevation: 1,                  // Material-UI elevation
    },

    // Heap indicator (special label for heap elements)
    heapIndicator: {
      enabled: true,                 // Show/hide heap indicator
      offset: [0, 0.8, 0],          // Offset from bar top [x, y, z]
      text: 'HEAP',                  // Text to display
      fontSize: '0.5rem',           // Font size for heap indicator
      fontWeight: 'bold',           // Font weight for heap indicator
      padding: {
        horizontal: 0.5,             // Horizontal padding inside indicator
        vertical: 0.1,               // Vertical padding inside indicator
      },
      borderRadius: 0.5,             // Border radius factor
      elevation: 2,                  // Material-UI elevation
    },
  },

  // ==================== BASE PLATFORM CONFIGURATION ====================
  basePlatform: {
    dimensions: {
      height: 0.2,                   // Height of the base platform
      lengthPadding: {
        left: 1,                     // Padding on the left side
        right: 1,                    // Padding on the right side
      },
      depth: 3,                      // Depth of the base platform
    },
    position: [0, -2, 2],            // MASTER position - controls entire main array group [x, y, z]
    material: {
      roughness: 0.8,                // Material roughness
      metalness: 0.1,                // Material metalness
    },
  },

  // ==================== HEAP TREE VISUALIZATION ====================
  heapTree: {
    enabled: true,                   // Enable/disable heap tree visualization
    position: [0, 0, -5],           // Position of heap tree relative to main array

    // Node configuration
    nodes: {
      radius: 0.3,                   // Radius of heap tree nodes
      segments: 16,                  // Number of segments for sphere geometry
      material: {
        roughness: 0.5,              // Material roughness
        metalness: 0.3,              // Material metalness
      },

      // Node spacing
      spacing: {
        horizontal: 2,               // Horizontal spacing between nodes
        vertical: 2,                 // Vertical spacing between levels
      },

      // Value labels on nodes
      valueLabels: {
        enabled: true,               // Show/hide value labels on nodes
        offset: [0, 0.5, 0],        // Offset from node center [x, y, z]
        fontSize: '12px',           // Font size for node labels
        fontWeight: 'bold',         // Font weight for node labels
        color: 'white',             // Text color
        textShadow: '1px 1px 1px rgba(0,0,0,0.5)', // Text shadow
      },
    },

    // Edge configuration
    edges: {
      enabled: true,                 // Show/hide edges between nodes
      color: '#888888',              // Edge color
      linewidth: 1,                  // Edge line width
      material: {
        opacity: 0.7,                // Edge opacity
        transparent: true,           // Enable transparency
      },
    },
  },

  // ==================== CAMERA CONFIGURATION ====================
  camera: {
    position: [0, 5, 15],            // Default camera position [x, y, z]
    lookAt: [0, 0, 0],               // Camera look-at point [x, y, z]
    fov: 55,                         // Field of view in degrees
    near: 0.1,                       // Near clipping plane
    far: 1000,                       // Far clipping plane

    // Dynamic positioning based on array size
    dynamicPositioning: {
      enabled: true,                 // Enable dynamic camera positioning
      minDistance: 10,               // Minimum camera distance
      paddingFactor: 2.5,            // Padding factor for camera distance calculation
      heightOffset: 5,               // Additional height offset
    },
  },

  // ==================== LIGHTING CONFIGURATION ====================
  lighting: {
    ambient: {
      intensity: 0.6,                // Ambient light intensity
      color: '#ffffff',              // Ambient light color
    },
    directional: {
      position: [10, 10, 5],         // Directional light position [x, y, z]
      intensity: 1.0,                // Directional light intensity
      color: '#ffffff',              // Directional light color
      castShadow: true,              // Whether directional light casts shadows
    },
    // Additional lights can be added here
    pointLights: [],                 // Array of point light configurations
    spotLights: [],                  // Array of spot light configurations
  },

  // ==================== UI ELEMENTS CONFIGURATION ====================

  // Step board configuration (shows current step information)
  stepBoard: {
    enabled: true,                   // Enable/disable step board
    position: [0, 5, 0.5],          // Position of the step board [x, y, z]
    dimensions: {
      width: 12,                     // Width of the step board
      height: 1.5,                   // Height of the step board
      depth: 0.1,                    // Depth of the step board
    },
    material: {
      opacity: 0.9,                  // Material opacity
      transparent: true,             // Enable transparency
    },
    text: {
      fontSize: 'h6',                // Material-UI typography variant
      fontWeight: 'bold',            // Font weight
      align: 'center',               // Text alignment
      padding: 2,                    // Padding around text
    },
    border: {
      enabled: true,                 // Enable border
      width: 2,                      // Border width
      radius: 2,                     // Border radius
    },
    elevation: 3,                    // Material-UI elevation
  },

  // Color legend configuration
  colorLegend: {
    enabled: true,                   // Enable/disable color legend
    position: [0, -3.5, 0.5],       // Position of the color legend [x, y, z]
    itemSpacing: 2.5,                // Spacing between legend items

    // Individual legend item configuration
    items: {
      dimensions: {
        width: 0.8,                  // Width of color sample
        height: 0.4,                 // Height of color sample
        depth: 0.2,                  // Depth of color sample
      },
      material: {
        roughness: 0.5,              // Material roughness
        metalness: 0.3,              // Material metalness
      },
      label: {
        fontSize: 'caption',         // Material-UI typography variant
        fontWeight: 'normal',        // Font weight
        offset: [0, -0.8, 0],       // Offset from color sample [x, y, z]
        padding: {
          horizontal: 0.5,           // Horizontal padding
          vertical: 0.2,             // Vertical padding
        },
        borderRadius: 1,             // Border radius factor
        elevation: 1,                // Material-UI elevation
      },
    },

    // Legend items definition
    legendItems: [
      { colorKey: 'bar', label: 'Default' },
      { colorKey: 'comparing', label: 'Comparing Elements' },
      { colorKey: 'swapping', label: 'Swapping Elements' },
      { colorKey: 'heapified', label: 'Heapified Elements' },
      { colorKey: 'sorted', label: 'Sorted Elements' }
    ],
  },

  // ==================== ANIMATION CONFIGURATION ====================
  animation: {
    // Timing configuration
    timing: {
      baseDuration: 500,             // Base animation duration in ms
      speedFactor: 1.0,              // Speed multiplier (higher = faster)

      // Speed-based delays (calculated dynamically)
      delays: {
        slow: 2000,                  // Delay for slow speed (1-3)
        medium: 1000,                // Delay for medium speed (4-7)
        fast: 400,                   // Delay for fast speed (8-10)
      },
    },

    // Easing functions
    easing: {
      default: 'ease-in-out',        // Default easing function
      bars: 'ease-out',              // Easing for bar movements
      labels: 'ease-in-out',         // Easing for label animations
      camera: 'ease-in-out',         // Easing for camera movements
    },

    // Animation types
    types: {
      swap: {
        enabled: true,               // Enable swap animations for HeapSort
        duration: 800,               // Duration for swap animations
        height: 1.5,                 // Height of swap arc
      },
      highlight: {
        enabled: true,               // Enable highlight animations
        duration: 300,               // Duration for highlight changes
        pulseEffect: true,           // Enable pulse effect for highlights
      },
      comparison: {
        enabled: true,               // Enable comparison animations
        duration: 500,               // Duration for comparison highlights
        arrowEnabled: false,         // Disable arrows for HeapSort
      },
      heapify: {
        enabled: true,               // Enable heapify animations
        duration: 600,               // Duration for heapify operations
        cascadeEffect: true,         // Enable cascade effect for heapify
      },
    },
  },

  // ==================== VISUAL SETTINGS ====================
  visual: {
    // Label visibility
    labels: {
      values: {
        enabled: true,               // Show value labels on bars
        adaptiveVisibility: false,   // Disable adaptive visibility temporarily
        visibilityThreshold: 0.0,    // Hide values when scale factor is below this
      },
      indices: {
        enabled: true,               // Show index labels on bars
        adaptiveVisibility: true,    // Hide labels for small bars
        visibilityThreshold: 0.0,    // Hide indices when scale factor is below this
      },
    },

    // Effects
    effects: {
      shadows: true,                 // Enable/disable shadows
      reflections: false,            // Enable/disable reflections
      bloom: false,                  // Enable/disable bloom effect
      antialiasing: true,            // Enable/disable antialiasing
    },

    // Levitation animation
    levitation: {
      enabled: true,                 // Enable/disable levitation animation
      disableDuringSimulation: true, // Disable levitation when algorithm is running
      amplitude: 0.1,                // Height of levitation movement (Y-axis)
      frequency: 1.0,                // Speed of levitation oscillation

      // Multi-axis movement
      movement: {
        y: {
          enabled: true,             // Enable Y-axis (vertical) movement
          amplitude: 0.1,            // Vertical movement amplitude
          frequency: 1.0,            // Vertical movement frequency
        },
        x: {
          enabled: false,            // Enable X-axis (horizontal) movement
          amplitude: 0.05,           // Horizontal movement amplitude
          frequency: 0.8,            // Horizontal movement frequency
        },
        z: {
          enabled: false,            // Enable Z-axis (depth) movement
          amplitude: 0.03,           // Depth movement amplitude
          frequency: 0.6,            // Depth movement frequency
        },
      },

      // Rotation effects
      rotation: {
        enabled: true,               // Enable rotation during levitation
        x: {
          enabled: true,             // Enable X-axis rotation
          amplitude: 0.01,           // X rotation amplitude (radians)
          frequency: 0.3,            // X rotation frequency
        },
        y: {
          enabled: true,             // Enable Y-axis rotation
          amplitude: 0.02,           // Y rotation amplitude (radians)
          frequency: 0.5,            // Y rotation frequency
        },
        z: {
          enabled: false,            // Enable Z-axis rotation
          amplitude: 0.005,          // Z rotation amplitude (radians)
          frequency: 0.4,            // Z rotation frequency
        },
      },

      // Advanced settings
      staggered: true,               // Enable staggered animation effects
      staggerDelay: 0.2,             // Delay between elements for staggered effect
      smoothTransition: true,        // Smooth transition when enabling/disabling
      transitionDuration: 1000,      // Duration for smooth transitions (ms)
    },
  },

  // ==================== COLOR CONFIGURATION ====================
  colors: {
    // Main color palette (can be overridden by theme)
    palette: {
      // Primary colors for different states
      default: '#2196f3',            // Default bar color (blue)
      comparing: '#ff9800',          // Comparing elements (orange)
      swapping: '#f44336',           // Swapping elements (red)
      sorted: '#4caf50',             // Sorted elements (green)
      heapified: '#9c27b0',          // Heapified elements (purple)

      // UI element colors
      base: '#e0e0e0',               // Base platform color
      ground: '#f5f5f5',             // Ground color
      placeholder: '#bdbdbd',        // Placeholder elements

      // Border and accent colors
      border: '#cccccc',             // Default border color
      accent: '#1976d2',             // Accent color for highlights

      // Background colors
      background: {
        primary: '#ffffff',          // Primary background
        secondary: '#f5f5f5',        // Secondary background
        paper: '#ffffff',            // Paper background
      },

      // Text colors
      text: {
        primary: '#212121',          // Primary text
        secondary: '#757575',        // Secondary text
        disabled: '#bdbdbd',         // Disabled text
      },
    },

    // Step-specific color mappings
    stepMappings: {
      init: 'default',               // Initial state
      compare: 'comparing',          // Element comparison
      swap: 'swapping',              // Element swapping
      swapped: 'swapping',           // After swap
      heapified: 'heapified',        // Heapified elements
      sorted: 'sorted',              // Sorted position
      complete: 'sorted',            // Algorithm complete
      info: 'default',               // Information step
    },

    // Theme-specific overrides
    themes: {
      light: {
        // Light theme color overrides
        base: '#f0f0f0',
        ground: '#fafafa',
        background: {
          primary: '#ffffff',
          secondary: '#f5f5f5',
          paper: '#ffffff',
        },
        text: {
          primary: '#212121',
          secondary: '#757575',
          disabled: '#bdbdbd',
        },
        border: '#e0e0e0',
      },
      dark: {
        // Dark theme color overrides
        base: '#424242',
        ground: '#303030',
        background: {
          primary: '#121212',
          secondary: '#1e1e1e',
          paper: '#1e1e1e',
        },
        text: {
          primary: '#ffffff',
          secondary: '#b0b0b0',
          disabled: '#666666',
        },
        border: '#555555',
      },
    },
  },

  // ==================== RESPONSIVE DESIGN CONFIGURATION ====================
  responsive: {
    // Breakpoints for different screen sizes
    breakpoints: {
      arraySize: {
        small: 8,                    // Arrays <= 8 elements
        medium: 15,                  // Arrays 9-15 elements
        large: 20,                   // Arrays 16+ elements
      },
      viewport: {
        mobile: 768,                 // Mobile viewport width
        tablet: 1024,                // Tablet viewport width
        desktop: 1200,               // Desktop viewport width
      },
    },

    // Scale factors based on array size
    scaleFactors: {
      small: 1.0,                    // Arrays <= 8 elements
      medium: 0.8,                   // Arrays 9-15 elements
      large: 0.6,                    // Arrays 16+ elements
    },

    // Adaptive settings
    adaptive: {
      // Camera positioning
      camera: {
        enabled: true,               // Enable adaptive camera positioning
        minDistance: 10,             // Minimum camera distance
        maxDistance: 25,             // Maximum camera distance
        scaleFactor: 0.03,           // Scale factor for distance calculation
      },

      // Label visibility
      labels: {
        enabled: true,               // Enable adaptive label visibility
        scaleThreshold: 0.5,         // Hide labels below this scale factor
        fadeTransition: true,        // Enable fade transition for labels
      },

      // Bar dimensions
      bars: {
        enabled: true,               // Enable adaptive bar sizing
        minWidth: 0.4,               // Minimum bar width
        maxWidth: 1.0,               // Maximum bar width
        minSpacing: 0.2,             // Minimum bar spacing
        maxSpacing: 1.0,             // Maximum bar spacing
      },
    },
  },

  // ==================== PERFORMANCE CONFIGURATION ====================
  performance: {
    // Array size limits
    arraySize: {
      min: 3,                        // Minimum array size
      max: 20,                       // Maximum array size
      default: 10,                   // Default array size
      recommended: 15,               // Recommended maximum for smooth performance
    },

    // Array value limits
    arrayValues: {
      min: 1,                        // Minimum value in generated arrays
      max: 999,                      // Maximum value in generated arrays
      default: {
        min: 1,                      // Default minimum for random generation
        max: 99,                     // Default maximum for random generation
      },
    },

    // Rendering optimizations
    rendering: {
      frustumCulling: true,          // Enable frustum culling
      levelOfDetail: false,          // Enable level of detail (LOD)
      instancedRendering: false,     // Enable instanced rendering for bars
      batchRendering: true,          // Enable batch rendering
    },

    // Animation optimizations
    animation: {
      maxConcurrent: 3,              // Maximum concurrent animations
      useRequestAnimationFrame: true, // Use requestAnimationFrame for smooth animations
      throttleUpdates: false,        // Throttle update frequency
      updateFrequency: 60,           // Target update frequency (FPS)
    },

    // Memory management
    memory: {
      disposeUnusedMaterials: true,  // Dispose unused materials
      disposeUnusedGeometries: true, // Dispose unused geometries
      garbageCollectionInterval: 30000, // Garbage collection interval (ms)
    },
  },

  // ==================== ALGORITHM-SPECIFIC SETTINGS ====================
  algorithm: {
    // Array generation settings
    arrayGeneration: {
      randomValues: {
        min: 1,                      // Minimum value for random generation
        max: 999,                    // Maximum value for random generation
        allowDuplicates: true,       // Allow duplicate values in random arrays
        strategy: 'uniform',         // 'uniform', 'gaussian', 'exponential'
      },
      reverseArray: {
        useForWorstCase: false,      // Use reverse sorted array for worst case
        startValue: null,            // Starting value (null = use array size)
      },
      sequentialArray: {
        startValue: 1,               // Starting value for sequential arrays
        increment: 1,                // Increment between values
      },
      validation: {
        enforceRange: true,          // Enforce min/max range for custom arrays
        warnOnLargeValues: true,     // Warn when values are very large
        largeValueThreshold: 1000,   // Threshold for large value warning
      },
    },

    // Heap configuration
    heap: {
      type: 'max',                   // 'max' or 'min' heap
      showTree: true,                // Show heap tree visualization
      highlightParentChild: true,    // Highlight parent-child relationships
    },

    // Step generation
    steps: {
      showIntermediateSteps: true,   // Show all intermediate steps
      showComparisons: true,         // Show comparison steps
      showHeapifySteps: true,        // Show heapify operation steps
      combineOperations: false,      // Combine consecutive operations
    },

    // Visualization features
    features: {
      highlightHeapified: true,      // Highlight heapified elements
      showHeapProperty: true,        // Show heap property validation
      animateHeapify: true,          // Animate heapify operations
      showTreeStructure: true,       // Show tree structure alongside array
    },
  },

  // ==================== ACCESSIBILITY CONFIGURATION ====================
  accessibility: {
    // High contrast mode
    highContrast: {
      enabled: false,                // Enable high contrast mode
      colorMultiplier: 1.5,          // Color contrast multiplier
    },

    // Reduced motion
    reducedMotion: {
      enabled: false,                // Enable reduced motion mode
      disableAnimations: false,      // Disable all animations
      simplifyTransitions: true,     // Simplify transitions
    },

    // Screen reader support
    screenReader: {
      enabled: true,                 // Enable screen reader support
      announceSteps: true,           // Announce algorithm steps
      describeVisualization: true,   // Describe visualization state
    },

    // Keyboard navigation
    keyboard: {
      enabled: true,                 // Enable keyboard navigation
      shortcuts: {
        play: 'Space',               // Play/pause shortcut
        stepForward: 'ArrowRight',   // Step forward shortcut
        stepBackward: 'ArrowLeft',   // Step backward shortcut
        reset: 'R',                  // Reset shortcut
      },
    },
  },

  // ==================== DEBUG CONFIGURATION ====================
  debug: {
    enabled: false,                  // Enable debug mode
    showBoundingBoxes: false,        // Show bounding boxes
    showCoordinates: false,          // Show coordinate system
    logSteps: false,                 // Log algorithm steps to console
    showPerformanceStats: false,     // Show performance statistics
    wireframeMode: false,            // Enable wireframe mode
  },
};

export default HeapSortConfig;
