// Arrow.js
// A reusable 3D arrow component for visualizations

import React, { useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { Html } from '@react-three/drei';

/**
 * A 3D arrow component for visualizations
 *
 * @param {Object} props - Component props
 * @param {Array} props.start - Start position [x, y, z]
 * @param {Array} props.end - End position [x, y, z]
 * @param {string} props.color - Arrow color
 * @param {number} props.thickness - Arrow shaft thickness
 * @param {number} props.headSize - Arrow head size
 * @param {string} props.label - Optional label for the arrow
 * @param {boolean} props.showLabel - Whether to show the label
 */
const Arrow = ({
  start = [0, 0, 0],
  end = [1, 0, 0],
  color = '#ff0000',
  thickness = 0.1,
  headSize = 0.3,
  label = '',
  showLabel = false
}) => {
  const { camera } = useThree();

  // Calculate direction and length
  const direction = useMemo(() => {
    const dir = new THREE.Vector3(
      end[0] - start[0],
      end[1] - start[1],
      end[2] - start[2]
    );
    return dir;
  }, [start, end]);

  const length = useMemo(() => direction.length(), [direction]);

  // Calculate midpoint for label
  const midpoint = useMemo(() => [
    start[0] + direction.x / 2,
    start[1] + direction.y / 2 + 0.3, // Offset the label slightly above the arrow
    start[2] + direction.z / 2
  ], [start, direction]);

  // Create arrow geometry
  const arrowGeometry = useMemo(() => {
    // Create a cylinder for the shaft
    const shaftGeometry = new THREE.CylinderGeometry(
      thickness / 2,
      thickness / 2,
      length - headSize,
      8
    );

    // Create a cone for the head
    const headGeometry = new THREE.ConeGeometry(
      headSize,
      headSize,
      8
    );

    // Position the head at the end of the shaft
    headGeometry.translate(0, (length - headSize) / 2 + headSize / 2, 0);

    // Merge geometries
    const mergedGeometry = new THREE.BufferGeometry();

    // Clone the geometries to avoid modifying the originals
    const shaftGeo = shaftGeometry.clone();
    const headGeo = headGeometry.clone();

    // Position the shaft
    shaftGeo.translate(0, 0, 0);

    // Combine the geometries
    const shaftPositions = shaftGeo.getAttribute('position').array;
    const headPositions = headGeo.getAttribute('position').array;

    // Create a new array to hold all positions
    const positions = new Float32Array(shaftPositions.length + headPositions.length);

    // Copy positions from shaft and head
    positions.set(shaftPositions, 0);
    positions.set(headPositions, shaftPositions.length);

    // Set the positions attribute
    mergedGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    return mergedGeometry;
  }, [length, thickness, headSize]);

  // Calculate rotation to point from start to end
  const rotation = useMemo(() => {
    // Default orientation is along the Y axis
    const defaultDirection = new THREE.Vector3(0, 1, 0);

    // Normalize the direction
    const normalizedDirection = direction.clone().normalize();

    // Calculate the rotation quaternion
    const quaternion = new THREE.Quaternion();
    quaternion.setFromUnitVectors(defaultDirection, normalizedDirection);

    // Convert quaternion to Euler angles
    const euler = new THREE.Euler();
    euler.setFromQuaternion(quaternion);

    return [euler.x, euler.y, euler.z];
  }, [direction]);

  return (
    <group>
      {/* Arrow mesh */}
      <mesh
        position={[
          start[0] + direction.x / 2,
          start[1] + direction.y / 2,
          start[2] + direction.z / 2
        ]}
        rotation={rotation}
      >
        <primitive object={arrowGeometry} attach="geometry" />
        <meshStandardMaterial color={color} />
      </mesh>

      {/* Label */}
      {showLabel && label && (
        <Html
          position={midpoint}
          center
          style={{
            color: color,
            fontSize: '14px',
            fontWeight: 'bold',
            padding: '2px 6px',
            backgroundColor: 'rgba(0,0,0,0.7)',
            borderRadius: '4px',
            whiteSpace: 'nowrap',
          }}
        >
          {label}
        </Html>
      )}
    </group>
  );
};

export default Arrow;
