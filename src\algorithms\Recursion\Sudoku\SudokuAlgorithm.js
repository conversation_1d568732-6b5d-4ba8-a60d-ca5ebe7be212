// SudokuAlgorithm.js
// Implementation of Sudoku solver algorithm using backtracking

import React from 'react';
import { useTheme } from '@mui/material/styles';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { Box, Typography } from '@mui/material';

/**
 * Generate steps for solving a Sudoku puzzle using backtracking
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
export const generateSudokuSteps = (params) => {
  console.log('generateSudokuSteps called with params:', params);
  const { board = getDefaultBoard(), difficulty = 'medium' } = params;
  const steps = [];
  const solutions = [];

  // Create a deep copy of the board to avoid modifying the original
  const boardCopy = board.map(row => [...row]);

  // Add initial step
  steps.push({
    type: 'init',
    message: 'Initialize Sudoku solver with the given board.',
    board: boardCopy,
    currentRow: -1,
    currentCol: -1,
    currentValue: null,
    isValid: true,
    solutions: [],
    progressStep: 'init',
    pseudocodeLine: 1
  });

  // Solve the Sudoku puzzle
  solveSudoku(boardCopy, 0, 0, steps, solutions);

  // Add final step
  if (solutions.length > 0) {
    steps.push({
      type: 'complete',
      message: 'Sudoku puzzle solved successfully!',
      board: solutions[0],
      currentRow: -1,
      currentCol: -1,
      currentValue: null,
      isValid: true,
      solutions: solutions,
      progressStep: 'complete',
      pseudocodeLine: 0
    });
  } else {
    steps.push({
      type: 'no_solution',
      message: 'No solution exists for this Sudoku puzzle.',
      board: boardCopy,
      currentRow: -1,
      currentCol: -1,
      currentValue: null,
      isValid: false,
      solutions: [],
      progressStep: 'complete',
      pseudocodeLine: 0
    });
  }

  return { steps, solutions };
};

/**
 * Solve the Sudoku puzzle using backtracking
 * @param {Array} board - The Sudoku board
 * @param {Number} row - Current row
 * @param {Number} col - Current column
 * @param {Array} steps - Array to store steps
 * @param {Array} solutions - Array to store solutions
 * @returns {Boolean} - Whether a solution was found
 */
const solveSudoku = (board, row, col, steps, solutions) => {
  // If we've gone through all rows, we've found a solution
  if (row === 9) {
    // Add the solution to the solutions array
    solutions.push(board.map(row => [...row]));
    return true;
  }

  // If we've gone through all columns in this row, move to the next row
  if (col === 9) {
    return solveSudoku(board, row + 1, 0, steps, solutions);
  }

  // If this cell is already filled, move to the next cell
  if (board[row][col] !== 0) {
    steps.push({
      type: 'skip',
      message: `Cell (${row + 1}, ${col + 1}) is already filled with ${board[row][col]}. Moving to the next cell.`,
      board: board.map(row => [...row]),
      currentRow: row,
      currentCol: col,
      currentValue: board[row][col],
      isValid: true,
      solutions: [],
      progressStep: 'process',
      pseudocodeLine: 5
    });
    return solveSudoku(board, row, col + 1, steps, solutions);
  }

  // Try each number from 1 to 9
  for (let num = 1; num <= 9; num++) {
    // Check if it's valid to place this number
    if (isValid(board, row, col, num)) {
      // Place the number
      board[row][col] = num;
      
      steps.push({
        type: 'place',
        message: `Placing ${num} at cell (${row + 1}, ${col + 1}).`,
        board: board.map(row => [...row]),
        currentRow: row,
        currentCol: col,
        currentValue: num,
        isValid: true,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 9
      });

      // Recursively solve the rest of the puzzle
      if (solveSudoku(board, row, col + 1, steps, solutions)) {
        return true;
      }

      // If we get here, this number didn't work, so backtrack
      board[row][col] = 0;
      
      steps.push({
        type: 'backtrack',
        message: `${num} at cell (${row + 1}, ${col + 1}) leads to an invalid solution. Backtracking.`,
        board: board.map(row => [...row]),
        currentRow: row,
        currentCol: col,
        currentValue: 0,
        isValid: false,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 14
      });
    } else {
      steps.push({
        type: 'invalid',
        message: `Cannot place ${num} at cell (${row + 1}, ${col + 1}) as it violates Sudoku rules.`,
        board: board.map(row => [...row]),
        currentRow: row,
        currentCol: col,
        currentValue: num,
        isValid: false,
        solutions: [],
        progressStep: 'process',
        pseudocodeLine: 7
      });
    }
  }

  // If we've tried all numbers and none worked, return false
  return false;
};

/**
 * Check if it's valid to place a number at a given position
 * @param {Array} board - The Sudoku board
 * @param {Number} row - Row index
 * @param {Number} col - Column index
 * @param {Number} num - Number to check
 * @returns {Boolean} - Whether the placement is valid
 */
const isValid = (board, row, col, num) => {
  // Check row
  for (let x = 0; x < 9; x++) {
    if (board[row][x] === num) {
      return false;
    }
  }

  // Check column
  for (let x = 0; x < 9; x++) {
    if (board[x][col] === num) {
      return false;
    }
  }

  // Check 3x3 box
  const boxRow = Math.floor(row / 3) * 3;
  const boxCol = Math.floor(col / 3) * 3;
  for (let i = 0; i < 3; i++) {
    for (let j = 0; j < 3; j++) {
      if (board[boxRow + i][boxCol + j] === num) {
        return false;
      }
    }
  }

  return true;
};

/**
 * Get a default Sudoku board
 * @returns {Array} - Default Sudoku board
 */
export const getDefaultBoard = () => {
  return [
    [5, 3, 0, 0, 7, 0, 0, 0, 0],
    [6, 0, 0, 1, 9, 5, 0, 0, 0],
    [0, 9, 8, 0, 0, 0, 0, 6, 0],
    [8, 0, 0, 0, 6, 0, 0, 0, 3],
    [4, 0, 0, 8, 0, 3, 0, 0, 1],
    [7, 0, 0, 0, 2, 0, 0, 0, 6],
    [0, 6, 0, 0, 0, 0, 2, 8, 0],
    [0, 0, 0, 4, 1, 9, 0, 0, 5],
    [0, 0, 0, 0, 8, 0, 0, 7, 9]
  ];
};

/**
 * Get a Sudoku board based on difficulty
 * @param {String} difficulty - Difficulty level (easy, medium, hard)
 * @returns {Array} - Sudoku board
 */
export const getBoardByDifficulty = (difficulty) => {
  switch (difficulty) {
    case 'easy':
      return [
        [5, 3, 0, 0, 7, 0, 0, 0, 0],
        [6, 0, 0, 1, 9, 5, 0, 0, 0],
        [0, 9, 8, 0, 0, 0, 0, 6, 0],
        [8, 0, 0, 0, 6, 0, 0, 0, 3],
        [4, 0, 0, 8, 0, 3, 0, 0, 1],
        [7, 0, 0, 0, 2, 0, 0, 0, 6],
        [0, 6, 0, 0, 0, 0, 2, 8, 0],
        [0, 0, 0, 4, 1, 9, 0, 0, 5],
        [0, 0, 0, 0, 8, 0, 0, 7, 9]
      ];
    case 'medium':
      return [
        [0, 0, 0, 2, 6, 0, 7, 0, 1],
        [6, 8, 0, 0, 7, 0, 0, 9, 0],
        [1, 9, 0, 0, 0, 4, 5, 0, 0],
        [8, 2, 0, 1, 0, 0, 0, 4, 0],
        [0, 0, 4, 6, 0, 2, 9, 0, 0],
        [0, 5, 0, 0, 0, 3, 0, 2, 8],
        [0, 0, 9, 3, 0, 0, 0, 7, 4],
        [0, 4, 0, 0, 5, 0, 0, 3, 6],
        [7, 0, 3, 0, 1, 8, 0, 0, 0]
      ];
    case 'hard':
      return [
        [0, 2, 0, 6, 0, 8, 0, 0, 0],
        [5, 8, 0, 0, 0, 9, 7, 0, 0],
        [0, 0, 0, 0, 4, 0, 0, 0, 0],
        [3, 7, 0, 0, 0, 0, 5, 0, 0],
        [6, 0, 0, 0, 0, 0, 0, 0, 4],
        [0, 0, 8, 0, 0, 0, 0, 1, 3],
        [0, 0, 0, 0, 2, 0, 0, 0, 0],
        [0, 0, 9, 8, 0, 0, 0, 3, 6],
        [0, 0, 0, 3, 0, 6, 0, 9, 0]
      ];
    default:
      return getDefaultBoard();
  }
};

// Get theme-aware syntax highlighting colors
const getSyntaxColors = (theme) => {
  const isDark = theme.palette.mode === 'dark';
  
  return {
    keyword: isDark ? '#ff79c6' : '#d73a49',
    function: isDark ? '#50fa7b' : '#6f42c1',
    comment: isDark ? '#6272a4' : '#6a737d',
    string: isDark ? '#f1fa8c' : '#032f62',
    variable: isDark ? '#bd93f9' : '#e36209',
    number: isDark ? '#bd93f9' : '#005cc5',
    operator: isDark ? '#ff79c6' : '#d73a49',
    punctuation: isDark ? '#f8f8f2' : '#24292e',
    
    // UI colors
    text: theme.palette.text.primary,
    background: theme.palette.background.paper,
    highlight: isDark ? "rgba(38, 79, 120, 0.3)" : "rgba(173, 214, 255, 0.3)",
    border: theme.palette.divider,
    stepBackground: isDark ? "rgba(38, 79, 120, 0.2)" : "rgba(173, 214, 255, 0.2)",
  };
};

const SudokuAlgorithm = (props) => {
  // Destructure props
  const { step = 0 } = props;
  
  // Get the steps from the AlgorithmContext
  const { steps: algorithmSteps } = useAlgorithm();

  // Get the current theme
  const theme = useTheme();

  // Get theme-aware syntax highlighting colors
  const colors = getSyntaxColors(theme);

  // Get the current algorithm step
  const currentAlgorithmStep = step > 0 && algorithmSteps && algorithmSteps.length >= step
    ? algorithmSteps[step - 1]
    : null;

  // Pseudocode for Sudoku solver
  const pseudocode = [
    { line: 'function solveSudoku(board, row, col):', highlight: currentAlgorithmStep?.pseudocodeLine === 1 },
    { line: '    // If we\'ve gone through all rows, we\'ve found a solution', highlight: currentAlgorithmStep?.pseudocodeLine === 2 },
    { line: '    if row == 9 return true', highlight: currentAlgorithmStep?.pseudocodeLine === 3 },
    { line: '    // If we\'ve gone through all columns in this row, move to the next row', highlight: currentAlgorithmStep?.pseudocodeLine === 4 },
    { line: '    if col == 9 return solveSudoku(board, row + 1, 0)', highlight: currentAlgorithmStep?.pseudocodeLine === 5 },
    { line: '    // If this cell is already filled, move to the next cell', highlight: currentAlgorithmStep?.pseudocodeLine === 6 },
    { line: '    if board[row][col] != 0 return solveSudoku(board, row, col + 1)', highlight: currentAlgorithmStep?.pseudocodeLine === 7 },
    { line: '    // Try each number from 1 to 9', highlight: currentAlgorithmStep?.pseudocodeLine === 8 },
    { line: '    for num = 1 to 9:', highlight: currentAlgorithmStep?.pseudocodeLine === 9 },
    { line: '        // Check if it\'s valid to place this number', highlight: currentAlgorithmStep?.pseudocodeLine === 10 },
    { line: '        if isValid(board, row, col, num):', highlight: currentAlgorithmStep?.pseudocodeLine === 11 },
    { line: '            // Place the number', highlight: currentAlgorithmStep?.pseudocodeLine === 12 },
    { line: '            board[row][col] = num', highlight: currentAlgorithmStep?.pseudocodeLine === 13 },
    { line: '            // Recursively solve the rest of the puzzle', highlight: currentAlgorithmStep?.pseudocodeLine === 14 },
    { line: '            if solveSudoku(board, row, col + 1) return true', highlight: currentAlgorithmStep?.pseudocodeLine === 15 },
    { line: '            // If we get here, this number didn\'t work, so backtrack', highlight: currentAlgorithmStep?.pseudocodeLine === 16 },
    { line: '            board[row][col] = 0', highlight: currentAlgorithmStep?.pseudocodeLine === 17 },
    { line: '    // If we\'ve tried all numbers and none worked, return false', highlight: currentAlgorithmStep?.pseudocodeLine === 18 },
    { line: '    return false', highlight: currentAlgorithmStep?.pseudocodeLine === 19 },
    { line: '', highlight: false },
    { line: 'function isValid(board, row, col, num):', highlight: currentAlgorithmStep?.pseudocodeLine === 20 },
    { line: '    // Check row', highlight: currentAlgorithmStep?.pseudocodeLine === 21 },
    { line: '    for x = 0 to 8:', highlight: currentAlgorithmStep?.pseudocodeLine === 22 },
    { line: '        if board[row][x] == num return false', highlight: currentAlgorithmStep?.pseudocodeLine === 23 },
    { line: '    // Check column', highlight: currentAlgorithmStep?.pseudocodeLine === 24 },
    { line: '    for x = 0 to 8:', highlight: currentAlgorithmStep?.pseudocodeLine === 25 },
    { line: '        if board[x][col] == num return false', highlight: currentAlgorithmStep?.pseudocodeLine === 26 },
    { line: '    // Check 3x3 box', highlight: currentAlgorithmStep?.pseudocodeLine === 27 },
    { line: '    boxRow = Math.floor(row / 3) * 3', highlight: currentAlgorithmStep?.pseudocodeLine === 28 },
    { line: '    boxCol = Math.floor(col / 3) * 3', highlight: currentAlgorithmStep?.pseudocodeLine === 29 },
    { line: '    for i = 0 to 2:', highlight: currentAlgorithmStep?.pseudocodeLine === 30 },
    { line: '        for j = 0 to 2:', highlight: currentAlgorithmStep?.pseudocodeLine === 31 },
    { line: '            if board[boxRow + i][boxCol + j] == num return false', highlight: currentAlgorithmStep?.pseudocodeLine === 32 },
    { line: '    return true', highlight: currentAlgorithmStep?.pseudocodeLine === 33 },
  ];

  return (
    <Box sx={{ 
      p: 2, 
      backgroundColor: colors.background,
      borderRadius: 1,
      border: `1px solid ${colors.border}`,
      fontFamily: 'monospace',
      fontSize: '0.9rem',
      overflow: 'auto',
      maxHeight: '400px'
    }}>
      {pseudocode.map((line, index) => (
        <Box 
          key={index}
          sx={{ 
            py: 0.5,
            backgroundColor: line.highlight ? colors.highlight : 'transparent',
            borderRadius: 1,
            display: 'flex'
          }}
        >
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              minWidth: '30px',
              textAlign: 'right',
              mr: 2,
              color: colors.comment
            }}
          >
            {index + 1}
          </Typography>
          <Typography 
            variant="body2" 
            component="span"
            sx={{ 
              color: colors.text,
              fontFamily: 'monospace',
              whiteSpace: 'pre'
            }}
          >
            {line.line}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default SudokuAlgorithm;
