// InsertionSort/index.js
// Export only what's needed from this algorithm

import InsertionSortVisualization from './InsertionSortVisualization';
import InsertionSortController from './InsertionSortController';
import InsertionSortAlgorithm from './InsertionSortAlgorithm';

export const metadata = {
  id: 'InsertionSort',
  name: 'Insertion Sort',
  description: 'A simple sorting algorithm that builds the final sorted array one item at a time by comparing each element with the already-sorted portion of the array.',
  timeComplexity: 'O(n²) worst and average case, O(n) best case',
  spaceComplexity: 'O(1)',
  defaultParams: {
    arraySize: 10,
    randomize: true,
    customArray: [],
  },
};

export const components = {
  visualization: InsertionSortVisualization,
  controller: InsertionSortController,
  algorithm: InsertionSortAlgorithm,
};

export default {
  ...metadata,
  ...components,
};
