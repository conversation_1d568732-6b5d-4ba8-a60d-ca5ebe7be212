// Button.js
// A reusable button component with consistent styling

import React from 'react';
import { Button as MuiButton, Tooltip } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * A reusable button component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.label - Button text
 * @param {function} props.onClick - Click handler
 * @param {string} props.variant - Button variant (contained, outlined, text)
 * @param {string} props.color - Button color (primary, secondary, error, etc.)
 * @param {string} props.size - Button size (small, medium, large)
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.tooltip - Tooltip text
 * @param {React.ReactNode} props.startIcon - Icon to display at the start of the button
 * @param {React.ReactNode} props.endIcon - Icon to display at the end of the button
 * @param {Object} props.sx - Additional styles
 */
const Button = ({
  label,
  onClick,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  disabled = false,
  tooltip = '',
  startIcon,
  endIcon,
  sx = {},
  ...rest
}) => {
  const button = (
    <MuiButton
      variant={variant}
      color={color}
      size={size}
      disabled={disabled}
      onClick={onClick}
      startIcon={startIcon}
      endIcon={endIcon}
      sx={{
        borderRadius: 2,
        textTransform: 'none',
        fontWeight: 'medium',
        ...sx
      }}
      {...rest}
    >
      {label}
    </MuiButton>
  );

  // Wrap with tooltip if provided
  if (tooltip) {
    return (
      <Tooltip title={tooltip} arrow>
        {button}
      </Tooltip>
    );
  }

  return button;
};

Button.propTypes = {
  label: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  variant: PropTypes.oneOf(['contained', 'outlined', 'text']),
  color: PropTypes.string,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  tooltip: PropTypes.string,
  startIcon: PropTypes.node,
  endIcon: PropTypes.node,
  sx: PropTypes.object
};

export default Button;
