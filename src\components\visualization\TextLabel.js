import React from 'react';
import { Html } from '@react-three/drei';
import { useTheme } from '@mui/material/styles';

/**
 * Reusable text label component for algorithm visualizations
 * 
 * @param {Object} props - Component props
 * @param {string} props.text - Text to display
 * @param {Array} props.position - [x, y, z] position of the text
 * @param {string} props.color - Text color
 * @param {string} props.backgroundColor - Background color
 * @param {number} props.size - Size of the text
 * @param {boolean} props.visible - Whether the text is visible
 * @param {string} props.fontWeight - Font weight (normal, bold, etc.)
 * @param {boolean} props.showBackground - Whether to show a background
 * @param {number} props.padding - Padding around the text
 * @param {number} props.borderRadius - Border radius of the background
 * @returns {JSX.Element} - The rendered text label component
 */
const TextLabel = ({ 
    text, 
    position, 
    color = null, 
    backgroundColor = null, 
    size = 1, 
    visible = true,
    fontWeight = 'bold',
    showBackground = true,
    padding = [2, 6],
    borderRadius = 6
}) => {
    const theme = useTheme();
    
    // Use theme-based colors if no specific colors are provided
    const textColor = color || theme.palette.text.primary;
    const bgColor = backgroundColor || 
        (theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.6)' : 'rgba(255,255,255,0.8)');
    
    // Calculate font size based on the size prop
    const fontSize = `${0.7 * size}rem`;
    
    // Calculate padding based on the padding prop
    const paddingValue = Array.isArray(padding) 
        ? `${padding[0]}px ${padding[1]}px` 
        : `${padding}px`;
    
    return (
        <Html
            position={position}
            center
            style={{ display: visible ? 'block' : 'none' }}
        >
            <div style={{
                color: textColor,
                background: showBackground ? bgColor : 'transparent',
                padding: showBackground ? paddingValue : '0',
                borderRadius: `${borderRadius}px`,
                fontSize: fontSize,
                fontWeight: fontWeight,
                textAlign: 'center',
                whiteSpace: 'nowrap',
                boxShadow: showBackground ? 
                    `0 2px 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)'}`
                    : 'none',
                border: showBackground ? 
                    `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`
                    : 'none',
                pointerEvents: 'none',
                transform: `scale(${size})`,
                transformOrigin: 'center center'
            }}>
                {text}
            </div>
        </Html>
    );
};

export default TextLabel;
