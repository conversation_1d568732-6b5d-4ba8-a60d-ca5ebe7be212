// RodCuttingAlgorithm.js
// Implementation of Rod Cutting algorithm using dynamic programming

/**
 * Generate steps for solving the Rod Cutting problem using dynamic programming
 * @param {Object} params - Algorithm parameters
 * @returns {Object} - Object containing steps and result
 */
const generateRodCuttingSteps = (params) => {
  console.log('generateRodCuttingSteps called with params:', params);
  const { prices = [0, 1, 5, 8, 9, 10, 17, 17, 20, 24, 30], rodLength = 8 } = params;
  const steps = [];

  // Validate input
  if (!Array.isArray(prices) || prices.length === 0) {
    throw new Error('Prices must be a non-empty array');
  }
  if (typeof rodLength !== 'number' || rodLength < 0 || rodLength >= prices.length) {
    throw new Error('Rod length must be a non-negative number less than the prices array length');
  }

  // Add initial step
  steps.push({
    type: 'initialize',
    message: `Initialize Rod Cutting algorithm with rod length ${rodLength} and prices [${prices.join(', ')}]`,
    prices,
    rodLength,
    dp: null,
    cuts: null,
    currentLength: null,
    currentCut: null,
    optimalCuts: null,
    result: null,
    pseudocodeLine: 1
  });

  // Create DP table
  // dp[i] represents the maximum value obtainable from a rod of length i
  const dp = Array(rodLength + 1).fill(0);

  // Create array to track the optimal cuts
  const cuts = Array(rodLength + 1).fill(0);

  // Add DP table initialization step
  steps.push({
    type: 'initialize_table',
    message: `Initialize the DP table. dp[i] will store the maximum value obtainable from a rod of length i.`,
    prices,
    rodLength,
    dp: [...dp],
    cuts: [...cuts],
    currentLength: null,
    currentCut: null,
    optimalCuts: null,
    result: null,
    pseudocodeLine: 4
  });

  // Fill the DP table
  for (let i = 1; i <= rodLength; i++) {
    // Add step for current rod length
    steps.push({
      type: 'consider_length',
      message: `Consider rod of length ${i}`,
      prices,
      rodLength,
      dp: [...dp],
      cuts: [...cuts],
      currentLength: i,
      currentCut: null,
      optimalCuts: null,
      result: null,
      pseudocodeLine: 6
    });

    let maxValue = -Infinity;
    let bestCut = 0;

    for (let j = 1; j <= i; j++) {
      // Add step for considering current cut
      steps.push({
        type: 'consider_cut',
        message: `Consider cutting the rod at position ${j} (price: ${prices[j]})`,
        prices,
        rodLength,
        dp: [...dp],
        cuts: [...cuts],
        currentLength: i,
        currentCut: j,
        optimalCuts: null,
        result: null,
        pseudocodeLine: 8
      });

      // Calculate value for this cut
      const currentValue = prices[j] + dp[i - j];
      
      // Add step for calculating value
      steps.push({
        type: 'calculate_value',
        message: `Calculate value: price[${j}] + dp[${i}-${j}] = ${prices[j]} + ${dp[i-j]} = ${currentValue}`,
        prices,
        rodLength,
        dp: [...dp],
        cuts: [...cuts],
        currentLength: i,
        currentCut: j,
        currentValue,
        maxValue,
        optimalCuts: null,
        result: null,
        pseudocodeLine: 9
      });

      // Update if this gives a better solution
      if (currentValue > maxValue) {
        maxValue = currentValue;
        bestCut = j;
        
        // Add step for updating maximum value
        steps.push({
          type: 'update_max',
          message: `Update maximum value for length ${i} to ${maxValue} with cut at position ${bestCut}`,
          prices,
          rodLength,
          dp: [...dp],
          cuts: [...cuts],
          currentLength: i,
          currentCut: j,
          currentValue,
          maxValue,
          bestCut,
          optimalCuts: null,
          result: null,
          pseudocodeLine: 11
        });
      } else {
        // Add step for not updating maximum value
        steps.push({
          type: 'no_update',
          message: `No update needed as current value ${currentValue} is not better than maximum value ${maxValue}`,
          prices,
          rodLength,
          dp: [...dp],
          cuts: [...cuts],
          currentLength: i,
          currentCut: j,
          currentValue,
          maxValue,
          optimalCuts: null,
          result: null,
          pseudocodeLine: 13
        });
      }
    }

    // Update DP table and cuts array
    dp[i] = maxValue;
    cuts[i] = bestCut;
    
    // Add step for updating DP table
    steps.push({
      type: 'update_dp',
      message: `Update dp[${i}] = ${maxValue} and cuts[${i}] = ${bestCut}`,
      prices,
      rodLength,
      dp: [...dp],
      cuts: [...cuts],
      currentLength: i,
      currentCut: null,
      optimalCuts: null,
      result: null,
      pseudocodeLine: 15
    });
  }

  // Trace back to find the optimal cuts
  const optimalCuts = [];
  let remainingLength = rodLength;
  
  // Add step for starting traceback
  steps.push({
    type: 'traceback_start',
    message: 'Start tracing back to find the optimal cuts',
    prices,
    rodLength,
    dp: [...dp],
    cuts: [...cuts],
    currentLength: null,
    currentCut: null,
    optimalCuts: [...optimalCuts],
    remainingLength,
    result: dp[rodLength],
    pseudocodeLine: 18
  });

  // Trace back to find the optimal cuts
  while (remainingLength > 0) {
    const cut = cuts[remainingLength];
    optimalCuts.push(cut);
    
    // Add step for selecting cut
    steps.push({
      type: 'select_cut',
      message: `Select cut of length ${cut} for remaining length ${remainingLength}`,
      prices,
      rodLength,
      dp: [...dp],
      cuts: [...cuts],
      currentLength: remainingLength,
      currentCut: cut,
      optimalCuts: [...optimalCuts],
      remainingLength,
      result: dp[rodLength],
      pseudocodeLine: 20
    });
    
    remainingLength -= cut;
    
    // Add step for updating remaining length
    if (remainingLength > 0) {
      steps.push({
        type: 'update_remaining',
        message: `Update remaining length: ${remainingLength + cut} - ${cut} = ${remainingLength}`,
        prices,
        rodLength,
        dp: [...dp],
        cuts: [...cuts],
        currentLength: remainingLength,
        currentCut: null,
        optimalCuts: [...optimalCuts],
        remainingLength,
        result: dp[rodLength],
        pseudocodeLine: 21
      });
    }
  }

  // Add final step
  steps.push({
    type: 'complete',
    message: `Algorithm complete. Maximum value: ${dp[rodLength]}. Optimal cuts: [${optimalCuts.join(', ')}]`,
    prices,
    rodLength,
    dp: [...dp],
    cuts: [...cuts],
    currentLength: null,
    currentCut: null,
    optimalCuts: [...optimalCuts],
    result: dp[rodLength],
    pseudocodeLine: 24
  });

  return { 
    steps, 
    result: dp[rodLength],
    optimalCuts
  };
};

// Create the algorithm object with helper functions
const RodCuttingAlgorithm = {
  // Placeholder function to satisfy component requirements
  default: () => null,
  
  // Helper functions
  generateRodCuttingSteps
};

export default RodCuttingAlgorithm;
