/**
 * Simple utility for storing and retrieving layout preferences
 */

// Constants for storage keys
const SIDEBAR_STATE_KEY = 'algorithm_simulator_sidebar_state';
const PANEL_WIDTH_KEY = 'algorithm_simulator_panel_width';

/**
 * Save sidebar collapsed state
 * @param {boolean} isCollapsed - Whether the sidebar is collapsed
 */
export const saveSidebarState = (isCollapsed) => {
  try {
    window.localStorage.setItem(SIDEBAR_STATE_KEY, isCollapsed ? '1' : '0');
    console.log(`Saved sidebar state: ${isCollapsed}`);
  } catch (error) {
    console.error('Failed to save sidebar state:', error);
  }
};

/**
 * Get sidebar collapsed state
 * @returns {boolean} - Whether the sidebar is collapsed
 */
export const getSidebarState = () => {
  try {
    const state = window.localStorage.getItem(SIDEBAR_STATE_KEY);
    return state === '1';
  } catch (error) {
    console.error('Failed to get sidebar state:', error);
    return false; // Default to expanded
  }
};

/**
 * Save panel width
 * @param {number} width - The panel width in pixels
 */
export const savePanelWidth = (width) => {
  try {
    window.localStorage.setItem(PANEL_WIDTH_KEY, width.toString());
    console.log(`Saved panel width: ${width}px`);
  } catch (error) {
    console.error('Failed to save panel width:', error);
  }
};

/**
 * Get panel width
 * @param {number} defaultWidth - Default width to return if no saved width exists
 * @returns {number} - The panel width in pixels
 */
export const getPanelWidth = (defaultWidth = 400) => {
  try {
    const width = window.localStorage.getItem(PANEL_WIDTH_KEY);
    if (width) {
      const parsedWidth = parseInt(width, 10);
      return isNaN(parsedWidth) ? defaultWidth : parsedWidth;
    }
    return defaultWidth;
  } catch (error) {
    console.error('Failed to get panel width:', error);
    return defaultWidth;
  }
};
